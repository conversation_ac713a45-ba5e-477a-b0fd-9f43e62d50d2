# Starter pipeline
# Start with a minimal pipeline that you can customize to build and deploy your code.
# Add steps that build, run tests, deploy, and more:
# https://aka.ms/yaml

trigger:
  - none

stages:
  - stage: Checkmarkx
    displayName:
    pool:
      name: private build
    jobs:
      - job:
        displayName:
        steps:
          - task: DownloadSecureFile@1
            inputs:
              secureFile: 'CxSast_root.pem'
          - task: Application security testing@2023
            inputs:
              projectName: '[APP-01756]Kotak Mobile Banking 2.0'
              syncMode: false
              enableProxy: false
              enableSastScan: true
              CheckmarxService: 'uao-Checkmarx-Connection'
              fullTeamName: 'CxServer\APP-01756:Kotak Mobile Banking 2.0'
              sastCaChainFilePath: '$(Agent.TempDirectory)/CxSast_root.pem'
              incScan: false
              comment: 'APP-01756:Kotak Mobile Banking 2.0:$(Build.Repository.Name):kotak-uao-db-service-FullScan'
              enableDependencyScan: false