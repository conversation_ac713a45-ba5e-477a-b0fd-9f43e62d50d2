package com.kotak.unified.dbservice.exceptions.custom;

import com.kotak.unified.dbservice.model.exception.DatabaseErrorResponse;
import lombok.Getter;
import org.springframework.http.HttpStatus;

import java.util.List;

@Getter
public class RestException extends RuntimeException {
    private final HttpStatus httpStatus;

    private final List<DatabaseErrorResponse> errorResponseList;

    public RestException(HttpStatus httpStatus, DatabaseErrorResponse errorResponse) {
        this(httpStatus, errorResponse, null);
    }

    public RestException(HttpStatus httpStatus, DatabaseErrorResponse errorResponse, Throwable cause) {
        this(httpStatus, List.of(errorResponse), cause);
    }

    public RestException(HttpStatus httpStatus, List<DatabaseErrorResponse> errorResponseList, Throwable cause) {
        super(cause);
        this.httpStatus = httpStatus;
        this.errorResponseList = errorResponseList;
    }
}
