package com.kotak.unified.dbservice.exceptions;

import com.kotak.unified.db.model.ErrorResponse;
import com.kotak.unified.dbservice.controller.UniversalKYCStatusController;
import com.kotak.unified.dbservice.enums.ErrorCause;
import com.kotak.unified.dbservice.exceptions.custom.RestException;
import com.kotak.unified.dbservice.model.exception.DatabaseErrorResponse;
import com.kotak.unified.dbservice.utils.Constants;
import jakarta.validation.ConstraintViolationException;
import jakarta.validation.Path;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.List;


@Slf4j
@Order(0)
@RestControllerAdvice(assignableTypes = {UniversalKYCStatusController.class})
public class OpenAPIExceptionHandler {

    @ExceptionHandler(value = {ConstraintViolationException.class})
    public ResponseEntity<ErrorResponse> handleConstraintViolationException(ConstraintViolationException e) {
        log.error("Encountered ConstraintViolationException", e);
        List<String> errorMessageList = e
                .getConstraintViolations()
                .stream()
                .map(constraintViolation -> {
                    Path propertyPath = constraintViolation.getPropertyPath();
                    String leafNode = null;
                    for (Path.Node node: propertyPath) {
                        leafNode = node.getName();
                    }
                    return String.format(Constants.VALIDATION_ERROR_MESSAGE_FORMAT, leafNode, constraintViolation.getMessage());
                })
                .toList();
        String errorMessage = String.join(", ", errorMessageList);
        return new ResponseEntity<>(
                ErrorResponse.builder()
                        .errorCode(ErrorCause.INPUT_VALIDATION_FAILED.getCode())
                        .errorMessage(errorMessage)
                        .build(),
                HttpStatus.BAD_REQUEST
        );
    }

    @ExceptionHandler(value = { MethodArgumentNotValidException.class })
    public ResponseEntity<ErrorResponse> handleMethodArgumentNotValidException(
            MethodArgumentNotValidException e
    ) {
        log.warn("Encountered MethodArgumentNotValidException", e);
        List<String> errorMessageList = e.getBindingResult()
                .getFieldErrors()
                .stream()
                .map(fieldError -> String.format(
                                Constants.VALIDATION_ERROR_MESSAGE_FORMAT,
                                fieldError.getField(),
                                fieldError.getDefaultMessage()
                        ))
                .toList();
        String errorMessage = String.join(", ", errorMessageList);
        return new ResponseEntity<>(
                ErrorResponse.builder()
                        .errorCode(ErrorCause.INPUT_VALIDATION_FAILED.getCode())
                        .errorMessage(errorMessage)
                        .build(),
                HttpStatus.BAD_REQUEST
        );
    }

    @ExceptionHandler(value = {RestException.class})
    public ResponseEntity<ErrorResponse> handleRestException(RestException e) {
        if (e.getHttpStatus() != null && e.getHttpStatus().is4xxClientError()) {
            log.warn("Encountered Rest Exception", e);
        } else {
            log.error("Encountered Rest Exception", e);
        }
        DatabaseErrorResponse databaseErrorResponse = e.getErrorResponseList().get(0);
        return ResponseEntity.status(e.getHttpStatus()).body(
                ErrorResponse.builder()
                        .errorMessage(databaseErrorResponse.getErrorMessage())
                        .errorCode(databaseErrorResponse.getErrorCode())
                        .build()
        );
    }


    @ExceptionHandler(value = {MissingServletRequestParameterException.class})
    public ResponseEntity<ErrorResponse> handleMissingParameterException(MissingServletRequestParameterException e) {
        log.error("Encountered missing servlet request parameter exception, {}", e.getCause(), e);
        ErrorResponse errorResponse = ErrorResponse.builder()
                .errorMessage(String.format(
                        Constants.GLOBAL_MISSING_QUERY_PARAM_EXCEPTION_MESSAGE,
                        e.getParameterName(),
                        e.getParameterType()
                    )
                )
                .errorCode(ErrorCause.QUERY_PARAM_NOT_FOUND.getCode())
                .build();
;
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
    }


    @ExceptionHandler(value = {IllegalArgumentException.class})
    public ResponseEntity<ErrorResponse> handleIllegalArgumentException(IllegalArgumentException e) {
        log.error("Encountered IllegalArgumentException", e);
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(
                ErrorResponse.builder()
                        .errorMessage(e.getMessage())
                        .errorCode(ErrorCause.INPUT_VALIDATION_FAILED.getCode())
                        .build()
        );
    }

    @ExceptionHandler(value = {RuntimeException.class})
    public ResponseEntity<ErrorResponse> handleRuntimeException(RuntimeException e) {
        log.error("Encountered Run time exception", e);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
                ErrorResponse.builder()
                        .errorMessage(e.getMessage())
                        .errorCode(ErrorCause.INTERNAL_SERVER_ERROR.getCode())
                        .build()
        );
    }



}
