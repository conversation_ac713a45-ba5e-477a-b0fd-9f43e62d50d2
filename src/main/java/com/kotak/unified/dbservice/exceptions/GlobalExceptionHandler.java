package com.kotak.unified.dbservice.exceptions;

import com.kotak.unified.common.response.ErrorResponse;
import com.kotak.unified.common.response.wrapper.ResponseWrapper;
import com.kotak.unified.dbservice.enums.ErrorCause;
import com.kotak.unified.dbservice.exceptions.custom.RestException;
import com.kotak.unified.dbservice.model.exception.DatabaseErrorResponse;
import com.kotak.unified.dbservice.utils.Constants;
import jakarta.validation.ConstraintViolationException;
import jakarta.validation.Path;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.ws.rs.core.Response;
import java.util.List;

@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    @ExceptionHandler(value = {RestException.class})
    public ResponseEntity<ResponseWrapper<Void>> handleRestException(RestException e) {
        if (e.getHttpStatus() != null && e.getHttpStatus().is4xxClientError()) {
            log.warn("Encountered Rest Exception", e);
        } else {
            log.error("Encountered Rest Exception", e);
        }
        return new ResponseEntity<>(
                ResponseWrapper.failure(
                        e.getErrorResponseList().stream().map(DatabaseErrorResponse::toErrorResponse).toList()
                ),
                e.getHttpStatus()
        );
    }

    @ExceptionHandler(value = { Throwable.class })
    public ResponseEntity<ResponseWrapper<Void>> handleGenericThrowable(Throwable throwable) {
        log.error(
                String.format(
                        Constants.GLOBAL_EXCEPTION_HANDLER_CAPTURE_MESSAGE,
                        throwable.getClass().getName()
                ),
                throwable
        );
        DatabaseErrorResponse errorResponse = DatabaseErrorResponse.fromErrorCode(ErrorCause.INTERNAL_SERVER_ERROR);
        return new ResponseEntity<>(
                ResponseWrapper.failure(List.of(errorResponse)),
                HttpStatus.INTERNAL_SERVER_ERROR
        );
    }

    @ExceptionHandler(value = { HttpMediaTypeNotSupportedException.class })
    public ResponseEntity<ResponseWrapper<Void>> handleHttpMediaTypeNotSupportedException(
            HttpMediaTypeNotSupportedException exception
    ) {
        DatabaseErrorResponse errorResponse = DatabaseErrorResponse.fromErrorCode(ErrorCause.INPUT_VALIDATION_FAILED);
        return new ResponseEntity<>(
                ResponseWrapper.failure(List.of(errorResponse)),
                HttpStatus.BAD_REQUEST
        );
    }

    @ExceptionHandler(value = {ConstraintViolationException.class})
    public ResponseEntity<ResponseWrapper<Void>> handleConstraintViolationException(ConstraintViolationException e) {
        log.error("Encountered ConstraintViolationException, {}", e.getMessage(), e);
        List<DatabaseErrorResponse> errorResponseList = e
                .getConstraintViolations()
                .stream()
                .map(constraintViolation -> {
                    Path propertyPath = constraintViolation.getPropertyPath();
                    String leafNode = null;
                    for (Path.Node node: propertyPath) {
                        leafNode = node.getName();
                    }
                    DatabaseErrorResponse databaseErrorResponse = DatabaseErrorResponse.fromErrorCode(ErrorCause.INPUT_VALIDATION_FAILED);
                    databaseErrorResponse.setErrorMessage(String.format(Constants.VALIDATION_ERROR_MESSAGE_FORMAT, leafNode, constraintViolation.getMessage()));
                    return databaseErrorResponse;
                })
                .toList();
        return new ResponseEntity<>(
                ResponseWrapper.failure(errorResponseList.stream().map(DatabaseErrorResponse::toErrorResponse).toList()),
                HttpStatus.BAD_REQUEST
        );
    }

    @ExceptionHandler(value = { MethodArgumentNotValidException.class })
    public ResponseEntity<ResponseWrapper<Void>> handleMethodArgumentNotValidException(
            MethodArgumentNotValidException exception
    ) {
        List<DatabaseErrorResponse> errorResponses = exception.getBindingResult()
                .getFieldErrors()
                .stream()
                .map(fieldError -> {
                    DatabaseErrorResponse errorResponse = DatabaseErrorResponse.fromErrorCode(ErrorCause.INPUT_VALIDATION_FAILED);
                    errorResponse.setErrorMessage(
                            String.format(
                                    Constants.VALIDATION_ERROR_MESSAGE_FORMAT,
                                    fieldError.getField(),
                                    fieldError.getDefaultMessage()
                            )
                    );
                    return errorResponse;
                })
                .toList();
        return new ResponseEntity<>(
                ResponseWrapper.failure(errorResponses.stream().map(DatabaseErrorResponse::toErrorResponse).toList()),
                HttpStatus.BAD_REQUEST
        );
    }

    @ExceptionHandler(value = {RuntimeException.class})
    public ResponseEntity<ResponseWrapper<Void>> handleRuntimeException(RuntimeException e) {
        log.error("Encountered Run time exception, {}", e.getMessage(), e.getCause());
        return new ResponseEntity<>(
                ResponseWrapper.failure(List.of(DatabaseErrorResponse.toErrorResponse(DatabaseErrorResponse.fromErrorCode(ErrorCause.INTERNAL_SERVER_ERROR)), new ErrorResponse(HttpStatus.INTERNAL_SERVER_ERROR.toString(),e.getMessage()))),
                HttpStatus.INTERNAL_SERVER_ERROR
        );
    }

    @ExceptionHandler(value = {MissingServletRequestParameterException.class})
    public ResponseEntity<ResponseWrapper<Void>> handleMissingParameterException(MissingServletRequestParameterException e) {
        log.error("Encountered missing servlet request parameter exception, {}", e.getMessage(), e.getCause());
        DatabaseErrorResponse errorResponse = DatabaseErrorResponse.fromErrorCode(ErrorCause.QUERY_PARAM_NOT_FOUND);
        errorResponse.setErrorMessage(
                String.format(
                        Constants.GLOBAL_MISSING_QUERY_PARAM_EXCEPTION_MESSAGE,
                        e.getParameterName(),
                        e.getParameterType()
                )
        );
        return new ResponseEntity<>(
                ResponseWrapper.failure(List.of(DatabaseErrorResponse.toErrorResponse(errorResponse))),
                HttpStatus.BAD_REQUEST
        );
    }

    @ExceptionHandler(value = {UnsupportedOperationException.class})
    public ResponseEntity<ResponseWrapper<Void>> handleUnsupportedOperationException(UnsupportedOperationException e)
    {
        log.error("Encountered unsupported operation exception " + e.getMessage(), e.getCause());
        DatabaseErrorResponse errorResponse = DatabaseErrorResponse.fromErrorCode(ErrorCause.INPUT_VALIDATION_FAILED);
        errorResponse.setErrorMessage(e.getMessage());
        return new ResponseEntity<>(
                ResponseWrapper.failure(List.of(DatabaseErrorResponse.toErrorResponse(errorResponse))),
                HttpStatus.BAD_REQUEST
        );
    }
}
