package com.kotak.unified.dbservice.accessor;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.kotak.unified.common.request.bcif.GetCrnDetailsRequest;
import com.kotak.unified.common.response.bcif.GetCrnDetailsResponse;
import com.kotak.unified.dbservice.model.ExternalServiceExecutionDetails;
import jakarta.inject.Named;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.ws.rs.client.WebTarget;

import static com.kotak.unified.dbservice.utils.WebTargetUtil.executePostRequest;

@Service
public class BcifServiceAccessor {
    private final WebTarget bcifServiceWebTarget;
    private static final String bcifBasePath = "/api/v1/bcif";
    private static final String getDynamicInfoAPIPath = bcifBasePath + "/get-crn-details";

    @Autowired
    public BcifServiceAccessor(@Named("BcifServiceWebTarget") WebTarget bcifServiceWebTarget) {
        this.bcifServiceWebTarget = bcifServiceWebTarget;
    }

    public ExternalServiceExecutionDetails<GetCrnDetailsRequest, GetCrnDetailsResponse> getDynamicInfo(String crn, String trackId)
            throws JsonProcessingException {
        GetCrnDetailsRequest request = GetCrnDetailsRequest.builder()
                .encryptedCRN(crn)
                .trackId(trackId)
                .build();
        return executePostRequest(bcifServiceWebTarget, getDynamicInfoAPIPath, request, GetCrnDetailsResponse.class);
    }
}

//getDynamicInfo   api