package com.kotak.unified.dbservice.accessor;

import com.kotak.unified.databridgeinterface.model.sqs.DataChangeNotificationMessage;
import com.kotak.unified.dbservice.config.DataChangeNotificationConfiguration;
import com.kotak.unified.dbservice.transformer.DataChangeNotificationMessageTransformer;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.awscore.exception.AwsServiceException;
import software.amazon.awssdk.services.sqs.SqsClient;
import software.amazon.awssdk.services.sqs.model.SendMessageRequest;

import javax.ws.rs.InternalServerErrorException;
import java.net.SocketException;

@Service
public class DataChangeNotificationQueueAccessor {
    private final DataChangeNotificationMessageTransformer dataChangeNotificationMessageTransformer;
    private final SqsClient sqsClient;
    private final DataChangeNotificationConfiguration dataChangeNotificationConfiguration;

    @Autowired
    public DataChangeNotificationQueueAccessor(DataChangeNotificationMessageTransformer dataChangeNotificationMessageTransformer, SqsClient sqsClient, DataChangeNotificationConfiguration dataChangeNotificationConfiguration) {
        this.dataChangeNotificationMessageTransformer = dataChangeNotificationMessageTransformer;
        this.sqsClient = sqsClient;
        this.dataChangeNotificationConfiguration = dataChangeNotificationConfiguration;
    }

    @Retryable(
            retryFor = {InternalServerErrorException.class, SocketException.class, AwsServiceException.class},
            backoff = @Backoff(multiplier = 3, delay = 500)
    )
    @SneakyThrows
    public void publishMessage(DataChangeNotificationMessage dataChangeNotificationMessage) {
        try {
            String queueUrl = dataChangeNotificationConfiguration.getQueueUrl();
            SendMessageRequest sendMessageRequest = dataChangeNotificationMessageTransformer.convertDataBridgePublishMessageInputToMessageRequest(
                    dataChangeNotificationMessage,
                    queueUrl,
                    0
            );
            sqsClient.sendMessage(sendMessageRequest);
        } catch (InternalServerErrorException | AwsServiceException e) {
            String throwableMessage = String.format(
                    "Retryable Message Publishing failed for tracking number = %s",
                    dataChangeNotificationMessage.getDataSourceTrackingId()
            );
            throw new InternalServerErrorException(throwableMessage, e.getCause());
        } catch (Exception e) {
            String throwableMessage = String.format(
                    "Message Publishing failed for tracking number = %s",
                    dataChangeNotificationMessage.getDataSourceTrackingId()
            );
            throw new RuntimeException(throwableMessage, e.getCause());
        }
    }
}
