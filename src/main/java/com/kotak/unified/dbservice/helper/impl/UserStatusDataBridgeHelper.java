package com.kotak.unified.dbservice.helper.impl;

import com.kotak.unified.common.exception.DataBridgePublishingException;
import com.kotak.unified.databridgeinterface.enums.DataSource;
import com.kotak.unified.databridgeinterface.model.sqs.DataChangeNotificationMessage;
import com.kotak.unified.dbservice.accessor.DataChangeNotificationQueueAccessor;
import com.kotak.unified.dbservice.annotation.Helper;
import com.kotak.unified.dbservice.helper.DataBridgeHelper;
import com.kotak.unified.orchestrator.common.dbmodels.UserStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

@Helper
@Slf4j
public class UserStatusDataBridgeHelper implements DataBridgeHelper<UserStatus> {
    private final DataChangeNotificationQueueAccessor dataChangeNotificationQueueAccessor;

    @Autowired
    public UserStatusDataBridgeHelper(DataChangeNotificationQueueAccessor dataChangeNotificationQueueAccessor) {
        this.dataChangeNotificationQueueAccessor = dataChangeNotificationQueueAccessor;
    }

    @Override
    public void publishToDataBridge(UserStatus userStatus, DataSource dataSource) throws DataBridgePublishingException {
        try {
            DataChangeNotificationMessage dataChangeNotificationMessage = DataChangeNotificationMessage.builder()
                    .dataSourceTrackingId(userStatus.getLeadTrackingNumber())
                    .dataSource(dataSource)
                    .build();
            dataChangeNotificationQueueAccessor.publishMessage(dataChangeNotificationMessage);
        } catch (Exception exception) {
            log.error( "User Status Publish Message failed for {} of type {}", userStatus.getLeadTrackingNumber(), dataSource, exception);
            throw new DataBridgePublishingException(userStatus.getLeadTrackingNumber(), userStatus.getLeadTrackingNumber(),
                    dataSource.name(), exception);
        }
    }
}
