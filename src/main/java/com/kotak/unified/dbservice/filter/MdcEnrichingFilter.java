package com.kotak.unified.dbservice.filter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebFilter;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.MDC;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.UUID;

import static org.springframework.core.Ordered.LOWEST_PRECEDENCE;

@Order(LOWEST_PRECEDENCE)
@Component
@WebFilter(filterName = "MdcEnrichingFilter", urlPatterns = "/*")
@ConditionalOnProperty(name = "web.filter.mdcEnrichingFilter",
        havingValue = "true", matchIfMissing = true)
public class MdcEnrichingFilter extends OncePerRequestFilter {

    private static final String X_CORRELATION_ID = "X-Correlation-ID";

    public static final String X_REQUEST_ID = "X-Request-ID";

    /**
     * MDC Enrichment Filter for Request Tracking. This filter functions as an
     * intermediary component within the application flow, specifically designed
     * to augment the Mapped Diagnostic Context (MDC) with pertinent request
     * tracking data.
     *
     * @param request     httpServletRequest object.
     * @param response    httpServletResponse object.
     * @param filterChain filterChain object.
     * @throws ServletException ServletException object.
     * @throws IOException      IOException object.
     */
    @Override
    protected void doFilterInternal(final HttpServletRequest request,
                                    @NotNull final HttpServletResponse response,
                                    @NotNull final FilterChain filterChain) throws ServletException, IOException {
        try {
      /*
        The RequestID is a unique identifier generated by the service upon receiving a request.
        It serves to differentiate between individual requests processed by the service concurrently.
       */
            final String requestId = UUID.randomUUID().toString();
      /*
        CorrelationID acts as a unifying thread across services. Ideally, this identifier should generate
        from the initial request source and is propagated (passed along) in all subsequent service calls
        as a header. This allows for tracing a user's journey through the entire system by linking
        together logs from different services that share the same CorrelationID.
       */
            String correlationId = request.getHeader(X_CORRELATION_ID);
            if (!this.isValidUUID(correlationId)) {
                correlationId = UUID.randomUUID().toString();
            }
            MDC.put(X_CORRELATION_ID, correlationId);
            MDC.put(X_REQUEST_ID, requestId);
            filterChain.doFilter(request, response);
        } finally {
            MDC.remove(X_CORRELATION_ID);
            MDC.remove(X_REQUEST_ID);
        }
    }

    private boolean isValidUUID(String uuid) {
        if (StringUtils.isBlank(uuid)) {
            return false;
        } else {
            try {
                UUID.fromString(uuid);
                return true;
            } catch (IllegalArgumentException var3) {
                return false;
            }
        }
    }
}
