package com.kotak.unified.dbservice.validator;

import com.kotak.unified.db.model.BiometricsKYCMetadataDTO;
import com.kotak.unified.db.model.CreateUKYCRecordRequest;
import com.kotak.unified.db.model.KYCChannelDTO;
import com.kotak.unified.db.model.KYCMetadataDTO;
import com.kotak.unified.db.model.UKYCStatusDTO;
import com.kotak.unified.db.model.UpdateUKYCRecordRequest;
import com.kotak.unified.db.model.VideoKYCMetadataDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class UniversalKYCStatusAPIRequestValidator {
    private static final Map<KYCChannelDTO, String> KYC_CHANNEL_TO_KYCMETADATA_TYPE_MAP = Map.ofEntries(
            Map.entry(KYCChannelDTO.VIDEO_KYC, VideoKYCMetadataDTO.class.getSimpleName()),
            Map.entry(KYCChannelDTO.BIOMETRICS_KYC, BiometricsKYCMetadataDTO.class.getSimpleName())
    );
    static String ukycAllowedJourneyTypes = System.getenv("UKYC_ALLOWED_JOURNEY_TYPES");

    public static void validateJourneyType(String journeyType, String leadTrackingId) {
        List<String> allowedJourneyTypes = List.of(ukycAllowedJourneyTypes.split(","));
        if (!allowedJourneyTypes.contains(journeyType)) {
            throw new IllegalArgumentException(journeyType + " journeyType can't access create or update UKYC API for leadTrackingID " + leadTrackingId);
        }
    }

    public static void validate(KYCMetadataDTO kycMetadata, KYCChannelDTO kycChannel) {
        if (kycChannel != null && kycMetadata != null) {
            String kycMetadataType = kycMetadata.getType();
            if (!kycMetadataType.equals(KYC_CHANNEL_TO_KYCMETADATA_TYPE_MAP.get(kycChannel))) {
                throw new IllegalArgumentException(
                        String.format("KycMetadata type: %s does not match kycChannel %s.",
                                kycMetadataType, kycChannel));
            }
        }
    }

    public void validate(CreateUKYCRecordRequest createUKYCRecordRequest) {
        validateJourneyType(createUKYCRecordRequest.getJourneyType(), createUKYCRecordRequest.getApplicationId());
        if (createUKYCRecordRequest.getKycChannel() != null) {
            if (createUKYCRecordRequest.getKycStatus() == null
                    || createUKYCRecordRequest.getActionTrackingId() == null) {
                throw new IllegalArgumentException(
                        "KycStatus or actionTrackingId or kycMetadata cannot be null when kycChannel is not null.");
            }
        } else {
            if (createUKYCRecordRequest.getActionTrackingId() != null ||
                    createUKYCRecordRequest.getKycMetadata() != null) {
                throw new IllegalArgumentException(
                        "actionTrackingId or kycMetadata cannot be non null when kycChannel is null.");
            }
        }
        validate(createUKYCRecordRequest.getKycMetadata(), createUKYCRecordRequest.getKycChannel());
    }

    public void validate(UpdateUKYCRecordRequest updateUKYCRecordRequest) {
        validateUpdateUKYCRecordRequest(updateUKYCRecordRequest);
    }

    private void validateUpdateUKYCRecordRequest(UpdateUKYCRecordRequest updateUKYCRecordRequest) {
        if (updateUKYCRecordRequest.getUkycStatus() != null) {
            if (updateUKYCRecordRequest.getActionTrackingId() != null ||
                    updateUKYCRecordRequest.getKycChannel() != null ||
                    updateUKYCRecordRequest.getKycStatus() != null) {
                throw new IllegalArgumentException(String.format("ActionTrackingId, KycChannel and KycStatus must be null, when ukycStatus is present."));
            }

            if (!(updateUKYCRecordRequest.getUkycStatus().equals(UKYCStatusDTO.ABORTED)
                    || updateUKYCRecordRequest.getUkycStatus().equals(UKYCStatusDTO.INITIATED)
                    || updateUKYCRecordRequest.getUkycStatus().equals(UKYCStatusDTO.PENDING))) {
                throw new IllegalArgumentException(String.format("Invalid ukycstatus value for update request."));
            }

        } else {
            if (updateUKYCRecordRequest.getActionTrackingId() == null) {
                throw new IllegalArgumentException(String.format("ActionTrackingId must be not null."));
            }

            if (updateUKYCRecordRequest.getKycChannel() == null) {
                throw new IllegalArgumentException(String.format("KycChannel must be not null."));
            }

            if (updateUKYCRecordRequest.getKycStatus() == null) {
                throw new IllegalArgumentException(String.format("KycStatus must be not null."));
            }
            validate(updateUKYCRecordRequest.getKycMetadata(), updateUKYCRecordRequest.getKycChannel());
        }
    }
}
