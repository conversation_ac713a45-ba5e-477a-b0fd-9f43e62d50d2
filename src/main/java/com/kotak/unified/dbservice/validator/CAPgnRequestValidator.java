package com.kotak.unified.dbservice.validator;

import com.kotak.unified.db.request.InsertCACrnAndAccountNumberRequest;
import com.kotak.unified.enums.CAJourneyType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class CAPgnRequestValidator {

    public static void validateInsertPGNRequest(InsertCACrnAndAccountNumberRequest request) {

        CAJourneyType journeyType = request.getJourneyType();

        if(journeyType == CAJourneyType.BUSINESS_NON_PREFERRED_CURRENT_ACCOUNT
                || journeyType == CAJourneyType.INDIVIDUAL_NON_PREFERRED_CURRENT_ACCOUNT){
            checkNotNull(request.getAccountNumber(), "Account Number can not be null");
        }

        if(journeyType == CAJourneyType.BUSINESS_NON_PREFERRED_CURRENT_ACCOUNT
                || journeyType == CAJourneyType.BUSINESS_PREFERRED_CURRENT_ACCOUNT){
            checkNotNull(request.getEntityCrn(), "Entity CRN can not be null");
        }
    }

    public static void checkNotNull(Object object, String errorMessage) {
        if(object == null) {
            throw new IllegalArgumentException(errorMessage);
        }
    }

}
