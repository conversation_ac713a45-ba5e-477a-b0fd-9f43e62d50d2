package com.kotak.unified.dbservice.repository;

import com.kotak.unified.enums.CAJourneyType;
import com.kotak.unified.dbservice.exceptions.PregeneratedCRNAccountNumDepletedException;
import com.kotak.unified.dbservice.model.CAPreGeneratedCRNAccountNumber;
import com.kotak.unified.dbservice.model.PGNStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Expression;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryEnhancedRequest;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.model.ScanEnhancedRequest;
import software.amazon.awssdk.enhanced.dynamodb.model.TransactUpdateItemEnhancedRequest;
import software.amazon.awssdk.enhanced.dynamodb.model.TransactWriteItemsEnhancedRequest;
import software.amazon.awssdk.services.dynamodb.model.AttributeValue;

import java.util.Collections;
import java.util.Iterator;
import java.util.Map;

@Repository
@Slf4j
public class CAPreGeneratedCRNAccountNumRepository {

    private final DynamoDbEnhancedClient dynamoDbEnhancedClient;

    private final DynamoDbTable<CAPreGeneratedCRNAccountNumber> caPreGeneratedCRNAccountNumberDynamoDbTable;


    public CAPreGeneratedCRNAccountNumRepository(
             DynamoDbEnhancedClient dynamoDbEnhancedClient,
             @Value("${dynamodb.CAPreGeneratedNumber.TableName}") String caPreGeneratedCRNAccountNumberDynamoDbTable) {
        this.dynamoDbEnhancedClient = dynamoDbEnhancedClient;
        this.caPreGeneratedCRNAccountNumberDynamoDbTable = dynamoDbEnhancedClient
                .table(caPreGeneratedCRNAccountNumberDynamoDbTable, TableSchema.fromBean(CAPreGeneratedCRNAccountNumber.class));
    }

    public CAPreGeneratedCRNAccountNumber assignPregeneratedCRNAccountNum(String leadTrackingNumber, CAJourneyType journeyType) throws PregeneratedCRNAccountNumDepletedException {

        Expression filterExpression = Expression.builder()
                .expression("#status = :UN_ASSIGNED")
                .putExpressionName("#status", "status")
                .expressionValues(Collections.singletonMap(":UN_ASSIGNED", AttributeValue.builder().s("UN_ASSIGNED").build()))
                .build();

        QueryEnhancedRequest getUnassignedPgnRequest = QueryEnhancedRequest.builder()
                .queryConditional(QueryConditional.keyEqualTo(Key.builder().partitionValue(AttributeValue.builder().s(journeyType.name()).build()).build()))
                .filterExpression(filterExpression)
                .consistentRead(true)
                .build();

        Iterator<CAPreGeneratedCRNAccountNumber> scanResult = caPreGeneratedCRNAccountNumberDynamoDbTable.query(getUnassignedPgnRequest).items().iterator();

        if (!scanResult.hasNext()){
            log.error("No pre-generated CRN account num available to assign with journey type: {}", journeyType.name());
            throw new PregeneratedCRNAccountNumDepletedException("No pre-generated CRN account num available to assign" +
                    " with journey type " + journeyType.name());
        }

        CAPreGeneratedCRNAccountNumber unassignedPGNItem = scanResult.next();

        TransactUpdateItemEnhancedRequest<CAPreGeneratedCRNAccountNumber> transactUpdateItemEnhancedRequest = TransactUpdateItemEnhancedRequest.<CAPreGeneratedCRNAccountNumber>builder(CAPreGeneratedCRNAccountNumber.class)
                .conditionExpression(Expression.builder()
                        .expression("#status = :UN_ASSIGNED")
                        .putExpressionValue(":UN_ASSIGNED", AttributeValue.builder().s("UN_ASSIGNED").build())
                        .putExpressionName("#status", "status")
                        .build()
                )
                .item(CAPreGeneratedCRNAccountNumber.builder()
                        .id(unassignedPGNItem.getId())
                        .entityCrn(unassignedPGNItem.getEntityCrn())
                        .accountNumber(unassignedPGNItem.getAccountNumber())
                        .crn(unassignedPGNItem.getCrn())
                        .journeyType(unassignedPGNItem.getJourneyType())
                        .status(PGNStatus.ASSIGNED)
                        .leadTrackingNumber(leadTrackingNumber)
                        .build())
                .build();

        TransactWriteItemsEnhancedRequest transactWriteItemsEnhancedRequest = TransactWriteItemsEnhancedRequest.builder()
                .addUpdateItem(caPreGeneratedCRNAccountNumberDynamoDbTable, transactUpdateItemEnhancedRequest)
                .build();

        dynamoDbEnhancedClient.transactWriteItems(transactWriteItemsEnhancedRequest);

        return CAPreGeneratedCRNAccountNumber.builder()
                .status(PGNStatus.ASSIGNED)
                .leadTrackingNumber(leadTrackingNumber)
                .journeyType(unassignedPGNItem.getJourneyType())
                .accountNumber(unassignedPGNItem.getAccountNumber())
                .crn(unassignedPGNItem.getCrn())
                .entityCrn(unassignedPGNItem.getEntityCrn())
                .build();
    }

    public void insertRecord(CAPreGeneratedCRNAccountNumber caPreGeneratedCRNAccountNumber) {
        caPreGeneratedCRNAccountNumberDynamoDbTable.putItem(caPreGeneratedCRNAccountNumber);
    }

    public boolean doesAccountNumberExists(String accountNumber) {

        Expression filterExpression = Expression.builder()
                .expression("accountNumber = :accountNumber")
                .expressionValues(Map.of(":accountNumber", AttributeValue.builder()
                        .s(accountNumber).build()))
                .build();

        ScanEnhancedRequest queryRequest = ScanEnhancedRequest.builder()
                .filterExpression(filterExpression)
                .consistentRead(true)
                .build();

        Iterator<CAPreGeneratedCRNAccountNumber> scanResult = caPreGeneratedCRNAccountNumberDynamoDbTable.scan(queryRequest).items().iterator();

        return scanResult.hasNext();
    }

    public boolean doesAusCrnExists(String crn) {

        Expression filterExpression = Expression.builder()
                .expression("crn = :crn")
                .expressionValues(Map.of(":crn", AttributeValue.builder().s(crn).build()))
                .build();

        ScanEnhancedRequest queryRequest = ScanEnhancedRequest.builder()
                .filterExpression(filterExpression)
                .consistentRead(true)
                .build();

        Iterator<CAPreGeneratedCRNAccountNumber> scanResult = caPreGeneratedCRNAccountNumberDynamoDbTable.scan(queryRequest).items().iterator();

        return scanResult.hasNext();
    }

    public boolean doesEntityCrnExists(String entityCrn) {

        Expression filterExpression = Expression.builder()
                .expression("entityCrn = :entityCrn")
                .expressionValues(Map.of(":entityCrn", AttributeValue.builder().s(entityCrn).build()))
                .build();

        ScanEnhancedRequest queryRequest = ScanEnhancedRequest.builder()
                .filterExpression(filterExpression)
                .consistentRead(true)
                .build();

        Iterator<CAPreGeneratedCRNAccountNumber> scanResult = caPreGeneratedCRNAccountNumberDynamoDbTable.scan(queryRequest).items().iterator();

        return scanResult.hasNext();
    }

    public boolean doesLeadTrackingNumberExists(String leadTrackingNumber) {

        Expression filterExpression = Expression.builder()
                .expression("#status = :ASSIGNED and leadTrackingNumber = :leadTrackingNumber")
                .expressionValues(Map.of(":ASSIGNED", AttributeValue.builder().s("ASSIGNED").build(),
                                ":leadTrackingNumber", AttributeValue.builder().s(leadTrackingNumber).build())
                )
                .putExpressionName("#status", "status")
                .build();

        ScanEnhancedRequest queryRequest = ScanEnhancedRequest.builder()
                .filterExpression(filterExpression)
                .consistentRead(true)
                .build();

        Iterator<CAPreGeneratedCRNAccountNumber> scanResult = caPreGeneratedCRNAccountNumberDynamoDbTable.scan(queryRequest).items().iterator();

        return scanResult.hasNext();
    }
}
