package com.kotak.unified.dbservice.repository;

import com.kotak.unified.dbservice.config.DynamoAWSConfiguration;
import com.kotak.unified.orchestrator.common.dbmodels.ApplicationDataDDBModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.NoHttpResponseException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Repository;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;

import static com.kotak.unified.dbservice.utils.Constants.DDB_MAX_RETRY_ATTEMPTS;
import static com.kotak.unified.dbservice.utils.Constants.RETRY_DEFAULT_DELAY;
import static com.kotak.unified.dbservice.utils.Constants.RETRY_DEFAULT_MULTIPLIER;

@Slf4j
@Repository
public class ApplicationDataDDBRepository {
    private final DynamoDbTable<ApplicationDataDDBModel> applicationDataDDBTable;

    public ApplicationDataDDBRepository(DynamoDbEnhancedClient dynamoDbEnhancedClient,
                                        DynamoAWSConfiguration dynamoAWSConfiguration) {
        applicationDataDDBTable = dynamoDbEnhancedClient.table(
                dynamoAWSConfiguration.getApplicationDataTableName(),
                TableSchema.fromBean(ApplicationDataDDBModel.class)
        );
    }

    @Retryable(
            retryFor = {NoHttpResponseException.class},
            maxAttempts = DDB_MAX_RETRY_ATTEMPTS,
            backoff = @Backoff(multiplier = RETRY_DEFAULT_MULTIPLIER, delay = RETRY_DEFAULT_DELAY)
    )
    public ApplicationDataDDBModel findByApplicationId(String applicationId) {
        try {
            return applicationDataDDBTable.getItem(
                    Key.builder().partitionValue(applicationId).build()
            );
        } catch (Exception ex) {
            // TODO - Added for temporary basis. Remove after verifying that retries are working on NoHttpResponseException
            log.warn("Encountered exception while findByApplicationId", ex);
            if (ex instanceof NoHttpResponseException) {
                log.warn("Encountered NoHttpResponseException for applicationId: {}", applicationId);
            }
            throw ex;
        }
    }

    public ApplicationDataDDBModel save(ApplicationDataDDBModel applicationData) {
        return applicationDataDDBTable.updateItem(applicationData);
    }
}
