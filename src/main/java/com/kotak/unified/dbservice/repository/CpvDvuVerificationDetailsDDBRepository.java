package com.kotak.unified.dbservice.repository;

import com.kotak.unified.dbservice.config.DynamoAWSConfiguration;
import com.kotak.unified.dbservice.model.cpv.CpvDvuVerificationDetailsDDBModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;

@Repository
public class CpvDvuVerificationDetailsDDBRepository {
    private final DynamoDbTable<CpvDvuVerificationDetailsDDBModel> cpvDvuVerificationDetailsDynamoDbTable;

    @Autowired
    public CpvDvuVerificationDetailsDDBRepository(
            DynamoDbEnhancedClient dynamoDbEnhancedClient,
            DynamoAWSConfiguration dynamoAWSConfiguration) {
        cpvDvuVerificationDetailsDynamoDbTable = dynamoDbEnhancedClient
                .table(dynamoAWSConfiguration.getCpvDvuVerificationDetailsTableName(),
                        TableSchema.fromBean(CpvDvuVerificationDetailsDDBModel.class));
    }

    public CpvDvuVerificationDetailsDDBModel save(CpvDvuVerificationDetailsDDBModel cpvDvuVerificationDetailsDDBModel) {
        return this.cpvDvuVerificationDetailsDynamoDbTable.updateItem(cpvDvuVerificationDetailsDDBModel);
    }

    public CpvDvuVerificationDetailsDDBModel getByActionTrackingId(String actionTrackingId) {
        return cpvDvuVerificationDetailsDynamoDbTable.getItem(
                Key.builder().partitionValue(actionTrackingId).build()
        );
    }
}
