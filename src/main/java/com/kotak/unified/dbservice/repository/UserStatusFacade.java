package com.kotak.unified.dbservice.repository;

import com.kotak.unified.dbservice.transformer.DDBModelTransformer;
import com.kotak.unified.orchestrator.common.dbmodels.ApplicationData;
import com.kotak.unified.orchestrator.common.dbmodels.ApplicationDataDDBModel;
import com.kotak.unified.orchestrator.common.dbmodels.UserJourneyStatusDDBModel;
import com.kotak.unified.orchestrator.common.dbmodels.UserStatus;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserStatusFacade {

    private final UserJourneyStatusDDBRepository userJourneyStatusDDBRepository;
    private final ApplicationDataDDBRepository applicationDataDDBRepository;
    private final DDBModelTransformer ddbModelTransformer;

    public UserStatus findByLeadTrackingNumber(final @NonNull String leadTrackingNumber) {
        try {
            UserJourneyStatusDDBModel userJourneyStatus = userJourneyStatusDDBRepository.findByLeadTrackingNumber(leadTrackingNumber);
            if (userJourneyStatus != null) {
                ApplicationDataDDBModel applicationData = applicationDataDDBRepository.findByApplicationId(leadTrackingNumber);
                return ddbModelTransformer.getUserStatusFromUserJourneyStatusAndApplicationData(userJourneyStatus, applicationData);
            } else {
                return  null;
            }
        } catch (Exception e) {
            log.error("Failure. findByLeadTrackingNumber from DynamoDB.", e);
            throw e;
        }
    }

    public ApplicationData findApplicationDataByLeadTrackingNumber(final @NonNull String leadTrackingNumber) {
        try {
            ApplicationDataDDBModel applicationData = applicationDataDDBRepository.findByApplicationId(leadTrackingNumber);
            if (applicationData != null) {
                return ddbModelTransformer.getApplicationDataFromApplicationDataDDBModel(applicationData);
            } else {
                String errorMessage = "ApplicationData not found for leadId: " + leadTrackingNumber;
                throw new RuntimeException(errorMessage);
            }
        } catch (Exception e) {
            log.error("Failure. findByLeadTrackingNumber from DynamoDB", e);
            throw e;
        }
    }

    public UserStatus save(@NonNull UserStatus userStatus) {
        UserJourneyStatusDDBModel userJourneyStatusDDBModel = ddbModelTransformer.getUserJourneyStatusDDBModelFromUserStatus(userStatus);
        ApplicationDataDDBModel applicationDataDDBModel = ddbModelTransformer.getApplicationDataDDBModelFromUserStatus(userStatus);
        //save to DynamoDB
        try {
            applicationDataDDBModel = applicationDataDDBRepository.save(applicationDataDDBModel);
            userJourneyStatusDDBModel = userJourneyStatusDDBRepository.save(userJourneyStatusDDBModel);
        } catch (Exception e) {
            log.error("Failure. save to DynamoDB", e);
            throw e;
        }
        ddbModelTransformer.updateUserStatus(userStatus, userJourneyStatusDDBModel, applicationDataDDBModel);
        return userStatus;
    }
}
