package com.kotak.unified.dbservice.repository;

import com.kotak.unified.dbservice.config.DynamoAWSConfiguration;
import com.kotak.unified.dbservice.model.pgn.PgnDDBModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import software.amazon.awssdk.core.pagination.sync.SdkIterable;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbIndex;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;
import software.amazon.awssdk.enhanced.dynamodb.model.Page;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryEnhancedRequest;

import java.util.Optional;

@Slf4j
@Repository
public class PgnDDBRepository {

    private final DynamoDbTable<PgnDDBModel> pgnDynamoDbTable;

    public PgnDDBRepository(DynamoDbEnhancedClient dynamoDbEnhancedClient,
                            DynamoAWSConfiguration dynamoAWSConfiguration) {

        this.pgnDynamoDbTable = dynamoDbEnhancedClient.table(dynamoAWSConfiguration.getPgnTableName(),
                TableSchema.fromBean(PgnDDBModel.class));
    }

    public PgnDDBModel findByLeadTrackingNumberAndPgnTypeAndPgnStatus(String leadTrackingNumber, String pgnType, String status) {
        String sortKeyValue = String.join("#", leadTrackingNumber, status);
        QueryEnhancedRequest queryEnhancedRequest = QueryEnhancedRequest.builder()
                .queryConditional(QueryConditional.keyEqualTo(
                        Key.builder().partitionValue(pgnType).sortValue(sortKeyValue).build())
                )
                .build();

        final DynamoDbIndex<PgnDDBModel> statusIndex = pgnDynamoDbTable.index("typeAndStatusIndex");
        final SdkIterable<Page<PgnDDBModel>> response = statusIndex.query(queryEnhancedRequest);
        Optional<Page<PgnDDBModel>> page = response.stream().findFirst();
        if (page.isPresent() && !page.get().items().isEmpty()) {
            return page.get().items().get(0);
        }
        return null;
    }

    public PgnDDBModel findByPgnTypeAndPgnStatus(String pgnType, String pgnStatus) {
        return findByLeadTrackingNumberAndPgnTypeAndPgnStatus("", pgnType, pgnStatus);
    }

    public PgnDDBModel save(PgnDDBModel pgnDDBModel) {
        return pgnDynamoDbTable.updateItem(pgnDDBModel);
    }
}
