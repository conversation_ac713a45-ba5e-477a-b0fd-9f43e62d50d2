package com.kotak.unified.dbservice.repository;

import com.kotak.unified.orchestrator.common.dbmodels.resumenotification.ResumeJourneyNotificationTrackNextCheck;

import java.util.Optional;

public interface ResumeJourneyNotificationTrackNextCheckRepository {
    public void putRecord(ResumeJourneyNotificationTrackNextCheck resumeJourneyNotificationTrackNextCheck);

    public Optional<ResumeJourneyNotificationTrackNextCheck> getRecord(String leadTrackingNumber);

    public void updateRecord(ResumeJourneyNotificationTrackNextCheck resumeJourneyNotificationTrackNextCheck);

}
