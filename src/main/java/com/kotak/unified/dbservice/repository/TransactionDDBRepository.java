package com.kotak.unified.dbservice.repository;

import com.kotak.unified.dbservice.config.DynamoAWSConfiguration;
import com.kotak.unified.orchestrator.common.dbmodels.TransactionDDBModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;

@Repository
public class TransactionDDBRepository {

    private final DynamoDbTable<TransactionDDBModel> transactionDDBModelDynamoDbTable;

    @Autowired
    public TransactionDDBRepository(
            DynamoDbEnhancedClient dynamoDbEnhancedClient,
            DynamoAWSConfiguration dynamoAWSConfiguration) {
        transactionDDBModelDynamoDbTable = dynamoDbEnhancedClient
                .table(dynamoAWSConfiguration.getTransactionTableName(), TableSchema.fromBean(TransactionDDBModel.class));
    }

    public TransactionDDBModel findTransactionByTxnId(String txnId) {
        return transactionDDBModelDynamoDbTable
                .getItem(Key.builder()
                        .partitionValue(txnId)
                        .build());
    }
}
