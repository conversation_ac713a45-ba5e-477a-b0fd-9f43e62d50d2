package com.kotak.unified.dbservice.repository.impl;

import com.kotak.unified.dbservice.repository.ResumeJourneyNotificationStatusRepository;
import com.kotak.unified.orchestrator.common.dbmodels.resumenotification.ResumeJourneyNotificationStatus;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;

import java.util.Optional;

@Repository
public class ResumeJourneyNotificationStatusImpl implements ResumeJourneyNotificationStatusRepository {

    private final DynamoDbTable<ResumeJourneyNotificationStatus> resumeJourneyNotificationStatusDynamoDbTable;

    public ResumeJourneyNotificationStatusImpl(DynamoDbEnhancedClient dynamoDbEnhancedClient,
                                               @Value("${dynamodb.ResumeJourneyNotificationStatus.TableName}") String resumeJourneyNotificationStatusTableName) {
        this.resumeJourneyNotificationStatusDynamoDbTable = dynamoDbEnhancedClient
                .table(resumeJourneyNotificationStatusTableName, TableSchema.fromBean(ResumeJourneyNotificationStatus.class));
    }

    @Override
    public void putRecord(ResumeJourneyNotificationStatus resumeJourneyNotificationStatus) {
        resumeJourneyNotificationStatusDynamoDbTable.putItem(resumeJourneyNotificationStatus);
    }

    @Override
    public Optional<ResumeJourneyNotificationStatus> getRecord(String leadTrackingNumber) {
        return Optional.ofNullable(resumeJourneyNotificationStatusDynamoDbTable.getItem(
                Key.builder().partitionValue(leadTrackingNumber).build()));
    }

    @Override
    public void updateRecord(ResumeJourneyNotificationStatus resumeJourneyNotificationStatus) {
        resumeJourneyNotificationStatusDynamoDbTable.updateItem(resumeJourneyNotificationStatus);
    }
}
