package com.kotak.unified.dbservice.repository;

import com.kotak.unified.orchestrator.common.dbmodels.vkycStatusModel.VideoKycDetails;
import com.kotak.unified.orchestrator.common.dbmodels.vkycStatusModel.VideoKycDetailsDDBModel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.List;

@Service
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Slf4j
public class VideoKYCStatusFacade {

    private final VideoKycStatusDDBRepository videoKycStatusDDBRepository;

    public VideoKycDetails findByTrackingId(@NonNull final String trackingId) {
        try {
            VideoKycDetails videoKycDetails;
            VideoKycDetailsDDBModel result = videoKycStatusDDBRepository.findByTrackingId(trackingId);
            if(result != null) {
                videoKycDetails = convertDDBToDTO(result);
                return videoKycDetails;
            } else {
                return null;
            }
        } catch (Exception e) {
            log.error("Exception occurred while reading from video kyc dynamodb for trackingId: {}",
                    trackingId, e);
            throw e;
        }
    }

    public VideoKycDetails findTopByLeadTrackingNumberOrderByCreatedAtDesc(@NonNull final String leadTrackingNumber) {
        try {
            List<VideoKycDetailsDDBModel> videoKycDetailsDDBModelList = videoKycStatusDDBRepository
                    .findVideoKycDetailsDDBModelsByLeadTrackingNumber(leadTrackingNumber);

            if (videoKycDetailsDDBModelList != null && !videoKycDetailsDDBModelList.isEmpty()) {
                VideoKycDetailsDDBModel latestRecord = findLatest(videoKycDetailsDDBModelList);
                return convertDDBToDTO(latestRecord);
            } else {
                return null;
            }
        } catch (Exception e) {
            log.error("Exception occurred while reading from video kyc dynamodb for leadTrackingNumber: {}",
                    leadTrackingNumber, e);
            throw e;
        }
    }

    public VideoKycDetails findTopByLeadTrackingNumberAndLatestStatusOrderByCreatedAt(@NonNull final String leadTrackingNumber, @NonNull final String latestStatus) {
        try {
            VideoKycDetails videoKycDetails;
            VideoKycDetailsDDBModel result = videoKycStatusDDBRepository.findTopByLeadTrackingNumberAndLatestStatusOrderByCreatedAt(
                    leadTrackingNumber, latestStatus);
            if(result != null) {
                videoKycDetails = convertDDBToDTO(result);
                return videoKycDetails;
            } else {
                return null;
            }
        } catch (Exception e) {
            log.error("Exception occurred while reading from video kyc dynamodb for leadTrackingNumber: {}, latestStatus: {}",
                    leadTrackingNumber, latestStatus, e);
            throw e;
        }
    }

    public VideoKycDetails save(VideoKycDetails videoKycDetails) {
        VideoKycDetailsDDBModel inputDDBModel = convertDTOToDDB(videoKycDetails);
        try {
            VideoKycDetailsDDBModel result = videoKycStatusDDBRepository.save(inputDDBModel);
            updateDTO(videoKycDetails, result);
        } catch (Exception e) {
            log.error("Exception occur while saving to videoKycDetails dynamodb", e);
            throw e;
        }
        videoKycDetails.setLastModifiedAt(Instant.now());
        return videoKycDetails;
    }

    private VideoKycDetailsDDBModel convertDTOToDDB(VideoKycDetails videoKycDetails) {
        return VideoKycDetailsDDBModel.builder()
                .trackingId(videoKycDetails.getTrackingId())
                .leadTrackingNumber(videoKycDetails.getLeadTrackingNumber())
                .latestStatus(videoKycDetails.getLatestStatus())
                .videoKycHeader(videoKycDetails.getVideoKycHeader())
                .vkycAgentCallCapturedDocuments(videoKycDetails.getVkycAgentCallCapturedDocuments())
                .vkycStatusList(videoKycDetails.getVkycStatusList())
                .createdAt(videoKycDetails.getCreatedAt())
                .version(videoKycDetails.getVersion())
                .build();
    }

    private VideoKycDetails convertDDBToDTO(VideoKycDetailsDDBModel videoKycDetailsDDBModel) {
        return VideoKycDetails.builder()
                .trackingId(videoKycDetailsDDBModel.getTrackingId())
                .leadTrackingNumber(videoKycDetailsDDBModel.getLeadTrackingNumber())
                .latestStatus(videoKycDetailsDDBModel.getLatestStatus())
                .videoKycHeader(videoKycDetailsDDBModel.getVideoKycHeader())
                .vkycAgentCallCapturedDocuments(videoKycDetailsDDBModel.getVkycAgentCallCapturedDocuments())
                .vkycStatusList(videoKycDetailsDDBModel.getVkycStatusList())
                .createdAt(videoKycDetailsDDBModel.getCreatedAt())
                .lastModifiedAt(videoKycDetailsDDBModel.getLastModifiedAt())
                .version(videoKycDetailsDDBModel.getVersion())
                .build();
    }

    private void updateDTO(VideoKycDetails videoKycDetails, VideoKycDetailsDDBModel result) {
        videoKycDetails.setVersion(result.getVersion());
    }

    private VideoKycDetailsDDBModel findLatest(@NonNull List<VideoKycDetailsDDBModel> videoKycDetailsDDBModelList) {
        VideoKycDetailsDDBModel latestRecord = null;
        for (VideoKycDetailsDDBModel videoKycDetailsDDBModel : videoKycDetailsDDBModelList) {
            if (latestRecord == null || videoKycDetailsDDBModel.getCreatedAt().isAfter(latestRecord.getCreatedAt())) {
                latestRecord = videoKycDetailsDDBModel;
            }
        }
        return latestRecord;
    }
}
