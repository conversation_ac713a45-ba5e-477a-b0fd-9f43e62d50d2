package com.kotak.unified.dbservice.repository;

import com.kotak.unified.dbservice.config.DynamoAWSConfiguration;
import com.kotak.unified.orchestrator.common.dbmodels.vkycStatusModel.VideoKycDetailsDDBModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import software.amazon.awssdk.core.pagination.sync.SdkIterable;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbIndex;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;
import software.amazon.awssdk.enhanced.dynamodb.model.Page;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryEnhancedRequest;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Repository
public class VideoKycStatusDDBRepository {
    private final DynamoDbTable<VideoKycDetailsDDBModel> videoKycDetailsDDBModelDynamoDbTable;

    @Autowired
    public VideoKycStatusDDBRepository(
            DynamoDbEnhancedClient dynamoDbEnhancedClient,
            DynamoAWSConfiguration dynamoAWSConfiguration) {
        videoKycDetailsDDBModelDynamoDbTable = dynamoDbEnhancedClient
                .table(dynamoAWSConfiguration.getVideoKycStatusTableName(), TableSchema.fromBean(VideoKycDetailsDDBModel.class));
    }

    public VideoKycDetailsDDBModel findByTrackingId(String trackingId) {
        return videoKycDetailsDDBModelDynamoDbTable
                .getItem(Key.builder()
                        .partitionValue(trackingId)
                        .build());
    }

    public VideoKycDetailsDDBModel findTopByLeadTrackingNumberAndLatestStatusOrderByCreatedAt(
            String leadTrackingNumber, String latestStatus) {
        // Create the prefix for sortKey
        String latestStatusPrefix = latestStatus + "#";
        // Create the enhanced query request
        QueryEnhancedRequest request = QueryEnhancedRequest.builder()
                .queryConditional(QueryConditional.sortBeginsWith(Key.builder().partitionValue(leadTrackingNumber)
                        .sortValue(latestStatusPrefix)
                        .build()))
                .limit(1)
                .scanIndexForward(false)
                .build();
        final DynamoDbIndex<VideoKycDetailsDDBModel> leadTrackingNumberIndex = videoKycDetailsDDBModelDynamoDbTable.index("LeadTrackingNumberIndex");
        final SdkIterable<Page<VideoKycDetailsDDBModel>> response = leadTrackingNumberIndex.query(request);
        Optional<Page<VideoKycDetailsDDBModel>> page = response.stream().findFirst();
        if (page.isPresent() && !page.get().items().isEmpty()) {
            return page.get().items().get(0);
        }
        return null;
    }

    public List<VideoKycDetailsDDBModel> findVideoKycDetailsDDBModelsByLeadTrackingNumber(String leadTrackingNumber) {

        final DynamoDbIndex<VideoKycDetailsDDBModel> leadTrackingNumberIndex = videoKycDetailsDDBModelDynamoDbTable.index("LeadTrackingNumberIndex");
        QueryEnhancedRequest request = QueryEnhancedRequest.builder()
                .queryConditional(QueryConditional.keyEqualTo(Key.builder().partitionValue(leadTrackingNumber)
                        .build())).build();
        final SdkIterable<Page<VideoKycDetailsDDBModel>> response = leadTrackingNumberIndex.query(request);
        List<VideoKycDetailsDDBModel> videoKycDetailsDDBModelList = new ArrayList<>();
        response.stream().forEach(videoKycDetailsDDBModelPage -> videoKycDetailsDDBModelList.addAll(videoKycDetailsDDBModelPage.items()));

        return videoKycDetailsDDBModelList;
    }

    public VideoKycDetailsDDBModel save(VideoKycDetailsDDBModel videoKycDetailsDDBModel) {
        return videoKycDetailsDDBModelDynamoDbTable
                .updateItem(videoKycDetailsDDBModel);
    }

}