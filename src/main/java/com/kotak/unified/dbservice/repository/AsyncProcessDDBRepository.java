package com.kotak.unified.dbservice.repository;

import com.kotak.unified.dbservice.config.DynamoAWSConfiguration;
import com.kotak.unified.orchestrator.common.dbmodels.asyncprocessing.AsyncProcessDDBModel;
import com.kotak.unified.orchestrator.library.sqsmodels.AsyncProcessType;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.NoHttpResponseException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Repository;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;

import static com.kotak.unified.dbservice.utils.Constants.DDB_MAX_RETRY_ATTEMPTS;
import static com.kotak.unified.dbservice.utils.Constants.RETRY_DEFAULT_DELAY;
import static com.kotak.unified.dbservice.utils.Constants.RETRY_DEFAULT_MULTIPLIER;

@Slf4j
@Repository
public class AsyncProcessDDBRepository {

    private final DynamoDbTable<AsyncProcessDDBModel> asyncProcessDDBModelDynamoDbTable;

    @Autowired
    public AsyncProcessDDBRepository(
            DynamoDbEnhancedClient dynamoDbEnhancedClient,
            DynamoAWSConfiguration dynamoAWSConfiguration) {
        asyncProcessDDBModelDynamoDbTable = dynamoDbEnhancedClient
                .table(dynamoAWSConfiguration.getAsyncProcessTableName(), TableSchema.fromBean(AsyncProcessDDBModel.class));
    }

    @Retryable(
            retryFor = {NoHttpResponseException.class},
            maxAttempts = DDB_MAX_RETRY_ATTEMPTS,
            backoff = @Backoff(multiplier = RETRY_DEFAULT_MULTIPLIER, delay = RETRY_DEFAULT_DELAY)
    )
    public AsyncProcessDDBModel getByLeadTrackingNumberAndProcessType(String leadTrackingNumber, AsyncProcessType asyncProcessType) {
        try {
            return asyncProcessDDBModelDynamoDbTable
                    .getItem(Key.builder()
                            .partitionValue(leadTrackingNumber)
                            .sortValue(asyncProcessType.name()).build());
        } catch (Exception ex) {
            // TODO - Added for temporary basis. Remove after verifying that retries are working on NoHttpResponseException
            log.warn("Encountered exception while getByLeadTrackingNumberAndProcessType", ex);
            if (ex instanceof NoHttpResponseException) {
                log.warn("Encountered NoHttpResponseException for leadTrackingNumber: {}, asyncProcessType: {}",
                        leadTrackingNumber, asyncProcessType);
            }
            throw ex;

        }
    }
}
