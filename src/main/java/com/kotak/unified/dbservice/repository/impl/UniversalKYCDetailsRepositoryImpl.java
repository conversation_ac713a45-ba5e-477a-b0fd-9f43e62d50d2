package com.kotak.unified.dbservice.repository.impl;

import com.kotak.unified.dbservice.repository.UniversalKYCDetailsRepository;
import com.kotak.unified.orchestrator.common.dbmodels.ukycStatusModel.UniversalKYCDetails;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;

import java.util.Optional;

@Repository
public class UniversalKYCDetailsRepositoryImpl implements UniversalKYCDetailsRepository {

    private final DynamoDbTable<UniversalKYCDetails> universalKYCDetailsDynamoDbTable;

    public UniversalKYCDetailsRepositoryImpl(
            DynamoDbEnhancedClient dynamoDbEnhancedClient,
            @Value("${dynamodb.UniversalKYCDetails.TableName}") String universalKYCDetailsTableName) {
        universalKYCDetailsDynamoDbTable = dynamoDbEnhancedClient
                .table(universalKYCDetailsTableName, TableSchema.fromBean(UniversalKYCDetails.class));
    }

    public void updateRecord(UniversalKYCDetails universalKYCDetails){
        universalKYCDetailsDynamoDbTable.updateItem(universalKYCDetails);
    }

    public void putRecord(UniversalKYCDetails universalKYCDetails) {
        universalKYCDetailsDynamoDbTable.putItem(universalKYCDetails);
    }

    public Optional<UniversalKYCDetails> getRecord(String aadhaarRefKey, String applicationId){
        return Optional.ofNullable(universalKYCDetailsDynamoDbTable.getItem(
                Key.builder().partitionValue(aadhaarRefKey).sortValue(applicationId).build()));
    }
}
