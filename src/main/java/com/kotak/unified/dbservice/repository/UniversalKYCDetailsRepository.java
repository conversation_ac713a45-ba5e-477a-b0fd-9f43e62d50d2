package com.kotak.unified.dbservice.repository;

import com.kotak.unified.orchestrator.common.dbmodels.ukycStatusModel.UniversalKYCDetails;

import java.util.Optional;

public interface UniversalKYCDetailsRepository {

    public void updateRecord(UniversalKYCDetails universalKYCDetails);

    public void putRecord(UniversalKYCDetails universalKYCDetails);

    public Optional<UniversalKYCDetails> getRecord(String aadhaarRefKey, String applicationId);

}
