package com.kotak.unified.dbservice.repository;

import com.kotak.unified.orchestrator.common.dbmodels.asyncprocessing.AsyncProcessDBModel;
import com.kotak.unified.orchestrator.common.dbmodels.asyncprocessing.AsyncProcessDDBModel;
import com.kotak.unified.orchestrator.common.dbmodels.asyncprocessing.AsyncProcessId;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class AsyncProcessFacade {

    private final AsyncProcessDDBRepository asyncProcessDDBRepository;

    public AsyncProcessDBModel getByProcessId(AsyncProcessId processId) {
        try {
            AsyncProcessDBModel asyncProcessDBModel;
            AsyncProcessDDBModel result = asyncProcessDDBRepository.getByLeadTrackingNumberAndProcessType(
                    processId.getLeadTrackingNumber(), processId.getProcessType());
            if (result != null) {
                asyncProcessDBModel = convertDDBToDTO(result);
                return asyncProcessDBModel;
            } else {
                return null;
            }
        } catch (Exception e) {
            log.error("Exception occur while reading from async process dynamodb for leadTrackingNumber: {} , processType: {}",
                    processId.getLeadTrackingNumber(), processId.getProcessType(), e);
            throw e;
        }
    }


    private AsyncProcessDBModel convertDDBToDTO(AsyncProcessDDBModel asyncProcessDDBModel) {
        return AsyncProcessDBModel.builder()
                .processId(
                        AsyncProcessId
                                .builder()
                                .leadTrackingNumber(asyncProcessDDBModel.getLeadTrackingNumber())
                                .processType(asyncProcessDDBModel.getProcessType()).build())
                .asyncProcessExecutionData(asyncProcessDDBModel.getAsyncProcessExecutionData())
                .asyncProcessStepList(asyncProcessDDBModel.getAsyncProcessStepList())
                .status(asyncProcessDDBModel.getStatus())
                .createdTime(asyncProcessDDBModel.getCreatedAt())
                .lastExecutedTime(asyncProcessDDBModel.getLastModifiedAt())
                .version(asyncProcessDDBModel.getVersion())
                .build();
    }
}