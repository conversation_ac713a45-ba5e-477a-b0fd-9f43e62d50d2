package com.kotak.unified.dbservice.repository;


import com.kotak.unified.orchestrator.common.dbmodels.resumenotification.ResumeJourneyNotificationStatus;

import java.util.Optional;

public interface ResumeJourneyNotificationStatusRepository {
    public void putRecord(ResumeJourneyNotificationStatus resumeJourneyNotificationStatus);

    public Optional<ResumeJourneyNotificationStatus> getRecord(String leadTrackingNumber);

    public void updateRecord(ResumeJourneyNotificationStatus resumeJourneyNotificationStatus);

}
