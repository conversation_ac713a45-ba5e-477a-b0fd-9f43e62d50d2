package com.kotak.unified.dbservice.repository;

import com.kotak.unified.dbservice.config.DynamoAWSConfiguration;
import com.kotak.unified.orchestrator.common.dbmodels.UserJourneyStatusDDBModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.NoHttpResponseException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Repository;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;

import static com.kotak.unified.dbservice.utils.Constants.DDB_MAX_RETRY_ATTEMPTS;
import static com.kotak.unified.dbservice.utils.Constants.RETRY_DEFAULT_DELAY;
import static com.kotak.unified.dbservice.utils.Constants.RETRY_DEFAULT_MULTIPLIER;

@Slf4j
@Repository
public class UserJourneyStatusDDBRepository {
    private final DynamoDbTable<UserJourneyStatusDDBModel> userJourneyStatusDDBTable;

    public UserJourneyStatusDDBRepository(DynamoDbEnhancedClient dynamoDbEnhancedClient,
                                          DynamoAWSConfiguration dynamoAWSConfiguration) {
        userJourneyStatusDDBTable = dynamoDbEnhancedClient.table(
                dynamoAWSConfiguration.getUserJourneyStatusTableName(),
                TableSchema.fromBean(UserJourneyStatusDDBModel.class)
        );
    }

    public UserJourneyStatusDDBModel save(UserJourneyStatusDDBModel userJourneyStatus) {
        return userJourneyStatusDDBTable.updateItem(userJourneyStatus);
    }

    @Retryable(
            retryFor = {NoHttpResponseException.class},
            maxAttempts = DDB_MAX_RETRY_ATTEMPTS,
            backoff = @Backoff(multiplier = RETRY_DEFAULT_MULTIPLIER, delay = RETRY_DEFAULT_DELAY)
    )
    public UserJourneyStatusDDBModel findByLeadTrackingNumber(String leadTrackingNumber) {
        try {
            return userJourneyStatusDDBTable.getItem(
                    Key.builder().partitionValue(leadTrackingNumber).build()
            );
        } catch (Exception ex) {
            // TODO - Added for temporary basis. Remove after verifying that retries are working on NoHttpResponseException
            log.warn("Encountered exception while findByLeadTrackingNumber", ex);
            if (ex instanceof NoHttpResponseException) {
                log.warn("Encountered NoHttpResponseException for leadTrackingNumber: {}", leadTrackingNumber);
            }
            throw ex;
        }
    }
}
