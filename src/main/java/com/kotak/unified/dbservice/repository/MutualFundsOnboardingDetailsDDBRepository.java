package com.kotak.unified.dbservice.repository;

import com.kotak.unified.db.request.mf.GetMutualFundsOnboardingDetailsRequest;
import com.kotak.unified.dbservice.config.DynamoAWSConfiguration;
import com.kotak.unified.dbservice.model.mf.MutualFundsOnboardingDetailsDDBModel;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import software.amazon.awssdk.core.pagination.sync.SdkIterable;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbIndex;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;
import software.amazon.awssdk.enhanced.dynamodb.model.Page;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryEnhancedRequest;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Repository
public class MutualFundsOnboardingDetailsDDBRepository {
    private final DynamoDbTable<MutualFundsOnboardingDetailsDDBModel> mutualFundOnboardingDetailsDDBTable;

    @Autowired
    MutualFundsOnboardingDetailsDDBRepository(DynamoDbEnhancedClient dynamoDbEnhancedClient,
                                              DynamoAWSConfiguration dynamoAWSConfiguration) {
        mutualFundOnboardingDetailsDDBTable = dynamoDbEnhancedClient.table(dynamoAWSConfiguration.getMutualFundsOnboardingDetailsTableName(), TableSchema.fromBean(MutualFundsOnboardingDetailsDDBModel.class));
    }

    public MutualFundsOnboardingDetailsDDBModel findByCrnEventTypeEventStatusAndCreatedAtDesc(GetMutualFundsOnboardingDetailsRequest getMutualFundsOnboardingDetailsRequest) {
        QueryEnhancedRequest request = QueryEnhancedRequest.builder()
                .queryConditional(QueryConditional.keyEqualTo(Key.builder()
                        .partitionValue(getMutualFundsOnboardingDetailsRequest.getCrn())
                        .build()))
                .scanIndexForward(false)
                .build();
        final DynamoDbIndex<MutualFundsOnboardingDetailsDDBModel> crnIndex = mutualFundOnboardingDetailsDDBTable.index("CRNIndex");
        final SdkIterable<Page<MutualFundsOnboardingDetailsDDBModel>> response = crnIndex.query(request);
        Optional<Page<MutualFundsOnboardingDetailsDDBModel>> firstPage = response.stream()
                .filter(p -> findLatest(p.items(), getMutualFundsOnboardingDetailsRequest) != null)
                .findFirst();
        if (firstPage.isPresent() && !firstPage.get().items().isEmpty()) {
            return findLatest(firstPage.get().items(), getMutualFundsOnboardingDetailsRequest);
        }
        return null;
    }

    public MutualFundsOnboardingDetailsDDBModel save(MutualFundsOnboardingDetailsDDBModel mutualFundsOnboardingDetailsDDBModel) {
        return mutualFundOnboardingDetailsDDBTable.updateItem(mutualFundsOnboardingDetailsDDBModel);
    }


    public MutualFundsOnboardingDetailsDDBModel findByEventTrackingId(String eventTrackingId) {
        return mutualFundOnboardingDetailsDDBTable.getItem(
                Key.builder().partitionValue(eventTrackingId).build()
        );
    }

    private MutualFundsOnboardingDetailsDDBModel findLatest(List<MutualFundsOnboardingDetailsDDBModel> mutualFundsOnboardingDetailsDDBModelList, GetMutualFundsOnboardingDetailsRequest getMutualFundsOnboardingDetailsRequest) {
        MutualFundsOnboardingDetailsDDBModel latestRecord = null;
        if (Objects.nonNull(mutualFundsOnboardingDetailsDDBModelList) && !mutualFundsOnboardingDetailsDDBModelList.isEmpty()) {
            for (MutualFundsOnboardingDetailsDDBModel mutualFundOnboardingDetailsItem : mutualFundsOnboardingDetailsDDBModelList) {
                if (StringUtils.equals(mutualFundOnboardingDetailsItem.getEventType(), getMutualFundsOnboardingDetailsRequest.getEventType()) && StringUtils.equals(mutualFundOnboardingDetailsItem.getLatestEventStatus(), getMutualFundsOnboardingDetailsRequest.getEventStatus())) {
                    latestRecord = mutualFundOnboardingDetailsItem;
                    break;
                }
            }
        }
        return latestRecord;
    }
}
