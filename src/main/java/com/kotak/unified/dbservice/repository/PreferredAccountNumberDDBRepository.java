package com.kotak.unified.dbservice.repository;

import com.kotak.unified.dbservice.config.DynamoAWSConfiguration;
import com.kotak.unified.dbservice.model.PreferredAccountNumberDDBModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;

@Repository
public class PreferredAccountNumberDDBRepository {

    private final DynamoDbTable<PreferredAccountNumberDDBModel> preferredAccountNumberDynamoDbTable;

    @Autowired
    public PreferredAccountNumberDDBRepository(
            DynamoDbEnhancedClient dynamoDbEnhancedClient,
            DynamoAWSConfiguration dynamoAWSConfiguration) {
        preferredAccountNumberDynamoDbTable = dynamoDbEnhancedClient
                .table(dynamoAWSConfiguration.getPreferredAccountNumberTableName(),
                       TableSchema.fromBean(PreferredAccountNumberDDBModel.class));
    }

    public PreferredAccountNumberDDBModel save(PreferredAccountNumberDDBModel preferredAccountNumberDDBModel) {
        return this.preferredAccountNumberDynamoDbTable.updateItem(preferredAccountNumberDDBModel);
    }

    public PreferredAccountNumberDDBModel getByPreferredAccountNumber(String preferredAccountNumber) {
        return preferredAccountNumberDynamoDbTable.getItem(
                Key.builder().partitionValue(preferredAccountNumber).build()
        );
    }
}
