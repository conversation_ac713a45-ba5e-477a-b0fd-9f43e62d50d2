package com.kotak.unified.dbservice.repository;

import com.kotak.unified.orchestrator.common.dbmodels.Transaction;
import com.kotak.unified.orchestrator.common.dbmodels.TransactionDDBModel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Slf4j
public class TransactionFacade {

    private final TransactionDDBRepository transactionDDBRepository;

    public Optional<Transaction> findById(String txnId) {
        try {
            Transaction transaction;
            TransactionDDBModel result = transactionDDBRepository.findTransactionByTxnId(
                    txnId);
            if(result != null) {
                transaction = convertDDBToDTO(result);
                return Optional.of(transaction);
            } else {
                return  Optional.empty();
            }
        } catch (Exception e) {
            log.error("Exception occurred while reading from transaction dynamodb for txnId: {}",
                    txnId, e);
            throw e;
        }
    }

    private Transaction convertDDBToDTO(TransactionDDBModel transactionDDBModel) {
        return Transaction
                .builder()
                .txnId(transactionDDBModel.getTxnId())
                .leadTrackingNumber(transactionDDBModel.getLeadTrackingNumber())
                .status(transactionDDBModel.getStatus())
                .fundsTransferStatus(transactionDDBModel.getFundsTransferStatus())
                .mihPayId(transactionDDBModel.getMihPayId())
                .mode(transactionDDBModel.getMode())
                .unmappedStatus(transactionDDBModel.getUnmappedStatus())
                .key(transactionDDBModel.getKey())
                .amount(transactionDDBModel.getAmount())
                .discount(transactionDDBModel.getDiscount())
                .netAmountDebit(transactionDDBModel.getNetAmountDebit())
                .addedOn(transactionDDBModel.getAddedOn())
                .productInfo(transactionDDBModel.getProductInfo())
                .firstName(transactionDDBModel.getFirstName())
                .lastName(transactionDDBModel.getLastName())
                .address1(transactionDDBModel.getAddress1())
                .address2(transactionDDBModel.getAddress2())
                .city(transactionDDBModel.getCity())
                .state(transactionDDBModel.getState())
                .country(transactionDDBModel.getCountry())
                .zipcode(transactionDDBModel.getZipcode())
                .email(transactionDDBModel.getEmail())
                .phone(transactionDDBModel.getPhone())
                .requestHash(transactionDDBModel.getRequestHash())
                .responseHash(transactionDDBModel.getResponseHash())
                .hashMatching(transactionDDBModel.getHashMatching())
                .field1(transactionDDBModel.getField1())
                .field2(transactionDDBModel.getField2())
                .field3(transactionDDBModel.getField3())
                .field4(transactionDDBModel.getField4())
                .field5(transactionDDBModel.getField5())
                .field6(transactionDDBModel.getField6())
                .field7(transactionDDBModel.getField7())
                .field8(transactionDDBModel.getField8())
                .field9(transactionDDBModel.getField9())
                .paymentSource(transactionDDBModel.getPaymentSource())
                .meCode(transactionDDBModel.getMeCode())
                .pgType(transactionDDBModel.getPgType())
                .bankRefNum(transactionDDBModel.getBankRefNum())
                .bankCode(transactionDDBModel.getBankCode())
                .error(transactionDDBModel.getError())
                .errorMessage(transactionDDBModel.getErrorMessage())
                .finacleProcessStatus(transactionDDBModel.getFinacleProcessStatus())
                .finacleTransactionDate(transactionDDBModel.getFinacleTransactionDate())
                .finacleProcessRemarks(transactionDDBModel.getFinacleProcessRemarks())
                .finacleTransactionId(transactionDDBModel.getFinacleTransactionId())
                .refundStatus(transactionDDBModel.getRefundStatus())
                .refundRequestId(transactionDDBModel.getRefundRequestId())
                .refundCompletedAt(transactionDDBModel.getRefundCompletedAt())
                .refundErrorCode(transactionDDBModel.getRefundErrorCode())
                .createdAt(transactionDDBModel.getCreatedAt())
                .version(transactionDDBModel.getVersion())
                .build();
    }
}
