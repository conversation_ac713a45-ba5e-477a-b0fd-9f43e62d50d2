package com.kotak.unified.dbservice.repository.impl;

import com.kotak.unified.dbservice.repository.ResumeJourneyNotificationTrackNextCheckRepository;
import com.kotak.unified.orchestrator.common.dbmodels.resumenotification.ResumeJourneyNotificationTrackNextCheck;
import org.apache.http.NoHttpResponseException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Repository;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;

import java.util.Optional;

import static com.kotak.unified.dbservice.utils.Constants.DDB_MAX_RETRY_ATTEMPTS;
import static com.kotak.unified.dbservice.utils.Constants.RETRY_DEFAULT_DELAY;
import static com.kotak.unified.dbservice.utils.Constants.RETRY_DEFAULT_MULTIPLIER;

@Repository
public class ResumeJourneyNotificationTrackNextCheckRepositoryImpl implements ResumeJourneyNotificationTrackNextCheckRepository {
    private final DynamoDbTable<ResumeJourneyNotificationTrackNextCheck> resumeJourneyNotificationTrackNextCheckDynamoDbTable;

    public ResumeJourneyNotificationTrackNextCheckRepositoryImpl(DynamoDbEnhancedClient dynamoDbEnhancedClient,
                                                                   @Value("${dynamodb.ResumeJourneyNotificationTrackNextCheck.TableName}") String resumeJourneyNotificationTrackNextCheckDynamoDbTableName) {
        this.resumeJourneyNotificationTrackNextCheckDynamoDbTable = dynamoDbEnhancedClient
                .table(resumeJourneyNotificationTrackNextCheckDynamoDbTableName, TableSchema.fromBean(ResumeJourneyNotificationTrackNextCheck.class));    }

    @Override
    public void putRecord(ResumeJourneyNotificationTrackNextCheck resumeJourneyNotificationTrackNextCheck) {
        resumeJourneyNotificationTrackNextCheckDynamoDbTable.putItem(resumeJourneyNotificationTrackNextCheck);
    }

    @Override
    @Retryable(
            retryFor = {NoHttpResponseException.class},
            maxAttempts = DDB_MAX_RETRY_ATTEMPTS,
            backoff = @Backoff(multiplier = RETRY_DEFAULT_MULTIPLIER, delay = RETRY_DEFAULT_DELAY)
    )
    public Optional<ResumeJourneyNotificationTrackNextCheck> getRecord(String leadTrackingNumber) {
        return Optional.ofNullable(resumeJourneyNotificationTrackNextCheckDynamoDbTable.getItem(
                Key.builder().partitionValue(leadTrackingNumber).build()));
    }

    @Override
    public void updateRecord(ResumeJourneyNotificationTrackNextCheck resumeJourneyNotificationTrackNextCheck) {
        resumeJourneyNotificationTrackNextCheckDynamoDbTable.updateItem(resumeJourneyNotificationTrackNextCheck);
    }
}
