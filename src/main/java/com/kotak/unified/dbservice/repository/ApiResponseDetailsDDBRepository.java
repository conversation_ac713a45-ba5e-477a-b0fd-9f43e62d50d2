package com.kotak.unified.dbservice.repository;

import com.kotak.unified.dbservice.config.DynamoAWSConfiguration;
import com.kotak.unified.orchestrator.common.dbmodels.asyncprocessing.ApiResponseDetailsDDBModel;
import com.kotak.unified.orchestrator.library.sqsmodels.ApiName;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;

@Repository
public class ApiResponseDetailsDDBRepository {
    private final DynamoDbTable<ApiResponseDetailsDDBModel> apiResponseDDBModelDynamoDbTable;

    @Autowired
    public ApiResponseDetailsDDBRepository(
            DynamoDbEnhancedClient dynamoDbEnhancedClient,
            DynamoAWSConfiguration dynamoAWSConfiguration) {
        apiResponseDDBModelDynamoDbTable = dynamoDbEnhancedClient
                .table(dynamoAWSConfiguration.getApiResponseDetailsTableName(), TableSchema.fromBean(ApiResponseDetailsDDBModel.class));
    }

    public ApiResponseDetailsDDBModel save(ApiResponseDetailsDDBModel apiResponseDetailsDDBModel) {
        return apiResponseDDBModelDynamoDbTable
                .updateItem(apiResponseDetailsDDBModel);
    }

    public ApiResponseDetailsDDBModel getByLeadTrackingNumberAndApiName(String leadTrackingNumber, ApiName apiName) {
        return apiResponseDDBModelDynamoDbTable
                .getItem(Key.builder()
                        .partitionValue(leadTrackingNumber)
                        .sortValue(apiName.name()).build());
    }
}
