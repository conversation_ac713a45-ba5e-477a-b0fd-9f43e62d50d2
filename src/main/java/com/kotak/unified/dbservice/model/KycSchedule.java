package com.kotak.unified.dbservice.model;

import com.kotak.unified.common.enums.ScheduleType;
import com.kotak.unified.common.request.BranchDetails;
import com.kotak.unified.dbservice.model.common.Address;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.time.Instant;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PROTECTED)
@SuperBuilder
public class KycSchedule extends BaseEntity {

    private String leadTrackingId;

    private Address address;

    private BranchDetails branchDetails;

    private ScheduleType scheduleType;

    private Instant scheduledAt;
}
