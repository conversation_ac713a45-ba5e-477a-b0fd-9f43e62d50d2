package com.kotak.unified.dbservice.model.pgn;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.kotak.unified.dbservice.converter.PgnMetadataAttributeConverter;
import com.kotak.unified.dbservice.enums.PgnType;
import com.kotak.unified.dbservice.model.PGNStatus;
import com.kotak.unified.orchestrator.common.dbmodels.BaseDynamoDBModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.StringUtils;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbConvertedBy;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
@DynamoDbBean
public class PgnDDBModel extends BaseDynamoDBModel {
    @Getter(onMethod_ = @DynamoDbPartitionKey)
    private String crn;

    private String leadTrackingNumber;

    @Getter(onMethod_ = @DynamoDbSecondaryPartitionKey(indexNames = "typeAndStatusIndex"))
    private PgnType pgnType;

    private PGNStatus status;

    @Getter(onMethod_ = @DynamoDbConvertedBy(PgnMetadataAttributeConverter.class))
    private PgnMetadata pgnMetadata;

    private String leadTrackingNumberAndStatus;

    @DynamoDbSecondarySortKey(indexNames = "typeAndStatusIndex")
    public String getLeadTrackingNumberAndStatus() {
        return (StringUtils.isBlank(this.leadTrackingNumber) ? "" : this.leadTrackingNumber) + "#" + this.status;
    }
}