package com.kotak.unified.dbservice.model.mf;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.kotak.unified.dbservice.model.converter.mf.EventMetadataAttributeConverter;
import com.kotak.unified.orchestrator.common.dbmodels.BaseDynamoDBModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.experimental.SuperBuilder;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbConvertedBy;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey;

import java.time.Instant;

@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
@DynamoDbBean
public class MutualFundsOnboardingDetailsDDBModel extends BaseDynamoDBModel {
    @NonNull
    @Getter(onMethod_ = @DynamoDbSecondaryPartitionKey(indexNames = "CRNIndex"))
    private String crn;

    @NonNull
    @Getter(onMethod_ = @DynamoDbPartitionKey)
    private String eventTrackingId;

    private String leadTrackingNumber;

    @NonNull
    private String eventType;

    private String latestEventStatus;

    @Getter(onMethod_ = @DynamoDbConvertedBy(EventMetadataAttributeConverter.class))
    private EventMetadata eventMetadata;

    private Instant eventTimeStamp;
}