package com.kotak.unified.dbservice.model.cpv;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class NRCpvDvuVerificationMetadata extends CpvDvuVerificationMetadata {
    private List<String> sectionsRejected;
    private String rejectionReason;
}
