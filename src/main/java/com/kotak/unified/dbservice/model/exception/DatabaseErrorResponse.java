package com.kotak.unified.dbservice.model.exception;

import com.kotak.unified.common.response.ErrorResponse;
import com.kotak.unified.dbservice.enums.ErrorCause;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class DatabaseErrorResponse extends com.kotak.unified.common.response.ErrorResponse {
    private DatabaseErrorResponse(ErrorCause errorCause) {
        super(errorCause.getCode(), errorCause.getMessage());
    }

    public static DatabaseErrorResponse fromErrorCode(ErrorCause errorCause) {
        return new DatabaseErrorResponse(errorCause);
    }

    public static ErrorResponse toErrorResponse(DatabaseErrorResponse databaseErrorResponse) {
        return new ErrorResponse(databaseErrorResponse.getErrorCode(), databaseErrorResponse.getErrorMessage());
    }
}
