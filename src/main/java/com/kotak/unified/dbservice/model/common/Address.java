package com.kotak.unified.dbservice.model.common;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PROTECTED)
@Builder
public class Address {
    private String line1;

    private String line2;

    private String line3;

    private String pincode;

    private String state;

    private String city;
}
