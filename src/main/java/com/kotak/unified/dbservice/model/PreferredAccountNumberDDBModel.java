package com.kotak.unified.dbservice.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.kotak.unified.orchestrator.common.dbmodels.BaseDynamoDBModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.experimental.SuperBuilder;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey;

@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
@DynamoDbBean
public class PreferredAccountNumberDDBModel extends BaseDynamoDBModel {
    @NonNull
    @Getter(onMethod_ = @DynamoDbPartitionKey)
    private String preferredAccountNumber;

    @NonNull
    private String leadTrackingNumber;

    @NonNull
    private String blockStatus;
}
