package com.kotak.unified.dbservice.model;

import com.kotak.unified.enums.CAJourneyType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Getter;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@DynamoDbBean
public class CAPreGeneratedCRNAccountNumber {

    @Getter(onMethod_=@DynamoDbSortKey)
    private String id;

    private String accountNumber;

    private String leadTrackingNumber;

    private String crn;

    private String entityCrn;

    private PGNStatus status;

    @Getter(onMethod_=@DynamoDbPartitionKey)
    private CAJourneyType journeyType;

}
