package com.kotak.unified.dbservice.model.cpv;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.kotak.unified.dbservice.model.converter.CpvDvuVerificationMetadataAttributeConverter;
import com.kotak.unified.orchestrator.common.dbmodels.BaseDynamoDBModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.experimental.SuperBuilder;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbConvertedBy;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey;

import java.time.Instant;

@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
@DynamoDbBean
public class CpvDvuVerificationDetailsDDBModel extends BaseDynamoDBModel {

    @NonNull
    @Getter(onMethod_=@DynamoDbPartitionKey)
    private String actionTrackingId;

    @NonNull
    private String leadTrackingNumber;

    @NonNull
    private String latestStatus;

    private String cpvCode;

    private String dvuCode;

    private Instant lastStatusEventRecordedAt;

    @Getter(onMethod_ = @DynamoDbConvertedBy(CpvDvuVerificationMetadataAttributeConverter.class))
    private CpvDvuVerificationMetadata cpvDvuVerificationMetadata;
}
