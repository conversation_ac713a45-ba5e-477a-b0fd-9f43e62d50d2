package com.kotak.unified.dbservice.model;

import com.kotak.unified.common.response.ErrorResponse;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ExternalServiceExecutionDetails<Q,T> {
    private Q request;
    private T successResponse;
    private ErrorResponse errorResponse;
}
