package com.kotak.unified.dbservice.utils;

public final class Constants {

    public static final String ERROR_CODE_PREFIX = "DB";

    public static final String VALIDATION_ERROR_MESSAGE_FORMAT = "%s: %s";

    public static final String GLOBAL_EXCEPTION_HANDLER_CAPTURE_MESSAGE = "Exception of type %s captured in GlobalExceptionHandler";

    public static final String GLOBAL_MISSING_QUERY_PARAM_EXCEPTION_MESSAGE = "Query param name %s of type %s is required.";

    public static final String APPLICATION_ID_HEADER = "X-Application-Id";
    public static final String CORRELATION_ID_HEADER = "X_CORRELATION_ID";
    public static final String PERSONAL_LOAN = "PL";
    public static final String PAYDAY_LOAN = "PDL";
    public static final String BUSINESS_LOAN = "BL";
    public static final String CURRENT_ACCOUNT = "CA";
    public static final String FSSAI_LFDCA_CODE = "LFDCA";
    public static final String DORMANT_ACCOUNT_ACTIVATION = "DormantAccountActivation";
    public static final String NR_SAVINGS_ACCOUNT = "NRSavingsAccount";
    public static final String NR_SAVINGS_ACCOUNT_JOURNEY_TYPE = "NRSavingsAccount";
    public static final String DIY_NR_SAVINGS_ACCOUNT_JOURNEY_TYPE = "DIYNRSavingsAccount";
    public static final String NR_REKYC_JOURNEY_TYPE = "NRReKyc";
    public static final String SAVINGS_ACCOUNT = "SavingsAccount";
    public static final String SALARY_ACCOUNT = "SalaryAccount";
    public static final String ETB_SAVINGS_ACCOUNT = "EtbSavingsAccount";
    public static final String ETB_SALARY_ACCOUNT = "EtbSalaryAccount";
    public static final String UPGRADE_CUSTOMER_PRODUCT = "UpgradeCustomer";
    public static final String CC = "CC";
    public static final String MUTUAL_FUNDS = "MutualFunds";
    public static final String PERSONAL_LOAN_GRAPH_NAME = "PersonalLoan";
    public static final String PERSONAL_LOAN_PARTNER_GRAPH_NAME = "PartnerPersonalLoan";
    public static final String BUSINESS_LOAN_GRAPH_NAME = "BusinessLoan";
    public static final String PAYDAY_LOAN_GRAPH_NAME = "PaydayLoan";
    public static final String INDIVIDUAL_CURRENT_ACCOUNT_GRAPH_NAME = "IndividualCurrentAccount";
    public static final String BUSINESS_CURRENT_ACCOUNT_GRAPH_NAME = "BusinessCurrentAccount";
    public static final String DIY_INDIVIDUAL_CURRENT_ACCOUNT_GRAPH_NAME = "DIYIndividualCurrentAccount";
    public static final String ASSISTED_INDIVIDUAL_CURRENT_ACCOUNT_GRAPH_NAME = "AssistedIndividualCurrentAccount";
    public static final String DIY_BUSINESS_CURRENT_ACCOUNT_GRAPH_NAME = "DIYBusinessCurrentAccount";
    public static final String ASSISTED_BUSINESS_CURRENT_ACCOUNT_GRAPH_NAME = "AssistedBusinessCurrentAccount";
    public static final String ETB_ASSISTED_BUSINESS_CURRENT_ACCOUNT_GRAPH_NAME = "EtbAssistedBusinessCurrentAccount";
    public static final String ETB_ASSISTED_INDIVIDUAL_CURRENT_ACCOUNT_GRAPH_NAME = "EtbAssistedIndividualCurrentAccount";
    public static final String DORMANT_ACCOUNT_ACTIVATION_GRAPH_NAME = "DormantAccountActivation";
    public static final String MUTUAL_FUNDS_GRAPH_NAME = "MutualFunds";
    public static final String UPGRADE_CUSTOMER_GRAPH_NAME = "UpgradeCustomer";
    public static final String MERCHANT_ONBOARDING = "MerchantOnboarding";

    public static final String RETRY_MAX_ATTEMPTS_EXPRESSION = "${retry.maxAttempts:3}";
    public static final String CC_DIY_GRAPH_NAME = "CreditCard";
    public static final String CC_PARTNER_GRAPH_NAME = "PartnerAPICC";
    public static final String SOFT_BLOCK = "SOFT_BLOCK";

    // retry constants
    public static final int DDB_MAX_RETRY_ATTEMPTS = 2;
    public static final int RETRY_DEFAULT_MULTIPLIER = 1;
    public static final int RETRY_DEFAULT_DELAY = 10;

    public static class HomeloanConstants {
        // PA constants
        public static final String PA_HL_DIY_GRAPH_NAME = "PreApprovedHomeLoanDiy";
        public static final String PA_BT_DIY_GRAPH_NAME = "PreApprovedBalanceTransfer";
        public static final String PA_LAP_DIY_GRAPH_NAME = "PreApprovedLoanAgainstProperty";
        public static final String PA_DIY_PRODUCT = "PA_DIY";

        // PQ constants
        public static final String PQ_HL_DIY_GRAPH_NAME = "PreQualifiedHomeLoanDiy";
        public static final String PQ_DIY_PRODUCT = "PQ_DIY";
    }

    // Environment Tags
    public static final String UAT_ENVIRONMENT = "UAT";
    public static final String DEV_ENVIRONMENT = "DEV";
    public static final String PROD_ENVIRONMENT = "PROD";
    public static final String CUG_ENVIRONMENT = "CUG";
    public static final String DR_ENVIRONMENT = "DR";
    private Constants() {}
}
