package com.kotak.unified.dbservice.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kotak.unified.common.response.ErrorResponse;
import com.kotak.unified.common.utils.CommonRequestResponseLoggerAsync;
import com.kotak.unified.dbservice.model.ExternalServiceExecutionDetails;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;

import javax.ws.rs.client.Entity;
import javax.ws.rs.client.WebTarget;
import javax.ws.rs.core.Response;

@Slf4j
public class WebTargetUtil {
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static CommonRequestResponseLoggerAsync requestResponseLogger;
    

    @Autowired
    public void configureRequestResponseLogger(CommonRequestResponseLoggerAsync requestResponseLogger) {
        WebTargetUtil.requestResponseLogger = requestResponseLogger;
    }

    public static <Req, Res> ExternalServiceExecutionDetails<Req, Res> executePostRequest(
            WebTarget webTarget,
            String path,
            Req request,
            Class<Res> responseClass
    )
            throws JsonProcessingException {
        return executePostRequest(webTarget, path, request, responseClass, null, "BCIF", s3AsyncAccessor);
    }

    public static <Req, Res> ExternalServiceExecutionDetails<Req, Res> executePostRequest(
            WebTarget webTarget,
            String path,
            Req request,
            Class<Res> responseClass,
            String trackId,
            String serviceName,
            String apiName
    )
            throws JsonProcessingException {
        String requestBody;
        try {
            requestBody = objectMapper.writeValueAsString(request);
            // Log request
            if (trackId != null) {
                logRequest(trackId, serviceName, apiName, requestBody, false, request.getClass(), objectMapper);
            }
        } catch (JsonProcessingException e) {
            log.warn("Processing exception thrown while preparing request body api path" + path +
                    " Error Message : " + e.getMessage());
            throw e;
        }

        log.info("Invoking api on path : " + path);
        Response response = webTarget.path(path).request()
                .post(Entity.entity(requestBody, "application/json"));
        String responseData = response.readEntity(String.class);
        Res successResponse = null;
        ErrorResponse errorResponse = null;

        if (HttpStatus.valueOf(response.getStatus()).is2xxSuccessful()) {
            log.info("Path : " + path + " received ({}) success response", response.getStatus());
            successResponse = objectMapper.readValue(responseData, responseClass);

            // Log successful response
            if (trackId != null) {
                logResponse(trackId, serviceName, apiName, successResponse, false, responseClass, objectMapper);
            }
        } else {
            log.warn("Path : " + path + " received ({}) error response : {}", response.getStatus(), responseData);
            errorResponse = objectMapper.readValue(responseData, ErrorResponse.class);
            if (errorResponse == null || StringUtils.isEmpty(errorResponse.getErrorCode())) {
                log.warn("Unable to deserialize response string to ErrorResponse object "
                        + " Using response string");
                errorResponse = ErrorResponse.builder().errorMessage(responseData)
                        .errorCode("Status code : " + response.getStatus())
                        .build();
            }

            // Log error response
            if (trackId != null) {
                logResponse(trackId, serviceName, apiName, errorResponse, false, ErrorResponse.class, objectMapper);
            }
        }

        return ExternalServiceExecutionDetails.<Req, Res>builder()
                .request(request)
                .successResponse(successResponse)
                .errorResponse(errorResponse)
                .build();
    }



    public static <T> void logResponse(String trackId, String serviceName, String apiName, Object response, boolean isXml, Class<T> responseClass, ObjectMapper mapper) {
        try {
            if (requestResponseLogger != null) {
                if (responseClass == null) {
                    requestResponseLogger.addResponse(trackId, serviceName, apiName, (String) response);
                } else if (response instanceof String) {
                    requestResponseLogger.addResponse(trackId, serviceName, apiName, mapper.readValue((String) response, responseClass));
                } else {
                    requestResponseLogger.addResponse(trackId, serviceName, apiName, response);
                }
            }
        } catch (Exception exception) {
            log.error("Error occurred during logging Response.", exception);
        }
    }

    public static <T> void logRequest(String trackId, String serviceName, String apiName, Object request, boolean isXml, Class<T> requestClass, ObjectMapper mapper) {
        try {
            if (requestResponseLogger != null) {
                if (requestClass == null) {
                    requestResponseLogger.addRequest(trackId, serviceName, apiName, (String) request);
                } else if (request instanceof String) {

                    requestResponseLogger.addRequest(trackId, serviceName, apiName, mapper.readValue((String) request, requestClass));
                }
                else {
                    requestResponseLogger.addRequest(trackId, serviceName, apiName, request);
                }
            }
        } catch (Exception exception) {
            log.error("Error occurred during logging Request.", exception);
        }
    }

}


