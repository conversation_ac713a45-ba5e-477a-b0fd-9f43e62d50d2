package com.kotak.unified.dbservice.utils;

import com.kotak.unified.common.exception.InvalidRequestException;
import com.kotak.unified.orchestrator.common.dbmodels.ApplicationData;
import com.kotak.unified.orchestrator.common.dbmodels.PersonalLoanJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.UserStatus;
import com.kotak.unified.orchestrator.common.dbmodels.assets.PLProductDetails;
import lombok.SneakyThrows;

import java.util.Optional;

import static com.kotak.unified.dbservice.utils.Constants.ASSISTED_BUSINESS_CURRENT_ACCOUNT_GRAPH_NAME;
import static com.kotak.unified.dbservice.utils.Constants.ASSISTED_INDIVIDUAL_CURRENT_ACCOUNT_GRAPH_NAME;
import static com.kotak.unified.dbservice.utils.Constants.BUSINESS_CURRENT_ACCOUNT_GRAPH_NAME;
import static com.kotak.unified.dbservice.utils.Constants.ETB_ASSISTED_INDIVIDUAL_CURRENT_ACCOUNT_GRAPH_NAME;
import static com.kotak.unified.dbservice.utils.Constants.ETB_ASSISTED_BUSINESS_CURRENT_ACCOUNT_GRAPH_NAME;
import static com.kotak.unified.dbservice.utils.Constants.BUSINESS_LOAN;
import static com.kotak.unified.dbservice.utils.Constants.BUSINESS_LOAN_GRAPH_NAME;
import static com.kotak.unified.dbservice.utils.Constants.CC;
import static com.kotak.unified.dbservice.utils.Constants.CC_DIY_GRAPH_NAME;
import static com.kotak.unified.dbservice.utils.Constants.CC_PARTNER_GRAPH_NAME;
import static com.kotak.unified.dbservice.utils.Constants.CURRENT_ACCOUNT;
import static com.kotak.unified.dbservice.utils.Constants.DIY_BUSINESS_CURRENT_ACCOUNT_GRAPH_NAME;
import static com.kotak.unified.dbservice.utils.Constants.DIY_INDIVIDUAL_CURRENT_ACCOUNT_GRAPH_NAME;
import static com.kotak.unified.dbservice.utils.Constants.DIY_NR_SAVINGS_ACCOUNT_JOURNEY_TYPE;
import static com.kotak.unified.dbservice.utils.Constants.DORMANT_ACCOUNT_ACTIVATION;
import static com.kotak.unified.dbservice.utils.Constants.ETB_SALARY_ACCOUNT;
import static com.kotak.unified.dbservice.utils.Constants.ETB_SAVINGS_ACCOUNT;
import static com.kotak.unified.dbservice.utils.Constants.INDIVIDUAL_CURRENT_ACCOUNT_GRAPH_NAME;
import static com.kotak.unified.dbservice.utils.Constants.MERCHANT_ONBOARDING;
import static com.kotak.unified.dbservice.utils.Constants.MUTUAL_FUNDS;
import static com.kotak.unified.dbservice.utils.Constants.MUTUAL_FUNDS_GRAPH_NAME;
import static com.kotak.unified.dbservice.utils.Constants.NR_REKYC_JOURNEY_TYPE;
import static com.kotak.unified.dbservice.utils.Constants.NR_SAVINGS_ACCOUNT;
import static com.kotak.unified.dbservice.utils.Constants.NR_SAVINGS_ACCOUNT_JOURNEY_TYPE;
import static com.kotak.unified.dbservice.utils.Constants.PAYDAY_LOAN;
import static com.kotak.unified.dbservice.utils.Constants.PAYDAY_LOAN_GRAPH_NAME;
import static com.kotak.unified.dbservice.utils.Constants.PERSONAL_LOAN_GRAPH_NAME;
import static com.kotak.unified.dbservice.utils.Constants.PERSONAL_LOAN_PARTNER_GRAPH_NAME;
import static com.kotak.unified.dbservice.utils.Constants.SALARY_ACCOUNT;
import static com.kotak.unified.dbservice.utils.Constants.SAVINGS_ACCOUNT;
import static com.kotak.unified.dbservice.utils.Constants.UPGRADE_CUSTOMER_GRAPH_NAME;
import static com.kotak.unified.dbservice.utils.Constants.UPGRADE_CUSTOMER_PRODUCT;

public class ApplicationDataUtil {

    @SneakyThrows
    public static String getProduct(UserStatus userStatus){
        String product = null;
        switch (userStatus.getJourneyType()) {
            case PERSONAL_LOAN_GRAPH_NAME, PERSONAL_LOAN_PARTNER_GRAPH_NAME -> {
                product = Optional.of(
                                ((PersonalLoanJourneyMetadata) userStatus.getJourneyMetadata()))
                        .map(PersonalLoanJourneyMetadata::getProductDetails)
                        .map(PLProductDetails::getProductName)
                        .orElseThrow(() ->
                                new InvalidRequestException(
                                        String.format(
                                                "No product exist for the applicationId %s",
                                                userStatus.getLeadTrackingNumber())
                                ));
            }
            case BUSINESS_LOAN_GRAPH_NAME -> product = BUSINESS_LOAN;
            case PAYDAY_LOAN_GRAPH_NAME -> product = PAYDAY_LOAN;
            case INDIVIDUAL_CURRENT_ACCOUNT_GRAPH_NAME, BUSINESS_CURRENT_ACCOUNT_GRAPH_NAME, ASSISTED_BUSINESS_CURRENT_ACCOUNT_GRAPH_NAME,
                    ASSISTED_INDIVIDUAL_CURRENT_ACCOUNT_GRAPH_NAME, DIY_BUSINESS_CURRENT_ACCOUNT_GRAPH_NAME, DIY_INDIVIDUAL_CURRENT_ACCOUNT_GRAPH_NAME,
                    ETB_ASSISTED_INDIVIDUAL_CURRENT_ACCOUNT_GRAPH_NAME, ETB_ASSISTED_BUSINESS_CURRENT_ACCOUNT_GRAPH_NAME
                    ->
                    product = CURRENT_ACCOUNT;
            case DORMANT_ACCOUNT_ACTIVATION -> product = DORMANT_ACCOUNT_ACTIVATION;
            case NR_SAVINGS_ACCOUNT_JOURNEY_TYPE, DIY_NR_SAVINGS_ACCOUNT_JOURNEY_TYPE, NR_REKYC_JOURNEY_TYPE  -> product = NR_SAVINGS_ACCOUNT;
            case SAVINGS_ACCOUNT, SALARY_ACCOUNT, ETB_SAVINGS_ACCOUNT, ETB_SALARY_ACCOUNT -> product = SAVINGS_ACCOUNT;
            case CC_DIY_GRAPH_NAME, CC_PARTNER_GRAPH_NAME -> {
                product = CC;
            }
            case MUTUAL_FUNDS_GRAPH_NAME -> product = MUTUAL_FUNDS;
            case UPGRADE_CUSTOMER_GRAPH_NAME -> product = UPGRADE_CUSTOMER_PRODUCT;
        }

        return product;
    }

    @SneakyThrows
    public static String getProduct(ApplicationData applicationDataDDBModel){
        String product = null;
        switch (applicationDataDDBModel.getApplicationType()) {
            case PERSONAL_LOAN_GRAPH_NAME, PERSONAL_LOAN_PARTNER_GRAPH_NAME -> {
                product = Optional.of(
                                ((PersonalLoanJourneyMetadata) applicationDataDDBModel.getJourneyMetadata()))
                        .map(PersonalLoanJourneyMetadata::getProductDetails)
                        .map(PLProductDetails::getProductName)
                        .orElseThrow(() ->
                                new InvalidRequestException(
                                        String.format(
                                                "No product exist for the applicationId %s",
                                                applicationDataDDBModel.getApplicationTrackingId())
                                ));
            }
            case BUSINESS_LOAN_GRAPH_NAME -> product = BUSINESS_LOAN;
            case PAYDAY_LOAN_GRAPH_NAME -> product = PAYDAY_LOAN;
            case INDIVIDUAL_CURRENT_ACCOUNT_GRAPH_NAME, BUSINESS_CURRENT_ACCOUNT_GRAPH_NAME, ASSISTED_BUSINESS_CURRENT_ACCOUNT_GRAPH_NAME,
                    ASSISTED_INDIVIDUAL_CURRENT_ACCOUNT_GRAPH_NAME, DIY_BUSINESS_CURRENT_ACCOUNT_GRAPH_NAME, DIY_INDIVIDUAL_CURRENT_ACCOUNT_GRAPH_NAME
                    ->
                    product = CURRENT_ACCOUNT;
            case DORMANT_ACCOUNT_ACTIVATION -> product = DORMANT_ACCOUNT_ACTIVATION;
            case NR_SAVINGS_ACCOUNT_JOURNEY_TYPE, DIY_NR_SAVINGS_ACCOUNT_JOURNEY_TYPE, NR_REKYC_JOURNEY_TYPE  -> product = NR_SAVINGS_ACCOUNT;
            case SAVINGS_ACCOUNT, SALARY_ACCOUNT, ETB_SAVINGS_ACCOUNT, ETB_SALARY_ACCOUNT -> product = SAVINGS_ACCOUNT;
            case CC_DIY_GRAPH_NAME, CC_PARTNER_GRAPH_NAME -> {
                product = CC;
            }
            case MUTUAL_FUNDS_GRAPH_NAME -> product = MUTUAL_FUNDS;
            case UPGRADE_CUSTOMER_GRAPH_NAME -> product = UPGRADE_CUSTOMER_PRODUCT;
            case Constants.HomeloanConstants.PA_HL_DIY_GRAPH_NAME, Constants.HomeloanConstants.PA_BT_DIY_GRAPH_NAME, Constants.HomeloanConstants.PA_LAP_DIY_GRAPH_NAME -> product = Constants.HomeloanConstants.PA_DIY_PRODUCT;
            case Constants.HomeloanConstants.PQ_HL_DIY_GRAPH_NAME -> product = Constants.HomeloanConstants.PQ_DIY_PRODUCT;
            case MERCHANT_ONBOARDING -> product = MERCHANT_ONBOARDING;
        }

        return product;
    }
}


