package com.kotak.unified.dbservice.enums;

import com.kotak.unified.dbservice.utils.Constants;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
public enum ErrorCause {
    INTERNAL_SERVER_ERROR("001", "Internal Server Error"),
    INPUT_VALIDATION_FAILED("002", "Invalid input"),
    EXISTING_KYC_SCHEDULE_FOUND("003", "Existing KYC Schedule found"),
    KYC_SCHEDULE_NOT_FOUND("004", "No Kyc Schedule Found"),
    QUERY_PARAM_NOT_FOUND("005","Required query parameter not found"),
    INVALID_COMPARATOR_FOUND("006", "Invalid comparator found"),
    LEAD_TRACKING_NUMBER_NOT_FOUND("007", "Lead tracking number not found"),
    ASYNC_PROCESS_STATUS_NOT_FOUND("008", "Async process status not found"),
    UKYC_RECORD_NOT_FOUND("009", "UKYC Record Not Found"),
    UKYC_RECORD_ALREADY_EXISTS("010", "UKYC Record with provided aadhaarRefKey and applicationId already exists"),
    UKYC_RECORD_VERSION_CONFLICT("011", "Version conflict while updating the UKYC record"),
    TRANSACTION_NOT_FOUND("012", "Transaction not found"),
    KYC_TRACKING_ID_NOT_FOUND("013", "kyc tracking id not found"),
    RESUME_NOTIFICATION_RECORD_ALREADY_EXISTS("014", "ResumeJourneyNotification Record with provided leadTrackingNumber already exists"),
    RESUME_NOTIFICATION_RECORD_NOT_FOUND("015", "ResumeJourneyNotification Record Not Found"),
    RESUME_NOTIFICATION_RECORD_VERSION_CONFLICT("016", "Version conflict while updating the ResumeJourneyNotification record"),
    RESUME_NOTIFICATION_TRACK_NEXT_CHECK_RECORD_ALREADY_EXISTS("017", "ResumeJourneyNotificationTrackNextCheck Record with provided leadTrackingNumber already exists"),
    RESUME_NOTIFICATION_TRACK_NEXT_CHECK_RECORD_NOT_FOUND("018", "ResumeJourneyNotificationTrackNextCheck Record Not Found"),
    RESUME_NOTIFICATION_TRACK_NEXT_CHECK_RECORD_VERSION_CONFLICT("019", "Version conflict while updating the ResumeJourneyNotificationTrackNextCheck record"),
    CPV_DVU_DETAILS_NOT_FOUND("020", "Cpv DVU verification details not found"),
    DDB_ITEM_VERSION_MISMATCH("021", "Mismatch in version of ddb item present in db and from request"),
    PREFERRED_ACCOUNT_NUMBER_NOT_FOUND("022", "Preferred account number not found"),
    MF_ONBOARDING_STATUS_RECORD_NOT_FOUND("022", "MutualFundOnboardingDetails Record not found"),
    API_RESPONSE_NOT_FOUND("023", "Api Response not found"),
    INVALID_API_NAME("024", "Invalid ApiName is provided");

    private final String code;

    private final String message;

    ErrorCause(String code, String message) {
        this.code = Constants.ERROR_CODE_PREFIX.concat(code);
        this.message = message;
    }
}
