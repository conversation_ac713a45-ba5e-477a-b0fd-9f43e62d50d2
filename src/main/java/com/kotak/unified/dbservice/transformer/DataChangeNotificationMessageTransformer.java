package com.kotak.unified.dbservice.transformer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kotak.unified.databridgeinterface.model.sqs.DataChangeNotificationMessage;
import com.kotak.unified.dbservice.annotation.Transformer;
import jakarta.inject.Named;
import lombok.SneakyThrows;
import software.amazon.awssdk.services.sqs.model.SendMessageRequest;

@Transformer
public class DataChangeNotificationMessageTransformer {
    private final ObjectMapper objectMapper;

    public DataChangeNotificationMessageTransformer(@Named("JsonObjectMapper") ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    @SneakyThrows
    public SendMessageRequest convertDataBridgePublishMessageInputToMessageRequest(DataChangeNotificationMessage dataChangeNotificationMessage, String queueUrl, Integer delay) {
        return SendMessageRequest.builder()
                .queueUrl(queueUrl)
                .messageBody(objectMapper.writeValueAsString(dataChangeNotificationMessage))
                .delaySeconds(delay)
                .build();
    }
}

