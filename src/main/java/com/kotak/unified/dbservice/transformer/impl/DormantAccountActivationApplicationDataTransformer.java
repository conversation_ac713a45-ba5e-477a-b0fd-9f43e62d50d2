package com.kotak.unified.dbservice.transformer.impl;

import com.kotak.unified.common.exception.InvalidRequestException;
import com.kotak.unified.db.ApplicationData;
import com.kotak.unified.db.ApplicationDataFilter;
import com.kotak.unified.db.DeclarationResponse;
import com.kotak.unified.db.GetApplicationDataRequest;
import com.kotak.unified.db.PersonalDetailsResponse;
import com.kotak.unified.db.dormant.DormantAccountActivationProductSpecificationResponse;
import com.kotak.unified.db.response.DormantAccountActivationApplicationData;
import com.kotak.unified.db.response.EtbAccountDetailDto;
import com.kotak.unified.db.response.EtbDetailsDto;
import com.kotak.unified.db.response.IneligibleDormantAccountDetailsDto;
import com.kotak.unified.dbservice.exceptions.EntityNotFoundException;
import com.kotak.unified.dbservice.transformer.ApplicationDataTransformer;
import com.kotak.unified.orchestrator.common.dbmodels.Declaration;
import com.kotak.unified.orchestrator.common.dbmodels.DormantAccountActivationJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.EtbAccountDetail;
import com.kotak.unified.orchestrator.common.dbmodels.JourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.UserStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.kotak.unified.dbservice.utils.Constants.DORMANT_ACCOUNT_ACTIVATION;

@Slf4j
@Component
@RequiredArgsConstructor
public class DormantAccountActivationApplicationDataTransformer implements ApplicationDataTransformer {
    private final ModelMapper modelMapper;

    @Override
    public ApplicationData populateApplicationData(GetApplicationDataRequest getApplicationDataRequest,
                                                   UserStatus userStatus) throws EntityNotFoundException, InvalidRequestException {
        JourneyMetadata journeyMetadata = userStatus.getJourneyMetadata();
        if (journeyMetadata == null) {
            log.warn("No application data found for lead id : "
                    + userStatus.getLeadTrackingNumber());
            throw new EntityNotFoundException("No application data found for lead id : "
                    + userStatus.getLeadTrackingNumber());
        }
        this.validateInput(userStatus);

        DormantAccountActivationApplicationData applicationData = new DormantAccountActivationApplicationData();
        for (ApplicationDataFilter applicationDataFilter : getApplicationDataRequest.getDataFilters()) {
            this.setApplicationData(userStatus, applicationDataFilter, applicationData, (DormantAccountActivationJourneyMetadata) journeyMetadata);
        }

        return applicationData;
    }

    @Override
    public String getProduct() {
        return DORMANT_ACCOUNT_ACTIVATION;
    }

    private void validateInput(final UserStatus userStatus) throws InvalidRequestException {
        if (!(userStatus.getJourneyMetadata() instanceof
                DormantAccountActivationJourneyMetadata)) {
            throw new InvalidRequestException(
                    "Application Id does not belong to Dormant account activation Journey for lead : " +
                            userStatus.getLeadTrackingNumber());
        }
    }

    private void setApplicationData(UserStatus userStatus,
                                    ApplicationDataFilter applicationDataFilter,
                                    DormantAccountActivationApplicationData dormantAccountActivationApplicationData,
                                    DormantAccountActivationJourneyMetadata dormantAccountJourneyMetadata) throws InvalidRequestException {
        switch (applicationDataFilter) {
            case PHONE_NUMBER -> dormantAccountActivationApplicationData.setPhoneNumber(userStatus.getPhoneNumber());
            case DATE_OF_BIRTH -> dormantAccountActivationApplicationData
                    .setCustomerDob(dormantAccountJourneyMetadata.getDob());
            case DORMANCY_REMOVAL_REASON -> dormantAccountActivationApplicationData
                    .setDormancyRemovalReason(dormantAccountJourneyMetadata.getDormancyReason());
            case EMAIL_VERIFIED -> {
                if (userStatus.getExecutionData() != null) {
                    dormantAccountActivationApplicationData.setIsEmailAddressVerified(userStatus.getExecutionData().getIsEmailAddressVerified());
                }
            }
            case COMMUNICATION_ADDRESS_ENTERED_IN_JOURNEY -> dormantAccountActivationApplicationData
                    .setIsCommunicationAddressSameAsExistingBcifAddress(dormantAccountJourneyMetadata.getIsCommunicationAddressSameAsExistingBcifAddress());
            case PERSONAL_DETAILS -> {
                if (dormantAccountJourneyMetadata.getPersonalDetails() != null) {
                    dormantAccountActivationApplicationData.setPersonalDetailsResponse
                            (this.modelMapper.map(dormantAccountJourneyMetadata.getPersonalDetails(), PersonalDetailsResponse.class));
                }
            }
            case ETB_DETAILS -> {
                if (dormantAccountJourneyMetadata.getEtbDetails() != null) {
                    dormantAccountActivationApplicationData.setEtbDetailsDto
                            (this.modelMapper.map(dormantAccountJourneyMetadata.getEtbDetails(), EtbDetailsDto.class));
                }
            }
            case VALID_DORMANT_ACCOUNTS -> {
                if (!CollectionUtils.isEmpty(dormantAccountJourneyMetadata.getValidDormantAccounts())) {
                    List<EtbAccountDetailDto> validDormantAccountList = new ArrayList<>();
                    for (EtbAccountDetail etbAccountDetail : dormantAccountJourneyMetadata.getValidDormantAccounts()) {
                        validDormantAccountList.add(this.modelMapper.map(etbAccountDetail, EtbAccountDetailDto.class));
                    }
                    dormantAccountActivationApplicationData.setValidDormantAccounts(validDormantAccountList);
                }
            }
            case DECLARATIONS -> {
                if (!CollectionUtils.isEmpty(dormantAccountJourneyMetadata.getDeclarations())) {
                    Map<String, DeclarationResponse> declarationResponseMap = new HashMap<>();
                    for (Map.Entry<String, Declaration> entry : dormantAccountJourneyMetadata.getDeclarations().entrySet()) {
                        DeclarationResponse declarationResponse = this.modelMapper.map(entry.getValue(), DeclarationResponse.class);
                        declarationResponseMap.put(entry.getKey(), declarationResponse);
                    }
                    dormantAccountActivationApplicationData.setDeclarationResponses(declarationResponseMap);
                }
            }
            case PRODUCT_SPECIFICATIONS -> {
                if (Objects.isNull(dormantAccountJourneyMetadata.getProductSpecifications())) {
                    log.warn("Dormant Product Specifications are null for lead id : {}",
                            userStatus.getLeadTrackingNumber());
                } else {
                    dormantAccountActivationApplicationData.setDormantAccountActivationProductSpecificationResponse(
                            modelMapper.map(dormantAccountJourneyMetadata.getProductSpecifications(),
                                    DormantAccountActivationProductSpecificationResponse.class));
                }
            }
            case INELIGIBLE_DORMANT_ACCOUNT_DETAILS_LIST -> {
                var ineligibleDormantAccountDetailsDtoList = Optional.ofNullable(dormantAccountJourneyMetadata.getIneligibleDormantAccountDetailsList())
                        .filter(list -> !list.isEmpty())
                        .map(list ->
                                list.stream()
                                        .map(ineligibleDormantAccountDetails -> this.modelMapper.map(ineligibleDormantAccountDetails, IneligibleDormantAccountDetailsDto.class))
                                        .toList())
                        .orElse(null);
                dormantAccountActivationApplicationData.setIneligibleDormantAccountDetailsDtoList(ineligibleDormantAccountDetailsDtoList);
            }
            default -> throw new InvalidRequestException("Data filter passed is not applicable : " + applicationDataFilter);
        }
    }
}