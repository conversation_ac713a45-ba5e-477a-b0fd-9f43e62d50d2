package com.kotak.unified.dbservice.transformer;

import com.kotak.unified.dbservice.annotation.Transformer;
import com.kotak.unified.enums.UserJourneyStatusField;
import com.kotak.unified.orchestrator.common.dbmodels.BankDetails;
import com.kotak.unified.orchestrator.common.dbmodels.CurrentAccountJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.JourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.ProductDetails;
import com.kotak.unified.orchestrator.common.dbmodels.SavingsAccountJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.UserStatus;
import com.kotak.unified.orchestrator.common.dbmodels.ca.CAProductSpecifications;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.Map;

@Transformer
public class UserStatusTransformer {
    public UserStatus applyPatch(UserStatus userStatus, Map<UserJourneyStatusField, Object> updates) {
        switch (userStatus.getJourneyType()) {
            case "SavingsAccount":
            case "SalaryAccount":
            case "EtbSavingsAccount":
            case "EtbSalaryAccount": {
                SavingsAccountJourneyMetadata savingsAccountJourneyMetadata = (SavingsAccountJourneyMetadata)
                        userStatus.getJourneyMetadata();
                userStatus.setJourneyMetadata(applyPatch(savingsAccountJourneyMetadata, updates));
                break;
            }
            case "AssistedBusinessCurrentAccount":
            case "AssistedIndividualCurrentAccount":{
                CurrentAccountJourneyMetadata currentAccountJourneyMetadata = (CurrentAccountJourneyMetadata)
                        userStatus.getJourneyMetadata();
                userStatus.setJourneyMetadata(applyPatch(currentAccountJourneyMetadata, updates));
                break;
            }
            default:
                throw new UnsupportedOperationException("Unsupported journeyType lead marked for update : "
                        + userStatus.getJourneyType());

        }

        return userStatus;
    }

    private JourneyMetadata applyPatch(SavingsAccountJourneyMetadata journeyMetadata, Map<UserJourneyStatusField, Object> updates) {
        for (Map.Entry<UserJourneyStatusField, Object> entry : updates.entrySet()) {
            switch (entry.getKey()) {
                case LC_CODE -> {
                    initializeProductDetailsIfEmpty(journeyMetadata);
                    journeyMetadata.getProductDetails().setLcCode((String) entry.getValue());
                }
                case BRANCH_CODE -> {
                    initializeBankDetailsIfEmpty(journeyMetadata);
                    journeyMetadata.getBankDetails().setBranchCode((String) entry.getValue());
                }
                case RM_CODE -> {
                    initializeProductDetailsIfEmpty(journeyMetadata);
                    journeyMetadata.getProductDetails().setRmCode((String) entry.getValue());
                }
                case LOB_CODE -> {
                    initializeProductDetailsIfEmpty(journeyMetadata);
                    journeyMetadata.getProductDetails().setLineOfBusiness((String) entry.getValue());
                }
                case COCO_CODE -> {
                    initializeProductDetailsIfEmpty(journeyMetadata);
                    journeyMetadata.getProductDetails().setCorpCode((String) entry.getValue());
                }
                case PROMO_CODE -> {
                    initializeProductDetailsIfEmpty(journeyMetadata);
                    journeyMetadata.getProductDetails().setPromoCode((String) entry.getValue());
                }
                case LG_CODE -> {
                    initializeProductDetailsIfEmpty(journeyMetadata);
                    journeyMetadata.getProductDetails().setLgCode((String) entry.getValue());
                }
            }
        }

        return journeyMetadata;
    }


    private JourneyMetadata applyPatch(CurrentAccountJourneyMetadata journeyMetadata, Map<UserJourneyStatusField, Object> updates) {

        CAProductSpecifications productSpecifications = (CAProductSpecifications) journeyMetadata.getProductSpecifications();
        if (ObjectUtils.isEmpty(productSpecifications)) {
            throw new RuntimeException("Product specifications is empty");
        }

        if ((updates.containsKey(UserJourneyStatusField.LOB_CODE) && !updates.containsKey(UserJourneyStatusField.SEGMENT_CODE)) ||
                (updates.containsKey(UserJourneyStatusField.SEGMENT_CODE) && !updates.containsKey(UserJourneyStatusField.LOB_CODE))) {
            throw new RuntimeException("Both LOB_CODE and RL_SEGMENT_CODE must be provided together in updates.");
        }

        for (Map.Entry<UserJourneyStatusField, Object> entry : updates.entrySet()) {
            switch (entry.getKey()) {
                case LC_CODE -> {
                    productSpecifications.setLcCode((String) entry.getValue());
                }
                case BRANCH_CODE -> {
                    productSpecifications.setBranchCode((String) entry.getValue());
                }
                case RM_CODE -> {
                    productSpecifications.setRmCode((String) entry.getValue());
                }
                case LOB_CODE -> {
                    productSpecifications.setLineOfBusiness((String) entry.getValue());
                }
                case COCO_CODE -> {
                    productSpecifications.setCocoCode((String) entry.getValue());
                }
                case PROMO_CODE -> {
                    productSpecifications.setPromoCode((String) entry.getValue());
                }
                case LG_CODE -> {
                    productSpecifications.setLgCode((String) entry.getValue());
                }
                case INITIATOR_CODE -> {
                    if (!ObjectUtils.isEmpty(entry.getValue()))
                        productSpecifications.setInitiatorCode(Integer.parseInt((String) entry.getValue()));
                }
                case SEGMENT_CODE -> {
                    if (!ObjectUtils.isEmpty(entry.getValue()))
                        productSpecifications.setSegment((String) entry.getValue());
                }
            }
        }
        return journeyMetadata;
    }

    private void initializeProductDetailsIfEmpty(SavingsAccountJourneyMetadata journeyMetadata) {
        if (journeyMetadata.getProductDetails() != null) return;
        journeyMetadata.setProductDetails(ProductDetails.builder().build());
    }

    private void initializeBankDetailsIfEmpty(SavingsAccountJourneyMetadata journeyMetadata) {
        if (journeyMetadata.getBankDetails() != null) return;
        journeyMetadata.setBankDetails(BankDetails.builder().build());
    }
}
