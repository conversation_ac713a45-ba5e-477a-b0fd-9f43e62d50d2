package com.kotak.unified.dbservice.transformer.impl;

import com.kotak.unified.common.exception.InvalidRequestException;
import com.kotak.unified.db.ApplicationData;
import com.kotak.unified.db.ApplicationDataFilter;
import com.kotak.unified.db.DeclarationResponse;
import com.kotak.unified.db.GetApplicationDataRequest;
import com.kotak.unified.db.PaHlDiyJourneyApplicationData;
import com.kotak.unified.db.hl.HomeLoanOfferDetails;
import com.kotak.unified.db.hl.PaHlDiyJourneyMilestone;
import com.kotak.unified.db.hl.PaDiyJourneyType;
import com.kotak.unified.db.hl.SanctionLetterDetails;
import com.kotak.unified.db.hl.UserDetails;
import com.kotak.unified.dbservice.exceptions.EntityNotFoundException;
import com.kotak.unified.dbservice.transformer.ApplicationDataTransformerV2;
import com.kotak.unified.dbservice.utils.Constants;
import com.kotak.unified.orchestrator.common.dbmodels.PaHlDiyJourneyMetadata;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Slf4j
@Component
@RequiredArgsConstructor
public class PaHlDiyJourneyApplicationDataTransformerV2 implements ApplicationDataTransformerV2 {
    private final ModelMapper modelMapper;

    @Override
    public ApplicationData populateApplicationData(GetApplicationDataRequest getApplicationDataRequest,
                                                   com.kotak.unified.orchestrator.common.dbmodels.ApplicationData applicationDataDDBModel)
            throws EntityNotFoundException, InvalidRequestException {
        validateInput(applicationDataDDBModel);

        PaHlDiyJourneyMetadata paHlDiyJourneyMetadata =
                (PaHlDiyJourneyMetadata) applicationDataDDBModel.getJourneyMetadata();

        PaHlDiyJourneyApplicationData paHlDiyJourneyApplicationData = PaHlDiyJourneyApplicationData.builder().build();
        for (ApplicationDataFilter applicationDataFilter : getApplicationDataRequest.getDataFilters()) {
            setApplicationData(applicationDataDDBModel, applicationDataFilter, paHlDiyJourneyApplicationData,
                    paHlDiyJourneyMetadata);
        }

        return paHlDiyJourneyApplicationData;
    }

    @Override
    public String getProduct() {
        return Constants.HomeloanConstants.PA_DIY_PRODUCT;
    }

    private void validateInput(
            final com.kotak.unified.orchestrator.common.dbmodels.ApplicationData applicationDataDDBModel)
            throws InvalidRequestException {
        if (Objects.isNull(applicationDataDDBModel.getJourneyMetadata()) ||
                !(applicationDataDDBModel.getJourneyMetadata() instanceof
                        PaHlDiyJourneyMetadata)) {
            throw new InvalidRequestException(
                    "Application Id does not belong to PaHlDiy Journey " +
                            applicationDataDDBModel.getApplicationTrackingId());
        }
    }


    private void setApplicationData(
            com.kotak.unified.orchestrator.common.dbmodels.ApplicationData applicationDataDDBModel,
            ApplicationDataFilter applicationDataFilter,
            PaHlDiyJourneyApplicationData paHlDiyJourneyApplicationData,
            PaHlDiyJourneyMetadata paHlDiyJourneyMetadata) throws InvalidRequestException {

        switch (applicationDataFilter) {
            case ALL -> {
                paHlDiyJourneyApplicationData.setCrn(applicationDataDDBModel.getCrn());

                if (paHlDiyJourneyMetadata.getPaHlDiyJourneyMilestone() != null) {
                    paHlDiyJourneyApplicationData.setPaHlDiyJourneyMilestone(
                            modelMapper.map(paHlDiyJourneyMetadata.getPaHlDiyJourneyMilestone(),
                                    PaHlDiyJourneyMilestone.class));
                }

                if (paHlDiyJourneyMetadata.getPaDiyJourneyType() != null) {
                    paHlDiyJourneyApplicationData.setPaDiyJourneyType(
                            modelMapper.map(paHlDiyJourneyMetadata.getPaDiyJourneyType(),
                                    PaDiyJourneyType.class));
                }

                if (paHlDiyJourneyMetadata.getDropOffReasons() != null) {
                    paHlDiyJourneyApplicationData.setDropOffReasons(paHlDiyJourneyMetadata.getDropOffReasons());
                }

                if (paHlDiyJourneyMetadata.getUserDetails() != null) {
                    paHlDiyJourneyApplicationData.setUserDetails(
                            modelMapper.map(paHlDiyJourneyMetadata.getUserDetails(), UserDetails.class));
                }

                if (paHlDiyJourneyMetadata.getHomeLoanOfferDetails() != null) {
                    paHlDiyJourneyApplicationData.setHomeLoanOfferDetails(
                            modelMapper.map(paHlDiyJourneyMetadata.getHomeLoanOfferDetails(),
                                    HomeLoanOfferDetails.class));
                }

                if (paHlDiyJourneyMetadata.getSanctionLetterDetails() != null) {
                    paHlDiyJourneyApplicationData.setSanctionLetterDetails(
                            modelMapper.map(paHlDiyJourneyMetadata.getSanctionLetterDetails(),
                                    SanctionLetterDetails.class));
                }

                if (paHlDiyJourneyMetadata.getTermsAndConditionsDeclaration() != null) {
                    paHlDiyJourneyApplicationData.setTermsAndConditionsDeclaration(
                            modelMapper.map(paHlDiyJourneyMetadata.getTermsAndConditionsDeclaration(),
                                    DeclarationResponse.class));
                }
            }

            default -> throw new InvalidRequestException(
                    String.format("Data filter passed is not applicable: %s for PaHlDiyJourneyType",
                            applicationDataFilter)
            );
        }
    }

}
