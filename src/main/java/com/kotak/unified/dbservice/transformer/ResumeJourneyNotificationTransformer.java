package com.kotak.unified.dbservice.transformer;


import com.kotak.unified.db.model.ActiveScheduledNotificationNudgeDetailsDTO;
import com.kotak.unified.db.model.CancelledNotificationNudgeDetailsDTO;
import com.kotak.unified.db.model.CreateResumeJourneyNotificationStatusRecordRequest;
import com.kotak.unified.db.model.CreateResumeJourneyNotificationStatusRecordResponse;
import com.kotak.unified.db.model.GetResumeJourneyNotificationStatusRecordResponse;
import com.kotak.unified.db.model.RecipientTypeDTO;
import com.kotak.unified.db.model.UpdateResumeJourneyNotificationStatusRecordRequest;
import com.kotak.unified.dbservice.annotation.Transformer;
import com.kotak.unified.orchestrator.common.dbmodels.resumenotification.ActiveScheduledNotificationNudgeDetails;
import com.kotak.unified.orchestrator.common.dbmodels.resumenotification.CancelledNotificationNudgeDetails;
import com.kotak.unified.orchestrator.common.dbmodels.resumenotification.RecipientType;
import com.kotak.unified.orchestrator.common.dbmodels.resumenotification.ResumeJourneyNotificationStatus;

import java.util.ArrayList;
import java.util.List;

@Transformer
public class ResumeJourneyNotificationTransformer {
    public ResumeJourneyNotificationStatus convertRequestToResumeJourneyNotificationStatus(CreateResumeJourneyNotificationStatusRecordRequest createResumeJourneyNotificationStatusRecordRequest) {
        ResumeJourneyNotificationStatus resumeJourneyNotificationStatus = ResumeJourneyNotificationStatus
                .builder()
                .leadTrackingNumber(createResumeJourneyNotificationStatusRecordRequest.getLeadTrackingNumber())
                .latestScheduledNotificationNudgeCreatedAt(createResumeJourneyNotificationStatusRecordRequest.getLatestScheduledNotificationNudgeCreatedAt())
                .activeScheduledNotificationNudgeDetailsList(convertDTOToActiveScheduledNotificationNudgeDetailsList(createResumeJourneyNotificationStatusRecordRequest.getActiveScheduledNotificationNudgeDetailsList()))
                .cancelledScheduledNotificationNudgeDetailsList(convertDTOToScheduledNotificationNudgeDetailsList(createResumeJourneyNotificationStatusRecordRequest.getCancelledScheduledNotificationNudgeDetailsList()))
                .build();

        return resumeJourneyNotificationStatus;
    }

    public List<ActiveScheduledNotificationNudgeDetails> convertDTOToActiveScheduledNotificationNudgeDetailsList(List<ActiveScheduledNotificationNudgeDetailsDTO> activeScheduledNotificationNudgeDetailsDTOList) {
        List<ActiveScheduledNotificationNudgeDetails> activeScheduledNotificationNudgeDetailsList = new ArrayList<>();
        if (activeScheduledNotificationNudgeDetailsDTOList == null || activeScheduledNotificationNudgeDetailsDTOList.isEmpty()) {
            return activeScheduledNotificationNudgeDetailsList;
        }
        for (ActiveScheduledNotificationNudgeDetailsDTO activeScheduledNotificationNudgeDetailsDTO: activeScheduledNotificationNudgeDetailsDTOList) {
            activeScheduledNotificationNudgeDetailsList.add(convertDTOToActiveScheduledNotificationNudgeDetails(activeScheduledNotificationNudgeDetailsDTO));
        }
        return activeScheduledNotificationNudgeDetailsList;
    }

    public ActiveScheduledNotificationNudgeDetails convertDTOToActiveScheduledNotificationNudgeDetails(ActiveScheduledNotificationNudgeDetailsDTO activeScheduledNotificationNudgeDetailsDTO) {
        if (activeScheduledNotificationNudgeDetailsDTO == null) {
            return null;
        }

        return ActiveScheduledNotificationNudgeDetails.builder()
                .runId(activeScheduledNotificationNudgeDetailsDTO.getRunId())
                .flowId(activeScheduledNotificationNudgeDetailsDTO.getFlowId())
                .recipientType(RecipientType.valueOf(activeScheduledNotificationNudgeDetailsDTO.getRecipientType().name()))
                .build();
    }

    public CreateResumeJourneyNotificationStatusRecordResponse getCreateResumeJourneyNotificationStatusRecordResponse(boolean isSuccess) {
        return CreateResumeJourneyNotificationStatusRecordResponse.builder()
                .isCreateRecordSuccessful(true)
                .build();
    }

    public GetResumeJourneyNotificationStatusRecordResponse convertModelToGetResumeJourneyNotificationStatusRecordResponse(ResumeJourneyNotificationStatus resumeJourneyNotificationStatus) {
        if (resumeJourneyNotificationStatus == null) {
            return null;
        }
        return GetResumeJourneyNotificationStatusRecordResponse
                .builder()
                .leadTrackingNumber(resumeJourneyNotificationStatus.getLeadTrackingNumber())
                .latestScheduledNotificationNudgeCreatedAt(resumeJourneyNotificationStatus.getLatestScheduledNotificationNudgeCreatedAt())
                .activeScheduledNotificationNudgeDetailsList(convertModelToActiveScheduledNotificationNudgeDetailsDTOList(resumeJourneyNotificationStatus.getActiveScheduledNotificationNudgeDetailsList()))
                .cancelledScheduledNotificationNudgeDetailsList(convertModelToScheduledNotificationNudgeDetailsDTOList(resumeJourneyNotificationStatus.getCancelledScheduledNotificationNudgeDetailsList()))
                .build();
    }

    public List<ActiveScheduledNotificationNudgeDetailsDTO> convertModelToActiveScheduledNotificationNudgeDetailsDTOList(List<ActiveScheduledNotificationNudgeDetails> activeScheduledNotificationNudgeDetailsList) {
        List<ActiveScheduledNotificationNudgeDetailsDTO> activeScheduledNotificationNudgeDetailsDTOList = new ArrayList<>();
        if (activeScheduledNotificationNudgeDetailsList == null || activeScheduledNotificationNudgeDetailsList.isEmpty()) {
            return activeScheduledNotificationNudgeDetailsDTOList;
        }
        for (ActiveScheduledNotificationNudgeDetails activeScheduledNotificationNudgeDetails: activeScheduledNotificationNudgeDetailsList) {
            activeScheduledNotificationNudgeDetailsDTOList.add(convertModelToActiveScheduledNotificationNudgeDetailsDTO(activeScheduledNotificationNudgeDetails));
        }
        return activeScheduledNotificationNudgeDetailsDTOList;
    }

    public ActiveScheduledNotificationNudgeDetailsDTO convertModelToActiveScheduledNotificationNudgeDetailsDTO(ActiveScheduledNotificationNudgeDetails activeScheduledNotificationNudgeDetails) {
        if(activeScheduledNotificationNudgeDetails == null) {
            return null;
        }

        return ActiveScheduledNotificationNudgeDetailsDTO.builder()
                .runId(activeScheduledNotificationNudgeDetails.getRunId())
                .flowId(activeScheduledNotificationNudgeDetails.getFlowId())
                .recipientType(RecipientTypeDTO.valueOf(activeScheduledNotificationNudgeDetails.getRecipientType().name()))
                .build();
    }

    public void updateResumeJourneyNotificationStatusDetails(ResumeJourneyNotificationStatus resumeJourneyNotificationStatus, UpdateResumeJourneyNotificationStatusRecordRequest updateResumeJourneyNotificationStatusRecordRequest) {
        resumeJourneyNotificationStatus.setCancelledScheduledNotificationNudgeDetailsList(convertDTOToScheduledNotificationNudgeDetailsList(updateResumeJourneyNotificationStatusRecordRequest.getCancelledScheduledNotificationNudgeDetailsList()));
        resumeJourneyNotificationStatus.setActiveScheduledNotificationNudgeDetailsList(convertDTOToActiveScheduledNotificationNudgeDetailsList(updateResumeJourneyNotificationStatusRecordRequest.getActiveScheduledNotificationNudgeDetailsList()));
        resumeJourneyNotificationStatus.setLatestScheduledNotificationNudgeCreatedAt(updateResumeJourneyNotificationStatusRecordRequest.getLatestScheduledNotificationNudgeCreatedAt());
    }


    public List<CancelledNotificationNudgeDetails> convertDTOToScheduledNotificationNudgeDetailsList(List<CancelledNotificationNudgeDetailsDTO> cancelledNotificationNudgeDetailsDTOList) {
        List<CancelledNotificationNudgeDetails> scheduledNotificationNudgeDetailsList = new ArrayList<>();
        if (cancelledNotificationNudgeDetailsDTOList == null || cancelledNotificationNudgeDetailsDTOList.isEmpty()) {
            return scheduledNotificationNudgeDetailsList;
        }
        for (CancelledNotificationNudgeDetailsDTO cancelledNotificationNudgeDetailsDTO: cancelledNotificationNudgeDetailsDTOList) {
            scheduledNotificationNudgeDetailsList.add(convertDTOToScheduledNotificationNudgeDetails(cancelledNotificationNudgeDetailsDTO));
        }
        return scheduledNotificationNudgeDetailsList;
    }

    public CancelledNotificationNudgeDetails convertDTOToScheduledNotificationNudgeDetails(CancelledNotificationNudgeDetailsDTO cancelledNotificationNudgeDetailsDTO) {
        if (cancelledNotificationNudgeDetailsDTO == null) {
            return null;
        }

        return CancelledNotificationNudgeDetails.builder()
                .runId(cancelledNotificationNudgeDetailsDTO.getRunId())
                .flowId(cancelledNotificationNudgeDetailsDTO.getFlowId())
                .recipientType(RecipientType.valueOf(cancelledNotificationNudgeDetailsDTO.getRecipientType().name()))
                .createdAt(cancelledNotificationNudgeDetailsDTO.getCreatedAt())
                .cancelledAt(cancelledNotificationNudgeDetailsDTO.getCancelledAt())
                .build();
    }

    public List<CancelledNotificationNudgeDetailsDTO> convertModelToScheduledNotificationNudgeDetailsDTOList(List<CancelledNotificationNudgeDetails> cancelledNotificationNudgeDetailsList) {
        List<CancelledNotificationNudgeDetailsDTO> cancelledNotificationNudgeDetailsDTOList = new ArrayList<>();
        if (cancelledNotificationNudgeDetailsList == null || cancelledNotificationNudgeDetailsList.isEmpty()) {
            return cancelledNotificationNudgeDetailsDTOList;
        }
        for (CancelledNotificationNudgeDetails cancelledNotificationNudgeDetails: cancelledNotificationNudgeDetailsList) {
            cancelledNotificationNudgeDetailsDTOList.add(convertModelToScheduledNotificationNudgeDetailsDTO(cancelledNotificationNudgeDetails));
        }
        return cancelledNotificationNudgeDetailsDTOList;
    }

    public CancelledNotificationNudgeDetailsDTO convertModelToScheduledNotificationNudgeDetailsDTO(CancelledNotificationNudgeDetails cancelledNotificationNudgeDetails) {
        if(cancelledNotificationNudgeDetails == null) {
            return null;
        }

        return CancelledNotificationNudgeDetailsDTO.builder()
                .runId(cancelledNotificationNudgeDetails.getRunId())
                .flowId(cancelledNotificationNudgeDetails.getFlowId())
                .recipientType(RecipientTypeDTO.valueOf(cancelledNotificationNudgeDetails.getRecipientType().name()))
                .createdAt(cancelledNotificationNudgeDetails.getCreatedAt())
                .cancelledAt(cancelledNotificationNudgeDetails.getCancelledAt())
                .build();
    }
}
