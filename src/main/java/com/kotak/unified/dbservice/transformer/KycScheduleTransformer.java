package com.kotak.unified.dbservice.transformer;

import com.kotak.unified.common.request.BranchDetails;
import com.kotak.unified.common.request.database.KycSchedulingRequest;
import com.kotak.unified.common.enums.ScheduleType;
import com.kotak.unified.db.AddressResponse;
import com.kotak.unified.db.BranchDetailsResponse;
import com.kotak.unified.db.KycSchedulingDetailsResponse;
import com.kotak.unified.db.KycSchedulingResponse;
import com.kotak.unified.dbservice.annotation.Transformer;
import com.kotak.unified.dbservice.model.KycSchedule;
import com.kotak.unified.dbservice.model.common.Address;

import java.time.Instant;
import java.util.List;
import java.util.stream.Collectors;

@Transformer
public class KycScheduleTransformer {
    public KycSchedule convertRequestToModel(KycSchedulingRequest kycSchedulingRequest) {
        return KycSchedule
                .builder()
                .leadTrackingId(kycSchedulingRequest.getLeadTrackingNumber())
                .scheduledAt(Instant.ofEpochMilli(kycSchedulingRequest.getScheduledAt()))
                .address(convertRequestToModel(kycSchedulingRequest.getAddress()))
                .branchDetails(kycSchedulingRequest.getBranchDetails())
                .scheduleType(kycSchedulingRequest.getScheduleType())
                .build();
    }

    public KycSchedulingResponse convertModelToResponse(KycSchedule kycSchedule) {
       return KycSchedulingResponse
                .builder()
                .leadTrackingNumber(kycSchedule.getLeadTrackingId())
                .scheduledAt(kycSchedule.getScheduledAt().toEpochMilli())
                .address(convertModelToResponse(kycSchedule.getAddress()))
                .branchDetails(convertModelToResponseBranchDetails(kycSchedule.getBranchDetails()))
                .scheduleType(kycSchedule.getScheduleType())
                .build();
    }

    public void updateKycSchedule(KycSchedule kycSchedule, KycSchedulingRequest kycSchedulingRequest) {
        ScheduleType scheduleType  = kycSchedulingRequest.getScheduleType();
        kycSchedule.setScheduledAt(Instant.ofEpochMilli(kycSchedulingRequest.getScheduledAt()));
        kycSchedule.setScheduleType(kycSchedule.getScheduleType());
        switch (scheduleType) {
            case DOOR_TO_DOOR_KYC: {
                kycSchedule.setAddress(convertRequestToModel(kycSchedulingRequest.getAddress()));
                kycSchedule.setBranchDetails(null);
                break;
            }
            case KYC_AT_BRANCH: {
                kycSchedule.setBranchDetails(kycSchedulingRequest.getBranchDetails());
                kycSchedule.setAddress(null);
                break;
            }
            default: {
                String errorMessage = String.format("unknown Schedule type found, for lead id {}", kycSchedulingRequest.getLeadTrackingNumber());
                throw new RuntimeException(errorMessage);
            }
        }
    }

    public Address convertRequestToModel(com.kotak.unified.common.request.Address addressRequest) {
        if(addressRequest == null)
            return null;

        return Address
                .builder()
                .line1(addressRequest.getLine1())
                .line2(addressRequest.getLine2())
                .line3(addressRequest.getLine3())
                .pincode(addressRequest.getPincode())
                .city(addressRequest.getCity())
                .state(addressRequest.getState())
                .build();
    }

    public AddressResponse convertModelToResponse(Address address) {
        if(address == null)
            return null;

        return AddressResponse
                .builder()
                .line1(address.getLine1())
                .line2(address.getLine2())
                .line3(address.getLine3())
                .pincode(address.getPincode())
                .city(address.getCity())
                .state(address.getState())
                .build();
    }

    public BranchDetailsResponse convertModelToResponseBranchDetails(BranchDetails branchDetails) {
        if(branchDetails == null)
            return null;

        return BranchDetailsResponse
                .builder()
                .address(branchDetails.getAddress())
                .area(branchDetails.getArea())
                .build();
    }

    public KycSchedulingDetailsResponse convertModelToResponse(List<KycSchedule> kycScheduleList) {
        return KycSchedulingDetailsResponse.builder()
                .kycSchedulingResponseList(kycScheduleList.stream().map(this::convertModelToResponse).collect(Collectors.toList()))
                .build();
    }

}
