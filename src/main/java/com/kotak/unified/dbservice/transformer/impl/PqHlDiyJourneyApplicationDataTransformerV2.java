package com.kotak.unified.dbservice.transformer.impl;

import com.kotak.unified.common.exception.InvalidRequestException;
import com.kotak.unified.db.ApplicationData;
import com.kotak.unified.db.ApplicationDataFilter;
import com.kotak.unified.db.DeclarationResponse;
import com.kotak.unified.db.GetApplicationDataRequest;
import com.kotak.unified.db.PqHlDiyJourneyApplicationData;
import com.kotak.unified.db.MilestoneHistory;
import com.kotak.unified.db.hl.EmploymentCheckDetails;
import com.kotak.unified.db.hl.HomeLoanOfferDetails;
import com.kotak.unified.db.hl.SanctionLetterDetails;
import com.kotak.unified.db.hl.UserDetails;
import com.kotak.unified.dbservice.exceptions.EntityNotFoundException;
import com.kotak.unified.dbservice.transformer.ApplicationDataTransformerV2;
import com.kotak.unified.dbservice.utils.Constants;
import com.kotak.unified.orchestrator.common.dbmodels.PqHlDiyJourneyMetadata;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Slf4j
@Component
@RequiredArgsConstructor
public class PqHlDiyJourneyApplicationDataTransformerV2 implements ApplicationDataTransformerV2 {
    private final ModelMapper modelMapper;

    @Override
    public ApplicationData populateApplicationData(GetApplicationDataRequest getApplicationDataRequest,
                                                   com.kotak.unified.orchestrator.common.dbmodels.ApplicationData applicationDataDDBModel)
            throws EntityNotFoundException, InvalidRequestException {
        validateInput(applicationDataDDBModel);

        PqHlDiyJourneyMetadata pqHlDiyJourneyMetadata =
                (PqHlDiyJourneyMetadata) applicationDataDDBModel.getJourneyMetadata();

        PqHlDiyJourneyApplicationData pqHlDiyJourneyApplicationData = PqHlDiyJourneyApplicationData.builder().build();
        for (ApplicationDataFilter applicationDataFilter : getApplicationDataRequest.getDataFilters()) {
            setApplicationData(applicationDataDDBModel, applicationDataFilter, pqHlDiyJourneyApplicationData,
                    pqHlDiyJourneyMetadata);
        }

        return pqHlDiyJourneyApplicationData;
    }

    @Override
    public String getProduct() {
        return Constants.HomeloanConstants.PQ_DIY_PRODUCT;
    }

    private void validateInput(
            final com.kotak.unified.orchestrator.common.dbmodels.ApplicationData applicationDataDDBModel)
            throws InvalidRequestException {
        if (Objects.isNull(applicationDataDDBModel.getJourneyMetadata()) ||
                !(applicationDataDDBModel.getJourneyMetadata() instanceof
                        PqHlDiyJourneyMetadata)) {
            throw new InvalidRequestException(
                    "Application Id does not belong to PqHlDiy Journey " +
                            applicationDataDDBModel.getApplicationTrackingId());
        }
    }


    private void setApplicationData(
            com.kotak.unified.orchestrator.common.dbmodels.ApplicationData applicationDataDDBModel,
            ApplicationDataFilter applicationDataFilter,
            PqHlDiyJourneyApplicationData pqHlDiyJourneyApplicationData,
            PqHlDiyJourneyMetadata pqHlDiyJourneyMetadata) throws InvalidRequestException {

        switch (applicationDataFilter) {
            case ALL -> {
                pqHlDiyJourneyApplicationData.setCrn(applicationDataDDBModel.getCrn());

                if (pqHlDiyJourneyMetadata.getPqHlDiyJourneyMilestone() != null) {
                    pqHlDiyJourneyApplicationData.setPqHlDiyJourneyMilestone(
                            pqHlDiyJourneyMetadata.getPqHlDiyJourneyMilestone().name()
                    );
                }

                if (pqHlDiyJourneyMetadata.getPqHlDiyMilestoneHistory() != null) {
                    pqHlDiyJourneyApplicationData.setPqHlDiyMilestoneHistory(
                            pqHlDiyJourneyMetadata.getPqHlDiyMilestoneHistory().stream()
                                    .map(this::toMilestoneHistory)
                                    .toList());
                }

                if (pqHlDiyJourneyMetadata.getPqHlDiyJourneyType() != null) {
                    pqHlDiyJourneyApplicationData.setPqHlDiyJourneyType(
                            pqHlDiyJourneyMetadata.getPqHlDiyJourneyType().name()
                    );
                }

                if (pqHlDiyJourneyMetadata.getDropOffReasons() != null) {
                    pqHlDiyJourneyApplicationData.setDropOffReasons(pqHlDiyJourneyMetadata.getDropOffReasons());
                }

                if (pqHlDiyJourneyMetadata.getUserDetails() != null) {
                    pqHlDiyJourneyApplicationData.setUserDetails(
                            modelMapper.map(pqHlDiyJourneyMetadata.getUserDetails(), UserDetails.class));
                }

                if (pqHlDiyJourneyMetadata.getHomeLoanOfferDetails() != null) {
                    pqHlDiyJourneyApplicationData.setHomeLoanOfferDetails(
                            modelMapper.map(pqHlDiyJourneyMetadata.getHomeLoanOfferDetails(),
                                    HomeLoanOfferDetails.class));
                }

                if (pqHlDiyJourneyMetadata.getSanctionLetterDetails() != null) {
                    pqHlDiyJourneyApplicationData.setSanctionLetterDetails(
                            modelMapper.map(pqHlDiyJourneyMetadata.getSanctionLetterDetails(),
                                    SanctionLetterDetails.class));
                }

                if (pqHlDiyJourneyMetadata.getEmploymentCheckDetails() != null) {
                    pqHlDiyJourneyApplicationData.setEmploymentCheckDetails(
                            modelMapper.map(pqHlDiyJourneyMetadata.getEmploymentCheckDetails(),
                                    EmploymentCheckDetails.class));
                }

                if (pqHlDiyJourneyMetadata.getBankingDataDeclaration() != null) {
                    pqHlDiyJourneyApplicationData.setBankingDataDeclaration(
                            modelMapper.map(pqHlDiyJourneyMetadata.getBankingDataDeclaration(),
                                    DeclarationResponse.class));
                }

                if (pqHlDiyJourneyMetadata.getNdncDeclaration() != null) {
                    pqHlDiyJourneyApplicationData.setNdncDeclaration(
                            modelMapper.map(pqHlDiyJourneyMetadata.getNdncDeclaration(),
                                    DeclarationResponse.class));
                }

                if (pqHlDiyJourneyMetadata.getTermsAndConditionsDeclaration() != null) {
                    pqHlDiyJourneyApplicationData.setTermsAndConditionsDeclaration(
                            modelMapper.map(pqHlDiyJourneyMetadata.getTermsAndConditionsDeclaration(),
                                    DeclarationResponse.class));
                }
            }

            default -> throw new InvalidRequestException(
                    String.format("Data filter passed is not applicable: %s for EtbPqHlDiyJourneyType",
                            applicationDataFilter)
            );
        }
    }

    private MilestoneHistory toMilestoneHistory(
            com.kotak.unified.orchestrator.common.dbmodels.MilestoneHistory milestoneHistory) {
        return MilestoneHistory.builder()
                .milestone(milestoneHistory.getMilestone())
                .timestamp(milestoneHistory.getTimestamp())
                .build();
    }
}
