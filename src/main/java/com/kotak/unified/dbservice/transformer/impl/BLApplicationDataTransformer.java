package com.kotak.unified.dbservice.transformer.impl;

import static com.kotak.unified.dbservice.utils.Constants.BUSINESS_LOAN;
import com.kotak.unified.common.exception.InvalidRequestException;
import com.kotak.unified.db.AadhaarDetailsResponse;
import com.kotak.unified.db.ApplicationData;
import com.kotak.unified.db.ApplicationDataFilter;
import com.kotak.unified.db.BLApplicationData;
import com.kotak.unified.db.GetApplicationDataRequest;
import com.kotak.unified.db.assets.LoanIntentResponse;
import com.kotak.unified.db.bl.AddressDetails;
import com.kotak.unified.db.bl.AgreementData;
import com.kotak.unified.db.bl.BLApplicationMilestone;
import com.kotak.unified.db.bl.BLProductSpecificationsResponse;
import com.kotak.unified.db.bl.BankStatementDetails;
import com.kotak.unified.db.bl.CrnDetails;
import com.kotak.unified.db.bl.InsuranceDetails;
import com.kotak.unified.db.bl.NomineeDetails;
import com.kotak.unified.db.bl.OfferDetails;
import com.kotak.unified.db.bl.PanDetails;
import com.kotak.unified.dbservice.transformer.ApplicationDataTransformer;
import com.kotak.unified.orchestrator.common.dbmodels.BusinessLoanJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.UserStatus;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class BLApplicationDataTransformer implements ApplicationDataTransformer {

  private final ModelMapper modelMapper;
  @Override
  public ApplicationData populateApplicationData(
      GetApplicationDataRequest applicationDataRequest, UserStatus userStatus) throws InvalidRequestException {
    this.validateInput(userStatus);
    BusinessLoanJourneyMetadata journeyMetadata = (BusinessLoanJourneyMetadata) userStatus.getJourneyMetadata();
    BLApplicationData applicationData = new BLApplicationData();
    for (ApplicationDataFilter dataFilter : applicationDataRequest.getDataFilters()) {
      this.setApplicationData(dataFilter, userStatus, journeyMetadata, applicationData);
    }
    return applicationData;
  }

  @SneakyThrows
  private void setApplicationData(ApplicationDataFilter dataFilter, UserStatus userStatus, BusinessLoanJourneyMetadata journeyMetadata,
      BLApplicationData applicationData) {
    switch (dataFilter) {
      case ADDRESS -> this.setAddressDetails(applicationData, journeyMetadata);
      case OFFER_DETAILS -> this.setOfferDetails(applicationData, journeyMetadata);
      case BANK_STATEMENT_DETAILS -> this.setBankStatement(applicationData, journeyMetadata);
      case CRN -> this.setSelectedCrnDetails(applicationData, journeyMetadata);
      case NEGATIVE_APPLICANT_DETAILS -> applicationData.setApplicantNegative(journeyMetadata.isApplicantNegative());
      case APPLICATION_CREATED_TIME -> applicationData.setApplicationCreatedTime(userStatus.getCreatedTime());
      case PAN -> this.setPanDetails(applicationData, journeyMetadata);
      case PHONE_NUMBER -> applicationData.setPhoneNumber(userStatus.getPhoneNumber());
      case EMAIL_ID -> applicationData.setEmail(journeyMetadata.getEmail());
      case AADHAAR -> applicationData.setAadhaarDetails(modelMapper.map(journeyMetadata.getAadhaarDetails(), AadhaarDetailsResponse.class));
      case INTENT -> this.setLoanIntent(applicationData, journeyMetadata);
      case PRODUCT -> applicationData.setProductDetails(null);//Not getting populated, set as null to not break the disbursement product call until the product filter is removed
      case JOURNEY_MILESTONE -> applicationData.setJourneyMilestone(
          Objects.nonNull(journeyMetadata.getJourneyMilestone())
              ? modelMapper.map(journeyMetadata.getJourneyMilestone(),
              BLApplicationMilestone.class) : null);
      case USER_IP -> applicationData.setUserIp(userStatus.getUserIp());
      case LC_CODE -> applicationData.setLcCode(journeyMetadata.getLcCode());
      case AGREEMENT_DATA -> applicationData.setAgreementData(
              journeyMetadata.getAgreementData() != null ? modelMapper.map(
                      journeyMetadata.getAgreementData(),
                      AgreementData.class) : null);
      case PRODUCT_SPECIFICATIONS -> this.setProductSpecifications(applicationData, journeyMetadata);
      case DECLARATIONS_ACCEPTED -> this.setDeclarationsAccepted(applicationData,journeyMetadata);
      case NOMINEE_DETAILS -> this.setNomineeDetails(applicationData, journeyMetadata);
      case INSURANCE_DETAILS -> this.setInsuranceDetails(applicationData, journeyMetadata);
      case LOAN_COMMENCEMENT_DATE -> applicationData.setLoanCommencementDate(userStatus.getCreatedTime().toString());
      case DATE_OF_BIRTH -> applicationData.setDateOfBirth(journeyMetadata.getDateOfBirth());
      case CHANNEL -> applicationData.setChannel(userStatus.getChannel());
      default -> throw new InvalidRequestException("Data filter passed is not applicable");
    }
  }

  private void setInsuranceDetails(BLApplicationData applicationData,
      BusinessLoanJourneyMetadata journeyMetadata) {
    if (Objects.isNull(journeyMetadata.getInsuranceDetails())){
      return;
    }
    applicationData.setInsuranceDetails(modelMapper.map(journeyMetadata.getInsuranceDetails(),
        InsuranceDetails.class));
  }

  private void setNomineeDetails(BLApplicationData applicationData,
      BusinessLoanJourneyMetadata journeyMetadata) {
    List<NomineeDetails> nomineeDetails = Optional.ofNullable(journeyMetadata.getNomineeDetails())
        .orElseGet(Collections::emptyList)
        .stream()
        .map(nomineeDetail -> modelMapper.map(nomineeDetail, NomineeDetails.class))
        .collect(Collectors.toList());
    applicationData.setNomineeDetails(nomineeDetails);
  }
  private void setProductSpecifications(BLApplicationData applicationData,
      BusinessLoanJourneyMetadata journeyMetadata){
    if (Objects.isNull(journeyMetadata.getProductSpecifications())){
      return;
    }
    applicationData.setProductSpecifications(modelMapper.map(journeyMetadata.getProductSpecifications(),
        BLProductSpecificationsResponse.class));
  }

  private void setLoanIntent(BLApplicationData applicationData, BusinessLoanJourneyMetadata journeyMetadata){
    if (Objects.isNull(journeyMetadata.getLoanIntent())){
      return;
    }
    applicationData.setIntent(modelMapper.map(journeyMetadata.getLoanIntent(), LoanIntentResponse.class));
  }

  private void setPanDetails(BLApplicationData applicationData,
      BusinessLoanJourneyMetadata journeyMetadata) {
    if (Objects.isNull(journeyMetadata.getPanDetails())) {
      return;
    }
    applicationData.setPanDetails(modelMapper.map(journeyMetadata.getPanDetails(), PanDetails.class));
  }

  private void setSelectedCrnDetails(final BLApplicationData applicationData,
      final BusinessLoanJourneyMetadata journeyMetadata){
    if (Objects.isNull(journeyMetadata.getSelectedCrnDetails())) {
      return;
    }
    applicationData.setSelectedCrnDetails(modelMapper.map(journeyMetadata.getSelectedCrnDetails(), CrnDetails.class));
  }
  private void setBankStatement(final BLApplicationData applicationData,
      final BusinessLoanJourneyMetadata journeyMetadata){
    if (Objects.isNull(journeyMetadata.getBankStatementDetails())) {
      return;
    }
    applicationData.setBankStatementDetails(modelMapper.map(journeyMetadata.getBankStatementDetails(), BankStatementDetails.class));
  }

  private void setOfferDetails(final BLApplicationData applicationData,
      final BusinessLoanJourneyMetadata journeyMetadata){

    List<OfferDetails> offerDetailsList = Optional.ofNullable(journeyMetadata.getOfferDetailsList())
        .orElseGet(Collections::emptyList)
        .stream()
        .map(offerDetails -> modelMapper.map(offerDetails,OfferDetails.class))
        .collect(Collectors.toList());
    applicationData.setOfferDetailsList(offerDetailsList);

  }

  private void setAddressDetails(
      final BLApplicationData applicationData,
      final BusinessLoanJourneyMetadata journeyMetadata) {
    List<AddressDetails> addressDetails = Optional.ofNullable(journeyMetadata.getAddressDetailsList())
        .orElseGet(Collections::emptyList)
        .stream()
        .map(address -> modelMapper.map(address, AddressDetails.class))
        .collect(Collectors.toList());
    applicationData.setAddressDetailsList(addressDetails);
  }

  private void setDeclarationsAccepted(final BLApplicationData applicationData,
                                       final BusinessLoanJourneyMetadata journeyMetadata){
    applicationData.setDeclarationsAccepted(journeyMetadata.isDeclarationsAccepted());
  }


  private void validateInput(final UserStatus userStatus) throws InvalidRequestException {
    if (Objects.isNull(userStatus.getJourneyMetadata())
        || !(userStatus.getJourneyMetadata() instanceof BusinessLoanJourneyMetadata)) {
      throw new InvalidRequestException(
          "Application Id does not belong to BL Journey " +
              userStatus.getLeadTrackingNumber());
    }
  }

  @Override
  public String getProduct() {
    return BUSINESS_LOAN;
  }
}
