package com.kotak.unified.dbservice.transformer;

import com.kotak.unified.db.pgn.AccountNumSchemeCodePgnMetadata;
import com.kotak.unified.db.pgn.PgnMetadata;
import com.kotak.unified.db.response.pgn.AssignPgnResponse;
import com.kotak.unified.dbservice.annotation.Transformer;
import com.kotak.unified.dbservice.enums.PgnType;
import com.kotak.unified.dbservice.model.pgn.PgnDDBModel;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;

@Transformer
public class PgnTransformer {

    private final ModelMapper modelMapper;

    @Autowired
    public PgnTransformer(ModelMapper modelMapper) {
        this.modelMapper = modelMapper;
    }

    public AssignPgnResponse convertPgnDdbModelToAssignPgnResponse(PgnDDBModel pgnDDBModel, boolean pgnAlreadyAssigned) {
        return AssignPgnResponse.builder()
                .crn(pgnDDBModel.getCrn())
                .pgnAlreadyAssigned(pgnAlreadyAssigned)
                .pgnType(pgnDDBModel.getPgnType().name())
                .leadTrackingNumber(pgnDDBModel.getLeadTrackingNumber())
                .pgnMetadata(getResponsePgnMetadataFromDdbModel(pgnDDBModel))
                .build();
    }

    private PgnMetadata getResponsePgnMetadataFromDdbModel(PgnDDBModel pgnDDBModel) {
        if (pgnDDBModel.getPgnMetadata() == null) {
            return null;
        }
        switch (pgnDDBModel.getPgnType()) {
            case SAVINGS, NR_SAVINGS_NRE, NR_SAVINGS_NRO -> {
                return modelMapper.map(pgnDDBModel.getPgnMetadata(), AccountNumSchemeCodePgnMetadata.class);
            }
            default -> throw new RuntimeException("Invalid PgnType");
        }
    }

    public com.kotak.unified.dbservice.model.pgn.PgnMetadata convertRequestPgnMetadataToPgnMetadataModel(String pgnType, PgnMetadata pgnMetadata) {
        if (pgnMetadata == null) {
            return null;
        }
        switch (PgnType.valueOf(pgnType)) {
            case SAVINGS, NR_SAVINGS_NRE, NR_SAVINGS_NRO -> {
                return modelMapper.map(pgnMetadata, com.kotak.unified.dbservice.model.pgn.AccountNumSchemeCodePgnMetadata.class);
            }
            default -> throw new RuntimeException("Invalid PgnType");
        }
    }
}
