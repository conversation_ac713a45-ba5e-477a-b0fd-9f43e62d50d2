package com.kotak.unified.dbservice.transformer.impl;

import com.kotak.unified.common.exception.InvalidRequestException;
import com.kotak.unified.db.ApplicationData;
import com.kotak.unified.db.ApplicationDataFilter;
import com.kotak.unified.db.GetApplicationDataRequest;
import com.kotak.unified.db.MerchantOnBoardingJourneyApplicationData;
import com.kotak.unified.db.response.EtbDetailsDto;
import com.kotak.unified.dbservice.exceptions.EntityNotFoundException;
import com.kotak.unified.dbservice.transformer.ApplicationDataTransformerV2;
import com.kotak.unified.orchestrator.common.dbmodels.EtbDetails;
import com.kotak.unified.orchestrator.common.dbmodels.merchantonboarding.MerchantOnboardingJourneyMetadata;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

import java.util.Objects;

import static com.kotak.unified.dbservice.utils.Constants.MERCHANT_ONBOARDING;

@Slf4j
@Component
@RequiredArgsConstructor
public class MerchantOnBoardingJourneyApplicationDataTransformer implements ApplicationDataTransformerV2 {

    private final ModelMapper modelMapper;

    @Override
    public ApplicationData populateApplicationData(GetApplicationDataRequest getApplicationDataRequest, com.kotak.unified.orchestrator.common.dbmodels.ApplicationData applicationDataDDBModel) throws EntityNotFoundException, InvalidRequestException {
        log.info("MerchantOnBoardingJourneyApplicationDataTransformer started for getJourneyMetadata: {}", applicationDataDDBModel.getJourneyMetadata());
        if (applicationDataDDBModel.getJourneyMetadata() == null) {
            log.warn("No application data found for lead id : {}", applicationDataDDBModel.getApplicationTrackingId());
            throw new EntityNotFoundException("No application data found for lead id : "
                    + applicationDataDDBModel.getApplicationTrackingId());
        }

        validateInput(applicationDataDDBModel);

        MerchantOnboardingJourneyMetadata journeyMetadata = (MerchantOnboardingJourneyMetadata) applicationDataDDBModel.getJourneyMetadata();

        MerchantOnBoardingJourneyApplicationData applicationData = MerchantOnBoardingJourneyApplicationData.builder().build();

        for (ApplicationDataFilter applicationDataFilter : getApplicationDataRequest.getDataFilters()) {
            setApplicationData(applicationDataFilter, applicationData, journeyMetadata);
        }

        return applicationData;
    }

    @Override
    public String getProduct() {
        return MERCHANT_ONBOARDING;
    }

    private void validateInput(final com.kotak.unified.orchestrator.common.dbmodels.ApplicationData applicationDataDDBModel) throws InvalidRequestException {
        if (Objects.isNull(applicationDataDDBModel.getJourneyMetadata()) ||
                !(applicationDataDDBModel.getJourneyMetadata() instanceof
                        MerchantOnboardingJourneyMetadata)) {

            throw new InvalidRequestException("Application Id does not belong to Upgrade Journey for lead : " +
                    applicationDataDDBModel.getApplicationTrackingId());
        }
    }

    private void setApplicationData(ApplicationDataFilter applicationDataFilter,
                                    MerchantOnBoardingJourneyApplicationData applicationData,
                                    MerchantOnboardingJourneyMetadata journeyMetadata) throws InvalidRequestException {

        switch (applicationDataFilter) {
            case ALL -> {
                EtbDetails crnEtbDetails = journeyMetadata.getCrnDetails();
                if (Objects.nonNull(crnEtbDetails)) {
                    applicationData.setCrnDetails(modelMapper.map(crnEtbDetails, EtbDetailsDto.class));
                }

                EtbDetails corpEtbDetails = journeyMetadata.getCorpCrnDetails();
                if (Objects.nonNull(corpEtbDetails)) {
                    applicationData.setCorpCrnDetails(modelMapper.map(corpEtbDetails, EtbDetailsDto.class));
                }

                applicationData.setUpiOnRupayCreditCardEnabled(journeyMetadata.isUpiOnRupayCreditCardEnabled());
                applicationData.setSelectedCurrentAccountNumber(journeyMetadata.getSelectedCurrentAccountNumber());
                applicationData.setQrDisplayName(journeyMetadata.getQrDisplayName());
            }

            default ->
                    throw new InvalidRequestException("Data filter passed is not applicable : " + applicationDataFilter);
        }
    }

}
