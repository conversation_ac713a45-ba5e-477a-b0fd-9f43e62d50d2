package com.kotak.unified.dbservice.transformer.impl;

import com.kotak.unified.common.exception.InvalidRequestException;
import com.kotak.unified.db.ApplicationData;
import com.kotak.unified.db.ApplicationDataFilter;
import com.kotak.unified.db.GetApplicationDataRequest;
import com.kotak.unified.db.mf.MFApplicationData;
import com.kotak.unified.dbservice.exceptions.EntityNotFoundException;
import com.kotak.unified.dbservice.transformer.ApplicationDataTransformer;
import com.kotak.unified.orchestrator.common.dbmodels.JourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.MutualFundsJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.UserStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import static com.kotak.unified.dbservice.utils.Constants.MUTUAL_FUNDS;

@Slf4j
@Component
@RequiredArgsConstructor
public class MFApplicationDataTransformer implements ApplicationDataTransformer {

    @Override
    public ApplicationData populateApplicationData(GetApplicationDataRequest getApplicationDataRequest,
                                                   UserStatus userStatus) throws EntityNotFoundException, InvalidRequestException {
        JourneyMetadata journeyMetadata = userStatus.getJourneyMetadata();
        if (journeyMetadata == null) {
            log.warn("No application data found for lead id : "
                    + userStatus.getLeadTrackingNumber());
            throw new EntityNotFoundException("No application data found for lead id : "
                    + userStatus.getLeadTrackingNumber());
        }
        this.validateInput(userStatus);

        MFApplicationData applicationData = new MFApplicationData();
        for (ApplicationDataFilter applicationDataFilter : getApplicationDataRequest.getDataFilters()) {
            this.setApplicationData(userStatus, applicationDataFilter, applicationData, (MutualFundsJourneyMetadata) journeyMetadata);
        }
        return applicationData;
    }

    @Override
    public String getProduct() {
        return MUTUAL_FUNDS;
    }

    private void validateInput(final UserStatus userStatus) throws InvalidRequestException {
        if (!(userStatus.getJourneyMetadata() instanceof
                MutualFundsJourneyMetadata)) {
            throw new InvalidRequestException(
                    "Application Id does not belong to MutualFunds Journey for lead : " +
                            userStatus.getLeadTrackingNumber());
        }
    }

    private void setApplicationData(UserStatus userStatus,
                                    ApplicationDataFilter applicationDataFilter,
                                    MFApplicationData mfApplicationData,
                                    MutualFundsJourneyMetadata mutualFundsJourneyMetadata) throws InvalidRequestException {
        switch (applicationDataFilter) {
            case IS_ONBOARDED -> mfApplicationData.setIsOnboarded(mutualFundsJourneyMetadata.getIsOnboarded());
            default -> throw new InvalidRequestException("Data filter passed is not applicable : " + applicationDataFilter);
        }
    }
}