package com.kotak.unified.dbservice.transformer;

import com.kotak.unified.common.exception.InvalidRequestException;
import com.kotak.unified.db.ApplicationData;
import com.kotak.unified.db.GetApplicationDataRequest;
import com.kotak.unified.dbservice.exceptions.EntityNotFoundException;
import com.kotak.unified.orchestrator.common.dbmodels.UserStatus;
import org.springframework.stereotype.Component;

@Component
public interface ApplicationDataTransformer {
    ApplicationData populateApplicationData(GetApplicationDataRequest getApplicationDataRequest,
        UserStatus userStatus)
            throws EntityNotFoundException, InvalidRequestException;

    String getProduct();
}
