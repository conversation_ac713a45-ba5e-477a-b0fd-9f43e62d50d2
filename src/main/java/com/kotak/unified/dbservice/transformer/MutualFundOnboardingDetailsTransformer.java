package com.kotak.unified.dbservice.transformer;

import com.kotak.unified.db.mf.EventMetadataRequest;
import com.kotak.unified.db.mf.MutualFundsOnboardingDetailsRequest;
import com.kotak.unified.dbservice.annotation.Transformer;

import com.kotak.unified.dbservice.model.mf.EventMetadata;
import com.kotak.unified.dbservice.model.mf.MutualFundsOnboardingDetailsDDBModel;
import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;

import java.util.Objects;

@Transformer
@RequiredArgsConstructor
public class MutualFundOnboardingDetailsTransformer {
    private final ModelMapper modelMapper;

    public MutualFundsOnboardingDetailsRequest convertDDBModelToResponseEntity(MutualFundsOnboardingDetailsDDBModel mutualFundsOnboardingDetailsDDBModel) {
        return MutualFundsOnboardingDetailsRequest.builder()
                .eventTrackingId(mutualFundsOnboardingDetailsDDBModel.getEventTrackingId())
                .crn(mutualFundsOnboardingDetailsDDBModel.getCrn())
                .leadTrackingNumber(mutualFundsOnboardingDetailsDDBModel.getLeadTrackingNumber())
                .latestEventStatus(mutualFundsOnboardingDetailsDDBModel.getLatestEventStatus())
                .eventMetadataRequest(convertDBModelToRequestEntity(mutualFundsOnboardingDetailsDDBModel.getEventMetadata()))
                .eventType(mutualFundsOnboardingDetailsDDBModel.getEventType())
                .eventTimeStamp(mutualFundsOnboardingDetailsDDBModel.getEventTimeStamp())
                .version(mutualFundsOnboardingDetailsDDBModel.getVersion())
                .createdAt(mutualFundsOnboardingDetailsDDBModel.getCreatedAt())
                .lastModifiedAt(mutualFundsOnboardingDetailsDDBModel.getLastModifiedAt())
                .version(mutualFundsOnboardingDetailsDDBModel.getVersion())
                .build();
    }

    public MutualFundsOnboardingDetailsDDBModel convertResponseEntityToDDBModel(MutualFundsOnboardingDetailsRequest mutualFundOnboardingDetailsRequest) {
        return MutualFundsOnboardingDetailsDDBModel.builder()
                .eventTrackingId(mutualFundOnboardingDetailsRequest.getEventTrackingId())
                .crn(mutualFundOnboardingDetailsRequest.getCrn())
                .leadTrackingNumber(mutualFundOnboardingDetailsRequest.getLeadTrackingNumber())
                .latestEventStatus(mutualFundOnboardingDetailsRequest.getLatestEventStatus())
                .eventMetadata(convertResponseEntityToDBModel(mutualFundOnboardingDetailsRequest.getEventMetadataRequest()))
                .eventType(mutualFundOnboardingDetailsRequest.getEventType())
                .eventTimeStamp(mutualFundOnboardingDetailsRequest.getEventTimeStamp())
                .version(mutualFundOnboardingDetailsRequest.getVersion())
                .createdAt(mutualFundOnboardingDetailsRequest.getCreatedAt())
                .lastModifiedAt(mutualFundOnboardingDetailsRequest.getLastModifiedAt())
                .version(mutualFundOnboardingDetailsRequest.getVersion())
                .build();
    }

    private EventMetadataRequest convertDBModelToRequestEntity(EventMetadata eventMetadata) {
        if(Objects.nonNull(eventMetadata)) {
            return this.modelMapper.map(eventMetadata, EventMetadataRequest.class);
        }
        return null;
    }

    private EventMetadata convertResponseEntityToDBModel(EventMetadataRequest eventMetadataRequest) {
        if(Objects.nonNull(eventMetadataRequest)) {
            return this.modelMapper.map(eventMetadataRequest, EventMetadata.class);
        }
        return null;
    }

}
