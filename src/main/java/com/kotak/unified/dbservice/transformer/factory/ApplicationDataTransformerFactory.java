package com.kotak.unified.dbservice.transformer.factory;

import com.kotak.unified.dbservice.transformer.ApplicationDataTransformer;
import jakarta.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ApplicationDataTransformerFactory {

    @NonNull
    @Autowired
    List<ApplicationDataTransformer> applicationDataTransformers;

    Map<String, ApplicationDataTransformer> applicationDataTransformerMap =
        new HashMap<>();

    @PostConstruct
    public void init(){
        applicationDataTransformers.forEach(applicationDataTransformer ->
                applicationDataTransformerMap.put(
                    applicationDataTransformer.getProduct(),
                    applicationDataTransformer));
    }

    public ApplicationDataTransformer selectTransformer(String product) {
        return applicationDataTransformerMap.get(product);
    }
}
