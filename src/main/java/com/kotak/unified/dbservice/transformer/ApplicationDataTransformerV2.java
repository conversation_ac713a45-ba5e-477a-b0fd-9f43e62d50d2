package com.kotak.unified.dbservice.transformer;

import com.kotak.unified.common.exception.InvalidRequestException;
import com.kotak.unified.db.ApplicationData;
import com.kotak.unified.db.GetApplicationDataRequest;
import com.kotak.unified.dbservice.exceptions.EntityNotFoundException;
import org.springframework.stereotype.Component;

@Component
public interface ApplicationDataTransformerV2 {
    ApplicationData populateApplicationData(GetApplicationDataRequest getApplicationDataRequest,
                                            com.kotak.unified.orchestrator.common.dbmodels.ApplicationData applicationDataDDBModel)
            throws EntityNotFoundException, InvalidRequestException;

    String getProduct();
}
