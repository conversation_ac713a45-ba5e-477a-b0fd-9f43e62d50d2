package com.kotak.unified.dbservice.transformer;

import com.kotak.unified.dbservice.annotation.Transformer;
import com.kotak.unified.orchestrator.common.dbmodels.ApplicationData;
import com.kotak.unified.orchestrator.common.dbmodels.ApplicationDataDDBModel;
import com.kotak.unified.orchestrator.common.dbmodels.UserJourneyStatusDDBModel;
import com.kotak.unified.orchestrator.common.dbmodels.UserStatus;
import lombok.NonNull;

@Transformer
public class DDBModelTransformer {
    public UserStatus getUserStatusFromUserJourneyStatusAndApplicationData(final @NonNull UserJourneyStatusDDBModel userJourneyStatus,
                                                                           final ApplicationDataDDBModel applicationData) {
        UserStatus.UserStatusBuilder userStatusBuilder = UserStatus.builder();

        userStatusBuilder
                .leadTrackingNumber(userJourneyStatus.getLeadTrackingNumber())
                .phoneNumber(userJourneyStatus.getPhoneNumber())
                .journeyStatus(userJourneyStatus.getJourneyStatus())
                .userIp(userJourneyStatus.getUserIp())
                .channel(userJourneyStatus.getChannel())
                .productId(userJourneyStatus.getProductId())
                .crn(userJourneyStatus.getCrn())
                .createdTime(userJourneyStatus.getCreatedAt())
                .lastModifiedTime(userJourneyStatus.getLastModifiedAt())
                .userJourneyStatusVersion(userJourneyStatus.getVersion())
                .bankLeadTrackingNumber(userJourneyStatus.getBankLeadTrackingNumber())
                .status(userJourneyStatus.getStatus())
                .journeyType(userJourneyStatus.getJourneyType())
                .productType(userJourneyStatus.getProductType())
                .customerType(userJourneyStatus.getCustomerType())
                .journeyStatusReasonCode(userJourneyStatus.getJourneyStatusReasonCode());
        if (applicationData != null) {
            userStatusBuilder.journeyMetadata(applicationData.getJourneyMetadata())
                    .executionData(applicationData.getExecutionData())
                    .partnerId(applicationData.getPartnerId())
                    .applicationDataVersion(applicationData.getVersion());
        }

        return userStatusBuilder.build();
    }

    public UserJourneyStatusDDBModel getUserJourneyStatusDDBModelFromUserStatus(final @NonNull UserStatus userStatus) {

        return UserJourneyStatusDDBModel.builder()
                .leadTrackingNumber(userStatus.getLeadTrackingNumber())
                .phoneNumber(userStatus.getPhoneNumber())
                .journeyStatus(userStatus.getJourneyStatus())
                .crn(userStatus.getCrn())
                .userIp(userStatus.getUserIp())
                .channel(userStatus.getChannel())
                .productId(userStatus.getProductId())
                .createdAt(userStatus.getCreatedTime())
                .version(userStatus.getUserJourneyStatusVersion())
                .bankLeadTrackingNumber(userStatus.getBankLeadTrackingNumber())
                .status(userStatus.getStatus())
                .journeyType(userStatus.getJourneyType())
                .productType(userStatus.getProductType())
                .customerType(userStatus.getCustomerType())
                .journeyStatusReasonCode(userStatus.getJourneyStatusReasonCode())
                .build();

    }

    public ApplicationDataDDBModel getApplicationDataDDBModelFromUserStatus(final @NonNull UserStatus userStatus) {

        return ApplicationDataDDBModel.builder()
                .applicationTrackingId(userStatus.getLeadTrackingNumber())
                .journeyMetadata(userStatus.getJourneyMetadata())
                .crn(userStatus.getCrn())
                .channel(userStatus.getChannel())
                .bankLeadTrackingNumber(userStatus.getBankLeadTrackingNumber())
                .productId(userStatus.getProductId())
                .applicationType(userStatus.getJourneyType())
                .userIp(userStatus.getUserIp())
                .executionData(userStatus.getExecutionData())
                .createdAt(userStatus.getCreatedTime())
                .version(userStatus.getApplicationDataVersion())
                .partnerId(userStatus.getPartnerId())
                .build();
    }

    public ApplicationData getApplicationDataFromApplicationDataDDBModel(final @NonNull ApplicationDataDDBModel applicationDataDDBModel) {
        ApplicationData applicationData = ApplicationData.builder()
                .applicationTrackingId(applicationDataDDBModel.getApplicationTrackingId())
                .phoneNumber(applicationDataDDBModel.getPhoneNumber())
                .crn(applicationDataDDBModel.getCrn())
                .channel(applicationDataDDBModel.getChannel())
                .bankLeadTrackingNumber(applicationDataDDBModel.getBankLeadTrackingNumber())
                .productId(applicationDataDDBModel.getProductId())
                .applicationType(applicationDataDDBModel.getApplicationType())
                .createdTime(applicationDataDDBModel.getCreatedAt())
                .lastModifiedTime(applicationDataDDBModel.getLastModifiedAt())
                .journeyMetadata(applicationDataDDBModel.getJourneyMetadata())
                .executionData(applicationDataDDBModel.getExecutionData())
                .version(applicationDataDDBModel.getVersion())
                .partnerId(applicationDataDDBModel.getPartnerId())
                .build();
        return applicationData;
    }

    /**
     *
     * @param userStatus
     * @param userJourneyStatus
     * @param applicationData
     *
     * Update UserStatus with
     * 1. versions of UserJourneyStatusDDBModel and ApplicationDataDDBModel
     * 2. LastModifiedAt of UserJourneyStatusDDBModel
     */
    public void updateUserStatus(@NonNull UserStatus userStatus,
                                 final @NonNull UserJourneyStatusDDBModel userJourneyStatus,
                                 final @NonNull ApplicationDataDDBModel applicationData) {

        userStatus.setUserJourneyStatusVersion(userJourneyStatus.getVersion());
        userStatus.setLastModifiedTime(userJourneyStatus.getLastModifiedAt());

        userStatus.setApplicationDataVersion(applicationData.getVersion());
    }
}
