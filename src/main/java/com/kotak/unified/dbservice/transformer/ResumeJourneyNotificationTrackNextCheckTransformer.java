package com.kotak.unified.dbservice.transformer;

import com.kotak.unified.db.model.CreateResumeJourneyNotificationTrackNextCheckRecordRequest;
import com.kotak.unified.db.model.CreateResumeJourneyNotificationTrackNextCheckRecordResponse;
import com.kotak.unified.db.model.GetResumeJourneyNotificationTrackNextCheckRecordResponse;
import com.kotak.unified.db.model.UpdateResumeJourneyNotificationTrackNextCheckRecordRequest;
import com.kotak.unified.dbservice.annotation.Transformer;
import com.kotak.unified.orchestrator.common.dbmodels.resumenotification.ResumeJourneyNotificationTrackNextCheck;

import java.time.Instant;

@Transformer
public class ResumeJourneyNotificationTrackNextCheckTransformer {
    public ResumeJourneyNotificationTrackNextCheck convertRequestToResumeJourneyNotificationTrackNextCheckRecord(CreateResumeJourneyNotificationTrackNextCheckRecordRequest createResumeJourneyNotificationTrackNextCheckRecordRequest) {
        return ResumeJourneyNotificationTrackNextCheck
                .builder()
                .leadTrackingNumber(createResumeJourneyNotificationTrackNextCheckRecordRequest.getLeadTrackingNumber())
                .nextCheckTime(createResumeJourneyNotificationTrackNextCheckRecordRequest.getNextCheckTime())
                .build();
    }

    public CreateResumeJourneyNotificationTrackNextCheckRecordResponse getCreateResumeJourneyNotificationTrackNextCheckRecordResponse(boolean isSuccess) {
        return CreateResumeJourneyNotificationTrackNextCheckRecordResponse
                .builder()
                .isCreateRecordSuccessful(isSuccess)
                .build();
    }

    public GetResumeJourneyNotificationTrackNextCheckRecordResponse convertModelToGetResumeJourneyNotificationTrackNextCheckRecordResponse(ResumeJourneyNotificationTrackNextCheck resumeJourneyNotificationTrackNextCheck) {
        return GetResumeJourneyNotificationTrackNextCheckRecordResponse
                .builder()
                .leadTrackingNumber(resumeJourneyNotificationTrackNextCheck.getLeadTrackingNumber())
                .nextCheckTime(resumeJourneyNotificationTrackNextCheck.getNextCheckTime())
                .build();
    }

    public void updateResumeJourneyNotificationTrackNextCheckDetails(ResumeJourneyNotificationTrackNextCheck resumeJourneyNotificationTrackNextCheck, UpdateResumeJourneyNotificationTrackNextCheckRecordRequest updateResumeJourneyNotificationTrackNextCheckRecordRequest) {
        resumeJourneyNotificationTrackNextCheck.setNextCheckTime(updateResumeJourneyNotificationTrackNextCheckRecordRequest.getNextCheckTime());
    }
}
