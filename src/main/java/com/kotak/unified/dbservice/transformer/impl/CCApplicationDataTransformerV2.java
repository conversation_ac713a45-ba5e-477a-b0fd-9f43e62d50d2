package com.kotak.unified.dbservice.transformer.impl;

import com.kotak.unified.common.exception.InvalidRequestException;
import com.kotak.unified.db.ApplicationData;
import com.kotak.unified.db.ApplicationDataFilter;
import com.kotak.unified.db.CCApplicationData;
import com.kotak.unified.db.CCProductSpecificationsResponse;
import com.kotak.unified.db.GetApplicationDataRequest;
import com.kotak.unified.db.MilestoneHistory;
import com.kotak.unified.dbservice.exceptions.EntityNotFoundException;
import com.kotak.unified.dbservice.transformer.ApplicationDataTransformerV2;
import com.kotak.unified.orchestrator.common.dbmodels.CCApplicationMileStone;
import com.kotak.unified.orchestrator.common.dbmodels.CreditCardJourneyMetadata;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.Objects;

import static com.kotak.unified.dbservice.utils.Constants.CC;

@Component
@Slf4j
public class CCApplicationDataTransformerV2 implements ApplicationDataTransformerV2 {

    private final ModelMapper modelMapper;

    public CCApplicationDataTransformerV2(@NonNull final ModelMapper modelMapper) {
        this.modelMapper = modelMapper;
    }

    @Override
    public ApplicationData populateApplicationData(GetApplicationDataRequest getApplicationDataRequest,
                                                   com.kotak.unified.orchestrator.common.dbmodels.ApplicationData applicationDataDDBModel) throws EntityNotFoundException, InvalidRequestException {
        validateInput(applicationDataDDBModel);

        CreditCardJourneyMetadata journeyMetadata =
                (CreditCardJourneyMetadata) applicationDataDDBModel.getJourneyMetadata();

        if(getApplicationDataRequest.getDataFilters().stream().anyMatch(filter -> filter.equals(ApplicationDataFilter.ALL))) {
            CCApplicationData ccApplicationData = modelMapper.map(journeyMetadata, CCApplicationData.class);
            ccApplicationData.setPhoneNumber(applicationDataDDBModel.getPhoneNumber());
            ccApplicationData.setPartnerId(applicationDataDDBModel.getPartnerId());
            if (Objects.nonNull(journeyMetadata.getProductSpecifications())) {
                ccApplicationData.setProductSpecifications(modelMapper.map(journeyMetadata.getProductSpecifications(),
                        CCProductSpecificationsResponse.class));
            }

            if (journeyMetadata.getCcApplicationMileStoneHistory() != null) {
                ccApplicationData.setCcApplicationMileStoneHistoryList(
                        journeyMetadata.getCcApplicationMileStoneHistory().stream()
                                .map(this::toMilestoneHistory)
                                .toList());
            }

            return ccApplicationData;
        }

        return CCApplicationData.builder().build();
    }

    private MilestoneHistory toMilestoneHistory(Pair<CCApplicationMileStone, Instant> ccApplicationMileStoneInstantPair) {
        return MilestoneHistory.builder()
                .milestone(String.valueOf(ccApplicationMileStoneInstantPair.getFirst()))
                .timestamp(ccApplicationMileStoneInstantPair.getSecond())
                .build();
    }

    private void validateInput(final com.kotak.unified.orchestrator.common.dbmodels.ApplicationData applicationDataDDBModel) throws InvalidRequestException {
        if (Objects.isNull(applicationDataDDBModel.getJourneyMetadata()) ||
                !(applicationDataDDBModel.getJourneyMetadata() instanceof
                        CreditCardJourneyMetadata)) {
            throw new InvalidRequestException(
                    "Application Id does not belong to CC Journey " +
                            applicationDataDDBModel.getApplicationTrackingId());
        }
    }

    @Override
    public String getProduct() {
        return CC;
    }
}
