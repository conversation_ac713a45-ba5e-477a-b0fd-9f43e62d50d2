package com.kotak.unified.dbservice.transformer.impl;

import com.kotak.unified.common.exception.InvalidRequestException;
import com.kotak.unified.db.ApplicationData;
import com.kotak.unified.db.ApplicationDataFilter;
import com.kotak.unified.db.CrnUpgradeDetailsResponse;
import com.kotak.unified.db.DeclarationResponse;
import com.kotak.unified.db.GetApplicationDataRequest;
import com.kotak.unified.db.UpgradeJourneyApplicationData;
import com.kotak.unified.db.response.EtbDetailsDto;
import com.kotak.unified.dbservice.exceptions.EntityNotFoundException;
import com.kotak.unified.dbservice.transformer.ApplicationDataTransformer;
import com.kotak.unified.orchestrator.common.dbmodels.Declaration;
import com.kotak.unified.orchestrator.common.dbmodels.UpgradeCustomerJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.UserStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

import static com.kotak.unified.dbservice.utils.Constants.UPGRADE_CUSTOMER_PRODUCT;

@Slf4j
@Component
@RequiredArgsConstructor
public class UpgradeJourneyApplicationDataTransformer implements ApplicationDataTransformer {
    private final ModelMapper modelMapper;

    @Override
    public ApplicationData populateApplicationData(GetApplicationDataRequest getApplicationDataRequest, UserStatus userStatus) throws EntityNotFoundException, InvalidRequestException {

        if (userStatus.getJourneyMetadata() == null) {
            log.warn("No application data found for lead id : "
                    + userStatus.getLeadTrackingNumber());
            throw new EntityNotFoundException("No application data found for lead id : "
                    + userStatus.getLeadTrackingNumber());
        }

        validateInput(userStatus);

        UpgradeCustomerJourneyMetadata journeyMetadata = (UpgradeCustomerJourneyMetadata) userStatus.getJourneyMetadata();

        UpgradeJourneyApplicationData applicationData = UpgradeJourneyApplicationData.builder().build();
        for (ApplicationDataFilter applicationDataFilter : getApplicationDataRequest.getDataFilters()) {
            setApplicationData(userStatus, applicationDataFilter, applicationData, journeyMetadata);
        }

        return applicationData;
    }

    @Override
    public String getProduct() {
        return UPGRADE_CUSTOMER_PRODUCT;
    }

    private void validateInput(final UserStatus userStatus) throws InvalidRequestException {
        if (!(userStatus.getJourneyMetadata() instanceof
                UpgradeCustomerJourneyMetadata)) {
            throw new InvalidRequestException(
                    "Application Id does not belong to Upgrade Journey for lead : " +
                            userStatus.getLeadTrackingNumber());
        }
    }

    private void setApplicationData(UserStatus userStatus,
                                    ApplicationDataFilter applicationDataFilter,
                                    UpgradeJourneyApplicationData applicationData,
                                    UpgradeCustomerJourneyMetadata journeyMetadata) throws InvalidRequestException {

        switch (applicationDataFilter) {
            case ALL -> {
                if (journeyMetadata.getPrimaryCrnDetails() != null) {
                    applicationData.setPrimaryCrnDetails(modelMapper.map(journeyMetadata.getPrimaryCrnDetails(), EtbDetailsDto.class));
                }

                if (journeyMetadata.getPrimaryCrnUpgradeDetails() != null) {
                    applicationData.setPrimaryCrnUpgradeDetails(modelMapper.map(journeyMetadata.getPrimaryCrnUpgradeDetails(), CrnUpgradeDetailsResponse.class));
                }

                if (!CollectionUtils.isEmpty(journeyMetadata.getDeclarations())) {
                    Map<String, DeclarationResponse> declarationResponseMap = new HashMap<>();
                    for (Map.Entry<String, Declaration> entry : journeyMetadata.getDeclarations().entrySet()) {
                        DeclarationResponse declarationResponse = this.modelMapper.map(entry.getValue(), DeclarationResponse.class);
                        declarationResponseMap.put(entry.getKey(), declarationResponse);
                    }
                    applicationData.setDeclarations(declarationResponseMap);
                }

                if (!CollectionUtils.isEmpty(journeyMetadata.getLinkedCrnDetails())) {
                    applicationData.setLinkedCrnDetails(journeyMetadata.getLinkedCrnDetails().stream()
                            .map(linkedCrnDetail -> modelMapper.map(linkedCrnDetail, EtbDetailsDto.class))
                            .collect(Collectors.toList()));
                }

                if (!CollectionUtils.isEmpty(journeyMetadata.getLinkedCrnUpgradeDetails())) {
                    applicationData.setLinkedCrnUpgradeDetails(journeyMetadata.getLinkedCrnUpgradeDetails().stream()
                            .map(linkedCrnUpgradeDetail -> modelMapper.map(linkedCrnUpgradeDetail, CrnUpgradeDetailsResponse.class))
                            .collect(Collectors.toList()));
                }

                if (journeyMetadata.getPrimaryCrnBcifUpdateStatus() != null) {
                    applicationData.setPrimaryCrnBcifUpdateStatus(journeyMetadata.getPrimaryCrnBcifUpdateStatus().toString());
                }
            }

            default -> throw new InvalidRequestException("Data filter passed is not applicable : " + applicationDataFilter);
        }
    }

}
