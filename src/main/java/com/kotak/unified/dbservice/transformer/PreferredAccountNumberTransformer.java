package com.kotak.unified.dbservice.transformer;

import com.kotak.unified.dbinterface.models.PreferredAccountNumberDto;
import com.kotak.unified.dbservice.annotation.Transformer;
import com.kotak.unified.dbservice.model.PreferredAccountNumberDDBModel;

@Transformer
public class PreferredAccountNumberTransformer {

    public PreferredAccountNumberDto convertDDBModelToDTO(
            PreferredAccountNumberDDBModel preferredAccountNumberDDBModel) {
        return PreferredAccountNumberDto.builder()
                .leadTrackingNumber(preferredAccountNumberDDBModel.getLeadTrackingNumber())
                .preferredAccountNumber(preferredAccountNumberDDBModel.getPreferredAccountNumber())
                .blockStatus(preferredAccountNumberDDBModel.getBlockStatus())
                .createdAt(preferredAccountNumberDDBModel.getCreatedAt())
                .lastModifiedAt(preferredAccountNumberDDBModel.getLastModifiedAt())
                .version(preferredAccountNumberDDBModel.getVersion())
                .build();

    }

    public PreferredAccountNumberDDBModel convertDtoToDDBModel(PreferredAccountNumberDto preferredAccountNumberDto) {
        return PreferredAccountNumberDDBModel.builder()
                .leadTrackingNumber(preferredAccountNumberDto.getLeadTrackingNumber())
                .preferredAccountNumber(preferredAccountNumberDto.getPreferredAccountNumber())
                .blockStatus(preferredAccountNumberDto.getBlockStatus())

                .build();
    }
}
