package com.kotak.unified.dbservice.transformer.impl;

import com.kotak.unified.common.enums.RiskProfile;
import com.kotak.unified.common.exception.InvalidRequestException;
import com.kotak.unified.db.AadhaarDetailsResponse;
import com.kotak.unified.db.AddressResponse;
import com.kotak.unified.db.ApplicationData;
import com.kotak.unified.db.ApplicationDataFilter;
import com.kotak.unified.db.BankDetailsResponse;
import com.kotak.unified.db.DeclarationResponse;
import com.kotak.unified.db.GetApplicationDataRequest;
import com.kotak.unified.db.NomineeDetails;
import com.kotak.unified.db.NomineeDetailsResponse;
import com.kotak.unified.db.PanDetailsResponse;
import com.kotak.unified.db.PersonalDetailsResponse;
import com.kotak.unified.db.VKYCDetailsResponse;
import com.kotak.unified.db.response.EtbDetailsDto;
import com.kotak.unified.db.sa.SavingsAccountApplicationData;
import com.kotak.unified.dbservice.exceptions.EntityNotFoundException;
import com.kotak.unified.dbservice.transformer.ApplicationDataTransformer;
import com.kotak.unified.orchestrator.common.dbmodels.Declaration;
import com.kotak.unified.orchestrator.common.dbmodels.JourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.PersonalDetails;
import com.kotak.unified.orchestrator.common.dbmodels.SavingsAccountJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.UserStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.kotak.unified.dbservice.utils.Constants.SAVINGS_ACCOUNT;


@Slf4j
@Component
@RequiredArgsConstructor
public class SavingsAccountApplicationDataTransformer implements ApplicationDataTransformer {

    private final ModelMapper modelMapper;

    @Override
    public ApplicationData populateApplicationData(GetApplicationDataRequest getApplicationDataRequest, UserStatus userStatus) throws EntityNotFoundException, InvalidRequestException {
        JourneyMetadata journeyMetadata = userStatus.getJourneyMetadata();
        if (journeyMetadata == null) {
            log.warn("No application data found for lead id : "
                    + userStatus.getLeadTrackingNumber());
            throw new EntityNotFoundException("No application data found for lead id : "
                    + userStatus.getLeadTrackingNumber());
        }
        this.validateInput(userStatus);

        SavingsAccountApplicationData applicationData = new SavingsAccountApplicationData();
        for (ApplicationDataFilter applicationDataFilter : getApplicationDataRequest.getDataFilters()) {
            this.setApplicationData(userStatus, applicationDataFilter, applicationData, (SavingsAccountJourneyMetadata) journeyMetadata);
        }

        return applicationData;
    }

    private void setApplicationData(UserStatus userStatus,
                                    ApplicationDataFilter applicationDataFilter,
                                    SavingsAccountApplicationData applicationData,
                                    SavingsAccountJourneyMetadata journeyMetadata) throws InvalidRequestException {
        switch (applicationDataFilter) {
            case ALL -> {
                applicationData.setPhoneNumber(userStatus.getPhoneNumber());

                if (journeyMetadata.getAadhaarDetails() != null) {
                    applicationData.setAadhaarDetails(modelMapper.map(journeyMetadata.getAadhaarDetails(), AadhaarDetailsResponse.class));
                }

                applicationData.setIsAddressPincodeServiceable(BooleanUtils.isTrue(journeyMetadata.getIsAddressPincodeServiceable()));

                if (journeyMetadata.getNomineeDetails() != null) {
                    applicationData.setNomineeDetails(modelMapper.map(journeyMetadata.getNomineeDetails(), NomineeDetailsResponse.class));
                }

                applicationData.setEmailAddress(journeyMetadata.getEmailAddress());
                applicationData.setIsPanNumberValid(BooleanUtils.isTrue(journeyMetadata.getIsPanNumberValid()));

                if (journeyMetadata.getPanDetails() != null) {
                    applicationData.setPan(journeyMetadata.getPanDetails().getNumber());
                    applicationData.setPanDetails(modelMapper.map(journeyMetadata.getPanDetails(), PanDetailsResponse.class));
                }
                if (journeyMetadata.getCommunicationAddress() != null) {
                    applicationData.setCommunicationAddress(modelMapper.map(journeyMetadata.getCommunicationAddress(), AddressResponse.class));
                }
                if (journeyMetadata.getVkycDetails() != null) {
                    applicationData.setVkycDetails(modelMapper.map(journeyMetadata.getVkycDetails(), VKYCDetailsResponse.class));
                }
                if (journeyMetadata.getBankDetails() != null) {
                    applicationData.setBankDetails(modelMapper.map(journeyMetadata.getBankDetails(), BankDetailsResponse.class));
                }
                if (journeyMetadata.getPersonalDetails() != null) {
                    applicationData.setPersonalDetails(modelMapper.map(journeyMetadata.getPersonalDetails(), PersonalDetailsResponse.class));
                }
                if (!CollectionUtils.isEmpty(journeyMetadata.getDeclarations())) {
                    Map<String, DeclarationResponse> declarationResponseMap = new HashMap<>();
                    for (Map.Entry<String, Declaration> entry : journeyMetadata.getDeclarations().entrySet()) {
                        DeclarationResponse declarationResponse = this.modelMapper.map(entry.getValue(), DeclarationResponse.class);
                        declarationResponseMap.put(entry.getKey(), declarationResponse);
                    }
                    applicationData.setDeclarations(declarationResponseMap);
                }

                if (journeyMetadata.getIsCommunicationAddressSameAsExistingBcifAddress() != null) {
                    applicationData.setIsCommunicationAddressSameAsExistingBcifAddress(BooleanUtils.isTrue(journeyMetadata.getIsCommunicationAddressSameAsExistingBcifAddress()));
                }

                if (journeyMetadata.getActivMoneyOptIn() != null) {
                    applicationData.setActivMoneyOptIn(BooleanUtils.isTrue(journeyMetadata.getActivMoneyOptIn()));
                }

                if (journeyMetadata.getEtbDetails() != null) {
                    applicationData.setEtbDetails(modelMapper.map(journeyMetadata.getEtbDetails(), EtbDetailsDto.class));
                }

                if (journeyMetadata.getEtbDob() != null) {
                    applicationData.setEtbDob(journeyMetadata.getEtbDob());
                }
            }
            case PHONE_NUMBER -> applicationData.setPhoneNumber(userStatus.getPhoneNumber());
            case PAN -> {
                if (journeyMetadata.getPanDetails()!=null) {
                    applicationData.setPan(journeyMetadata.getPanDetails().getNumber());
                }
            }
            default ->
                    throw new InvalidRequestException("Data filter passed is not applicable : " + applicationDataFilter);
        }
    }

    private void validateInput(UserStatus userStatus) throws InvalidRequestException {
        if (!(userStatus.getJourneyMetadata() instanceof
                SavingsAccountJourneyMetadata)) {
            throw new InvalidRequestException(
                    "Application Id does not belong to Savings account Journey for lead : " +
                            userStatus.getLeadTrackingNumber());
        }
    }


    @Override
    public String getProduct() {
        return SAVINGS_ACCOUNT;
    }
}

