package com.kotak.unified.dbservice.transformer;

import com.kotak.unified.db.enums.RefundTransferStatusDTO;
import com.kotak.unified.db.response.TransactionResponse;
import com.kotak.unified.dbservice.annotation.Transformer;
import com.kotak.unified.enums.FundsTransferStatus;
import com.kotak.unified.orchestrator.common.dbmodels.Transaction;

@Transformer
public class TransactionTransformer {
    public TransactionResponse convertTransactionToResponse(Transaction transaction) {
        return TransactionResponse
                .builder()
                .txnId(transaction.getTxnId())
                .leadTrackingNumber(transaction.getLeadTrackingNumber())
                .status(transaction.getStatus())
                .fundsTransferStatus(FundsTransferStatus.valueOf(transaction.getFundsTransferStatus().name()))
                .createdAt(transaction.getCreatedAt())
                .mihPayId(transaction.getMihPayId())
                .mode(transaction.getMode())
                .unmappedStatus(transaction.getUnmappedStatus())
                .key(transaction.getKey())
                .amount(transaction.getAmount())
                .discount(transaction.getDiscount())
                .netAmountDebit(transaction.getNetAmountDebit())
                .addedOn(transaction.getAddedOn())
                .productInfo(transaction.getProductInfo())
                .firstName(transaction.getFirstName())
                .lastName(transaction.getLastName())
                .address1(transaction.getAddress1())
                .address2(transaction.getAddress2())
                .city(transaction.getCity())
                .state(transaction.getState())
                .country(transaction.getCountry())
                .zipcode(transaction.getZipcode())
                .email(transaction.getEmail())
                .phone(transaction.getPhone())
                .requestHash(transaction.getRequestHash())
                .responseHash(transaction.getResponseHash())
                .hashMatching(transaction.getHashMatching())
                .field1(transaction.getField1())
                .field2(transaction.getField2())
                .field3(transaction.getField3())
                .field4(transaction.getField4())
                .field5(transaction.getField5())
                .field6(transaction.getField6())
                .field7(transaction.getField7())
                .field8(transaction.getField8())
                .field9(transaction.getField9())
                .paymentSource(transaction.getPaymentSource())
                .meCode(transaction.getMeCode())
                .pgType(transaction.getPgType())
                .bankRefNum(transaction.getBankRefNum())
                .bankCode(transaction.getBankCode())
                .error(transaction.getError())
                .errorMessage(transaction.getErrorMessage())
                .finacleProcessStatus(transaction.getFinacleProcessStatus())
                .finacleTransactionDate(transaction.getFinacleTransactionDate())
                .finacleProcessRemarks(transaction.getFinacleProcessRemarks())
                .finacleTransactionId(transaction.getFinacleTransactionId())
                .refundStatus(transaction.getRefundStatus()!=null ? RefundTransferStatusDTO.valueOf(transaction.getRefundStatus().toString()):null)
                .refundRequestId(transaction.getRefundRequestId())
                .refundCompletedAt(transaction.getRefundCompletedAt())
                .refundErrorCode(transaction.getRefundErrorCode())
                 .build();
    }
}
