package com.kotak.unified.dbservice.transformer.impl;

import com.kotak.unified.common.exception.InvalidRequestException;
import com.kotak.unified.db.AadhaarDetailsResponse;
import com.kotak.unified.db.AddressResponse;
import com.kotak.unified.db.ApplicationData;
import com.kotak.unified.db.ApplicationDataFilter;
import com.kotak.unified.db.AssociatedEmployeeDetails;
import com.kotak.unified.db.DisbursementDetails;
import com.kotak.unified.db.EmploymentDetailsResponse;
import com.kotak.unified.db.GetApplicationDataRequest;
import com.kotak.unified.db.PLAddressTypeResponse;
import com.kotak.unified.db.PLApplicationData;
import com.kotak.unified.db.PLApplicationMileStoneResponse;
import com.kotak.unified.db.PanDetailsResponse;
import com.kotak.unified.db.TMXDetails;
import com.kotak.unified.db.assets.AdditionalDetailsResponse;
import com.kotak.unified.db.assets.IncomeDetailsResponse;
import com.kotak.unified.db.assets.LoanIntentResponse;
import com.kotak.unified.db.assets.PLBasicDetails;
import com.kotak.unified.db.assets.PLProductDetailsResponse;
import com.kotak.unified.dbservice.transformer.ApplicationDataTransformer;
import com.kotak.unified.orchestrator.common.dbmodels.AadhaarDetails;
import com.kotak.unified.orchestrator.common.dbmodels.Address;
import com.kotak.unified.orchestrator.common.dbmodels.NomineeDetails;
import com.kotak.unified.orchestrator.common.dbmodels.ExecutionData;
import com.kotak.unified.orchestrator.common.dbmodels.PLApplicationMileStone;
import com.kotak.unified.orchestrator.common.dbmodels.PanDetails;
import com.kotak.unified.orchestrator.common.dbmodels.PersonalLoanJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.UserStatus;
import com.kotak.unified.orchestrator.common.dbmodels.assets.AdditionalDetails;
import com.kotak.unified.orchestrator.common.dbmodels.assets.ApplicantDetails;
import com.kotak.unified.orchestrator.common.dbmodels.assets.CrnDetails;
import com.kotak.unified.orchestrator.common.dbmodels.assets.CrnNameMap;
import com.kotak.unified.orchestrator.common.dbmodels.assets.PLAddressType;
import lombok.NonNull;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.modelmapper.TypeToken;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.kotak.unified.db.ApplicationDataFilter.ACCOUNT_AGGREGATOR_DETAILS;
import static com.kotak.unified.dbservice.utils.Constants.PERSONAL_LOAN;

@Component
@Slf4j
public class PLApplicationDataTransformer implements
        ApplicationDataTransformer {

    private final ModelMapper modelMapper;

    public PLApplicationDataTransformer(@NonNull final ModelMapper modelMapper) {
        this.modelMapper = modelMapper;
    }

    public ApplicationData populateApplicationData(@NonNull
                                                   GetApplicationDataRequest getApplicationDataRequest,
                                                   @NonNull UserStatus userStatus) {
        validateInput(userStatus);
        PersonalLoanJourneyMetadata journeyMetadata =
                (PersonalLoanJourneyMetadata) userStatus.getJourneyMetadata();
        PLApplicationData applicationData = new PLApplicationData();
        getApplicationDataRequest.getDataFilters().forEach(
                applicationDataFilter -> updateApplicationData(applicationDataFilter,
                        userStatus, applicationData, journeyMetadata));
        updateApplicationDataWithLastModifiedTime(applicationData,
                userStatus);
        return applicationData;
    }

    @SneakyThrows
    private void updateApplicationData(
            final ApplicationDataFilter applicationDataFilter,
            final UserStatus userStatus,
            final PLApplicationData applicationData,
            final PersonalLoanJourneyMetadata journeyMetadata) {
        updateApplicationDataWithBankLeadTrackingNumber(applicationData, userStatus);
        switch (applicationDataFilter) {
            case DISBURSEMENT_DETAILS -> updateApplicationDataWithDisbursementDetails(applicationData,
                    journeyMetadata, userStatus.getUserIp());
            case AADHAAR -> updateApplicationDataWithAadhaarDetails(applicationData,
                    journeyMetadata);
            case PAN -> updateApplicationDataWithPanDetails(applicationData,
                    journeyMetadata);
            case INCOME -> updateApplicationDataWithIncomeDetails(applicationData,
                    journeyMetadata);
            case ADDRESS -> updateApplicationDataWithAddressDetails(applicationData,
                    journeyMetadata);
            case CRN -> updateApplicationDataWithSelectedCrn(applicationData,
                    journeyMetadata);
            case PRODUCT -> updateApplicationDataWithProductDetails(applicationData,
                    journeyMetadata);
            case VKYC -> updateApplicationDataWithVkycDetails();
            case EMAIL_ID -> updateApplicationDataWithEmail(applicationData,
                    journeyMetadata, userStatus.getExecutionData());
            case ADDITIONAL_DETAILS -> updateApplicationDataWithAdditionalDetails(applicationData,
                    journeyMetadata);
            case PHONE_NUMBER -> updateApplicationDataWithPhoneNumber(applicationData,
                    userStatus);
            case INTENT -> updateApplicationDataWithLoanIntent(applicationData,
                    journeyMetadata);
            case BASIC_DETAIL -> updateApplicationDataWithBasicDetails(applicationData,
                    journeyMetadata);
            case TMX_DETAILS -> updateApplicationDataWithTmxDetails(applicationData,
                    journeyMetadata);
            case MILESTONE_HISTORY_LIST -> updateApplicationDataWithMilestoneHistory(applicationData,
                    journeyMetadata);
            case BLOCKED_REASONS -> updateApplicationDataWithBlockedReasons(applicationData,
                    journeyMetadata);
            case OCCUPATION_TYPE -> updateApplicationDataWithOccupationType(applicationData, journeyMetadata);
            case EMPLOYMENT_DETAILS -> updateApplicationDataWithEmploymentDetails(applicationData, journeyMetadata);
            case FI_CONSENTS -> updateApplicationDataWithFiConsents(applicationData, journeyMetadata);
            case RM_CODE -> updateApplicationDataWithRelationShipManagerCode(applicationData, journeyMetadata);
            case EMPLOYEE_RELATION ->
                    updateApplicationDataWithEmployeeRelationShipNameAndCode(applicationData, journeyMetadata);
            case LOAN_TYPE -> updateApplicationDataWithLoanType(applicationData,
                    userStatus);
            case KLI_DETAILS -> updateApplicationDataWithKLIDetails(applicationData, journeyMetadata);
            case NOMINEE_DETAILS -> updateApplicationDataWithNomineeDetails(applicationData, journeyMetadata);
            case JOURNEY_MILESTONE -> updateJourneyMileStone(applicationData, journeyMetadata);
            case UTM_TAGS -> updateUtmTags(applicationData, journeyMetadata);
            case RESUME_UTM_TAGS -> updateResumeUtmTags(applicationData,journeyMetadata);
            case LEAD_CREATION_CHANNEL -> updateLeadCreationChannel(applicationData,journeyMetadata);
            case OTP_LOG -> updateOtpLog(applicationData, journeyMetadata);
            case ACCOUNT_AGGREGATOR_DETAILS -> updateAADetails(applicationData, journeyMetadata);
            case APPLICATION_CREATED_TIME -> updateApplicationCreatedTime(applicationData, userStatus);
            case CHANNEL -> applicationData.setChannel(userStatus.getChannel());
            case PARTNER_ID -> applicationData.setPartnerId(userStatus.getPartnerId());
            default -> throw new InvalidRequestException(
                    "Data filter passed is not applicable");
        }
    }

    private void updateAADetails(PLApplicationData applicationData, PersonalLoanJourneyMetadata journeyMetadata) {
        log.info("Setting FIP and bankAccountLinkedPhoneNumber");
        String selectedFip = Optional.ofNullable(journeyMetadata)
                .map(PersonalLoanJourneyMetadata::getApplicantDetails)
                .map(ApplicantDetails::getLinkedBankAccounts)
                .filter(linkedBankAccounts -> !linkedBankAccounts.isEmpty())
                .map(linkedBankAccounts -> linkedBankAccounts.get(0))
                .orElse(null);
        String bankAccountLinkedPhoneNumber = Optional.ofNullable(journeyMetadata)
                .map(PersonalLoanJourneyMetadata::getApplicantDetails)
                .map(ApplicantDetails::getBankAccountLinkedPhoneNumber)
                .orElse(null);

        applicationData.setSelectedFip(selectedFip);
        applicationData.setBankAccountLinkedPhoneNumber(bankAccountLinkedPhoneNumber);
    }

    private void updateApplicationCreatedTime(
      final PLApplicationData applicationData,
      final UserStatus userStatus) {
    applicationData.setApplicationCreatedTime(
        userStatus.getCreatedTime());
  }

  private void updateOtpLog(final PLApplicationData applicationData,
      final PersonalLoanJourneyMetadata journeyMetadata) {
    applicationData.setOtpLog(journeyMetadata.getOtpLog());
  }

  private void updateUtmTags(final PLApplicationData applicationData,
      final PersonalLoanJourneyMetadata journeyMetadata) {
    applicationData.setUtmTags(journeyMetadata.getUtmTags());
  }

  private void updateResumeUtmTags(final PLApplicationData applicationData,
                                   final PersonalLoanJourneyMetadata journeyMetadata) {
    applicationData.setResumeUtmTags(journeyMetadata.getResumeUtmTags());
  }

    private void updateLeadCreationChannel(final PLApplicationData applicationData,
                                           final PersonalLoanJourneyMetadata journeyMetadata) {
        applicationData.setLeadCreationChannel(journeyMetadata.getLeadCreationChannel());
    }

  private void updateJourneyMileStone(final PLApplicationData applicationData,
      final PersonalLoanJourneyMetadata journeyMetadata) {
      if(Objects.isNull(journeyMetadata.getPlApplicationMileStone())) {
        log.info("Application milestone is null returning");
        return;
      }

      applicationData.setJourneyMileStone(
          PLApplicationMileStoneResponse.valueOf(journeyMetadata.getPlApplicationMileStone().name()));
  }

  private void updateApplicationDataWithBankLeadTrackingNumber(final PLApplicationData applicationData,
                                                                final UserStatus userStatus) {
        final String bankLeadTrackingNumber =
                Optional.ofNullable(userStatus)
                        .map(UserStatus::getBankLeadTrackingNumber).orElse(null);
        applicationData.setBankLeadTrackingNumber(bankLeadTrackingNumber);
    }

    private void updateApplicationDataWithLoanType(final PLApplicationData applicationData,
                                                   final UserStatus userStatus) {
        final String loanType =
                Optional.ofNullable(userStatus)
                        .map(UserStatus::getExecutionData)
                        .map(ExecutionData::getLatestCreditOffer).orElse(null);
        applicationData.setLoanType(loanType);
    }

    private void updateApplicationDataWithRelationShipManagerCode(
            final PLApplicationData applicationData,
            final PersonalLoanJourneyMetadata journeyMetadata) {
        applicationData.setRmCode(journeyMetadata.getRmCode());
    }

    private void updateApplicationDataWithEmployeeRelationShipNameAndCode(
            final PLApplicationData applicationData,
            final PersonalLoanJourneyMetadata journeyMetadata) {
        if (Objects.nonNull(
                journeyMetadata.getApplicantDetails().getAssociatedEmployeeDetails())) {
            applicationData.setAssociatedEmployeeDetails(modelMapper.map(journeyMetadata.getApplicantDetails().getAssociatedEmployeeDetails(),
                    AssociatedEmployeeDetails.class));
        }
    }

    private void updateApplicationDataWithFiConsents(
            final PLApplicationData applicationData,
            final PersonalLoanJourneyMetadata journeyMetadata) {
        applicationData.setFiConsentDetails(journeyMetadata.getFiConsents());
    }

   private void updateApplicationDataWithOccupationType(
            final PLApplicationData applicationData,
            final PersonalLoanJourneyMetadata journeyMetadata) {
        applicationData.setOccupation(
                journeyMetadata.getApplicantDetails().getOccupation());
    }

    private void updateApplicationDataWithEmploymentDetails(
            final PLApplicationData applicationData,
            final PersonalLoanJourneyMetadata journeyMetadata) {
        if (Objects.nonNull(
                journeyMetadata.getApplicantDetails().getEmploymentDetails())) {
            applicationData.setEmploymentDetailsResponse(modelMapper.map(
                    journeyMetadata.getApplicantDetails().getEmploymentDetails(),
                    EmploymentDetailsResponse.class));
        }
    }

    private void updateApplicationDataWithTmxDetails(
            final PLApplicationData applicationData,
            final PersonalLoanJourneyMetadata journeyMetadata) {
        if (Objects.nonNull(journeyMetadata.getTmxSession())) {
            applicationData.setTmxSession(
                    modelMapper.map(journeyMetadata.getTmxSession()
                            , TMXDetails.class));
        }
    }

    private void updateApplicationDataWithMilestoneHistory(
            final PLApplicationData applicationData,
            final PersonalLoanJourneyMetadata journeyMetadata) {
        if (Objects.nonNull(journeyMetadata.getPlApplicationMileStoneHistory())) {
            applicationData.setPlApplicationMileStoneHistory(
                    modelMapper.map(journeyMetadata.getPlApplicationMileStoneHistory(),
                            new TypeToken<List<Pair<PLApplicationMileStone, Instant>>>() {
                            }.getType()));
        }
    }

    private void updateApplicationDataWithBlockedReasons(
            final PLApplicationData applicationData,
            final PersonalLoanJourneyMetadata journeyMetadata) {
        if (Objects.nonNull(journeyMetadata.getBlockedReasons())) {
            applicationData.setBlockedReasons(
                    modelMapper.map(
                            journeyMetadata.getBlockedReasons(),
                            new TypeToken<List<String>>() {
                            }.getType()));
        }
    }

    private void updateApplicationDataWithDisbursementDetails(
            final PLApplicationData applicationData,
            final PersonalLoanJourneyMetadata journeyMetadata,
            final String userIp) {
        applicationData.setDisbursementDetails(
                modelMapper.map(
                        journeyMetadata
                                .getDisbursementDetails(), DisbursementDetails.class));
        DisbursementDetails disbursementDetails = applicationData.getDisbursementDetails();
        ApplicantDetails applicantDetails = journeyMetadata.getApplicantDetails();
        if (applicantDetails != null) {
            disbursementDetails.setKfsId(applicantDetails.getKfsId());
            disbursementDetails.setLanguage(applicantDetails.getLanguage());
        }
        disbursementDetails.setUserIp(userIp);
        applicationData.setDisbursementDetails(disbursementDetails);
    }

    private void updateApplicationDataWithBasicDetails(
            final PLApplicationData applicationData,
            final PersonalLoanJourneyMetadata journeyMetadata) {
        applicationData.setBasicDetails(
                getBasicDetailsFromJourneyMetadata(journeyMetadata));
    }

    @SneakyThrows
    private PLBasicDetails getBasicDetailsFromJourneyMetadata(
            final PersonalLoanJourneyMetadata journeyMetadata) {
        return PLBasicDetails.builder()
                .customerName(getCustomerNameFromJourneyMetadata(journeyMetadata))
                .dob(getDobFromJourneyMetadata(journeyMetadata))
                .gender(getGenderFromJourneyMetadata(journeyMetadata))
                .build();
    }

    private String getDobFromJourneyMetadata(PersonalLoanJourneyMetadata journeyMetadata) {
      return Optional.ofNullable(journeyMetadata)
          .map(PersonalLoanJourneyMetadata::getApplicantDetails)
          .map(ApplicantDetails::getDob)
          .orElseGet(() ->
              Optional.ofNullable(journeyMetadata)
                  .map(PersonalLoanJourneyMetadata::getApplicantDetails)
                  .map(ApplicantDetails::getPanDetails)
                  .map(PanDetails::getDateOfBirth)
                  .orElse(null)
          );
    }

    private String getCustomerNameFromJourneyMetadata(final PersonalLoanJourneyMetadata journeyMetadata) {
        return Optional.ofNullable(journeyMetadata.getApplicantDetails().getCrnDetails())
            .map(CrnDetails::getSelectedCrn)
            .map(CrnNameMap::getCrnName)
            .orElseGet(() ->
                Optional.ofNullable(journeyMetadata.getApplicantDetails().getPanDetails())
                    .map(PanDetails::getDisplayName)
                    .orElse(null)
            );
    }

    private String getGenderFromJourneyMetadata(final PersonalLoanJourneyMetadata journeyMetadata) {
       return Optional.ofNullable(journeyMetadata.getApplicantDetails())
          .map(ApplicantDetails::getAdditionalDetails)
          .map(AdditionalDetails::getGender)
          .orElse(null);

    }

    private void updateApplicationDataWithAadhaarDetails(
            final PLApplicationData applicationData,
            final PersonalLoanJourneyMetadata journeyMetadata) {
        final AadhaarDetails aadhaarDetails =
                Optional.ofNullable(journeyMetadata)
                        .map(PersonalLoanJourneyMetadata::getApplicantDetails)
                        .map(ApplicantDetails::getAadhaarDetails).orElse(null);
        if(Objects.nonNull(aadhaarDetails)) {
            applicationData.setAadhaarDetails(
                    modelMapper.map(aadhaarDetails,
                            AadhaarDetailsResponse.class));
        }
    }

    private void updateApplicationDataWithPanDetails(
            final PLApplicationData applicationData,
            final PersonalLoanJourneyMetadata journeyMetadata) {
        final PanDetails panDetails =
                Optional.ofNullable(journeyMetadata)
                        .map(PersonalLoanJourneyMetadata::getApplicantDetails)
                        .map(ApplicantDetails::getPanDetails).orElse(null);
        if (Objects.nonNull(panDetails)) {
            applicationData.setPanDetails(
                    modelMapper.map(panDetails, PanDetailsResponse.class));
        }
    }

    private void updateApplicationDataWithIncomeDetails(
        final PLApplicationData applicationData,
        final PersonalLoanJourneyMetadata journeyMetadata) {

      Optional.ofNullable(journeyMetadata)
          .map(PersonalLoanJourneyMetadata::getApplicantDetails)
          .map(ApplicantDetails::getIncomeDetails)
          .ifPresent(incomeDetails ->
              applicationData.setIncomeDetails(
                  modelMapper.map(incomeDetails, IncomeDetailsResponse.class)
              )
          );
    }


    private void updateApplicationDataWithAddressDetails(
            final PLApplicationData applicationData,
            final PersonalLoanJourneyMetadata journeyMetadata) {
        final Address address =
                Optional.ofNullable(journeyMetadata)
                        .map(PersonalLoanJourneyMetadata::getApplicantDetails)
                        .map(ApplicantDetails::getAddress).orElse(null);
        if (Objects.nonNull(address)) {
            applicationData.setAddress(
                    modelMapper.map(address, AddressResponse.class));
        }
        final PLAddressType plAddressType =
                Optional.ofNullable(journeyMetadata)
                        .map(PersonalLoanJourneyMetadata::getApplicantDetails)
                        .map(ApplicantDetails::getCommunicationAddressSameAsAddress)
                        .orElse(null);
        if (Objects.nonNull(plAddressType)) {
            applicationData.setCommunicationAddressSameAsAddress(
                    modelMapper.map(plAddressType,
                            PLAddressTypeResponse.class));
        }
    }

    private void updateApplicationDataWithSelectedCrn(
            final PLApplicationData applicationData,
            final PersonalLoanJourneyMetadata journeyMetadata) {
        applicationData.setSelectedCrn(
                Optional.ofNullable(journeyMetadata
                                        .getApplicantDetails().getCrnDetails())
                        .map(CrnDetails::getSelectedCrn)
                        .map(CrnNameMap::getCrn).orElse(null));
    }

    private void updateApplicationDataWithProductDetails(
            final PLApplicationData applicationData,
            final PersonalLoanJourneyMetadata journeyMetadata) {
        applicationData.setProductDetails(
                modelMapper.map(journeyMetadata.getProductDetails(),
                        PLProductDetailsResponse.class));
    }

    private void updateApplicationDataWithVkycDetails() {
        // VKYC as filter is deprecated, use vkyc details api of db service directly
    }

    private void updateApplicationDataWithKLIDetails(final PLApplicationData applicationData, final PersonalLoanJourneyMetadata journeyMetadata) {
        com.kotak.unified.orchestrator.common.dbmodels.KotakLifeInsuranceDetails kotakLifeInsurance = journeyMetadata.getKli();
        if (Objects.isNull(kotakLifeInsurance)) {
            return;
        }
        com.kotak.unified.db.KotakLifeInsuranceDetails kliDetails = com.kotak.unified.db.KotakLifeInsuranceDetails.builder().insuranceAmount(kotakLifeInsurance.getInsuranceAmount()).isEnabled(kotakLifeInsurance.isEnabled()).build();
        applicationData.setKotakLifeInsurance(kliDetails);
    }

    private void updateApplicationDataWithNomineeDetails(final PLApplicationData applicationData, final PersonalLoanJourneyMetadata journeyMetadata) {
        NomineeDetails nomineeDetails = journeyMetadata.getApplicantDetails().getNomineeDetails();
        if (Objects.isNull(nomineeDetails)) {
            return;
        }
        com.kotak.unified.db.NomineeDetails nomineeDetailDB = new com.kotak.unified.db.NomineeDetails();
        nomineeDetailDB.setDOB(nomineeDetails.getDOB());
        nomineeDetailDB.setGender(nomineeDetails.getGender());
        nomineeDetailDB.setName(nomineeDetails.getName());
        nomineeDetailDB.setRelationshipType(nomineeDetails.getRelationshipType());
        applicationData.setNomineeDetails(nomineeDetailDB);
    }

    private void updateApplicationDataWithEmail(
            final PLApplicationData applicationData,
            final PersonalLoanJourneyMetadata journeyMetadata,
            final ExecutionData executionData) {
        applicationData.setEmailId(
                journeyMetadata
                        .getApplicantDetails().getEmailId());
        if(Objects.nonNull(executionData))
          applicationData.setIsEmailVerified(
              executionData.getIsEmailAddressVerified());

    }

    private void updateApplicationDataWithAdditionalDetails(
            final PLApplicationData applicationData,
            final PersonalLoanJourneyMetadata journeyMetadata) {
        final AdditionalDetails additionalDetails =
                Optional.ofNullable(journeyMetadata)
                        .map(PersonalLoanJourneyMetadata::getApplicantDetails)
                        .map(ApplicantDetails::getAdditionalDetails)
                        .orElse(null);
        if (Objects.nonNull(additionalDetails)) {
            applicationData.setAdditionalDetails(
                    modelMapper.map(additionalDetails,
                            AdditionalDetailsResponse.class));
        }
    }

    private void updateApplicationDataWithPhoneNumber(
            final PLApplicationData applicationData,
            final UserStatus userStatus) {
        if(Objects.nonNull(userStatus)) {
            applicationData.setPhoneNumber(
                    userStatus.getPhoneNumber());
        }
    }

    private void updateApplicationDataWithLoanIntent(
            final PLApplicationData applicationData,
            final PersonalLoanJourneyMetadata journeyMetadata) {
      Optional.ofNullable(journeyMetadata)
          .map(PersonalLoanJourneyMetadata::getLoanIntent)
          .ifPresent(loanIntent ->
              applicationData.setIntent(
                  modelMapper.map(loanIntent, LoanIntentResponse.class)
              )
          );
    }

    private void updateApplicationDataWithLastModifiedTime(
            final PLApplicationData applicationData,
            final UserStatus userStatus) {
        applicationData.setLastModifiedTime(
                userStatus.getLastModifiedTime());

    }

    @SneakyThrows
    private void validateInput(final UserStatus userStatus) {
        if (Objects.isNull(userStatus.getJourneyMetadata()) ||
                !(userStatus.getJourneyMetadata() instanceof
                        PersonalLoanJourneyMetadata)) {
            throw new InvalidRequestException(
                    "Application Id does not belong to PL Journey " +
                            userStatus.getLeadTrackingNumber());
        }
    }

    @Override
    public String getProduct() {
        return PERSONAL_LOAN;
    }
}