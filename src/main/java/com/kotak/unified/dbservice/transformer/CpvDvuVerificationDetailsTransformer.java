package com.kotak.unified.dbservice.transformer;

import com.kotak.unified.dbinterface.models.CpvDvuVerificationDetailsDto;
import com.kotak.unified.dbinterface.models.CpvDvuVerificationMetadataDto;
import com.kotak.unified.dbinterface.models.NRCpvDvuVerificationMetadataDto;
import com.kotak.unified.dbservice.annotation.Transformer;
import com.kotak.unified.dbservice.model.cpv.CpvDvuVerificationDetailsDDBModel;
import com.kotak.unified.dbservice.model.cpv.CpvDvuVerificationMetadata;
import com.kotak.unified.dbservice.model.cpv.NRCpvDvuVerificationMetadata;

@Transformer
public class CpvDvuVerificationDetailsTransformer {
    public CpvDvuVerificationDetailsDDBModel convertDtoToDDBModel(CpvDvuVerificationDetailsDto cpvDvuVerificationDetailsDto) {
        return CpvDvuVerificationDetailsDDBModel.builder()
                .leadTrackingNumber(cpvDvuVerificationDetailsDto.getLeadTrackingNumber())
                .actionTrackingId(cpvDvuVerificationDetailsDto.getActionTrackingId())
                .cpvCode(cpvDvuVerificationDetailsDto.getCpvCode())
                .dvuCode(cpvDvuVerificationDetailsDto.getDvuCode())
                .latestStatus(cpvDvuVerificationDetailsDto.getLatestStatus())
                .lastStatusEventRecordedAt(cpvDvuVerificationDetailsDto.getLastStatusEventRecordedAt())
                .version(cpvDvuVerificationDetailsDto.getVersion())
                .cpvDvuVerificationMetadata(this.convertMetadataDtoToDDBModel(cpvDvuVerificationDetailsDto.getCpvDvuVerificationMetadataDto()))
                .build();
    }

    public CpvDvuVerificationDetailsDto convertDDBModelToDto(CpvDvuVerificationDetailsDDBModel cpvDvuVerificationDetailsDDBModel) {
        if (cpvDvuVerificationDetailsDDBModel == null) return null;
        return CpvDvuVerificationDetailsDto.builder()
                .leadTrackingNumber(cpvDvuVerificationDetailsDDBModel.getLeadTrackingNumber())
                .actionTrackingId(cpvDvuVerificationDetailsDDBModel.getActionTrackingId())
                .cpvCode(cpvDvuVerificationDetailsDDBModel.getCpvCode())
                .dvuCode(cpvDvuVerificationDetailsDDBModel.getDvuCode())
                .latestStatus(cpvDvuVerificationDetailsDDBModel.getLatestStatus())
                .lastStatusEventRecordedAt(cpvDvuVerificationDetailsDDBModel.getLastStatusEventRecordedAt())
                .createdAt(cpvDvuVerificationDetailsDDBModel.getCreatedAt())
                .lastModifiedAt(cpvDvuVerificationDetailsDDBModel.getLastModifiedAt())
                .version(cpvDvuVerificationDetailsDDBModel.getVersion())
                .cpvDvuVerificationMetadataDto(this.convertMetadataDDBModelToDto(cpvDvuVerificationDetailsDDBModel.getCpvDvuVerificationMetadata()))
                .build();
    }

    private CpvDvuVerificationMetadata convertMetadataDtoToDDBModel(CpvDvuVerificationMetadataDto cpvDvuVerificationMetadataDto) {
        if (cpvDvuVerificationMetadataDto == null) return null;
        if (cpvDvuVerificationMetadataDto instanceof NRCpvDvuVerificationMetadataDto) {
            NRCpvDvuVerificationMetadataDto nrCpvDvuVerificationMetadataDto = (NRCpvDvuVerificationMetadataDto) cpvDvuVerificationMetadataDto;
            return NRCpvDvuVerificationMetadata.builder()
                    .rejectionReason(nrCpvDvuVerificationMetadataDto.getRejectionReason())
                    .sectionsRejected(nrCpvDvuVerificationMetadataDto.getSectionsRejected())
                    .build();
        }
        return null;
    }

    private CpvDvuVerificationMetadataDto convertMetadataDDBModelToDto(CpvDvuVerificationMetadata cpvDvuVerificationMetadata) {
        if (cpvDvuVerificationMetadata == null) return null;
        if (cpvDvuVerificationMetadata instanceof NRCpvDvuVerificationMetadata) {
            NRCpvDvuVerificationMetadata nrCpvDvuVerificationMetadata = (NRCpvDvuVerificationMetadata) cpvDvuVerificationMetadata;
            return NRCpvDvuVerificationMetadataDto.builder()
                    .rejectionReason(nrCpvDvuVerificationMetadata.getRejectionReason())
                    .sectionsRejected(nrCpvDvuVerificationMetadata.getSectionsRejected())
                    .build();
        }
        return null;
    }
}
