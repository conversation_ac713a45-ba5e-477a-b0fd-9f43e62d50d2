package com.kotak.unified.dbservice.transformer.impl;

import com.kotak.unified.common.exception.InvalidRequestException;
import com.kotak.unified.db.AadhaarDetailsResponse;
import com.kotak.unified.db.ApplicationData;
import com.kotak.unified.db.ApplicationDataFilter;
import com.kotak.unified.db.DisbursementDetails;
import com.kotak.unified.db.GetApplicationDataRequest;
import com.kotak.unified.db.PDLApplicationData;
import com.kotak.unified.db.PanDetailsResponse;
import com.kotak.unified.db.assets.PDLBasicDetails;
import com.kotak.unified.db.assets.PDLProductDetailsResponse;
import com.kotak.unified.dbservice.transformer.ApplicationDataTransformer;
import com.kotak.unified.orchestrator.common.dbmodels.ExecutionData;
import com.kotak.unified.orchestrator.common.dbmodels.PDLApplicationMileStone;
import com.kotak.unified.orchestrator.common.dbmodels.PaydayLoanJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.UserStatus;
import com.kotak.unified.orchestrator.common.dbmodels.assets.ApplicantDetails;
import com.kotak.unified.orchestrator.common.dbmodels.assets.CrnDetails;
import com.kotak.unified.orchestrator.common.dbmodels.assets.CrnNameMap;
import com.kotak.unified.orchestrator.common.dbmodels.assets.CustomerName;
import java.time.Instant;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.NonNull;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.modelmapper.TypeToken;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Component;

import static com.kotak.unified.dbservice.utils.Constants.PAYDAY_LOAN;

@Component
@Slf4j
public class PDLApplicationDataTransformer implements ApplicationDataTransformer {

    private final ModelMapper modelMapper;

    public PDLApplicationDataTransformer(@NonNull final ModelMapper modelMapper) {
        this.modelMapper = modelMapper;
    }

    public ApplicationData populateApplicationData(@NonNull
                                                   GetApplicationDataRequest getApplicationDataRequest,
                                                   @NonNull UserStatus userStatus) {
        validateInput(userStatus);
        PaydayLoanJourneyMetadata journeyMetadata =
                (PaydayLoanJourneyMetadata) userStatus.getJourneyMetadata();
        PDLApplicationData applicationData = new PDLApplicationData();
        getApplicationDataRequest.getDataFilters().forEach(
                applicationDataFilter -> updateApplicationData(applicationDataFilter,
                        userStatus, applicationData, journeyMetadata));
        updateApplicationDataWithLastModifiedTime(applicationData,
                userStatus);
        return applicationData;
    }

    @SneakyThrows
    private void updateApplicationData(
            final ApplicationDataFilter applicationDataFilter,
            final UserStatus userStatus,
            final PDLApplicationData applicationData,
            final PaydayLoanJourneyMetadata journeyMetadata) {
        updateApplicationDataWithBankLeadTrackingNumber(applicationData, userStatus);
        switch (applicationDataFilter) {
            case DISBURSEMENT_DETAILS -> updateApplicationDataWithDisbursementDetails(applicationData,
                    journeyMetadata, userStatus.getUserIp());
            case AADHAAR -> updateApplicationDataWithAadhaarDetails(applicationData,
                    journeyMetadata);
            case PAN -> updateApplicationDataWithPanDetails(applicationData,
                    journeyMetadata);
            case ADDRESS -> updateApplicationDataWithAddressDetails(applicationData,
                    journeyMetadata);
            case CRN -> updateApplicationDataWithSelectedCrn(applicationData,
                    journeyMetadata);
            case PRODUCT -> updateApplicationDataWithProductDetails(applicationData,
                    journeyMetadata);
            case EMAIL_ID -> updateApplicationDataWithEmail(applicationData, journeyMetadata, userStatus.getExecutionData());
            case PHONE_NUMBER -> updateApplicationDataWithPhoneNumber(applicationData,
                    userStatus);
            case BASIC_DETAIL -> updateApplicationDataWithBasicDetails(applicationData,
                    journeyMetadata);
            case MILESTONE_HISTORY_LIST -> updateApplicationDataWithMilestoneHistory(applicationData, journeyMetadata);
            case BLOCKED_REASONS -> updateApplicationDataWithBlockedReasons(applicationData,
                    journeyMetadata);
            case LOAN_TYPE -> updateApplicationDataWithLoanType(applicationData, userStatus);
            case RM_CODE -> updateApplicationDataWithRelationShipManagerCode(applicationData, journeyMetadata);
            default -> throw new InvalidRequestException(
                    "Data filter passed is not applicable");
        }
    }

    //dummy function to avoid data filter exception to make compatible with generic pre qualification data filters.
    private void updateApplicationDataWithAddressDetails(final PDLApplicationData applicationData,
                                                                 final PaydayLoanJourneyMetadata journeyMetadata) {
    }


    private void updateApplicationDataWithBankLeadTrackingNumber(final PDLApplicationData applicationData,
                                                                 final UserStatus userStatus) {
        final String bankLeadTrackingNumber =
                Optional.ofNullable(userStatus)
                        .map(UserStatus::getBankLeadTrackingNumber).orElse(null);
        applicationData.setBankLeadTrackingNumber(bankLeadTrackingNumber);
    }

    private void updateApplicationDataWithLoanType(final PDLApplicationData applicationData,
                                                   final UserStatus userStatus) {
        final String loanType =
                Optional.ofNullable(userStatus)
                        .map(UserStatus::getExecutionData)
                        .map(ExecutionData::getLatestCreditOffer).orElse(null);
        applicationData.setLoanType(loanType);
    }

    private void updateApplicationDataWithMilestoneHistory(
            final PDLApplicationData applicationData,
            final PaydayLoanJourneyMetadata journeyMetadata) {
        if (Objects.nonNull(journeyMetadata.getPdlApplicationMileStoneHistory())) {
            applicationData.setPdlApplicationMileStoneHistory(
                    modelMapper.map(journeyMetadata.getPdlApplicationMileStoneHistory(),
                            new TypeToken<List<Pair<PDLApplicationMileStone, Instant>>>() {
                            }.getType()));
        }
    }

    private void updateApplicationDataWithBlockedReasons(
            final PDLApplicationData applicationData,
            final PaydayLoanJourneyMetadata journeyMetadata) {
        if (Objects.nonNull(journeyMetadata.getBlockedReasons())) {
            applicationData.setBlockedReasons(
                    modelMapper.map(
                            journeyMetadata.getBlockedReasons(),
                            new TypeToken<List<String>>() {
                            }.getType()));
        }
    }

    private void updateApplicationDataWithDisbursementDetails(
            final PDLApplicationData applicationData,
            final PaydayLoanJourneyMetadata journeyMetadata,
            final String userIp) {
        applicationData.setDisbursementDetails(
                modelMapper.map(
                        journeyMetadata
                                .getDisbursementDetails(), DisbursementDetails.class));
        DisbursementDetails disbursementDetails = applicationData.getDisbursementDetails();
        ApplicantDetails applicantDetails = journeyMetadata.getApplicantDetails();
        if (applicantDetails != null) {
            disbursementDetails.setKfsId(applicantDetails.getKfsId());
            disbursementDetails.setLanguage(applicantDetails.getLanguage());
        }
        disbursementDetails.setUserIp(userIp);
        applicationData.setDisbursementDetails(disbursementDetails);
    }

    private void updateApplicationDataWithBasicDetails(
            final PDLApplicationData applicationData,
            final PaydayLoanJourneyMetadata journeyMetadata) {
        applicationData.setBasicDetails(
                getBasicDetailsFromJourneyMetadata(journeyMetadata));
    }

    @SneakyThrows
    private PDLBasicDetails getBasicDetailsFromJourneyMetadata(
            final PaydayLoanJourneyMetadata journeyMetadata) {
        return PDLBasicDetails.builder()
                .customerName(getCustomerNameFromJourneyMetadata(journeyMetadata))
                .build();
    }

    private String getCustomerNameFromJourneyMetadata(final PaydayLoanJourneyMetadata journeyMetadata) {
        return Optional.ofNullable(journeyMetadata.getApplicantDetails())
                .map(ApplicantDetails::getCustomerName)
                .map(CustomerName::getFullName)
                .orElseGet(() ->
                        Optional.ofNullable(
                                        journeyMetadata.getApplicantDetails().getCrnDetails())
                                .map(CrnDetails::getSelectedCrn)
                                .map(CrnNameMap::getCrnName)
                                .orElse(null));
    }

    private void updateApplicationDataWithAadhaarDetails(
            final PDLApplicationData applicationData,
            final PaydayLoanJourneyMetadata journeyMetadata) {
        applicationData.setAadhaarDetails(
                modelMapper.map((journeyMetadata.getApplicantDetails()
                                .getAadhaarDetails()),
                        AadhaarDetailsResponse.class));
    }

    private void updateApplicationDataWithPanDetails(
            final PDLApplicationData applicationData,
            final PaydayLoanJourneyMetadata journeyMetadata) {
        applicationData.setPanDetails(
                modelMapper.map(journeyMetadata
                                .getApplicantDetails().getPanDetails(),
                        PanDetailsResponse.class));
    }

    private void updateApplicationDataWithSelectedCrn(
            final PDLApplicationData applicationData,
            final PaydayLoanJourneyMetadata journeyMetadata) {
        applicationData.setCrn(
                Optional.of(journeyMetadata.getApplicantDetails().getCrnDetails())
                        .map(CrnDetails::getSelectedCrn)
                        .map(CrnNameMap::getCrn).orElse(null));
    }

    private void updateApplicationDataWithProductDetails(
            final PDLApplicationData applicationData,
            final PaydayLoanJourneyMetadata journeyMetadata) {
        applicationData.setProductDetails(
                modelMapper.map(journeyMetadata.getProductDetails(),
                        PDLProductDetailsResponse.class));
    }

    private void updateApplicationDataWithEmail(
            final PDLApplicationData applicationData,
            final PaydayLoanJourneyMetadata journeyMetadata, ExecutionData executionData) {
        applicationData.setEmailId(journeyMetadata.getApplicantDetails().getEmailId());

        if(Objects.nonNull(executionData)) {
            applicationData.setEmailVerified(Boolean.TRUE.equals(executionData.getIsEmailAddressVerified()));
        }
    }

    private void updateApplicationDataWithPhoneNumber(
            final PDLApplicationData applicationData,
            final UserStatus userStatus) {
        applicationData.setPhoneNumber(userStatus.getPhoneNumber());

    }

    private void updateApplicationDataWithLastModifiedTime(
            final PDLApplicationData applicationData,
            final UserStatus userStatus) {
        applicationData.setLastModifiedTime(
                userStatus.getLastModifiedTime());
    }

    @SneakyThrows
    private void validateInput(final UserStatus userStatus) {
        if (Objects.isNull(userStatus.getJourneyMetadata()) ||
                !(userStatus.getJourneyMetadata() instanceof
                        PaydayLoanJourneyMetadata)) {
            throw new InvalidRequestException(
                    "Application Id does not belong to PDL Journey " +
                            userStatus.getLeadTrackingNumber());
        }
    }

    private void updateApplicationDataWithRelationShipManagerCode(
        final PDLApplicationData applicationData,
        final PaydayLoanJourneyMetadata journeyMetadata) {
        applicationData.setRmCode(journeyMetadata.getRmCode());
    }

    @Override
    public String getProduct() {
        return PAYDAY_LOAN;
    }
}
