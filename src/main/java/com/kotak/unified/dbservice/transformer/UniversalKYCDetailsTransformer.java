package com.kotak.unified.dbservice.transformer;

import com.kotak.unified.db.model.BiometricsKYCMetadataDTO;
import com.kotak.unified.db.model.CreateUKYCRecordRequest;
import com.kotak.unified.db.model.CreateUKYCRecordResponse;
import com.kotak.unified.db.model.GetUKYCRecordResponse;
import com.kotak.unified.db.model.KYCChannelDTO;
import com.kotak.unified.db.model.KYCMetadataDTO;
import com.kotak.unified.db.model.KYCStatusDTO;
import com.kotak.unified.db.model.LocationDTO;
import com.kotak.unified.db.model.UKYCChannelStatusDTO;
import com.kotak.unified.db.model.UKYCStatusDTO;
import com.kotak.unified.db.model.UpdateUKYCRecordRequest;
import com.kotak.unified.db.model.UpdateUKYCRecordResponse;
import com.kotak.unified.db.model.VideoKYCMetadataDTO;
import com.kotak.unified.dbservice.annotation.Transformer;
import com.kotak.unified.orchestrator.common.dbmodels.ukycStatusModel.BiometricsKYCMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.ukycStatusModel.KYCChannel;
import com.kotak.unified.orchestrator.common.dbmodels.ukycStatusModel.KYCMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.ukycStatusModel.KYCStatus;
import com.kotak.unified.orchestrator.common.dbmodels.ukycStatusModel.Location;
import com.kotak.unified.orchestrator.common.dbmodels.ukycStatusModel.UKYCChannelStatus;
import com.kotak.unified.orchestrator.common.dbmodels.ukycStatusModel.UKYCStatus;
import com.kotak.unified.orchestrator.common.dbmodels.ukycStatusModel.UniversalKYCDetails;
import com.kotak.unified.orchestrator.common.dbmodels.ukycStatusModel.VideoKYCMetadata;
import jakarta.validation.Valid;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

/**
 * Transformer to convert UniversalKYCDetails Db model to DTO/request/response objects and vice versa
 */
@Transformer
public class UniversalKYCDetailsTransformer {

    /**
     * Convert UniversalKYCDetails db model to UKYCRecordResponse.
     * @param universalKYCDetails - UniversalKYCDetails db model
     * @return UKYCRecordResponse
     */
    public GetUKYCRecordResponse convertModelToGetUKYCRecordResponse(UniversalKYCDetails universalKYCDetails) {
        if (universalKYCDetails == null) {
            return null;
        }
        return GetUKYCRecordResponse.builder()
                .aadhaarRefKey(universalKYCDetails.getAadhaarRefKey())
                .crn(universalKYCDetails.getCrn())
                .clientId(universalKYCDetails.getClientId())
                .journeyType(universalKYCDetails.getJourneyType())
                .ukycStatus(convertModelToUKYCStatusDTO(universalKYCDetails.getUkycStatus()))
                .completionTime(universalKYCDetails.getCompletionTime())
                .ukycChannelStatusList(convertModelToUKYCChannelStatusDTOList(universalKYCDetails.getUkycChannelStatusList()))
                .build();
    }

    /**
     * Convert UKYCChannelStatusList db model to DTO
     * @param ukycStatusList List<UKYCChannelStatus> from UniversalKYCDetails db model
     * @return List<UKYCChannelStatusDTO>
     */
    public List<UKYCChannelStatusDTO> convertModelToUKYCChannelStatusDTOList(List<UKYCChannelStatus> ukycStatusList) {
        List<UKYCChannelStatusDTO> ukycStatusDTOList = new ArrayList<>();
        if (ukycStatusList == null) {
            return ukycStatusDTOList;
        }
        for (UKYCChannelStatus ukycChannelStatus : ukycStatusList) {
            ukycStatusDTOList.add(convertModelToUKYCChannelStatusDTO(ukycChannelStatus));
        }
        return ukycStatusDTOList;
    }

    /**
     * Convert UKYCChannelStatus db model to DTO.
     * @param ukycChannelStatus UKYCChannelStatus
     * @return UKYCChannelStatusDTO
     */
    public UKYCChannelStatusDTO convertModelToUKYCChannelStatusDTO(UKYCChannelStatus ukycChannelStatus) {
        if (ukycChannelStatus == null) {
            return null;
        }
        return UKYCChannelStatusDTO.builder()
                .actionTrackingId(ukycChannelStatus.getActionTrackingId())
                .kycStatus(convertModelToKYCStatusDTO(ukycChannelStatus.getKycStatus()))
                .kycChannel(convertModelToKYCChannelDTO(ukycChannelStatus.getKycChannel()))
                .createdAt(ukycChannelStatus.getCreatedAt())
                .lastModifiedAt(ukycChannelStatus.getLastModifiedAt())
                .completionTime(ukycChannelStatus.getCompletionTime())
                .kycMetadata(convertModelToKYCMetadataDTO(ukycChannelStatus.getKycMetadata(), ukycChannelStatus.getKycChannel()))
                .build();
    }


    /**
     * Convert KYCChannel db model to DTO
     * @param kycChannel KYCChannel from UniversalKYCDetails db model.
     * @return KYCChannelDTO
     */
    public KYCChannelDTO convertModelToKYCChannelDTO(KYCChannel kycChannel) {
        if (kycChannel == null) {
            return null;
        }
        return KYCChannelDTO.valueOf(kycChannel.name());
    }

    /**
     * Convert KYCChannelDTO to KYCChannel db model
     * @param kycChannelDTO KYCChannel from request
     * @return KYCChannel
     */
    public KYCChannel convertDTOToKYCChannel(KYCChannelDTO kycChannelDTO) {
        if (kycChannelDTO == null) {
            return null;
        }
        return KYCChannel.valueOf(kycChannelDTO.name());
    }

    /**
     *  Convert KYCStatus db model to DTO
     * @param kycStatus KYCStatus from UniversalKYCDetails db model.
     * @return KYCStatusDTO
     */
    public KYCStatusDTO convertModelToKYCStatusDTO(KYCStatus kycStatus) {
        if (kycStatus == null) {
            return null;
        }
        return KYCStatusDTO.valueOf(kycStatus.name());
    }

    /**
     *  Convert KYCStatusDTO to KYCStatus db model
     * @param kycStatusDTO KYCStatusDTO from request
     * @return KYCStatus
     */
    public KYCStatus convertDTOToKYCStatus(KYCStatusDTO kycStatusDTO) {
        if (kycStatusDTO == null) {
            return null;
        }
        return KYCStatus.valueOf(kycStatusDTO.name());
    }

    /**
     *  Convert KYCStatus db model to DTO
     * @param ukycStatus UKYCStatus from UniversalKYCDetails db model.
     * @return UKYCStatusDTO
     */
    public UKYCStatusDTO convertModelToUKYCStatusDTO(UKYCStatus ukycStatus) {
        if (ukycStatus == null) {
            return null;
        }
        return UKYCStatusDTO.valueOf(ukycStatus.name());
    }

    /**
     *  Convert KYCStatusDTO to KYCStatus db model
     * @param kycStatusDTO KYCStatusDTO from request
     * @return UKYCStatus
     */
    public UKYCStatus convertDTOToUKYCStatus(KYCStatusDTO kycStatusDTO) {
        if (kycStatusDTO == null) {
            return null;
        }
        return UKYCStatus.valueOf(kycStatusDTO.name());
    }



    /**
     * Convert KYCMetadata db model to DTO
     * @param kycMetadata KYCMetadata from UniversalKYCDetails db model.
     * @param kycChannel KYCChannel from UniversalKYCDetails db model.
     * @return KYCMetadataDTO
     */
    public KYCMetadataDTO convertModelToKYCMetadataDTO(KYCMetadata kycMetadata, KYCChannel kycChannel) {
        if (kycMetadata == null || kycChannel == null) {
            return null;
        }

        return switch (kycChannel) {
            case VIDEO_KYC -> convertModelToVideoKYCMetadataDTO((VideoKYCMetadata) kycMetadata);
            default -> convertModelToBiometricsKYCMetadataDTO((BiometricsKYCMetadata) kycMetadata);
        };
    }

    /**
     * Convert KYCMetadataDTO to KYCMetadata db model
     * @param kycMetadataDTO KYCMetadataDTO from request
     * @param kycChannel KYCChannelDTO from request
     * @return KYCMetadata
     */
    public KYCMetadata convertDTOToKYCMetadata(KYCMetadataDTO kycMetadataDTO, KYCChannelDTO kycChannel) {
        if (kycMetadataDTO == null || kycChannel == null) {
            return null;
        }
        return switch (kycChannel) {
            case VIDEO_KYC -> convertDTOToVideoKYCMetadata((VideoKYCMetadataDTO) kycMetadataDTO);
            default -> convertDTOToBiometricsKYCMetadata((BiometricsKYCMetadataDTO) kycMetadataDTO);
        };
    }

    /**
     * Convert VideoKYCMetadata to VideoKYCMetadataDTO.
     * @param videoKYCMetadata videoKYCMetadata from UniversalKYCDetails db model
     * @return VideoKYCMetadataDTO object.
     */
    public VideoKYCMetadataDTO convertModelToVideoKYCMetadataDTO(VideoKYCMetadata videoKYCMetadata) {
        return VideoKYCMetadataDTO.builder()
                .agentId(videoKYCMetadata.getAgentId())
                .auditorId(videoKYCMetadata.getAuditorId())
                .build();
    }

    /**
     * Convert VideoKYCMetadataDTO to VideoKYCMetadata db model
     * @param videoKYCMetadataDTO videoKYCMetadataDTO from request
     * @return VideoKYCMetadata
     */
    public VideoKYCMetadata convertDTOToVideoKYCMetadata(VideoKYCMetadataDTO videoKYCMetadataDTO) {
        if (videoKYCMetadataDTO == null) {
            return null;
        }
        return VideoKYCMetadata.builder()
                .agentId(videoKYCMetadataDTO.getAgentId())
                .auditorId(videoKYCMetadataDTO.getAuditorId())
                .build();
    }

    /**
     * Convert BiometricsKYCMetadata to BiometricsKYCMetadataDTO.
     * @param biometricsKYCMetadata KYCMetadata from UniversalKYCDetails db model
     * @return BiometricsKYCMetadataDTO object.
     */
    public BiometricsKYCMetadataDTO convertModelToBiometricsKYCMetadataDTO(BiometricsKYCMetadata biometricsKYCMetadata) {
        return BiometricsKYCMetadataDTO.builder()
               .agentId(biometricsKYCMetadata.getAgentId())
               .location(convertModelToLocationDTO(biometricsKYCMetadata.getLocation()))
                .build();
    }

    /**
     * Convert BiometricsKYCMetadataDTO to BiometricsKYCMetadata db model
     * @param biometricsKYCMetadataDTO biometricsKYCMetadataDTO from request
     * @return BiometricsKYCMetadata object
     */
    public BiometricsKYCMetadata convertDTOToBiometricsKYCMetadata(BiometricsKYCMetadataDTO biometricsKYCMetadataDTO) {
        if (biometricsKYCMetadataDTO == null) {
            return null;
        }
        return BiometricsKYCMetadata.builder()
                .agentId(biometricsKYCMetadataDTO.getAgentId())
                .location(convertDTOToLocation(biometricsKYCMetadataDTO.getLocation()))
                .build();
    }

    /**
     * Convert Location model to LocationDTO
     * @param location Location model object to be converted to LocationDTO object.
     * @return LocationDTO object.
     */
    public LocationDTO convertModelToLocationDTO(Location location) {
        if (location == null) {
            return null;
        }
        return LocationDTO.builder()
                .latitude(location.getLatitude())
                .longitude(location.getLongitude())
                .build();
    }

    /**
     * Convert LocationDTO to Location model
     * @param locationDTO LocationDTO object to be converted to Location model object.
     * @return Location model object.
     */
    public Location convertDTOToLocation(LocationDTO locationDTO) {
        if (locationDTO == null) {
            return null;
        }
        return Location.builder()
                .latitude(locationDTO.getLatitude())
                .longitude(locationDTO.getLongitude())
                .build();
    }

    /**
     * Convert CreateUKYCRecordRequest to UniversalKYCDetails model.
     * @param createUKYCRecordRequest CreateUKYCRecordRequest
     * @return UniversalKYCDetails object.
     */
    public UniversalKYCDetails convertRequestToUniversalKYCDetails(CreateUKYCRecordRequest createUKYCRecordRequest) {
        UniversalKYCDetails universalKYCDetails = UniversalKYCDetails.builder()
                .crn(createUKYCRecordRequest.getCrn())
                .clientId(createUKYCRecordRequest.getClientId())
                .aadhaarRefKey(createUKYCRecordRequest.getAadhaarRefKey())
                .journeyType(createUKYCRecordRequest.getJourneyType())
                .createdBy(createUKYCRecordRequest.getActor())
                .applicationId(createUKYCRecordRequest.getApplicationId())
                .completionTime(createUKYCRecordRequest.getCompletionTime())
                .ukycChannelStatusList(List.of())
                .build();

        if (createUKYCRecordRequest.getKycChannel() != null) {
            Instant creationTime = Instant.now();
            KYCStatus status = convertDTOToKYCStatus(createUKYCRecordRequest.getKycStatus());
            UKYCChannelStatus ukycChannelStatus = UKYCChannelStatus.builder()
                    .kycMetadata(
                            convertDTOToKYCMetadata(
                                    createUKYCRecordRequest.getKycMetadata(), createUKYCRecordRequest.getKycChannel())
                    )
                    .kycChannel(convertDTOToKYCChannel(createUKYCRecordRequest.getKycChannel()))
                    .kycStatus(status)
                    .actionTrackingId(createUKYCRecordRequest.getActionTrackingId())
                    .createdAt(creationTime)
                    .lastModifiedAt(creationTime)
                    .build();
            UKYCStatus ukycStatus = convertDTOToUKYCStatus(createUKYCRecordRequest.getKycStatus());
            universalKYCDetails.setUkycStatus(ukycStatus);
            universalKYCDetails.setUkycChannelStatusList(List.of(ukycChannelStatus));
        } else {
            if (createUKYCRecordRequest.getKycStatus() == null) {
                universalKYCDetails.setUkycStatus(UKYCStatus.PENDING);
            } else {
                universalKYCDetails.setUkycStatus(convertDTOToUKYCStatus(createUKYCRecordRequest.getKycStatus()));
            }
        }

        return universalKYCDetails;
    }


    public UKYCStatus getAppropriateUKYCStatus(List<UKYCChannelStatus> ukycChannelStatusList) {
        if (ukycChannelStatusList == null || ukycChannelStatusList.isEmpty()) {
            return UKYCStatus.PENDING;
        }

        // return REJECTED if all statuses are REJECTED
        if (ukycChannelStatusList.stream()
                .allMatch(ukycChannelStatus -> ukycChannelStatus.getKycStatus() == KYCStatus.REJECTED)) {
            return UKYCStatus.REJECTED;
        }

        // return COMPLETED if any one of statuses is COMPLETED
        if (ukycChannelStatusList.stream()
                .anyMatch(ukycChannelStatus -> ukycChannelStatus.getKycStatus() == KYCStatus.COMPLETED)) {
            return UKYCStatus.COMPLETED;
        }

        // return PENDING if any one of statuses is PENDING
        if (ukycChannelStatusList.stream()
                .anyMatch(ukycChannelStatus -> ukycChannelStatus.getKycStatus() == KYCStatus.PENDING)) {
            return UKYCStatus.PENDING;
        }

        // return ABORTED if all statuses are ABORTED
        if (ukycChannelStatusList.stream()
                .allMatch(ukycChannelStatus -> ukycChannelStatus.getKycStatus() == KYCStatus.ABORTED)) {
            return UKYCStatus.ABORTED;
        }

        return UKYCStatus.INITIATED;
    }

    public CreateUKYCRecordResponse getCreateUKYCRecordResponse(boolean isSuccess) {
        return CreateUKYCRecordResponse.builder().isCreateRecordSuccessful(isSuccess).build();
    }

    public UpdateUKYCRecordResponse getUpdateUKYCRecordResponse(boolean isSuccess) {
        return UpdateUKYCRecordResponse.builder().isUpdateRecordSuccessful(isSuccess).build();
    }

    public void updateUniversalKYCDetails(UniversalKYCDetails universalKYCDetails, UpdateUKYCRecordRequest updateUKYCRecordRequest) {

        universalKYCDetails.setLastModifiedBy(updateUKYCRecordRequest.getActor());
        universalKYCDetails.setCompletionTime(updateUKYCRecordRequest.getCompletionTime());

        if (updateUKYCRecordRequest.getUkycStatus() != null) {
            updateUkycStatus(universalKYCDetails, updateUKYCRecordRequest.getUkycStatus());
            return;
        }

        if (updateUKYCRecordRequest.getCrn() != null) {
            universalKYCDetails.setCrn(updateUKYCRecordRequest.getCrn());
        }
        KYCStatus kycStatus = convertDTOToKYCStatus(updateUKYCRecordRequest.getKycStatus());
        KYCChannel kycChannel = convertDTOToKYCChannel(updateUKYCRecordRequest.getKycChannel());
        KYCMetadata kycMetadata = convertDTOToKYCMetadata(
                updateUKYCRecordRequest.getKycMetadata(), updateUKYCRecordRequest.getKycChannel()
        );

        UKYCChannelStatus ukycChannelStatus = null;
        if (universalKYCDetails.getUkycChannelStatusList() != null) {
            // find ukycChannelStatus from ukycChannelStatusList that has matching actionTrackingId from request
            ukycChannelStatus = universalKYCDetails.getUkycChannelStatusList().stream()
                    .filter(ukycChannelStatus1 -> ukycChannelStatus1.getActionTrackingId().equals(updateUKYCRecordRequest.getActionTrackingId()))
                    .findFirst().orElse(null);
        }

        if (ukycChannelStatus != null) {

            if (ukycChannelStatus.getKycStatus() == KYCStatus.COMPLETED &&
                    updateUKYCRecordRequest.getKycStatus() != KYCStatusDTO.COMPLETED) {
                // Do not allow if update request tries to modify the kyc status from completed to other statuses,
                throw new IllegalArgumentException(
                        "KycStatus can not be modified from COMPLETED status to any other status");
            }


            if (kycMetadata != null) {
                ukycChannelStatus.setKycMetadata(kycMetadata);
            }
            ukycChannelStatus.setKycStatus(kycStatus);
            ukycChannelStatus.setLastModifiedAt(Instant.now());
            ukycChannelStatus.setCompletionTime(updateUKYCRecordRequest.getCompletionTime());

        } else {
            ukycChannelStatus = UKYCChannelStatus.builder()
                    .createdAt(Instant.now())
                    .lastModifiedAt(Instant.now())
                    .actionTrackingId(updateUKYCRecordRequest.getActionTrackingId())
                    .kycChannel(kycChannel)
                    .kycStatus(kycStatus)
                    .kycMetadata(kycMetadata)
                    .completionTime(updateUKYCRecordRequest.getCompletionTime())
                    .build();
            if (universalKYCDetails.getUkycChannelStatusList() == null) {
                universalKYCDetails.setUkycChannelStatusList(new ArrayList<>());
            }
            universalKYCDetails.getUkycChannelStatusList().add(ukycChannelStatus);
        }

        // Update over all ukyc status based on multiple statuses
        universalKYCDetails.setUkycStatus(
                getAppropriateUKYCStatus(universalKYCDetails.getUkycChannelStatusList())
        );
    }

    private void updateUkycStatus(UniversalKYCDetails universalKYCDetails, @Valid UKYCStatusDTO kycStatus) {
        if (kycStatus.equals(UKYCStatusDTO.ABORTED)) {
            updateKYCChannelListWithLastModifiedAt(universalKYCDetails.getUkycChannelStatusList());
            updateKYCChannelListWithKycStatus(universalKYCDetails.getUkycChannelStatusList());
            universalKYCDetails.setUkycStatus(UKYCStatus.ABORTED);
        } else if (kycStatus.equals(UKYCStatusDTO.INITIATED)) {
            universalKYCDetails.setUkycStatus(UKYCStatus.INITIATED);
        } else if (kycStatus.equals(UKYCStatusDTO.PENDING)) {
            universalKYCDetails.setUkycStatus(UKYCStatus.PENDING);
        }
    }

    private void updateKYCChannelListWithLastModifiedAt(List<UKYCChannelStatus> ukycChannelStatusList) {
        for (UKYCChannelStatus ukycChannel : ukycChannelStatusList) {
            ukycChannel.setLastModifiedAt(Instant.now());
        }
    }

    private void updateKYCChannelListWithKycStatus(List<UKYCChannelStatus> ukycChannelStatusList) {
        for (UKYCChannelStatus ukycChannel : ukycChannelStatusList) {
            ukycChannel.setKycStatus(KYCStatus.ABORTED);
        }
    }

}
