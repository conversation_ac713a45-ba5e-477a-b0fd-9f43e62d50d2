package com.kotak.unified.dbservice.transformer.impl;

import com.kotak.unified.common.exception.InvalidRequestException;
import com.kotak.unified.db.AddressProofResponse;
import com.kotak.unified.db.AddressResponse;
import com.kotak.unified.db.ApplicationData;
import com.kotak.unified.db.ApplicationDataFilter;
import com.kotak.unified.db.BranchCodeDetailsResponse;
import com.kotak.unified.db.GetApplicationDataRequest;
import com.kotak.unified.db.PanDetailsResponse;
import com.kotak.unified.db.PersonalDetailsResponse;
import com.kotak.unified.db.TagResponse;
import com.kotak.unified.db.nr.NRAccountTypeResponse;
import com.kotak.unified.db.nr.NRCPVAttemptDetailsResponse;
import com.kotak.unified.db.nr.NROCIDetailsResponse;
import com.kotak.unified.db.nr.NRPassportDetails;
import com.kotak.unified.db.nr.NRProductSpecifications;
import com.kotak.unified.db.nr.NRSavingsAccountApplicationData;
import com.kotak.unified.db.nr.NRTaxDetails;
import com.kotak.unified.db.nr.NRUploadedDocumentLinksResponse;
import com.kotak.unified.db.nr.NRVisaDetails;
import com.kotak.unified.dbservice.exceptions.EntityNotFoundException;
import com.kotak.unified.dbservice.transformer.ApplicationDataTransformer;
import com.kotak.unified.orchestrator.common.dbmodels.AddressProof;
import com.kotak.unified.orchestrator.common.dbmodels.CPVAttemptDetails;
import com.kotak.unified.orchestrator.common.dbmodels.JourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.NRSavingsAccountJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.PanDetails;
import com.kotak.unified.orchestrator.common.dbmodels.PersonalDetails;
import com.kotak.unified.orchestrator.common.dbmodels.Tag;
import com.kotak.unified.orchestrator.common.dbmodels.UserStatus;
import com.kotak.unified.orchestrator.common.dbmodels.nr.NRUploadedDocumentLinks;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

import java.util.stream.Collectors;

import static com.kotak.unified.dbservice.utils.Constants.NR_SAVINGS_ACCOUNT;


@Slf4j
@Component
@RequiredArgsConstructor
public class NRSavingsAccountApplicationDataTransformer implements ApplicationDataTransformer {
    private final ModelMapper modelMapper;

    @Override
    public ApplicationData populateApplicationData(GetApplicationDataRequest getApplicationDataRequest, UserStatus userStatus) throws EntityNotFoundException, InvalidRequestException {
        JourneyMetadata journeyMetadata = userStatus.getJourneyMetadata();
        if (journeyMetadata == null) {
            log.warn("No application data found for lead id : "
                    + userStatus.getLeadTrackingNumber());
            throw new EntityNotFoundException("No application data found for lead id : "
                    + userStatus.getLeadTrackingNumber());
        }
        this.validateInput(userStatus);

        NRSavingsAccountApplicationData applicationData = new NRSavingsAccountApplicationData();
        for (ApplicationDataFilter applicationDataFilter : getApplicationDataRequest.getDataFilters()) {
            this.setApplicationData(userStatus, applicationDataFilter, applicationData, (NRSavingsAccountJourneyMetadata) journeyMetadata);
        }

        return applicationData;
    }

    private void setApplicationData(UserStatus userStatus,
                                    ApplicationDataFilter applicationDataFilter,
                                    NRSavingsAccountApplicationData applicationData,
                                    NRSavingsAccountJourneyMetadata journeyMetadata) throws InvalidRequestException {
        switch (applicationDataFilter) {
            case PHONE_NUMBER -> applicationData.setPhoneNumber(userStatus.getPhoneNumber());
            case ISD_CODE -> applicationData.setIsdCode(journeyMetadata.getIsdCode());
            case EMAIL_ID -> applicationData.setEmailAddress(journeyMetadata.getEmailAddress());
            case COUNTRY_OF_RESIDENCE -> applicationData.setCountryOfResidence(getTagResponse(journeyMetadata.getCountryOfResidence()));
            case VISA_DETAILS -> {
                if (journeyMetadata.getVisaDetails() != null) {
                    NRVisaDetails nrVisaDetails = NRVisaDetails.builder()
                            .visaIssuanceCountry(this.getTagResponse(journeyMetadata.getVisaDetails().getVisaIssuanceCountry()))
                            .visaType(journeyMetadata.getVisaDetails().getVisaType())
                            .visaExpiryDate(journeyMetadata.getVisaDetails().getExpiryDate())
                            .visaIssueDate(journeyMetadata.getVisaDetails().getIssueDate())
                            .build();
                    applicationData.setVisaDetails(nrVisaDetails);
                }
            }
            case PASSPORT_DETAILS -> {
                if (journeyMetadata.getPassportDetails() != null) {
                    NRPassportDetails nrPassportDetails = NRPassportDetails.builder()
                            .passportNumber(journeyMetadata.getPassportDetails().getPassportNumber())
                            .passportName(journeyMetadata.getPassportDetails().getGivenName())
                            .passportSurname(journeyMetadata.getPassportDetails().getSurname())
                            .passportDob(journeyMetadata.getPassportDetails().getDateOfBirth())
                            .passportIssueDate(journeyMetadata.getPassportDetails().getIssueDate())
                            .passportExpiryDate(journeyMetadata.getPassportDetails().getExpiryDate())
                            .nationality(this.getTagResponse(journeyMetadata.getPassportDetails().getNationality()))
                            .gender(journeyMetadata.getPassportDetails().getGender())
                            .build();
                    applicationData.setPassportDetails(nrPassportDetails);
                }
            }
            case PRODUCT_SPECIFICATIONS -> {
                if (journeyMetadata.getProductSpecifications() != null) {
                    NRProductSpecifications nrProductSpecifications = NRProductSpecifications.builder()
                            .productName(journeyMetadata.getProductSpecifications().getProductName())
                            .productVariant(journeyMetadata.getProductSpecifications().getProductVariant())
                            .nroSchemeCode(journeyMetadata.getProductSpecifications().getNroSchemeCode())
                            .nreSchemeCode(journeyMetadata.getProductSpecifications().getNreSchemeCode())
                            .classification(journeyMetadata.getProductSpecifications().getClassification())
                            .finacleClassification(journeyMetadata.getProductSpecifications().getFinacleClassification())
                            .lcCode(journeyMetadata.getProductSpecifications().getLcCode())
                            .lgCode(journeyMetadata.getProductSpecifications().getLgCode())
                            .lcName(journeyMetadata.getProductSpecifications().getLcName())
                            .rmCode(journeyMetadata.getProductSpecifications().getRmCode())
                            .promoCode(journeyMetadata.getProductSpecifications().getPromoCode())
                            .lineOfBusiness(journeyMetadata.getProductSpecifications().getLineOfBusiness())
                            .sourceCode(journeyMetadata.getProductSpecifications().getSourceCode())
                            .channel(journeyMetadata.getProductSpecifications().getChannel())
                            .build();

                    applicationData.setNrProductSpecifications(nrProductSpecifications);
                }
            }
            case PERSONAL_DETAILS -> {
                if (journeyMetadata.getPersonalDetails() != null) {
                    PersonalDetails personalDetails = journeyMetadata.getPersonalDetails();
                    PersonalDetailsResponse personalDetailsResponse = PersonalDetailsResponse.builder()
                            .motherName(personalDetails.getMotherName())
                            .fatherName(personalDetails.getFatherName())
                            .annualSalary(personalDetails.getAnnualSalary())
                            .countryOfBirth(getTagResponse(personalDetails.getCountryOfBirth()))
                            .build();

                    if (personalDetails.getMaritalStatus() != null) {
                        personalDetailsResponse.setMaritalStatus(TagResponse.builder()
                                .code(personalDetails.getMaritalStatus().getCode())
                                .value(personalDetails.getMaritalStatus().getValue())
                                .build());
                    }

                    if (personalDetails.getOccupation() != null) {
                        personalDetailsResponse.setOccupation(TagResponse.builder()
                                .code(personalDetails.getOccupation().getCode())
                                .value(personalDetails.getOccupation().getValue())
                                .build());
                    }

                    if (personalDetails.getSourceOfIncome() != null) {
                        personalDetailsResponse.setSourceOfIncome(TagResponse.builder()
                                .code(personalDetails.getSourceOfIncome().getCode())
                                .value(personalDetails.getSourceOfIncome().getValue())
                                .build());
                    }

                    if (personalDetails.getAnnualIncome() != null) {
                        personalDetailsResponse.setAnnualIncome(TagResponse.builder()
                                .code(personalDetails.getAnnualIncome().getCode())
                                .value(personalDetails.getAnnualIncome().getValue())
                                .build());
                    }

                    applicationData.setPersonalDetailsResponse(personalDetailsResponse);
                }
            }

            case SECOND_ADDRESS -> {
                if (journeyMetadata.getSecondAddress() != null) {
                    applicationData.setSecondAddress(this.modelMapper.map(journeyMetadata.getSecondAddress(), AddressResponse.class));
                }
            }
            case OVERSEAS_ADDRESS -> {
                if (journeyMetadata.getOverseasAddress() != null) {
                    applicationData.setOverseasAddress(this.modelMapper.map(journeyMetadata.getOverseasAddress(), AddressResponse.class));
                }
            }
            case TAX_DETAILS -> {
                if (journeyMetadata.getNrTaxDetails() != null && journeyMetadata.getNrTaxDetails().getPanDetails() != null) {
                    PanDetailsResponse panDetailsResponse = PanDetailsResponse.builder()
                            .number(journeyMetadata.getNrTaxDetails().getPanDetails().getNumber())
                            .statusFlag(journeyMetadata.getNrTaxDetails().getPanDetails().getStatusFlag())
                            .validFlag(journeyMetadata.getNrTaxDetails().getPanDetails().getValidFlag())
                            .lastName(journeyMetadata.getNrTaxDetails().getPanDetails().getLastName())
                            .firstName(journeyMetadata.getNrTaxDetails().getPanDetails().getFirstName())
                            .middleName(journeyMetadata.getNrTaxDetails().getPanDetails().getMiddleName())
                            .title(journeyMetadata.getNrTaxDetails().getPanDetails().getTitle())
                            .updateDate(journeyMetadata.getNrTaxDetails().getPanDetails().getUpdateDate())
                            .displayName(journeyMetadata.getNrTaxDetails().getPanDetails().getDisplayName())
                            .aadhaarSeed(journeyMetadata.getNrTaxDetails().getPanDetails().getAadhaarSeed())
                            .dateOfBirth(journeyMetadata.getNrTaxDetails().getPanDetails().getDateOfBirth())
                            .build();
                    NRTaxDetails nrTaxDetails = NRTaxDetails.builder()
                            .panDetailsResponse(panDetailsResponse)
                            .build();
                    applicationData.setNrTaxDetails(nrTaxDetails);
                }
            }

            case BRANCH_CODE_DETAILS -> {
                if (journeyMetadata.getPreferredBranchDetails() != null) {
                    applicationData.setBranchCodeDetailsResponse(BranchCodeDetailsResponse.builder().branchCode(journeyMetadata.getPreferredBranchDetails().getBranchCode()).build());
                }
            }

            case DOCUMENT_AVAILABLE_TYPE -> {
                if (journeyMetadata.getDocumentAvailableType() != null) {
                    applicationData.setDocumentAvailableType(journeyMetadata.getDocumentAvailableType().name());

                }
            }

            case IS_OVERSEAS_ADDRESS_PROVIDED ->
                    applicationData.setIsOverseasAddressProvided(journeyMetadata.getIsOverseasAddressProvided());

            case IS_SECOND_ADDRESS_PROVIDED ->
                    applicationData.setIsSecondAddressProvided(journeyMetadata.getIsSecondAddressProvided());

            case ADDRESS_TYPE_FOR_COMMUNICATION -> {
                if (journeyMetadata.getAddressTypeForCommunication() != null) {
                    applicationData.setAddressTypeForCommunication(journeyMetadata.getAddressTypeForCommunication());
                }
            }
            case SECOND_ADDRESS_PROOF -> {
                if (journeyMetadata.getSecondAddressProof() != null) {
                    AddressProofResponse addressProofResponse = getAddressProofResponse(journeyMetadata.getSecondAddressProof());
                    applicationData.setSecondAddressProof(addressProofResponse);
                }
            }

            case OVERSEAS_ADDRESS_PROOF -> {
                if (journeyMetadata.getOverseasAddressProof() != null) {
                    AddressProofResponse addressProofResponse = getAddressProofResponse(journeyMetadata.getOverseasAddressProof());
                    applicationData.setOverseasAddressProof(addressProofResponse);
                }
            }

            case IS_PAN_AVAILABLE -> {
                if (journeyMetadata.getNrTaxDetails() != null) {
                    applicationData.setIsPanAvailable(journeyMetadata.getNrTaxDetails().getHasPanNumber());
                }
            }

            case IS_NOMINEE_SKIPPED -> applicationData.setIsNomineeSkipped(journeyMetadata.getIsNomineeSkipped());

            case NR_ACCOUNT_TYPES -> {
                if (journeyMetadata.getAccountTypes() != null) {
                    applicationData.setAccountTypes(journeyMetadata.getAccountTypes().stream()
                            .map(nrAccountType -> NRAccountTypeResponse.valueOf(nrAccountType.name()))
                            .collect(Collectors.toList()));
                }
            }

            case CPV_ATTEMPT_ACTION_TRACKING_ID -> applicationData.setActionTrackingId(journeyMetadata.getLatestCpvAttemptActionTrackingId());

            case OCI_DETAILS -> {
                if (journeyMetadata.getOciDetails() != null) {
                    NROCIDetailsResponse nrOciDetailsResponse = NROCIDetailsResponse.builder()
                            .ociNumber(journeyMetadata.getOciDetails().getOciNumber())
                            .dateOfIssue(journeyMetadata.getOciDetails().getDateOfIssue())
                            .placeOfIssue(journeyMetadata.getOciDetails().getPlaceOfIssue())
                            .build();
                    applicationData.setNrOciDetailsResponse(nrOciDetailsResponse);
                }
            }

            case LATEST_CPV_ATTEMPT_DETAILS -> {
                CPVAttemptDetails cpvAttemptDetails = journeyMetadata.getLatestCpvAttemptDetails();
                if (cpvAttemptDetails != null) {
                    NRCPVAttemptDetailsResponse nrcpvAttemptDetailsResponse = NRCPVAttemptDetailsResponse.builder()
                                .cpvDVUStatus(cpvAttemptDetails.getCpvDVUStatus())
                                .actionTrackingId(cpvAttemptDetails.getActionTrackingId())
                                .rejectionReason(cpvAttemptDetails.getRejectionReason())
                                .sectionsRejected(cpvAttemptDetails.getSectionsRejected()).build();
                    applicationData.setLatestCpvAttemptDetails(nrcpvAttemptDetailsResponse);
                }
            }

            case UPLOADED_DOCUMENT_LINKS -> {
                NRUploadedDocumentLinks nrUploadedDocumentLinks = journeyMetadata.getUploadedDocumentLinks();
                if (nrUploadedDocumentLinks != null) {
                    NRUploadedDocumentLinksResponse nrUploadedDocumentLinksResponse = NRUploadedDocumentLinksResponse.builder()
                            .passportImages(nrUploadedDocumentLinks.getPassportImages())
                            .visaImages(nrUploadedDocumentLinks.getVisaImages())
                            .ociImages(nrUploadedDocumentLinks.getOciImages())
                            .overseasAddressImages(nrUploadedDocumentLinks.getOverseasAddressImages())
                            .secondAddressImages(nrUploadedDocumentLinks.getSecondAddressImages())
                            .signatureImage(nrUploadedDocumentLinks.getSignatureImage())
                            .customerImage(nrUploadedDocumentLinks.getCustomerImage())
                            .build();
                    applicationData.setUploadedDocumentLinks(nrUploadedDocumentLinksResponse);
                }
            }

            default ->
                    throw new InvalidRequestException("Data filter passed is not applicable : " + applicationDataFilter);
        }
    }

    private void validateInput(UserStatus userStatus) throws InvalidRequestException {
        if (!(userStatus.getJourneyMetadata() instanceof
                NRSavingsAccountJourneyMetadata)) {
            throw new InvalidRequestException(
                    "Application Id does not belong to NR Savings account Journey for lead : " +
                            userStatus.getLeadTrackingNumber());
        }
    }

    private TagResponse getTagResponse(Tag tag) {
        if (tag == null) return null;
        return TagResponse.builder().code(tag.getCode())
                .value(tag.getValue())
                .build();
    }

    @Override
    public String getProduct() {
        return NR_SAVINGS_ACCOUNT;
    }

    private AddressProofResponse getAddressProofResponse(AddressProof addressProof) {
        return AddressProofResponse.builder()
                .documentCode(addressProof.getDocumentCode())
                .documentName(addressProof.getDocumentName())
                .documentNumber(addressProof.getDocumentNumber())
                .documentType(addressProof.getDocumentType())
                .issueDate(addressProof.getIssueDate())
                .expiryDate(addressProof.getExpiryDate())
                .build();
    }
}

