package com.kotak.unified.dbservice.transformer.impl;

import com.kotak.unified.common.exception.InvalidRequestException;
import com.kotak.unified.db.AadhaarDetailsResponse;
import com.kotak.unified.db.AddressResponse;
import com.kotak.unified.db.ApplicationData;
import com.kotak.unified.db.ApplicationDataFilter;
import com.kotak.unified.db.BankDetailsResponse;
import com.kotak.unified.db.GetApplicationDataRequest;
import com.kotak.unified.db.NomineeDetailsResponse;
import com.kotak.unified.db.PanDetailsResponse;
import com.kotak.unified.db.DeclarationResponse;
import com.kotak.unified.db.AddOnsResponse;
import com.kotak.unified.db.ca.BasicDetailResponse;
import com.kotak.unified.db.ca.CAApplicationData;
import com.kotak.unified.db.ca.CAOccupationDetailsResponse;
import com.kotak.unified.db.ca.CAProductSpecificationsResponse;
import com.kotak.unified.db.ca.DerivedEntityProof2DetailsResponse;
import com.kotak.unified.db.ca.EtbDetailsResponse;
import com.kotak.unified.db.ca.FSSAIDetailsResponse;
import com.kotak.unified.db.ca.GstDetailsResponse;
import com.kotak.unified.db.ca.IECDetailsResponse;
import com.kotak.unified.db.ca.OperatingAddressResponse;
import com.kotak.unified.db.ca.PrivyDetailsResponse;
import com.kotak.unified.db.ca.UdyamDetailsResponse;
import com.kotak.unified.db.ca.EntityProofDetailsResponse;
import com.kotak.unified.dbservice.transformer.ApplicationDataTransformer;
import com.kotak.unified.orchestrator.common.dbmodels.CurrentAccountJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.Declaration;
import com.kotak.unified.orchestrator.common.dbmodels.UserStatus;
import com.kotak.unified.orchestrator.common.dbmodels.AddOns;
import com.kotak.unified.orchestrator.common.dbmodels.ca.EntityProofDetails;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.List;
import java.util.ArrayList;

import static com.kotak.unified.dbservice.utils.Constants.CURRENT_ACCOUNT;
import static com.kotak.unified.dbservice.utils.Constants.FSSAI_LFDCA_CODE;

@Component
@Slf4j
@RequiredArgsConstructor
public class CAApplicationDataTransformer implements ApplicationDataTransformer {

    // TODO: Use custom transformers for converting object in place of ModelMapper
    private final ModelMapper modelMapper;
    private static final String IEC = "IEC";
    private static final String UDYAM = "MSME_Udyam";
    @Override
    public ApplicationData populateApplicationData(GetApplicationDataRequest applicationDataRequest, UserStatus userStatus) throws InvalidRequestException {
        this.validateInput(userStatus);
        CurrentAccountJourneyMetadata journeyMetadata = (CurrentAccountJourneyMetadata) userStatus.getJourneyMetadata();
        CAApplicationData applicationData = new CAApplicationData();
        for (ApplicationDataFilter dataFilter : applicationDataRequest.getDataFilters()) {
            this.setApplicationData(dataFilter, userStatus, journeyMetadata, applicationData);
        }
        return applicationData;
    }

    @SneakyThrows
    private void setApplicationData(ApplicationDataFilter dataFilter, UserStatus userStatus,
                                    CurrentAccountJourneyMetadata journeyMetadata,
                                    CAApplicationData applicationData) {
        switch (dataFilter) {
            case AADHAAR -> {
                if (Objects.isNull(journeyMetadata.getAadhaarDetails())) {
                    log.warn("Aadhar details are null for lead id : {}",
                        userStatus.getLeadTrackingNumber());
                } else {
                    applicationData.setAadhaarDetails(
                        modelMapper.map(journeyMetadata.getAadhaarDetails(),
                            AadhaarDetailsResponse.class));
                }
            }
            case EMAIL_ID -> applicationData.setEmailAddress(journeyMetadata.getEmailAddress());
            case PAN -> {
                if (Objects.isNull(journeyMetadata.getPanDetails())) {
                    log.warn("Pan Details are null for lead id : {}",
                        userStatus.getLeadTrackingNumber());
                } else {
                    applicationData.setPanDetails(
                        modelMapper.map(journeyMetadata.getPanDetails(), PanDetailsResponse.class));
                }
            }
            case CRN -> {
                applicationData.setCrn(journeyMetadata.getCrn());
                applicationData.setEntityCrn(journeyMetadata.getEntityCrn());
            }
            case GST_DETAILS -> {
                if (Objects.isNull(journeyMetadata.getGstDetails())) {
                    log.warn("Gst Details are null for lead id : {}",
                        userStatus.getLeadTrackingNumber());
                } else {
                    applicationData.setGstDetails(
                        modelMapper.map(journeyMetadata.getGstDetails(), GstDetailsResponse.class));
                }
                applicationData.setIsGstAddressSameAsOperatingAddress(journeyMetadata.getIsGstAddressSameAsOperatingAddress());
            }
            case SECONDARY_ENTITY_PROOF_DETAILS -> {
                applicationData.setOperatingAddressType(journeyMetadata.getOperatingAddressType());
                applicationData.setCertificateNumber(journeyMetadata.getCertificateNumber());

                if (Objects.isNull(journeyMetadata.getDerivedEntityProof2Details())) {
                    log.warn("Derived Entity Proof 2 details are null for lead id : {}",
                            userStatus.getLeadTrackingNumber());
                } else {
                    applicationData.setDerivedEntityProof2Details(
                            modelMapper.map(journeyMetadata.getDerivedEntityProof2Details(), DerivedEntityProof2DetailsResponse.class));
                }

                if(IEC.equals(journeyMetadata.getOperatingAddressType())){
                    if (Objects.isNull(journeyMetadata.getIecDetails())) {
                        log.warn("IEC Details are null for lead id : {}",
                                userStatus.getLeadTrackingNumber());
                    } else {
                        applicationData.setIecDetails(
                                modelMapper.map(journeyMetadata.getIecDetails(), IECDetailsResponse.class));
                    }
                }
                if(FSSAI_LFDCA_CODE.equals(journeyMetadata.getOperatingAddressType())){
                    if (Objects.isNull(journeyMetadata.getFssaiDetails())) {
                        log.warn("FSSAI_LFDCA_CODE Details are null for lead id : {}",
                                userStatus.getLeadTrackingNumber());
                    } else {
                        applicationData.setFssaiDetails(
                                modelMapper.map(journeyMetadata.getFssaiDetails(), FSSAIDetailsResponse.class));
                    }
                }
                if("UDYAM".equals(journeyMetadata.getOperatingAddressType())){
                    if (Objects.isNull(journeyMetadata.getUdyamDetails())) {
                        log.warn("Udyam Details are null for lead id : {}",
                                userStatus.getLeadTrackingNumber());
                    } else {
                        applicationData.setUdyamDetails(
                                modelMapper.map(journeyMetadata.getUdyamDetails(), UdyamDetailsResponse.class));
                    }
                }
            }
            case BANK_DETAILS ->{
                if (Objects.isNull(journeyMetadata.getBankDetails())) {
                    log.warn("Bank Details are null for lead id : {}",
                        userStatus.getLeadTrackingNumber());
                } else {
                    applicationData.setBankDetails(modelMapper.map(journeyMetadata.getBankDetails(),
                        BankDetailsResponse.class));
                }
                applicationData.setBankLeadTrackingNumber(userStatus.getBankLeadTrackingNumber());
            }
            case ETB_DETAILS ->{
                if (Objects.isNull(journeyMetadata.getEtbDetails())) {
                    log.warn("ETB Details are null for lead id : {}",
                            userStatus.getLeadTrackingNumber());
                } else {
                    applicationData.setEtbDetailsResponse(modelMapper.map(journeyMetadata.getEtbDetails(),
                            EtbDetailsResponse.class));
                }
            }

            case ACCOUNT_DETAILS -> {
                applicationData.setAccountNumber(journeyMetadata.getAccountNumber());
                applicationData.setPreferredAccountNumber(journeyMetadata.getPreferredAccountNumber());
                applicationData.setIsActivMoneyOpted(journeyMetadata.getIsActivMoneyOpted());
                applicationData.setAccountOpeningDate(journeyMetadata.getAccountOpeningDate());
                applicationData.setIsInstakitOpted(journeyMetadata.getIsInstakitOpted());
            }
            case PHONE_NUMBER -> applicationData.setPhoneNumber(userStatus.getPhoneNumber());
            case PRODUCT_SPECIFICATIONS -> {
                if (Objects.isNull(journeyMetadata.getProductSpecifications())) {
                    log.warn("Product Specifications are null for lead id : {}",
                        userStatus.getLeadTrackingNumber());
                } else {
                    applicationData.setCaProductSpecificationsResponse(
                        modelMapper.map(journeyMetadata.getProductSpecifications(),
                            CAProductSpecificationsResponse.class));
                }
            }
            case BASIC_DETAIL -> {
                if (Objects.isNull(journeyMetadata.getBasicDetails())) {
                    log.warn("Basic Details are null for lead id : {}",
                        userStatus.getLeadTrackingNumber());
                } else {
                    applicationData.setBasicDetails(
                        modelMapper.map(journeyMetadata.getBasicDetails(),
                            BasicDetailResponse.class));
                }
            }
            case PRIVY_DETAILS -> {
                if (Objects.isNull(journeyMetadata.getPrivyDetails())) {
                    log.warn("Privy Details are null for lead id : {}",
                            userStatus.getLeadTrackingNumber());
                } else {
                    applicationData.setPrivyDetails(
                            modelMapper.map(journeyMetadata.getPrivyDetails(),
                                    PrivyDetailsResponse.class));
                }
            }
            case NOMINEE_DETAILS -> {
                if (Objects.isNull(journeyMetadata.getNomineeDetails())) {
                    log.warn("Nominee Details are null for lead id : {}",
                        userStatus.getLeadTrackingNumber());
                } else {
                    applicationData.setNomineeDetails(
                        modelMapper.map(journeyMetadata.getNomineeDetails(),
                            NomineeDetailsResponse.class));
                }
            }
            case ADDRESS -> {
                applicationData.setIsCommunicationAddressSameAsAadhaarAddress(journeyMetadata.getIsCommunicationAddressSameAsAadhaarAddress());
                applicationData.setIsOperatingAddressSameAsAadhaarAddress(journeyMetadata.isOperatingAddressSameAsAadhaarAddress());
                if (Objects.isNull(journeyMetadata.getCommunicationAddress())) {
                    log.warn("Communication Address is null for lead id : {}",
                        userStatus.getLeadTrackingNumber());
                } else {
                    applicationData.setCommunicationAddress(
                        modelMapper.map(journeyMetadata.getCommunicationAddress(),
                            AddressResponse.class));
                }
                if (Objects.isNull(journeyMetadata.getOperatingAddress())) {
                    log.warn("Operating Address is null for lead id : {}",
                        userStatus.getLeadTrackingNumber());
                } else {
                    applicationData.setOperatingAddress(
                        modelMapper.map(journeyMetadata.getOperatingAddress(),
                            OperatingAddressResponse.class));
                }
            }
            case OCCUPATION_DETAILS -> {
                if (Objects.isNull(journeyMetadata.getOccupationDetails())) {
                    log.warn("Occupation Details are null for lead id : {}",
                        userStatus.getLeadTrackingNumber());
                } else {
                    applicationData.setOccupationDetails(
                        modelMapper.map(journeyMetadata.getOccupationDetails(),
                            CAOccupationDetailsResponse.class));
                }
            }
            case FUNDING_DETAILS -> {
                applicationData.setFundingAmount(journeyMetadata.getFundingAmount());
            }
            case DECLARATIONS -> {
                if (Objects.isNull(journeyMetadata.getDeclarations())) {
                    log.warn("Declarations are null for lead id : {}",
                            userStatus.getLeadTrackingNumber());
                } else {
                    Map<String, DeclarationResponse> declarationsResponseMap = new HashMap<>();
                    for (Map.Entry<String, Declaration> entry : journeyMetadata.getDeclarations().entrySet()){
                        DeclarationResponse declarationResponse = modelMapper.map(entry.getValue(), DeclarationResponse.class);
                        declarationsResponseMap.put(entry.getKey(), declarationResponse);
                    }
                    applicationData.setDeclarationsResponse(declarationsResponseMap);
                }
            }
            case USER_IP ->  {
                if (Objects.isNull(userStatus.getUserIp())) {
                    log.warn("UserIp is null for lead id : {}",
                            userStatus.getLeadTrackingNumber());
                } else {
                    applicationData.setUserIp(userStatus.getUserIp());
                }
            }
            case ADD_ONS -> {
                if (Objects.isNull(journeyMetadata.getAddOns())) {
                    log.info("AddOns are null for lead id : {}",
                            userStatus.getLeadTrackingNumber());
                } else {
                    List<AddOnsResponse> addOns = new ArrayList<>();
                    for (AddOns entry : journeyMetadata.getAddOns()){
                        AddOnsResponse addOn = modelMapper.map(entry, AddOnsResponse.class);
                        addOns.add(addOn);
                    }
                    applicationData.setAddOnResponses(addOns);
                }
            }
            case ENTITY_PROOF_DETAILS -> {
                if (Objects.isNull(journeyMetadata.getPrimaryEntityProofDetails())) {
                    log.warn("PrimaryEntityProofDetails are null for lead id : {}", userStatus.getLeadTrackingNumber());
                } else {
                    // Map primary entity proof details
                    applicationData.setPrimaryEntityProofDetails(
                            modelMapper.map(journeyMetadata.getPrimaryEntityProofDetails(), EntityProofDetailsResponse.class));

                    applicationData.setIsSecondaryEntityProofOpted(journeyMetadata.getIsSecondaryEntityProofOpted());
                    // Check if secondary entity proof is opted
                    if (BooleanUtils.isTrue(journeyMetadata.getIsSecondaryEntityProofOpted())) {

                        if (Objects.isNull(journeyMetadata.getSecondaryEntityProofDetails())) {
                            log.warn("SecondaryEntityProofDetails are null for lead id : {}", userStatus.getLeadTrackingNumber());
                        } else {
                            // Map secondary entity proof details
                            applicationData.setSecondaryEntityProofDetails(
                                    modelMapper.map(journeyMetadata.getSecondaryEntityProofDetails(), EntityProofDetailsResponse.class));
                        }
                    }

                    // Entity name, date of incorporation and communication address preference
                    applicationData.setEntityName(journeyMetadata.getEntityName());
                    applicationData.setDateOfIncorporation(journeyMetadata.getDateOfIncorporation());
                    applicationData.setIsPrimaryEntityAddressOptedAsCommunicationAddress(
                            journeyMetadata.getIsPrimaryEntityAddressOptedAsCommunicationAddress());

                    // Map GST details if available
                    if (Objects.isNull(journeyMetadata.getGstDetails())) {
                        log.warn("GstProofDetails are null for lead id : {}", userStatus.getLeadTrackingNumber());
                    } else {
                        applicationData.setGstDetails(modelMapper.map(journeyMetadata.getGstDetails(), GstDetailsResponse.class));
                    }

                    // Handle IEC, FSSAI_LFDCA_CODE, and UDYAM details based on entity proof codes
                    handleEntityProofDetails(journeyMetadata, applicationData, userStatus);
                }
            }
            case APPLICATION_CREATED_TIME -> {
                applicationData.setApplicationCreatedTime(userStatus.getCreatedTime());
            }
            default -> throw new InvalidRequestException("Data filter passed is not applicable");
        }
    }

    private void handleEntityProofDetails(CurrentAccountJourneyMetadata journeyMetadata,
                                          CAApplicationData applicationData,
                                          UserStatus userStatus) {
        EntityProofDetails primaryEntityProof = journeyMetadata.getPrimaryEntityProofDetails();
        EntityProofDetails secondaryEntityProof = journeyMetadata.getSecondaryEntityProofDetails();

        // Check and handle IEC details
        if ((primaryEntityProof != null && IEC.equals(primaryEntityProof.getEntityProofCode())) ||
                (secondaryEntityProof != null && IEC.equals(secondaryEntityProof.getEntityProofCode()))) {
            if (Objects.isNull(journeyMetadata.getIecDetails())) {
                log.warn("IECProofDetails are null for lead id : {}", userStatus.getLeadTrackingNumber());
            } else {
                applicationData.setIecDetails(modelMapper.map(journeyMetadata.getIecDetails(), IECDetailsResponse.class));
            }
        }

        // Check and handle FSSAI_LFDCA_CODE details
        if ((primaryEntityProof != null && FSSAI_LFDCA_CODE.equals(primaryEntityProof.getEntityProofCode())) ||
                (secondaryEntityProof != null && FSSAI_LFDCA_CODE.equals(secondaryEntityProof.getEntityProofCode()))) {
            if (Objects.isNull(journeyMetadata.getFssaiDetails())) {
                log.warn("FSSAIProofDetails are null for lead id : {}", userStatus.getLeadTrackingNumber());
            } else {
                applicationData.setFssaiDetails(modelMapper.map(journeyMetadata.getFssaiDetails(), FSSAIDetailsResponse.class));
            }
        }

        // Check and handle UDYAM details
        if (secondaryEntityProof != null && UDYAM.equals(secondaryEntityProof.getEntityProofCode())) {
            if (Objects.isNull(journeyMetadata.getUdyamDetails())) {
                log.warn("UdyamProofDetails are null for lead id : {}", userStatus.getLeadTrackingNumber());
            } else {
                applicationData.setUdyamDetails(modelMapper.map(journeyMetadata.getUdyamDetails(), UdyamDetailsResponse.class));
            }
        }
    }

    private void validateInput(final UserStatus userStatus) throws InvalidRequestException {
        if (Objects.isNull(userStatus.getJourneyMetadata())
                || !(userStatus.getJourneyMetadata() instanceof CurrentAccountJourneyMetadata)) {
            throw new InvalidRequestException(
                    "Application Id does not belong to CA Journey " +
                            userStatus.getLeadTrackingNumber());
        }
    }

    @Override
    public String getProduct() {
        return CURRENT_ACCOUNT;
    }
}
