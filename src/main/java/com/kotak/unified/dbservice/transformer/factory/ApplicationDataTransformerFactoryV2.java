package com.kotak.unified.dbservice.transformer.factory;

import com.kotak.unified.dbservice.transformer.ApplicationDataTransformerV2;
import jakarta.annotation.PostConstruct;
import lombok.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class ApplicationDataTransformerFactoryV2 {

    @NonNull
    @Autowired
    List<ApplicationDataTransformerV2> applicationDataTransformersV2;

    Map<String, ApplicationDataTransformerV2> applicationDataTransformerMap =
            new HashMap<>();

    @PostConstruct
    public void init(){
        applicationDataTransformersV2.forEach(applicationDataTransformer ->
                applicationDataTransformerMap.put(
                        applicationDataTransformer.getProduct(),
                        applicationDataTransformer));
    }

    public ApplicationDataTransformerV2 selectTransformer(String product) {
        return applicationDataTransformerMap.get(product);
    }
}
