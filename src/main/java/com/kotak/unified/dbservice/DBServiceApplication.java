package com.kotak.unified.dbservice;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.retry.annotation.EnableRetry;

@SpringBootApplication
@ComponentScan({"com.kotak" , "com.bol.config"})
@EnableRetry
public class DBServiceApplication {

	public static void main(String[] args) {
		SpringApplication.run(DBServiceApplication.class, args);
	}

}
