package com.kotak.unified.dbservice.controller;

import com.kotak.unified.db.apiinterface.ResumeJourneyNotificationStatusApi;
import com.kotak.unified.db.model.CreateResumeJourneyNotificationStatusRecordRequest;
import com.kotak.unified.db.model.CreateResumeJourneyNotificationStatusRecordResponse;
import com.kotak.unified.db.model.GetResumeJourneyNotificationStatusRecordResponse;
import com.kotak.unified.db.model.UpdateResumeJourneyNotificationStatusRecordRequest;
import com.kotak.unified.db.model.UpdateResumeJourneyNotificationStatusRecordResponse;
import com.kotak.unified.dbservice.service.ResumeJourneyNotificationService;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
public class ResumeJourneyNotificationController implements ResumeJourneyNotificationStatusApi {

    private final ResumeJourneyNotificationService resumeJourneyNotificationService;

    @Autowired
    public ResumeJourneyNotificationController(ResumeJourneyNotificationService resumeJourneyNotificationService) {
        this.resumeJourneyNotificationService = resumeJourneyNotificationService;
    }

    @Override
    public ResponseEntity<CreateResumeJourneyNotificationStatusRecordResponse> createResumeJourneyNotificationStatus(@Valid CreateResumeJourneyNotificationStatusRecordRequest createResumeJourneyNotificationStatusRecordRequest) {
        log.info("Received createResumeJourneyNotificationStatusRecordRequest request: {}", createResumeJourneyNotificationStatusRecordRequest);
        CreateResumeJourneyNotificationStatusRecordResponse createResumeJourneyNotificationStatusRecordResponse = resumeJourneyNotificationService.createResumeJourneyNotificationStatusRecord(createResumeJourneyNotificationStatusRecordRequest);
        log.info("Response from createResumeJourneyNotificationStatusRecordRequest: {}", createResumeJourneyNotificationStatusRecordResponse);
        return ResponseEntity.ok(createResumeJourneyNotificationStatusRecordResponse);
    }

    @Override
    public ResponseEntity<GetResumeJourneyNotificationStatusRecordResponse> getResumeJourneyNotificationStatusRecord(String leadTrackingNumber) {
        log.info("Received getResumeJourneyNotificationStatusRecord request: leadTrackingNumber: '{}'", leadTrackingNumber);
        GetResumeJourneyNotificationStatusRecordResponse getResumeJourneyNotificationStatusRecordResponse = resumeJourneyNotificationService.getResumeJourneyNotificationStatusRecord(leadTrackingNumber);
        return ResponseEntity.ok(getResumeJourneyNotificationStatusRecordResponse);
    }

    @Override
    public ResponseEntity<UpdateResumeJourneyNotificationStatusRecordResponse> updateResumeJourneyNotificationStatusRecord(String leadTrackingNumber, @Valid UpdateResumeJourneyNotificationStatusRecordRequest updateResumeJourneyNotificationStatusRecordRequest) {
        log.info("Received updateResumeJourneyNotificationStatusRecord request for leadTrackingNumber: '{}', {}",
                leadTrackingNumber, updateResumeJourneyNotificationStatusRecordRequest);
        UpdateResumeJourneyNotificationStatusRecordResponse updateResumeJourneyNotificationStatusRecordResponse = resumeJourneyNotificationService.updateResumeJourneyNotificationStatusRecord(
                leadTrackingNumber, updateResumeJourneyNotificationStatusRecordRequest);
        return ResponseEntity.ok(updateResumeJourneyNotificationStatusRecordResponse);
    }
}
