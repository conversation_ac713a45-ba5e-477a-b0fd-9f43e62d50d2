package com.kotak.unified.dbservice.controller;

import apiinterface.UserStatusResource;
import com.kotak.unified.common.response.ErrorResponse;
import com.kotak.unified.common.response.wrapper.ResponseWrapper;
import com.kotak.unified.db.UserStatusResponse;
import com.kotak.unified.db.request.GetFilteredUserStatusRequest;
import com.kotak.unified.dbservice.service.UserStatusService;
import com.kotak.unified.request.PatchUserJourneyStatusRequest;
import com.kotak.unified.request.PatchUserJourneyStatusResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

@RestController
public class UserStatusController implements UserStatusResource {
    private final UserStatusService userStatusService;

    @Autowired
    public UserStatusController(UserStatusService userStatusService) {
        this.userStatusService = userStatusService;
    }

    @Override
    public ResponseEntity<ResponseWrapper<UserStatusResponse>> getUserStatus(String leadTrackingId) {
        UserStatusResponse userStatusResponse = userStatusService.getUserStatus(leadTrackingId);
        return ResponseEntity.status(HttpStatus.OK).body(ResponseWrapper.success(userStatusResponse));
    }

    @Override
    public ResponseEntity<ResponseWrapper<List<UserStatusResponse>>> getFilteredUserStatus(GetFilteredUserStatusRequest getFilteredUserStatusRequest) {
        List<ErrorResponse> errorResponses = Arrays.asList(ErrorResponse.builder().errorCode("405").errorMessage("Api not supported").build());
        return ResponseEntity.status(HttpStatus.METHOD_NOT_ALLOWED).body(ResponseWrapper.failure(errorResponses));
    }

    @Override
    public ResponseEntity<ResponseWrapper<PatchUserJourneyStatusResponse>> patchUserJourneyStatus(String leadTrackingId,
                                                                                                  PatchUserJourneyStatusRequest request) {
        PatchUserJourneyStatusResponse patchUserJourneyStatusResponse = userStatusService.updateUserStatus(leadTrackingId, request);
        return ResponseEntity.status(HttpStatus.ACCEPTED).body(ResponseWrapper.success(patchUserJourneyStatusResponse));
    }
}
