package com.kotak.unified.dbservice.controller;

import com.kotak.unified.common.request.database.KycSchedulingRequest;
import com.kotak.unified.common.response.wrapper.ResponseWrapper;
import com.kotak.unified.db.KycSchedulingDetailsResponse;
import com.kotak.unified.db.KycSchedulingResponse;
import com.kotak.unified.dbservice.service.KycScheduleService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;



@RestController
@RequestMapping("/api/v1/kyc/schedule")
public class KycScheduleController {
    private final KycScheduleService kycScheduleService;

    @Autowired
    public KycScheduleController(KycScheduleService kycScheduleService) {
        this.kycScheduleService = kycScheduleService;
    }

    @PostMapping
    public ResponseEntity<ResponseWrapper<KycSchedulingResponse>> createKycSchedule(
            @RequestBody @Valid KycSchedulingRequest kycSchedulingRequest
    ) {
        KycSchedulingResponse kycSchedulingResponse = kycScheduleService.createKycSchedule(kycSchedulingRequest);
        return ResponseEntity.status(HttpStatus.CREATED).body(ResponseWrapper.success(kycSchedulingResponse));
    }

    @PutMapping
    public ResponseEntity<ResponseWrapper<KycSchedulingResponse>> updateKycSchedule(
            @RequestBody @Valid KycSchedulingRequest kycSchedulingRequest
    ) {
        KycSchedulingResponse kycSchedulingResponse = kycScheduleService.updateKycSchedule(kycSchedulingRequest);
        return ResponseEntity.status(HttpStatus.OK).body(ResponseWrapper.success(kycSchedulingResponse));
    }

    @GetMapping("/{leadTrackingId}")
    public ResponseEntity<ResponseWrapper<KycSchedulingResponse>> getKycSchedule(
            @PathVariable("leadTrackingId") String leadTrackingId
    ) {
        KycSchedulingResponse kycSchedulingResponse = kycScheduleService.getKycScheduleForLeadTrackingId(leadTrackingId);
        return ResponseEntity.status(HttpStatus.OK).body(ResponseWrapper.success(kycSchedulingResponse));
    }

    @GetMapping
    public ResponseEntity<ResponseWrapper<KycSchedulingDetailsResponse>> getKycSchedule(
            @RequestParam(value = "timestamp") Long timestamp,
            @RequestParam(value = "comparisonOperator") String operator
    ) {
        KycSchedulingDetailsResponse kycSchedulingDetailsResponse = kycScheduleService.getKycSchedulesBasedOnTimestamp(timestamp, operator);
        return ResponseEntity.status(HttpStatus.OK).body(ResponseWrapper.success(kycSchedulingDetailsResponse));
    }




}
