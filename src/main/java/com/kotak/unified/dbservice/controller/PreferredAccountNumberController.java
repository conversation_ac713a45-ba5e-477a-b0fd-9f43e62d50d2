package com.kotak.unified.dbservice.controller;

import apiinterface.PreferredAccountNumberResource;
import com.kotak.unified.common.response.wrapper.ResponseWrapper;
import com.kotak.unified.dbinterface.models.PreferredAccountNumberDto;
import com.kotak.unified.dbservice.service.PreferredAccountNumberService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Slf4j
public class PreferredAccountNumberController implements PreferredAccountNumberResource {

    private final PreferredAccountNumberService preferredAccountNumberService;

    @Override
    public ResponseEntity<ResponseWrapper<PreferredAccountNumberDto>> savePreferredAccountNumberRecord(
            @Valid PreferredAccountNumberDto preferredAccountNumberDto) {
        PreferredAccountNumberDto savedDto =
                preferredAccountNumberService.savePreferredAccountNumberRecord(preferredAccountNumberDto);
        return ResponseEntity.status(HttpStatus.OK).body(ResponseWrapper.success(savedDto));
    }

    @Override
    public ResponseEntity<ResponseWrapper<PreferredAccountNumberDto>> getPreferredAccountNumberRecord(
            String preferredAccountNumber) {
        PreferredAccountNumberDto preferredAccountNumberDto =
                preferredAccountNumberService.getPreferredAccountNumberRecord(preferredAccountNumber);
        return ResponseEntity.status(HttpStatus.OK).body(ResponseWrapper.success(preferredAccountNumberDto));
    }
}
