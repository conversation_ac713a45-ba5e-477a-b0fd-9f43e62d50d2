package com.kotak.unified.dbservice.controller;

import apiinterface.CpvDvuVerificationDetailsResource;
import com.kotak.unified.common.response.wrapper.ResponseWrapper;
import com.kotak.unified.dbinterface.models.CpvDvuVerificationDetailsDto;
import com.kotak.unified.dbservice.service.impl.CpvDvuVerificationDetailsService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Slf4j
public class CpvDvuVerificationDetailsController implements CpvDvuVerificationDetailsResource {

    private final CpvDvuVerificationDetailsService cpvDvuVerificationDetailsService;

    @Override
    public ResponseEntity<ResponseWrapper<CpvDvuVerificationDetailsDto>> saveCpvDvuVerificationDetailsRecord(CpvDvuVerificationDetailsDto cpvDvuVerificationDetailsDto) {
        CpvDvuVerificationDetailsDto savedDto =  this.cpvDvuVerificationDetailsService.save(cpvDvuVerificationDetailsDto);
        return ResponseEntity.status(HttpStatus.OK).body(ResponseWrapper.success(savedDto));
    }

    @Override
    public ResponseEntity<ResponseWrapper<CpvDvuVerificationDetailsDto>> getCpvDvuVerificationDetails(String actionTrackingId) {
        CpvDvuVerificationDetailsDto cpvDvuVerificationDetailsDto = this.cpvDvuVerificationDetailsService
                .getCpvDvuVerificationDetails(actionTrackingId);
        return ResponseEntity.status(HttpStatus.OK).body(ResponseWrapper.success(cpvDvuVerificationDetailsDto));
    }
}
