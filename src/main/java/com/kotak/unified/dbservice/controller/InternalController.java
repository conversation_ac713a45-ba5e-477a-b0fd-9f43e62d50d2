package com.kotak.unified.dbservice.controller;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/internal")
public class InternalController {

    @RequestMapping(method = {RequestMethod.GET}, path = {"/ping"})
    public ResponseEntity<?> ping() {
        return new ResponseEntity<>("Success", HttpStatus.OK);
    }
}