package com.kotak.unified.dbservice.controller;

import apiinterface.VideoKycDetailsResource;
import com.kotak.unified.common.response.ErrorResponse;
import com.kotak.unified.common.response.wrapper.ResponseWrapper;
import com.kotak.unified.db.vkycStatusResponse.VideoKycDetailsResponse;
import com.kotak.unified.dbservice.service.VideoKycDetailsService;
import lombok.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

@RestController
public class VideoKycDetailsController implements VideoKycDetailsResource {
    private final VideoKycDetailsService videoKycDetailsService;
    @Autowired
    public VideoKycDetailsController(@NonNull final VideoKycDetailsService videoKycDetailsService) {
        this.videoKycDetailsService = videoKycDetailsService;
    }
    @Override
    public ResponseEntity<ResponseWrapper<VideoKycDetailsResponse>> getVideoKycDetails(@NonNull final String kycTrackingId) {
        VideoKycDetailsResponse videoKycDetailsResponse = videoKycDetailsService.getVideoKycDetails(kycTrackingId);
        return ResponseEntity.status(HttpStatus.OK).body(ResponseWrapper.success(videoKycDetailsResponse));
    }

    @Override
    public ResponseEntity<ResponseWrapper<List<VideoKycDetailsResponse>>> getAllVideoKycDetails(@NonNull final String leadTrackingId) {
        List<ErrorResponse> errorResponses = Arrays.asList(ErrorResponse.builder().errorCode("405").errorMessage("Api not supported").build());
        return ResponseEntity.status(HttpStatus.METHOD_NOT_ALLOWED).body(ResponseWrapper.failure(errorResponses));
    }

    @Override
    public ResponseEntity<ResponseWrapper<VideoKycDetailsResponse>> saveVideoKycDetails(@NonNull final VideoKycDetailsResponse videoKycDetailsRequest) {
        VideoKycDetailsResponse videoKycDetailsResponse = videoKycDetailsService.save(videoKycDetailsRequest);
        return ResponseEntity.status(HttpStatus.OK).body(ResponseWrapper.success(videoKycDetailsResponse));
    }

    @Override
    public ResponseEntity<ResponseWrapper<VideoKycDetailsResponse>> getTopByLeadTrackingNumberOrderByCreatedAtDesc(@NonNull final String leadTrackingNumber) {
        VideoKycDetailsResponse videoKycDetailsResponse = videoKycDetailsService.getTopByLeadTrackingNumberOrderByCreatedAtDesc(leadTrackingNumber);
        return ResponseEntity.status(HttpStatus.OK).body(ResponseWrapper.success(videoKycDetailsResponse));
    }

    @Override
    public ResponseEntity<ResponseWrapper<VideoKycDetailsResponse>> getTopByLeadTrackingNumberAndLatestStatusOrderByCreatedAt(
            @NonNull final String leadTrackingNumber, @NonNull final String latestStatus) {
        VideoKycDetailsResponse videoKycDetailsResponse = videoKycDetailsService.getTopByLeadTrackingNumberAndLatestStatusOrderByCreatedAt(
                leadTrackingNumber, latestStatus);
        return ResponseEntity.status(HttpStatus.OK).body(ResponseWrapper.success(videoKycDetailsResponse));
    }
}
