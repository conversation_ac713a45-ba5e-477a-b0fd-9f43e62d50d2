package com.kotak.unified.dbservice.controller;

import com.kotak.unified.common.response.wrapper.ResponseWrapperWithTypeSet;
import com.kotak.unified.db.ApiResponseDetailsDto;
import com.kotak.unified.dbservice.service.ApiResponseService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping({"/api/v1/apiresponse"})
public class ApiResponseDetailsController {

    private final ApiResponseService apiResponseService;

    public ApiResponseDetailsController(ApiResponseService apiResponseService) {
        this.apiResponseService = apiResponseService;
    }

    @SneakyThrows
    @GetMapping({"/{leadTrackingId}/{apiName}"})
    public ResponseEntity<ResponseWrapperWithTypeSet<ApiResponseDetailsDto>> getApiResponseData(
            @PathVariable String leadTrackingId, @PathVariable String apiName) {
        ApiResponseDetailsDto apiResponseData = apiResponseService
                .getApiResponseData(leadTrackingId, apiName);
        return new ResponseEntity<>(ResponseWrapperWithTypeSet.success(apiResponseData), HttpStatus.OK);
    }
}
