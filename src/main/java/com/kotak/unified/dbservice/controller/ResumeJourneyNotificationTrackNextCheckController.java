package com.kotak.unified.dbservice.controller;

import com.kotak.unified.db.apiinterface.ResumeJourneyNotificationTrackNextCheckApi;
import com.kotak.unified.db.model.CreateResumeJourneyNotificationTrackNextCheckRecordRequest;
import com.kotak.unified.db.model.CreateResumeJourneyNotificationTrackNextCheckRecordResponse;
import com.kotak.unified.db.model.GetResumeJourneyNotificationStatusRecordResponse;
import com.kotak.unified.db.model.GetResumeJourneyNotificationTrackNextCheckRecordResponse;
import com.kotak.unified.db.model.UpdateResumeJourneyNotificationStatusRecordResponse;
import com.kotak.unified.db.model.UpdateResumeJourneyNotificationTrackNextCheckRecordRequest;
import com.kotak.unified.db.model.UpdateResumeJourneyNotificationTrackNextCheckRecordResponse;
import com.kotak.unified.dbservice.service.ResumeJourneyNotificationTrackNextCheckService;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
public class ResumeJourneyNotificationTrackNextCheckController implements ResumeJourneyNotificationTrackNextCheckApi {

    private final ResumeJourneyNotificationTrackNextCheckService resumeJourneyNotificationTrackNextCheckService;

    @Autowired
    public ResumeJourneyNotificationTrackNextCheckController(ResumeJourneyNotificationTrackNextCheckService resumeJourneyNotificationTrackNextCheckService) {
        this.resumeJourneyNotificationTrackNextCheckService = resumeJourneyNotificationTrackNextCheckService;
    }


    @Override
    public ResponseEntity<CreateResumeJourneyNotificationTrackNextCheckRecordResponse> createResumeJourneyNotificationNextCheck(@Valid CreateResumeJourneyNotificationTrackNextCheckRecordRequest createResumeJourneyNotificationTrackNextCheckRecordRequest) {
        log.info("Received createResumeJourneyNotificationStatusRecordRequest request: leadTrackingNumber: '{}'", createResumeJourneyNotificationTrackNextCheckRecordRequest.getLeadTrackingNumber());
        CreateResumeJourneyNotificationTrackNextCheckRecordResponse createResumeJourneyNotificationTrackNextCheckRecordResponse = resumeJourneyNotificationTrackNextCheckService.createResumeJourneyNotificationTrackNextCheckRecord(createResumeJourneyNotificationTrackNextCheckRecordRequest);
        log.info("Response from createResumeJourneyNotificationStatusRecordRequest: leadTrackingNumber: '{}': isCreateRecordSuccessful : {}",
                createResumeJourneyNotificationTrackNextCheckRecordRequest.getLeadTrackingNumber(),
                createResumeJourneyNotificationTrackNextCheckRecordResponse.getIsCreateRecordSuccessful());
        return ResponseEntity.ok(createResumeJourneyNotificationTrackNextCheckRecordResponse);
    }

    @Override
    public ResponseEntity<GetResumeJourneyNotificationTrackNextCheckRecordResponse> getResumeJourneyNotificationTrackNextCheckRecord(String leadTrackingNumber) {
        log.info("Received getResumeJourneyNotificationTrackNextCheckRecord request: leadTrackingNumber: '{}'", leadTrackingNumber);
        GetResumeJourneyNotificationTrackNextCheckRecordResponse getResumeJourneyNotificationTrackNextCheckRecordResponse = resumeJourneyNotificationTrackNextCheckService.getResumeJourneyNotificationTrackNextCheckRecord(leadTrackingNumber);
        return ResponseEntity.ok(getResumeJourneyNotificationTrackNextCheckRecordResponse);
    }

    @Override
    public ResponseEntity<UpdateResumeJourneyNotificationTrackNextCheckRecordResponse> updateResumeJourneyNotificationTrackNextCheckRecord(String leadTrackingNumber, @Valid UpdateResumeJourneyNotificationTrackNextCheckRecordRequest updateResumeJourneyNotificationTrackNextCheckRecordRequest) {
        log.info("Received updateResumeJourneyNotificationTrackNextCheckRecord request for leadTrackingNumber: '{}'  {}",
                leadTrackingNumber, updateResumeJourneyNotificationTrackNextCheckRecordRequest);
        UpdateResumeJourneyNotificationTrackNextCheckRecordResponse updateResumeJourneyNotificationTrackNextCheckRecordResponse = resumeJourneyNotificationTrackNextCheckService.updateResumeJourneyNotificationTrackNextCheckRecord(
                leadTrackingNumber, updateResumeJourneyNotificationTrackNextCheckRecordRequest);
        return ResponseEntity.ok(updateResumeJourneyNotificationTrackNextCheckRecordResponse);
    }
}
