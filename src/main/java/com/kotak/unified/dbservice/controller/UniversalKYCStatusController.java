package com.kotak.unified.dbservice.controller;

import com.kotak.unified.db.apiinterface.UkycStatusApi;
import com.kotak.unified.db.model.CreateUKYCRecordRequest;
import com.kotak.unified.db.model.CreateUKYCRecordResponse;
import com.kotak.unified.db.model.GetUKYCRecordResponse;
import com.kotak.unified.db.model.UpdateUKYCRecordRequest;
import com.kotak.unified.db.model.UpdateUKYCRecordResponse;
import com.kotak.unified.dbservice.service.UniversalKYCStatusService;
import com.kotak.unified.dbservice.validator.UniversalKYCStatusAPIRequestValidator;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;


@Slf4j
@RestController
public class UniversalKYCStatusController implements UkycStatusApi {


    private final UniversalKYCStatusService universalKYCStatusService;
    private final UniversalKYCStatusAPIRequestValidator universalKYCStatusAPIRequestValidator;

    @Autowired
    public UniversalKYCStatusController(
            UniversalKYCStatusService universalKYCStatusService,
            UniversalKYCStatusAPIRequestValidator universalKYCStatusAPIRequestValidator) {
        this.universalKYCStatusService = universalKYCStatusService;
        this.universalKYCStatusAPIRequestValidator = universalKYCStatusAPIRequestValidator;
    }

    @SneakyThrows
    @Override
    public ResponseEntity<CreateUKYCRecordResponse> createUKYCRecord(CreateUKYCRecordRequest createUKYCRecordRequest) {
            log.info("Received createUKYCRecord request: {}", createUKYCRecordRequest);
            universalKYCStatusAPIRequestValidator.validate(createUKYCRecordRequest);
            CreateUKYCRecordResponse createUKYCRecordResponse = universalKYCStatusService.createUKYCRecord(createUKYCRecordRequest);
            log.info("Response from createUKYCRecord: {}", createUKYCRecordResponse);
            return ResponseEntity.ok(createUKYCRecordResponse);
    }


    @Override
    public ResponseEntity<GetUKYCRecordResponse> getUKYCRecord( String aadhaarRefKey, String applicationId) {
        log.info("Received getUKYCRecord request: aadhaarRefKey: '{}', applicationId: '{}'", aadhaarRefKey, applicationId);
        GetUKYCRecordResponse getUKYCRecordResponse = universalKYCStatusService.getUKYCRecord(aadhaarRefKey, applicationId);
        return ResponseEntity.ok(getUKYCRecordResponse);
    }


    @Override
    public ResponseEntity<UpdateUKYCRecordResponse> updateUKYCRecord(
            String aadhaarRefKey, String applicationId, UpdateUKYCRecordRequest updateUKYCRecordRequest) {
        log.info("Received updateUKYCRecord request for aadhaarRefKey: '{}' and applicationId: {}, {}",
                aadhaarRefKey, applicationId, updateUKYCRecordRequest);
        universalKYCStatusAPIRequestValidator.validate(updateUKYCRecordRequest);
        UpdateUKYCRecordResponse updateUKYCRecordResponse = universalKYCStatusService.updateUKYCRecord(
                aadhaarRefKey, applicationId,  updateUKYCRecordRequest);
        return ResponseEntity.ok(updateUKYCRecordResponse);
    }


}
