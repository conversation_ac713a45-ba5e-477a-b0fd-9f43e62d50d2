package com.kotak.unified.dbservice.controller;

import apiinterface.AsyncProcessResource;
import com.kotak.unified.common.response.ErrorResponse;
import com.kotak.unified.common.response.wrapper.ResponseWrapper;
import com.kotak.unified.common.response.wrapper.ResponseWrapperWithTypeSet;
import com.kotak.unified.db.AsyncProcessDetailsResponse;
import com.kotak.unified.db.AsyncProcessExecutionDataResponse;
import com.kotak.unified.dbservice.enums.AsyncExecutionDataProcessType;
import com.kotak.unified.dbservice.exceptions.custom.RestException;
import com.kotak.unified.dbservice.model.exception.DatabaseErrorResponse;
import com.kotak.unified.dbservice.service.AsyncProcessService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

@Slf4j
@RestController
public class AsyncProcessController implements AsyncProcessResource {

    private final AsyncProcessService asyncProcessService;

    public AsyncProcessController(AsyncProcessService asyncProcessService) {
        this.asyncProcessService = asyncProcessService;
    }

    @Override
    @Deprecated
    public ResponseEntity<ResponseWrapper<List<AsyncProcessDetailsResponse>>> getFilteredAsyncProcess(String leadTrackingNumber,
                                                                                                      String asyncProcessTypes,
                                                                                                      String asyncProcessStatuses,
                                                                                                      Long creationStartTime,
                                                                                                      Long creationEndTime) {
        List<ErrorResponse> errorResponses = Arrays.asList(ErrorResponse.builder().errorCode("405").errorMessage("Api not supported").build());
        return ResponseEntity.status(HttpStatus.METHOD_NOT_ALLOWED).body(ResponseWrapper.failure(errorResponses));
    }

    @SneakyThrows
    @Override
    @Deprecated
    public ResponseEntity<ResponseWrapperWithTypeSet<AsyncProcessExecutionDataResponse>> getAsyncProcessExecutionData(
            String leadTrackingId, String processType) {
        AsyncExecutionDataProcessType asyncExecutionDataProcessType;
        try {
            asyncExecutionDataProcessType = AsyncExecutionDataProcessType.valueOf(processType);
        } catch (IllegalArgumentException e) {
            BindingResult bindingResult = new BeanPropertyBindingResult(processType, "processType");
            bindingResult.addError(
                    new FieldError(
                            "processType", "processType",
                            String.format("Invalid Enum value for %s", AsyncProcessExecutionDataResponse.class)));
            throw new MethodArgumentNotValidException((MethodParameter) null, bindingResult);
        }
        try {
            AsyncProcessExecutionDataResponse executionDataResponse = asyncProcessService
                    .getAsyncProcessExecutionData(leadTrackingId, asyncExecutionDataProcessType);
            return ResponseEntity.status(HttpStatus.OK).body(ResponseWrapperWithTypeSet.success(executionDataResponse));
        } catch (RestException e) {
            if (e.getHttpStatus().is4xxClientError()) {
                return ResponseEntity.status(e.getHttpStatus()).body(ResponseWrapperWithTypeSet.failure(
                        e.getErrorResponseList().stream().map(DatabaseErrorResponse::toErrorResponse).toList()
                ));
            }
            throw e;
        }
    }

    @SneakyThrows
    @Override
    public ResponseEntity<ResponseWrapper<AsyncProcessDetailsResponse>> getAsyncProcessData(
            String leadTrackingId, String processType) {
        AsyncProcessDetailsResponse asyncProcessData = asyncProcessService
                .getAsyncProcessData(leadTrackingId, processType);
        return ResponseEntity.status(HttpStatus.OK).body(ResponseWrapper.success(asyncProcessData));
    }
}
