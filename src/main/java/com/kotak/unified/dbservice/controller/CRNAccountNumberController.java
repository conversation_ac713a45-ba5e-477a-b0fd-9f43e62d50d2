package com.kotak.unified.dbservice.controller;

import com.kotak.unified.common.response.ErrorResponse;
import com.kotak.unified.common.response.async.AddCrnAndAccountNumberResponse;
import com.kotak.unified.db.request.AssignCACRNAndAccountNumberRequest;
import com.kotak.unified.db.request.InsertCACrnAndAccountNumberRequest;
import com.kotak.unified.db.response.AssignCACRNAndAccountNumberResponse;
import com.kotak.unified.dbservice.apiinterface.CRNAccountNumberResource;
import com.kotak.unified.dbservice.exceptions.EntityExistsException;
import com.kotak.unified.dbservice.exceptions.PregeneratedCRNAccountNumDepletedException;
import com.kotak.unified.dbservice.service.CAPreGeneratedCRNAccountNumService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;
import software.amazon.awssdk.awscore.exception.AwsServiceException;

@RestController
@Slf4j
@CrossOrigin(origins = "*")
public class CRNAccountNumberController implements CRNAccountNumberResource {
    private final CAPreGeneratedCRNAccountNumService caPreGeneratedCRNAccountNumService;

    @Autowired
    public CRNAccountNumberController(CAPreGeneratedCRNAccountNumService caPreGeneratedCRNAccountNumService) {
        this.caPreGeneratedCRNAccountNumService = caPreGeneratedCRNAccountNumService;
    }

    @Override
    public ResponseEntity<?> addCACrnAndAccountNumber(InsertCACrnAndAccountNumberRequest insertRequest) {

        log.info("Received input to insert pgn with crn: {}, account number: {}, entity crn: {}",
                insertRequest.getCrn(), insertRequest.getAccountNumber(), insertRequest.getEntityCrn());
        try {
            caPreGeneratedCRNAccountNumService.insertCrnAndAccountNumber(insertRequest);

            log.info("Successfully inserted pgn with crn: {}, account number: {}, entity crn: {}",
                    insertRequest.getCrn(), insertRequest.getAccountNumber(), insertRequest.getEntityCrn());

            AddCrnAndAccountNumberResponse addCrnAndAccountNumberResponse = AddCrnAndAccountNumberResponse.builder().isSuccess(true).build();
            return new ResponseEntity<>(addCrnAndAccountNumberResponse, HttpStatus.CREATED);
        } catch (EntityExistsException e) {
            ErrorResponse errorResponse = ErrorResponse.builder().errorCode("ENTITY_ALREADY_EXISTS").errorMessage(e.getMessage()).build();
            log.warn("Entity already exists with crn: {}, account number: {}, entity crn: {}",
                    insertRequest.getCrn(), insertRequest.getAccountNumber(), insertRequest.getEntityCrn());
            return new ResponseEntity<>(errorResponse, HttpStatus.CONFLICT);
        } catch (AwsServiceException e){
            log.error("DynamoDB error occurred while processing crn: {}, account number: {}, entity crn: {}",
                    insertRequest.getCrn(), insertRequest.getAccountNumber(), insertRequest.getEntityCrn(), e);
            ErrorResponse errorResponse = ErrorResponse.builder().errorCode("DB_ERROR").errorMessage(e.getMessage()).build();
            return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        } catch (Exception e) {
            log.error("Unexpected error occurred while processing crn: {}, account number: {}, entity crn: {}",
                    insertRequest.getCrn(), insertRequest.getAccountNumber(), insertRequest.getEntityCrn(), e);
            ErrorResponse errorResponse = ErrorResponse.builder().errorCode("UN_EXPECTED_ERROR").errorMessage(e.getMessage()).build();
            return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<?> assignCACrnAndAccountNumber(AssignCACRNAndAccountNumberRequest assignRequest) {

        log.info("Received input to assign lead tracking number: {} and journey type: {}",
                assignRequest.getLeadTrackingNumber(), assignRequest.getJourneyType().name());
        try {
            AssignCACRNAndAccountNumberResponse response = caPreGeneratedCRNAccountNumService.assignAccountNumber(assignRequest);

            log.info("Successfully assigned pgn with crn: {}, account number: {}, entity crn: {} to lead tracking number: {}",
                    response.getCrn(), response.getAccountNumber(), response.getEntityCrn(), assignRequest.getLeadTrackingNumber());

            return new ResponseEntity<>(response, HttpStatus.OK);
        } catch (EntityExistsException e) {
            log.warn("Entity already exists with leadId: {}, journey type: {}",
                    assignRequest.getLeadTrackingNumber(), assignRequest.getJourneyType().name());
            ErrorResponse errorResponse = ErrorResponse.builder().errorCode("ENTITY_ALREADY_EXISTS").errorMessage(e.getMessage()).build();
            return new ResponseEntity<>(errorResponse, HttpStatus.CONFLICT);
        } catch (PregeneratedCRNAccountNumDepletedException e) {
            log.warn("Cannot assign a PGN to leadId: {} with journey type: {} due to PGN exhaustion",
                    assignRequest.getLeadTrackingNumber(), assignRequest.getJourneyType().name());
            ErrorResponse errorResponse = ErrorResponse.builder().errorCode("CRN_ACCOUNTNUM_DEPLETED").errorMessage(e.getMessage()).build();
            return new ResponseEntity<>(errorResponse, HttpStatus.NOT_FOUND);
        } catch (AwsServiceException e){
            log.error("DynamoDB error occurred while processing lead tracking number: {}, journey type: {}",
                    assignRequest.getLeadTrackingNumber(), assignRequest.getJourneyType(), e);
            ErrorResponse errorResponse = ErrorResponse.builder().errorCode("DB_ERROR").errorMessage(e.getMessage()).build();
            return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        } catch (Exception e) {
            log.error("Unexpected error occurred while processing leadId: {}, with journey type: {}",
                    assignRequest.getLeadTrackingNumber(), assignRequest.getJourneyType().name(), e);
            ErrorResponse errorResponse = ErrorResponse.builder().errorCode("UNEXPECTED_ERROR").errorMessage(e.getMessage()).build();
            return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
