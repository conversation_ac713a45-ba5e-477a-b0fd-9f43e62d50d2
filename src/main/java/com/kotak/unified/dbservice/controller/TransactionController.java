package com.kotak.unified.dbservice.controller;

import apiinterface.TransactionResource;
import com.kotak.unified.common.response.ErrorResponse;
import com.kotak.unified.common.response.wrapper.ResponseWrapper;
import com.kotak.unified.db.response.TransactionResponse;
import com.kotak.unified.dbservice.service.TransactionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping("/api/v1/transaction")
public class TransactionController implements TransactionResource {
    private final TransactionService transactionService;

    @Autowired
    public TransactionController(TransactionService transactionService) {
        this.transactionService = transactionService;
    }

    @Override
    public ResponseEntity<ResponseWrapper<TransactionResponse>> getTransaction(String transactionId) {
        TransactionResponse transaction = transactionService.getTransaction(transactionId);
        return ResponseEntity.status(HttpStatus.OK).body(ResponseWrapper.success(transaction));
    }

    @Override
    public ResponseEntity<ResponseWrapper<List<TransactionResponse>>> getTransactionsForLeadTrackingNumber(String leadTrackingId) {
        List<ErrorResponse> errorResponses = Arrays.asList(ErrorResponse.builder().errorCode("405").errorMessage("Api not supported").build());
        return ResponseEntity.status(HttpStatus.METHOD_NOT_ALLOWED).body(ResponseWrapper.failure(errorResponses));
    }
}
