package com.kotak.unified.dbservice.controller;

import com.kotak.unified.common.exception.InvalidRequestException;
import com.kotak.unified.common.response.ErrorResponse;
import com.kotak.unified.db.ApplicationData;
import com.kotak.unified.db.GetApplicationDataRequest;
import com.kotak.unified.dbservice.exceptions.EntityNotFoundException;
import com.kotak.unified.dbservice.service.impl.ApplicationDataService;
import jakarta.validation.Valid;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import static com.kotak.unified.dbservice.utils.Constants.APPLICATION_ID_HEADER;

@Slf4j
@RestController
@RequestMapping("/api/v1/application-data")
public class ApplicationDataController {

  private final ApplicationDataService applicationDataService;

  public ApplicationDataController(
      @NonNull final ApplicationDataService applicationDataService) {
    this.applicationDataService = applicationDataService;
  }

  @RequestMapping(method = RequestMethod.POST, path = "/get")
  public ResponseEntity<?> getApplicationData(
      @RequestHeader(value = APPLICATION_ID_HEADER, required = true) String applicationId,
      @Valid @RequestBody GetApplicationDataRequest getApplicationDataRequest) {
    try {
      applicationId = applicationId.replace("\t", "").replace("\n", "").replace("\r", "");
      log.info(
          "[ApplicationDataController -> getApplicationData] Get Application data call received for applicationId : {}, correlationId : {}",
          applicationId);
      ApplicationData applicationData = applicationDataService
          .getApplicationData(getApplicationDataRequest, applicationId);
      return new ResponseEntity<>(applicationData, HttpStatus.OK);
    } catch (InvalidRequestException e) {
      ErrorResponse errorResponse = ErrorResponse.builder()
          .errorCode("INVALID_REQUEST").errorMessage(e.getMessage()).build();
      return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);
    } catch (EntityNotFoundException e) {
      ErrorResponse errorResponse = ErrorResponse.builder()
          .errorCode("NOT_FOUND").errorMessage(e.getMessage()).build();
      return new ResponseEntity<>(errorResponse, HttpStatus.NOT_FOUND);
    } catch (Exception e) {
      log.error("Error occurred while getting application data for applicationId : {}", applicationId, e);
      ErrorResponse errorResponse = ErrorResponse.builder()
          .errorCode("UNKNOWN_ERROR").errorMessage(e.getMessage()).build();
      return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

}
