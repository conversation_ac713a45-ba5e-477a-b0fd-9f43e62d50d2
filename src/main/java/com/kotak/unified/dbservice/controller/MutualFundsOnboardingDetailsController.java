package com.kotak.unified.dbservice.controller;


import apiinterface.MutualFundsOnboardingDetailsResource;
import com.kotak.unified.common.response.wrapper.ResponseWrapper;
import com.kotak.unified.db.mf.MutualFundsOnboardingDetailsRequest;
import com.kotak.unified.db.request.mf.GetMutualFundsOnboardingDetailsRequest;
import com.kotak.unified.dbservice.service.impl.MutualFundsOnboardingDetailsService;
import lombok.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class MutualFundsOnboardingDetailsController implements MutualFundsOnboardingDetailsResource {
    private final MutualFundsOnboardingDetailsService mutualFundsOnboardingDetailsService;

    @Autowired
    public MutualFundsOnboardingDetailsController(@NonNull final MutualFundsOnboardingDetailsService mutualFundsOnboardingDetailsService) {
        this.mutualFundsOnboardingDetailsService = mutualFundsOnboardingDetailsService;
    }

    @Override
    public ResponseEntity<ResponseWrapper<MutualFundsOnboardingDetailsRequest>> getMutualFundOnboardingDetails(@NonNull final String eventTrackingId) {
        MutualFundsOnboardingDetailsRequest mutualFundOnboardingDetailsRequest = mutualFundsOnboardingDetailsService.getMutualFundOnboardingDetails(eventTrackingId);
        return ResponseEntity.status(HttpStatus.OK).body(ResponseWrapper.success(mutualFundOnboardingDetailsRequest));
    }

    @Override
    public ResponseEntity<ResponseWrapper<MutualFundsOnboardingDetailsRequest>> save(@NonNull final MutualFundsOnboardingDetailsRequest mutualFundOnboardingDetailsRequest) {
        MutualFundsOnboardingDetailsRequest savedMutualFundOnboardingDetailsRequest = mutualFundsOnboardingDetailsService.save(mutualFundOnboardingDetailsRequest);
        return ResponseEntity.status(HttpStatus.OK).body(ResponseWrapper.success(savedMutualFundOnboardingDetailsRequest));
    }

    @Override
    public ResponseEntity<ResponseWrapper<MutualFundsOnboardingDetailsRequest>> getCrnByEventTypeEventStatusAndCreatedAtDesc(GetMutualFundsOnboardingDetailsRequest getMutualFundOnboardingDetailsRequest) {
        // TODO: Make EventStatus optional, for a given crn and eventType return the latest event details if eventStatus is empty
        MutualFundsOnboardingDetailsRequest mutualFundOnboardingDetailsResponse = mutualFundsOnboardingDetailsService.getCrnByEventTypeEventStatusAndCreatedAtDesc(getMutualFundOnboardingDetailsRequest);
        return ResponseEntity.status(HttpStatus.OK).body(ResponseWrapper.success(mutualFundOnboardingDetailsResponse));
    }
}
