package com.kotak.unified.dbservice.controller;

import apiinterface.PgnResource;
import com.kotak.unified.common.response.ErrorResponse;
import com.kotak.unified.db.request.pgn.AssignPgnRequest;
import com.kotak.unified.db.request.pgn.InsertPgnRequest;
import com.kotak.unified.db.response.pgn.AssignPgnResponse;
import com.kotak.unified.db.response.pgn.InsertPgnResponse;
import com.kotak.unified.dbservice.exceptions.EntityExistsException;
import com.kotak.unified.dbservice.exceptions.PgnDepletedException;
import com.kotak.unified.dbservice.service.PgnService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
@CrossOrigin(origins = "*")
public class PgnController implements PgnResource {

    private final PgnService pgnService;

    @Autowired
    public PgnController(PgnService pgnService) {
        this.pgnService = pgnService;
    }

    @Override
    public ResponseEntity<?> assignPgn(AssignPgnRequest assignPgnRequest) {
        log.info("Received request to assign PGN, PGN type: {} and lead Id : {}",
                assignPgnRequest.getPgnType(), assignPgnRequest.getLeadTrackingNumber());
        try {
            AssignPgnResponse assignPgnResponse = pgnService.assignPgn(assignPgnRequest);
            return new ResponseEntity<>(assignPgnResponse, HttpStatus.OK);
        } catch (PgnDepletedException e) {
            log.error("PGN DEPLETED, encountered error in ASSIGN PGN API: ", e);
            ErrorResponse errorResponse = ErrorResponse.builder().errorCode("PGN_DEPLETED").errorMessage(e.getMessage()).build();
            return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        } catch (Exception e) {
            log.error("Unexpected error, encountered error in ASSIGN PGN API: ", e);
            ErrorResponse errorResponse = ErrorResponse.builder().errorCode("UNEXPECTED_ERROR").errorMessage(e.getMessage()).build();
            return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<?> insertPgn(InsertPgnRequest insertPgnRequest) {
        try {
            InsertPgnResponse insertPgnResponse = pgnService.insertPgn(insertPgnRequest);
            return new ResponseEntity<>(insertPgnResponse, HttpStatus.OK);
        } catch (EntityExistsException e) {
            log.error("PGN record already exists, ", e);
            ErrorResponse errorResponse = ErrorResponse.builder().errorCode("ENTITY_ALREADY_EXISTS").errorMessage(e.getMessage()).build();
            return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        } catch (Exception e) {
            log.error("Unexpected error, encountered error in ASSIGN PGN API: ", e);
            ErrorResponse errorResponse = ErrorResponse.builder().errorCode("UNEXPECTED_ERROR").errorMessage(e.getMessage()).build();
            return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
