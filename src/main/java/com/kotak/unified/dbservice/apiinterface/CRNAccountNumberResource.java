package com.kotak.unified.dbservice.apiinterface;

import com.kotak.unified.db.request.AssignCACRNAndAccountNumberRequest;
import com.kotak.unified.db.request.InsertCACrnAndAccountNumberRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/v1")
public interface CRNAccountNumberResource {

    @RequestMapping(value = "/add-ca-crn-and-account-number", method = RequestMethod.POST)
    ResponseEntity<?> addCACrnAndAccountNumber(@RequestBody InsertCACrnAndAccountNumberRequest insertRequest);

    @RequestMapping(value = "/assign-ca-crn-and-account-number", method = RequestMethod.POST)
    ResponseEntity<?> assignCACrnAndAccountNumber(@RequestBody AssignCACRNAndAccountNumberRequest assignRequest);
}
