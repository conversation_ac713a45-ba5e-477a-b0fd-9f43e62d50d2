package com.kotak.unified.dbservice.config;

import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.validator.constraints.URL;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.validation.annotation.Validated;

@Configuration
@ConfigurationProperties(prefix = "data.change.notification")
@Getter
@Setter
@NoArgsConstructor
@Validated
public class DataChangeNotificationConfiguration {
    @NotBlank
    @URL
    private String queueUrl;
}