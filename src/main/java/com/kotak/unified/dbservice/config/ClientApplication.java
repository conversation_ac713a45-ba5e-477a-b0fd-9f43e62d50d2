package com.kotak.unified.dbservice.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import jakarta.inject.Named;
import jakarta.inject.Singleton;
import org.modelmapper.ModelMapper;
import org.modelmapper.convention.MatchingStrategies;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProviderChain;
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.core.internal.http.loader.DefaultSdkHttpClientBuilder;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.extensions.AutoGeneratedTimestampRecordExtension;
import software.amazon.awssdk.enhanced.dynamodb.extensions.VersionedRecordExtension;
import software.amazon.awssdk.http.SdkHttpClient;
import software.amazon.awssdk.http.SdkHttpConfigurationOption;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;
import software.amazon.awssdk.services.sqs.SqsClient;
import software.amazon.awssdk.utils.AttributeMap;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import javax.ws.rs.client.Client;
import javax.ws.rs.client.ClientBuilder;
import javax.ws.rs.client.WebTarget;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;

@Configuration
public class ClientApplication {
    @Value("${bcif.BCIF_SERVICE_ENDPOINT_URL}")
    private String bcifServiceEndpointUrl;

    @Value("${TRUST_ALL_CERTS}")
    private String trustAllCerts;

    @Value("${AWS_REGION}")
    private String awsRegion;

    private static final String TRUE_STR = "true";

    @Bean
    @Named("BcifServiceWebTarget")
    public WebTarget getBcifServiceClient() throws NoSuchAlgorithmException, KeyManagementException {
        return getCommonWebTarget(bcifServiceEndpointUrl);
    }

    private WebTarget getCommonWebTarget(String url) throws NoSuchAlgorithmException, KeyManagementException {
        TrustManager[] trustManager = null;
        if (TRUE_STR.equals(trustAllCerts)) {
            trustManager = new TrustManager[]{
                    new X509TrustManager() {
                        @Override
                        public void checkClientTrusted(java.security.cert.X509Certificate[] chain, String authType) {
                        }

                        @Override
                        public void checkServerTrusted(java.security.cert.X509Certificate[] chain, String authType) {
                        }

                        @Override
                        public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                            return new java.security.cert.X509Certificate[]{};
                        }
                    }
            };
        }
        SSLContext sslContext = SSLContext.getInstance("TLS");
        sslContext.init(null, trustManager, new java.security.SecureRandom());
        ClientBuilder clientBuilder = ClientBuilder.newBuilder()
                .sslContext(sslContext)
                .hostnameVerifier((hostname, session) -> true);
        Client client = clientBuilder.build();
        return client.target(url);
    }

    @Bean
    public ModelMapper getModelMapper() {
        ModelMapper modelMapper = new ModelMapper();
        modelMapper.getConfiguration().setMatchingStrategy(MatchingStrategies.STRICT);
        return modelMapper;
    }

    @Bean
    public AwsCredentialsProviderChain getAwsCredentials() {
        return AwsCredentialsProviderChain.of(DefaultCredentialsProvider.create());
    }

    @Bean
    public DynamoDbEnhancedClient getDynamoDbEnhancedClient(AwsCredentialsProviderChain awsCredentialsProviderChain) {
        DynamoDbClient dynamoDbClient = DynamoDbClient.builder()
                .credentialsProvider(awsCredentialsProviderChain)
                .region(Region.of(awsRegion))
                .build();
        return DynamoDbEnhancedClient.builder()
              .dynamoDbClient(dynamoDbClient)
              .extensions(AutoGeneratedTimestampRecordExtension.create(),
                      VersionedRecordExtension.builder().build())
              .build();
    }

    @Bean
    public SqsClient getSQSClient() {
        final AttributeMap attributeMap = AttributeMap.builder()
                .put(SdkHttpConfigurationOption.TRUST_ALL_CERTIFICATES, true)
                .build();
        final SdkHttpClient sdkHttpClient = new DefaultSdkHttpClientBuilder().buildWithDefaults(attributeMap);

        return SqsClient.builder()
                .region(Region.of(awsRegion))
                .httpClient(sdkHttpClient)
                .build();
    }
}
