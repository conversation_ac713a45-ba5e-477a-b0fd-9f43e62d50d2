package com.kotak.unified.dbservice.config;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.NonNull;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "aws.dynamo")
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class DynamoAWSConfiguration {

    @NonNull
    private String asyncProcessTableName;

    @NonNull
    private String apiResponseDetailsTableName;

    @NonNull
    private String videoKycStatusTableName;

    @NonNull
    private String transactionTableName;

    @NonNull
    private String userJourneyStatusTableName;

    @NonNull
    private String applicationDataTableName;

    @NonNull
    private String cpvDvuVerificationDetailsTableName;

    @NonNull
    private String pgnTableName;

    @NonNull
    private String preferredAccountNumberTableName;

    @NonNull
    private String mutualFundsOnboardingDetailsTableName;
}
