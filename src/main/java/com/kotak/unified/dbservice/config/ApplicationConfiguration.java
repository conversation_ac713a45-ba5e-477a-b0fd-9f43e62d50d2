package com.kotak.unified.dbservice.config;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.kotak.unified.common.utils.CommonRequestResponseLoggerAsync;
import com.kotak.unified.common.utils.S3AsyncAccessor;
import jakarta.inject.Named;
import jakarta.inject.Singleton;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import software.amazon.awssdk.core.internal.http.loader.DefaultSdkHttpClientBuilder;
import software.amazon.awssdk.http.SdkHttpClient;
import software.amazon.awssdk.http.TlsTrustManagersProvider;
//import software.amazon.awssdk.http.nio.netty.TrustAllTlsTrustManagersProvider;
import software.amazon.awssdk.http.SdkHttpConfigurationOption;
import software.amazon.awssdk.http.async.SdkAsyncHttpClient;
import software.amazon.awssdk.http.nio.netty.NettyNioAsyncHttpClient;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3AsyncClient;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.utils.AttributeMap;

@Configuration
public class ApplicationConfiguration {

    @Value("${AWS_REGION}")
    private String awsRegion;
    @Bean
    @Singleton
    @Named("JsonObjectMapper")
    @Primary
    public ObjectMapper getObjectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        return objectMapper;
    }

    @Bean
    @Named("S3Client")
    public S3Client getS3Client() {
        final AttributeMap attributeMap = AttributeMap.builder()
                .put(SdkHttpConfigurationOption.TRUST_ALL_CERTIFICATES, true)
                .build();
        final SdkHttpClient sdkHttpClient = new DefaultSdkHttpClientBuilder().buildWithDefaults(attributeMap);

        return S3Client.builder()
                .region(Region.of(awsRegion))
                .httpClient(sdkHttpClient)
                .build();
    }

    @Bean
    @Named("S3AsyncClient")
    public S3AsyncClient getS3AsyncClient() {
        SdkAsyncHttpClient httpClient = NettyNioAsyncHttpClient.builder()
//                .tlsTrustManagersProvider(TrustAllTlsTrustManagersProvider.create())
                .build();
        return S3AsyncClient.builder()
                .httpClient(httpClient)
                .region(Region.of(awsRegion))
                .build();
    }

    @Bean
    public CommonRequestResponseLoggerAsync getCommonRequestResponseLoggerAsync(
            @Named("S3AsyncClient") S3AsyncClient s3AsyncClient,
            @Value("${S3_REQUEST_RESPONSE_BUCKET_NAME}") String S3_BUCKET_NAME,
            @Value("${S3_REQUEST_RESPONSE_LOG_FILEPATH}") String S3_REQUEST_RESPONSE_LOG_FILEPATH,
            @Named("JsonObjectMapper") ObjectMapper objectMapper) {
        return new CommonRequestResponseLoggerAsync(new S3AsyncAccessor(s3AsyncClient, S3_BUCKET_NAME), S3_REQUEST_RESPONSE_LOG_FILEPATH, objectMapper);
    }
}
