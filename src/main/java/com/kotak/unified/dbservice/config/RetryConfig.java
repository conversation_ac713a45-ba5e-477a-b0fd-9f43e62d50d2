package com.kotak.unified.dbservice.config;

import com.kotak.unified.dbservice.exceptions.CrnActivatedException;
import com.kotak.unified.dbservice.exceptions.PgnAssignmentException;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.retry.backoff.ExponentialRandomBackOffPolicy;
import org.springframework.retry.policy.SimpleRetryPolicy;
import org.springframework.retry.support.RetryTemplate;
import software.amazon.awssdk.services.dynamodb.model.ConditionalCheckFailedException;

import java.util.HashMap;
import java.util.Map;

@Configuration
public class RetryConfig {

    @Bean
    public RetryTemplate retryTemplate() {
        RetryTemplate retryTemplate = new RetryTemplate();

        ExponentialRandomBackOffPolicy backOffPolicy = new ExponentialRandomBackOffPolicy();
        backOffPolicy.setInitialInterval(500);//setting initial delay of 0.5ms
        backOffPolicy.setMaxInterval(3000);//setting maximum delay of 3s
        backOffPolicy.setMultiplier(2.0);//increasing delay by 2x each time
        retryTemplate.setBackOffPolicy(backOffPolicy);

        Map<Class<? extends Throwable>, Boolean> exceptionMap = new HashMap<>();
        exceptionMap.put(ConditionalCheckFailedException.class, Boolean.TRUE);
        exceptionMap.put(CrnActivatedException.class, Boolean.TRUE);
        exceptionMap.put(PgnAssignmentException.class, Boolean.TRUE);
        SimpleRetryPolicy retryPolicy = new SimpleRetryPolicy(3, exceptionMap);
        retryTemplate.setRetryPolicy(retryPolicy);

        return retryTemplate;
    }
}
