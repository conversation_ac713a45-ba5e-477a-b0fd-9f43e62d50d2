package com.kotak.unified.dbservice.service;

import com.kotak.unified.db.model.CreateResumeJourneyNotificationTrackNextCheckRecordRequest;
import com.kotak.unified.db.model.CreateResumeJourneyNotificationTrackNextCheckRecordResponse;
import com.kotak.unified.db.model.GetResumeJourneyNotificationTrackNextCheckRecordResponse;
import com.kotak.unified.db.model.UpdateResumeJourneyNotificationTrackNextCheckRecordRequest;
import com.kotak.unified.db.model.UpdateResumeJourneyNotificationTrackNextCheckRecordResponse;
import com.kotak.unified.dbservice.enums.ErrorCause;
import com.kotak.unified.dbservice.exceptions.custom.RestException;
import com.kotak.unified.dbservice.model.exception.DatabaseErrorResponse;
import com.kotak.unified.dbservice.repository.ResumeJourneyNotificationTrackNextCheckRepository;
import com.kotak.unified.dbservice.transformer.ResumeJourneyNotificationTrackNextCheckTransformer;
import com.kotak.unified.orchestrator.common.dbmodels.resumenotification.ResumeJourneyNotificationTrackNextCheck;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.services.dynamodb.model.ConditionalCheckFailedException;

@Slf4j
@Service
@AllArgsConstructor
public class ResumeJourneyNotificationTrackNextCheckService {

    @Autowired
    private final ResumeJourneyNotificationTrackNextCheckRepository resumeJourneyNotificationTrackNextCheckRepository;
    @Autowired
    private final ResumeJourneyNotificationTrackNextCheckTransformer resumeJourneyNotificationTrackNextCheckTransformer;
    public CreateResumeJourneyNotificationTrackNextCheckRecordResponse createResumeJourneyNotificationTrackNextCheckRecord(CreateResumeJourneyNotificationTrackNextCheckRecordRequest createResumeJourneyNotificationTrackNextCheckRecordRequest) {
        ResumeJourneyNotificationTrackNextCheck resumeJourneyNotificationTrackNextCheckRecord = resumeJourneyNotificationTrackNextCheckTransformer
                .convertRequestToResumeJourneyNotificationTrackNextCheckRecord(createResumeJourneyNotificationTrackNextCheckRecordRequest);
        try {
            resumeJourneyNotificationTrackNextCheckRepository.putRecord(resumeJourneyNotificationTrackNextCheckRecord);
        } catch (ConditionalCheckFailedException e) {
            log.error("Encountered ConditionalCheckFailedException while creating createResumeJourneyNotificationTrackNextCheckRecord, " +
                            "ResumeJourneyNotification Record with leadTrackingNumber {} already exists",
                    createResumeJourneyNotificationTrackNextCheckRecordRequest.getLeadTrackingNumber(), e);
            throw new RestException(
                    HttpStatus.CONFLICT,
                    DatabaseErrorResponse.fromErrorCode(ErrorCause.RESUME_NOTIFICATION_TRACK_NEXT_CHECK_RECORD_ALREADY_EXISTS)
            );
        }
        return resumeJourneyNotificationTrackNextCheckTransformer.getCreateResumeJourneyNotificationTrackNextCheckRecordResponse(true);
    }

    public GetResumeJourneyNotificationTrackNextCheckRecordResponse getResumeJourneyNotificationTrackNextCheckRecord(String leadTrackingNumber) {
        ResumeJourneyNotificationTrackNextCheck resumeJourneyNotificationTrackNextCheck = resumeJourneyNotificationTrackNextCheckRepository.getRecord(leadTrackingNumber)
                .orElseThrow(() -> {
                    log.warn("ResumeJourneyNotificationTrackNextCheck Record doesn't exists for leadTrackingNumber {} ", leadTrackingNumber);
                    return new RestException(
                            HttpStatus.NOT_FOUND,
                            DatabaseErrorResponse.fromErrorCode(ErrorCause.RESUME_NOTIFICATION_TRACK_NEXT_CHECK_RECORD_NOT_FOUND)
                    );
                });
        return resumeJourneyNotificationTrackNextCheckTransformer.convertModelToGetResumeJourneyNotificationTrackNextCheckRecordResponse(resumeJourneyNotificationTrackNextCheck);
    }

    public UpdateResumeJourneyNotificationTrackNextCheckRecordResponse updateResumeJourneyNotificationTrackNextCheckRecord(String leadTrackingNumber, UpdateResumeJourneyNotificationTrackNextCheckRecordRequest updateResumeJourneyNotificationTrackNextCheckRecordRequest) {
        ResumeJourneyNotificationTrackNextCheck resumeJourneyNotificationTrackNextCheck = resumeJourneyNotificationTrackNextCheckRepository.getRecord(leadTrackingNumber)
                .orElseThrow(() -> {
                    log.error("ResumeJourneyNotificationTrackNextCheck Record doesn't exists for leadTrackingNumber {}", leadTrackingNumber);
                    return new RestException(
                            HttpStatus.NOT_FOUND,
                            DatabaseErrorResponse.fromErrorCode(ErrorCause.RESUME_NOTIFICATION_TRACK_NEXT_CHECK_RECORD_NOT_FOUND)
                    );
                });

        resumeJourneyNotificationTrackNextCheckTransformer.updateResumeJourneyNotificationTrackNextCheckDetails(resumeJourneyNotificationTrackNextCheck, updateResumeJourneyNotificationTrackNextCheckRecordRequest);

        try {
            resumeJourneyNotificationTrackNextCheckRepository.updateRecord(resumeJourneyNotificationTrackNextCheck);
        } catch (ConditionalCheckFailedException e) {
            log.error("Encountered ConditionalCheckFailedException while updating ResumeJourneyNotificationTrackNextCheckRecord, " +
                            "for record with leadTrackingNumber {} and version {}",
                    leadTrackingNumber, resumeJourneyNotificationTrackNextCheck.getVersion(), e);
            throw new RestException(
                    HttpStatus.CONFLICT,
                    DatabaseErrorResponse.fromErrorCode(ErrorCause.RESUME_NOTIFICATION_TRACK_NEXT_CHECK_RECORD_VERSION_CONFLICT)
            );
        }

        return UpdateResumeJourneyNotificationTrackNextCheckRecordResponse.builder()
                .isUpdateRecordSuccessful(true)
                .build();
    }
}
