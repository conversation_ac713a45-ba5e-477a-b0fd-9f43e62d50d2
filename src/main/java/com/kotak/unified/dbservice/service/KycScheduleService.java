package com.kotak.unified.dbservice.service;

import com.kotak.unified.common.request.database.KycSchedulingRequest;
import com.kotak.unified.db.KycSchedulingDetailsResponse;
import com.kotak.unified.db.KycSchedulingResponse;



public interface KycScheduleService {
    KycSchedulingResponse createKycSchedule(KycSchedulingRequest kycSchedulingRequest);

    KycSchedulingResponse updateKycSchedule(KycSchedulingRequest kycSchedulingRequest);

    KycSchedulingResponse getKycScheduleForLeadTrackingId(String leadTrackingId);

    KycSchedulingDetailsResponse getKycSchedulesBasedOnTimestamp(Long timestamp, String comparisonOperator);
}
