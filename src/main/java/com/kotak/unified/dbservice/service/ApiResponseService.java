package com.kotak.unified.dbservice.service;

import com.kotak.unified.db.ApiResponseDetailsDto;
import com.kotak.unified.dbservice.enums.ErrorCause;
import com.kotak.unified.dbservice.exceptions.custom.RestException;
import com.kotak.unified.dbservice.model.exception.DatabaseErrorResponse;
import com.kotak.unified.dbservice.repository.ApiResponseDetailsDDBRepository;
import com.kotak.unified.orchestrator.common.dbmodels.asyncprocessing.ApiResponseDetailsDDBModel;
import com.kotak.unified.orchestrator.library.sqsmodels.ApiName;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ApiResponseService {
    private final ApiResponseDetailsDDBRepository apiResponseDetailsDDBRepository;

    @Autowired
    public ApiResponseService(ApiResponseDetailsDDBRepository apiResponseDetailsDDBRepository) {
        this.apiResponseDetailsDDBRepository = apiResponseDetailsDDBRepository;
    }

    public ApiResponseDetailsDto getApiResponseData(
            String leadTrackingNumber, String apiNameString) {
        ApiName apiName;
        try {
            apiName = ApiName.valueOf(apiNameString);
        } catch (IllegalArgumentException e) {
            throw new RestException(HttpStatus.BAD_REQUEST, DatabaseErrorResponse
                    .fromErrorCode(ErrorCause.INVALID_API_NAME));
        }

        ApiResponseDetailsDDBModel apiResponseDDBModel = apiResponseDetailsDDBRepository.getByLeadTrackingNumberAndApiName(leadTrackingNumber, apiName);
        if (apiResponseDDBModel == null) {
            throw new RestException(HttpStatus.NOT_FOUND, DatabaseErrorResponse
                    .fromErrorCode(ErrorCause.API_RESPONSE_NOT_FOUND));
        }
        return convertDBModelToResponseEntity(apiResponseDDBModel);
    }

    private ApiResponseDetailsDto convertDBModelToResponseEntity(ApiResponseDetailsDDBModel apiResponseDetailsDDBModel) {
        return ApiResponseDetailsDto.builder()
                .content(apiResponseDetailsDDBModel.getContent())
                .apiName(apiResponseDetailsDDBModel.getApiName().toString())
                .leadTrackingNumber(apiResponseDetailsDDBModel.getLeadTrackingNumber())
                .build();
    }
}
