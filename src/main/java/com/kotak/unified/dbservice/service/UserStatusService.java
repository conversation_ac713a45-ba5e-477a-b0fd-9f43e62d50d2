package com.kotak.unified.dbservice.service;

import com.kotak.unified.databridgeinterface.enums.DataSource;
import com.kotak.unified.db.BusinessLoanJourneyMetadataResponse;
import com.kotak.unified.db.DormantAccountJourneyMetadataResponse;
import com.kotak.unified.db.JourneyMetadataResponse;
import com.kotak.unified.db.PaydayLoanJourneyMetadataResponse;
import com.kotak.unified.db.PersonalLoanJourneyMetadataResponse;
import com.kotak.unified.db.SavingsAccountJourneyMetadataResponse;
import com.kotak.unified.db.UserStatusResponse;
import com.kotak.unified.dbservice.enums.ErrorCause;
import com.kotak.unified.dbservice.exceptions.custom.RestException;
import com.kotak.unified.dbservice.helper.impl.UserStatusDataBridgeHelper;
import com.kotak.unified.dbservice.model.exception.DatabaseErrorResponse;
import com.kotak.unified.dbservice.repository.UserStatusFacade;
import com.kotak.unified.dbservice.transformer.UserStatusTransformer;
import com.kotak.unified.orchestrator.common.dbmodels.BusinessLoanJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.DormantAccountActivationJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.JourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.PaydayLoanJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.PersonalLoanJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.SavingsAccountJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.UserStatus;
import com.kotak.unified.request.PatchUserJourneyStatusRequest;
import com.kotak.unified.request.PatchUserJourneyStatusResponse;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class UserStatusService {
    private final UserStatusFacade userStatusFacade;
    private final UserStatusTransformer userStatusTransformer;
    private final ModelMapper modelMapper;
    private final UserStatusDataBridgeHelper userStatusDataBridgeHelper;

    @Autowired
    public UserStatusService(UserStatusFacade userStatusFacade,
                             ModelMapper modelMapper,
                             UserStatusTransformer userStatusTransformer, UserStatusDataBridgeHelper userStatusDataBridgeHelper) {
        this.userStatusFacade = userStatusFacade;
        this.modelMapper = modelMapper;
        this.userStatusTransformer = userStatusTransformer;
        this.userStatusDataBridgeHelper = userStatusDataBridgeHelper;
    }

    public UserStatusResponse getUserStatus(String leadTrackingNumber) {
        UserStatus userStatus = this.userStatusFacade.findByLeadTrackingNumber(leadTrackingNumber);
        if (userStatus == null) {
            throw new RestException(HttpStatus.NOT_FOUND, DatabaseErrorResponse
                    .fromErrorCode(ErrorCause.LEAD_TRACKING_NUMBER_NOT_FOUND));
        }

        return this.convertDBModelToResponseEntity(userStatus);
    }

    public PatchUserJourneyStatusResponse updateUserStatus(String leadTrackingNumber, PatchUserJourneyStatusRequest request) {
        UserStatus userStatus = this.userStatusFacade.findByLeadTrackingNumber(leadTrackingNumber);

        if (userStatus == null) {
            throw new RestException(HttpStatus.NOT_FOUND, DatabaseErrorResponse
                    .fromErrorCode(ErrorCause.LEAD_TRACKING_NUMBER_NOT_FOUND),
                    new Exception("LeadTrackingNumber not found : " + leadTrackingNumber));
        }

        log.info("Received db-update for lead : " + leadTrackingNumber
                + " for fields : " + request.getUpdates().keySet());

        UserStatus patchedUserStatus = this.userStatusTransformer.applyPatch(userStatus, request.getUpdates());
        this.userStatusFacade.save(patchedUserStatus);
        try {
            this.userStatusDataBridgeHelper.publishToDataBridge(userStatus, DataSource.USER_JOURNEY_STATUS);
        } catch (Exception ignored) {

        }

        log.info("Successfully updated db for lead : " + leadTrackingNumber);
        return PatchUserJourneyStatusResponse.builder().isPatchSuccessful(true).build();
    }

    private UserStatusResponse convertDBModelToResponseEntity(UserStatus userStatus) {
        // ToDo - <EMAIL>
        //  Evaluate if modelmapper takes more latency and see if we need to have builder in code directly
        // https://dev.azure.com/kmbl-devops/Kotak-Uao/_workitems/edit/185272
        UserStatusResponse response = modelMapper.map(userStatus, UserStatusResponse.class);
        response.setJourneyMetadata(this.getJourneyMetadataResponse(userStatus.getJourneyMetadata()));
        return response;
    }

    private JourneyMetadataResponse getJourneyMetadataResponse(JourneyMetadata journeyMetadata) {

        if ((journeyMetadata instanceof SavingsAccountJourneyMetadata savingsAccountJourneyMetadata)) {
            return modelMapper.map(savingsAccountJourneyMetadata, SavingsAccountJourneyMetadataResponse.class);
        } else if (journeyMetadata instanceof BusinessLoanJourneyMetadata) {
            return new BusinessLoanJourneyMetadataResponse();
        } else if (journeyMetadata instanceof PersonalLoanJourneyMetadata personalLoanJourneyMetadata) {
            return modelMapper.map(personalLoanJourneyMetadata, PersonalLoanJourneyMetadataResponse.class);
        } else if (journeyMetadata instanceof PaydayLoanJourneyMetadata paydayLoanJourneyMetadata) {
            return modelMapper.map(paydayLoanJourneyMetadata, PaydayLoanJourneyMetadataResponse.class);
        } else if (journeyMetadata instanceof DormantAccountActivationJourneyMetadata dormantAccountActivationJourneyMetadata) {
            return modelMapper.map(dormantAccountActivationJourneyMetadata, DormantAccountJourneyMetadataResponse.class);
        }
        return null;
    }
}
