package com.kotak.unified.dbservice.service;

import com.kotak.unified.db.vkycStatusResponse.VideoKycDetailsResponse;
import com.kotak.unified.dbservice.enums.ErrorCause;
import com.kotak.unified.dbservice.exceptions.custom.RestException;
import com.kotak.unified.dbservice.model.exception.DatabaseErrorResponse;
import com.kotak.unified.dbservice.repository.VideoKYCStatusFacade;
import com.kotak.unified.orchestrator.common.dbmodels.vkycStatusModel.VideoKycDetails;
import lombok.NonNull;
import org.modelmapper.ModelMapper;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
@Service
public class VideoKycDetailsService {
    private final VideoKYCStatusFacade videoKYCStatusFacade;
    private final ModelMapper modelMapper;
    public VideoKycDetailsService(@NonNull final VideoKYCStatusFacade videoKYCStatusFacade,
                                  @NonNull final ModelMapper modelMapper) {
        this.videoKYCStatusFacade = videoKYCStatusFacade;
        this.modelMapper = modelMapper;
    }

    public VideoKycDetailsResponse getVideoKycDetails(@NonNull final String kycTrackingId) {
        VideoKycDetails videoKycDetails = videoKYCStatusFacade.findByTrackingId(kycTrackingId);
        if (videoKycDetails == null) {
            throw new RestException(HttpStatus.NOT_FOUND, DatabaseErrorResponse
                    .fromErrorCode(ErrorCause.KYC_TRACKING_ID_NOT_FOUND));
        }
        VideoKycDetailsResponse videoKycDetailsResponse = convertDBModelToResponseEntity(videoKycDetails);

        return videoKycDetailsResponse;
    }
    public VideoKycDetailsResponse save(@NonNull final VideoKycDetailsResponse videoKycDetailsRequest) {
        VideoKycDetails videoKycDetails = modelMapper.map(videoKycDetailsRequest, VideoKycDetails.class);
        VideoKycDetails savedVideoKycDetails = videoKYCStatusFacade.save(videoKycDetails);
        return convertDBModelToResponseEntity(savedVideoKycDetails);
    }

    public VideoKycDetailsResponse getTopByLeadTrackingNumberOrderByCreatedAtDesc(@NonNull final String leadTrackingNumber) {
        VideoKycDetails videoKycDetails = videoKYCStatusFacade.findTopByLeadTrackingNumberOrderByCreatedAtDesc(leadTrackingNumber);
        if (videoKycDetails == null) {
            throw new RestException(HttpStatus.NOT_FOUND, DatabaseErrorResponse
                    .fromErrorCode(ErrorCause.LEAD_TRACKING_NUMBER_NOT_FOUND));
        }
        return convertDBModelToResponseEntity(videoKycDetails);
    }

    public VideoKycDetailsResponse getTopByLeadTrackingNumberAndLatestStatusOrderByCreatedAt(
            @NonNull final String leadTrackingNumber, @NonNull final String latestStatus) {
        VideoKycDetails videoKycDetails = videoKYCStatusFacade
                .findTopByLeadTrackingNumberAndLatestStatusOrderByCreatedAt(leadTrackingNumber,
                        latestStatus);
        if (videoKycDetails == null) {
            throw new RestException(HttpStatus.NOT_FOUND, DatabaseErrorResponse
                    .fromErrorCode(ErrorCause.INTERNAL_SERVER_ERROR));
        }
        return convertDBModelToResponseEntity(videoKycDetails);
    }

    private VideoKycDetailsResponse convertDBModelToResponseEntity(VideoKycDetails videoKycDetails) {
        return modelMapper.map(videoKycDetails, VideoKycDetailsResponse.class);
    }
}
