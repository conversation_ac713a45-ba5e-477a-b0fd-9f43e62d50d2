package com.kotak.unified.dbservice.service;

import com.kotak.unified.db.request.AssignCACRNAndAccountNumberRequest;
import com.kotak.unified.db.request.InsertCACrnAndAccountNumberRequest;
import com.kotak.unified.db.response.AssignCACRNAndAccountNumberResponse;
import com.kotak.unified.dbservice.exceptions.EntityExistsException;
import com.kotak.unified.dbservice.exceptions.PregeneratedCRNAccountNumDepletedException;
import com.kotak.unified.dbservice.model.CAPreGeneratedCRNAccountNumber;
import com.kotak.unified.dbservice.model.PGNStatus;
import com.kotak.unified.dbservice.repository.CAPreGeneratedCRNAccountNumRepository;
import com.kotak.unified.dbservice.utils.Constants;
import com.kotak.unified.dbservice.validator.CAPgnRequestValidator;
import com.kotak.unified.enums.CAJourneyType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.awscore.exception.AwsServiceException;
import software.amazon.awssdk.services.dynamodb.model.TransactionCanceledException;
import software.amazon.awssdk.services.dynamodb.model.TransactionConflictException;

import java.util.UUID;

@Service
@Slf4j
public class CAPreGeneratedCRNAccountNumService {

    private final CAPreGeneratedCRNAccountNumRepository caPreGeneratedCRNAccountNumRepository;

    public CAPreGeneratedCRNAccountNumService(CAPreGeneratedCRNAccountNumRepository caPreGeneratedCRNAccountNumRepository) {
        this.caPreGeneratedCRNAccountNumRepository = caPreGeneratedCRNAccountNumRepository;
    }

    public void insertCrnAndAccountNumber(InsertCACrnAndAccountNumberRequest insertRequest) throws EntityExistsException {

        CAPgnRequestValidator.validateInsertPGNRequest(insertRequest);

        CAJourneyType journeyType = insertRequest.getJourneyType();

        CAPreGeneratedCRNAccountNumber.CAPreGeneratedCRNAccountNumberBuilder builder = CAPreGeneratedCRNAccountNumber.builder();

        if ((journeyType == CAJourneyType.INDIVIDUAL_NON_PREFERRED_CURRENT_ACCOUNT || journeyType == CAJourneyType.BUSINESS_NON_PREFERRED_CURRENT_ACCOUNT)
                && caPreGeneratedCRNAccountNumRepository.doesAccountNumberExists(insertRequest.getAccountNumber())) {
            throw new EntityExistsException(String.format("Input account number: %s already exists in the DB", insertRequest.getAccountNumber()));
        }

        if (caPreGeneratedCRNAccountNumRepository.doesAusCrnExists(insertRequest.getCrn())) {
            throw new EntityExistsException(String.format("Input crn: %s already exists in the DB", insertRequest.getCrn()));
        }

        if ((journeyType == CAJourneyType.BUSINESS_NON_PREFERRED_CURRENT_ACCOUNT || journeyType == CAJourneyType.BUSINESS_PREFERRED_CURRENT_ACCOUNT)
                && caPreGeneratedCRNAccountNumRepository.doesEntityCrnExists(insertRequest.getEntityCrn())) {
            throw new EntityExistsException(String.format("Input entity crn: %s already exists in the DB", insertRequest.getEntityCrn()));
        }

        if(journeyType == CAJourneyType.BUSINESS_NON_PREFERRED_CURRENT_ACCOUNT || journeyType == CAJourneyType.BUSINESS_PREFERRED_CURRENT_ACCOUNT){
            builder.entityCrn(insertRequest.getEntityCrn());
        }

        if (journeyType == CAJourneyType.BUSINESS_NON_PREFERRED_CURRENT_ACCOUNT || journeyType == CAJourneyType.INDIVIDUAL_NON_PREFERRED_CURRENT_ACCOUNT){
            builder.accountNumber(insertRequest.getAccountNumber());
        }

        CAPreGeneratedCRNAccountNumber caPreGeneratedCRNAccountNumber = builder
                .id(UUID.randomUUID().toString())
                .crn(insertRequest.getCrn())
                .status(PGNStatus.UN_ASSIGNED)
                .journeyType(insertRequest.getJourneyType())
                .build();

        try {
            caPreGeneratedCRNAccountNumRepository.insertRecord(caPreGeneratedCRNAccountNumber);
        } catch (Exception e){
            log.error("DB error occurred while inserting crn: {}, account number: {}, entity crn: {} " +
                            "with message: {}", insertRequest.getCrn(), insertRequest.getAccountNumber(),
                    insertRequest.getEntityCrn(), e.getMessage());
            throw AwsServiceException.builder().message(String.format("DB error occurred while inserting crn: %s, " +
                            "account number: %s, entity crn: %s", insertRequest.getCrn(),
                    insertRequest.getAccountNumber(), insertRequest.getEntityCrn()))
                    .cause(e).build();
        }
    }
    
    public AssignCACRNAndAccountNumberResponse assignAccountNumber(AssignCACRNAndAccountNumberRequest assignRequest) throws EntityExistsException, PregeneratedCRNAccountNumDepletedException {

        if (caPreGeneratedCRNAccountNumRepository.doesLeadTrackingNumberExists(assignRequest.getLeadTrackingNumber())) {
            log.error("LeadTrackingNumber: {} is already assigned a pgn", assignRequest.getLeadTrackingNumber());
            throw new EntityExistsException("LeadTrackingNumber is already assigned a pgn");
        }
        
        CAPreGeneratedCRNAccountNumber assignedPreGeneratedCRNAccountNum = null;

        try {
            assignedPreGeneratedCRNAccountNum = getNotActivatedPreGeneratedCRNAccountNum(assignRequest);
        } catch (Exception e){
            log.error("DB error occurred while processing processing lead tracking number: {}, journey type: {}",
                            assignRequest.getLeadTrackingNumber(), assignRequest.getJourneyType());
            throw AwsServiceException.builder().message(String.format("DB error occurred while processing lead tracking number: " +
                    "%s, journey type: %s", assignRequest.getLeadTrackingNumber(), assignRequest.getJourneyType()))
                    .cause(e).build();
        }

        return AssignCACRNAndAccountNumberResponse.builder()
                .accountNumber(assignedPreGeneratedCRNAccountNum.getAccountNumber())
                .crn(assignedPreGeneratedCRNAccountNum.getCrn())
                .entityCrn(assignedPreGeneratedCRNAccountNum.getEntityCrn())
                .journeyType(assignRequest.getJourneyType())
                .success(true)
                .build();
    }

    @Retryable(
            retryFor = {TransactionCanceledException.class, TransactionConflictException.class},
            maxAttemptsExpression = Constants.RETRY_MAX_ATTEMPTS_EXPRESSION
    )
    private CAPreGeneratedCRNAccountNumber getNotActivatedPreGeneratedCRNAccountNum(AssignCACRNAndAccountNumberRequest assignPgnRequest) throws PregeneratedCRNAccountNumDepletedException {

        return caPreGeneratedCRNAccountNumRepository.assignPregeneratedCRNAccountNum(
                assignPgnRequest.getLeadTrackingNumber(), assignPgnRequest.getJourneyType());
    }

}
