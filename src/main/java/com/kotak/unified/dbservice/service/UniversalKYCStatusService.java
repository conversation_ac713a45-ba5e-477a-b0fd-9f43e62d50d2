package com.kotak.unified.dbservice.service;

import com.kotak.unified.db.model.CreateUKYCRecordRequest;
import com.kotak.unified.db.model.CreateUKYCRecordResponse;
import com.kotak.unified.db.model.GetUKYCRecordResponse;
import com.kotak.unified.db.model.UpdateUKYCRecordRequest;
import com.kotak.unified.db.model.UpdateUKYCRecordResponse;
import com.kotak.unified.dbservice.enums.ErrorCause;
import com.kotak.unified.dbservice.exceptions.custom.RestException;
import com.kotak.unified.dbservice.model.exception.DatabaseErrorResponse;
import com.kotak.unified.dbservice.repository.UniversalKYCDetailsRepository;
import com.kotak.unified.dbservice.transformer.UniversalKYCDetailsTransformer;
import com.kotak.unified.dbservice.validator.UniversalKYCStatusAPIRequestValidator;
import com.kotak.unified.orchestrator.common.dbmodels.ukycStatusModel.KYCChannel;
import com.kotak.unified.orchestrator.common.dbmodels.ukycStatusModel.KYCMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.ukycStatusModel.KYCStatus;
import com.kotak.unified.orchestrator.common.dbmodels.ukycStatusModel.UKYCChannelStatus;
import com.kotak.unified.orchestrator.common.dbmodels.ukycStatusModel.UniversalKYCDetails;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.services.dynamodb.model.ConditionalCheckFailedException;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Optional;

import static com.kotak.unified.dbservice.validator.UniversalKYCStatusAPIRequestValidator.validateJourneyType;

@Slf4j
@Service
@AllArgsConstructor
public class UniversalKYCStatusService {
    @Autowired
    private final UniversalKYCDetailsRepository universalKYCDetailsRepository;
    @Autowired
    private final UniversalKYCDetailsTransformer universalKYCDetailsTransformer;
    /**
     * get UniversalKYCDetails object from database based on the applicationId.
     * @param applicationId - unique id for a customer journey, also known as leadTrackingId
     * @return GetUKYCRecordResponse object containing the UniversalKYCDetails object from the database.
     */
    public GetUKYCRecordResponse getUKYCRecord(String aadhaarRefKey, String applicationId) {
        UniversalKYCDetails universalKYCDetails = universalKYCDetailsRepository.getRecord(aadhaarRefKey, applicationId)
                .orElseThrow(() -> {
                    log.warn("UKYC Record doesn't exists for aadhaarRefKey {} and applicationId {}", aadhaarRefKey, applicationId);
                    return new RestException(
                            HttpStatus.NOT_FOUND,
                            DatabaseErrorResponse.fromErrorCode(ErrorCause.UKYC_RECORD_NOT_FOUND)
                    );
                });
        return universalKYCDetailsTransformer.convertModelToGetUKYCRecordResponse(universalKYCDetails);
    }

    /**
     * Create a new UniversalKYCDetails object and insert it into the database.
     * @param createUKYCRecordRequest - CreateUKYCRecordRequest object containing the data to be inserted into the database.
     * @return CreateUKYCRecordResponse
     */
    public CreateUKYCRecordResponse createUKYCRecord(CreateUKYCRecordRequest createUKYCRecordRequest) {
        UniversalKYCDetails universalKYCDetails = universalKYCDetailsTransformer
                .convertRequestToUniversalKYCDetails(createUKYCRecordRequest);
        try {
            universalKYCDetailsRepository.putRecord(universalKYCDetails);
        } catch (ConditionalCheckFailedException e) {
            log.error("Encountered ConditionalCheckFailedException while creating UKYCRecord, " +
                    "UKYC Record with aadhaarRefKey {} and applicationId {} already exists",
                    createUKYCRecordRequest.getAadhaarRefKey(), createUKYCRecordRequest.getApplicationId(), e);
            throw new RestException(
                    HttpStatus.CONFLICT,
                    DatabaseErrorResponse.fromErrorCode(ErrorCause.UKYC_RECORD_ALREADY_EXISTS)
            );
        }
        return universalKYCDetailsTransformer.getCreateUKYCRecordResponse(true);
    }

    /**
     * Update an existing UniversalKYCDetails record in database
     * @param aadhaarRefKey - Reference key associated with Aadhaar number.
     * @param updateUKYCRecordRequest - UpdateUKYCRecordRequest object containing the data to be updated in the database.
     * @return UpdateUKYCRecordResponse
     */
    public UpdateUKYCRecordResponse updateUKYCRecord( String aadhaarRefKey, String applicationId,
                                                     UpdateUKYCRecordRequest updateUKYCRecordRequest) {
        UniversalKYCDetails universalKYCDetails = universalKYCDetailsRepository.getRecord(aadhaarRefKey, applicationId)
                .orElseThrow(() -> {
                    log.error("UKYC Record doesn't exists for aadhaarRefKey {} and applicationId {}", aadhaarRefKey, applicationId);
                    return new RestException(
                            HttpStatus.NOT_FOUND,
                            DatabaseErrorResponse.fromErrorCode(ErrorCause.UKYC_RECORD_NOT_FOUND)
                    );
                });

        validateJourneyType(universalKYCDetails.getJourneyType(), applicationId);
        universalKYCDetailsTransformer.updateUniversalKYCDetails(universalKYCDetails, updateUKYCRecordRequest);

        try {
            universalKYCDetailsRepository.updateRecord(universalKYCDetails);
        } catch (ConditionalCheckFailedException e) {
            log.error("Encountered ConditionalCheckFailedException while updating UKYCRecord, " +
                            "for record with aadhaarRefKey {} and applicationId {} and version {}",
                    aadhaarRefKey, applicationId, universalKYCDetails.getVersion(), e);
            throw new RestException(
                    HttpStatus.CONFLICT,
                    DatabaseErrorResponse.fromErrorCode(ErrorCause.UKYC_RECORD_VERSION_CONFLICT)
            );
        }

        return universalKYCDetailsTransformer.getUpdateUKYCRecordResponse(true);
    }

}
