package com.kotak.unified.dbservice.service.impl;

import com.kotak.unified.dbinterface.models.CpvDvuVerificationDetailsDto;
import com.kotak.unified.dbservice.enums.ErrorCause;
import com.kotak.unified.dbservice.exceptions.custom.RestException;
import com.kotak.unified.dbservice.model.cpv.CpvDvuVerificationDetailsDDBModel;
import com.kotak.unified.dbservice.model.exception.DatabaseErrorResponse;
import com.kotak.unified.dbservice.repository.CpvDvuVerificationDetailsDDBRepository;
import com.kotak.unified.dbservice.transformer.CpvDvuVerificationDetailsTransformer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.services.dynamodb.model.ConditionalCheckFailedException;

@Service
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Slf4j
public class CpvDvuVerificationDetailsService {

    private final CpvDvuVerificationDetailsDDBRepository cpvDvuVerificationDetailsDDBRepository;

    private final CpvDvuVerificationDetailsTransformer cpvDvuVerificationDetailsTransformer;

    public CpvDvuVerificationDetailsDto save(CpvDvuVerificationDetailsDto cpvDvuVerificationDetailsDto) {
        try {
            CpvDvuVerificationDetailsDDBModel cpvDvuVerificationDetailsDDBModel = this.cpvDvuVerificationDetailsTransformer
                    .convertDtoToDDBModel(cpvDvuVerificationDetailsDto);
            CpvDvuVerificationDetailsDDBModel savedCpvDvuVerificationDetails = this.cpvDvuVerificationDetailsDDBRepository.save(cpvDvuVerificationDetailsDDBModel);
            return this.cpvDvuVerificationDetailsTransformer.convertDDBModelToDto(savedCpvDvuVerificationDetails);
        } catch (ConditionalCheckFailedException e) {
            log.warn(String.format("ConditionalCheckFailedException occurred while saving cpvDvuVerificationDetailsDto" +
                            " with actionTrackingId : %s, leadId : %s ",
                    cpvDvuVerificationDetailsDto.getActionTrackingId(), cpvDvuVerificationDetailsDto.getLeadTrackingNumber()));
            throw new RestException(HttpStatus.BAD_REQUEST,
                    DatabaseErrorResponse.fromErrorCode(ErrorCause.DDB_ITEM_VERSION_MISMATCH));
        }
    }

    public CpvDvuVerificationDetailsDto getCpvDvuVerificationDetails(String actionTrackingId) {
        CpvDvuVerificationDetailsDDBModel cpvDvuVerificationDetailsDDBModel = this.cpvDvuVerificationDetailsDDBRepository
                .getByActionTrackingId(actionTrackingId);
        if (cpvDvuVerificationDetailsDDBModel == null) {
            log.warn(String.format("CpvDvuVerificationDetails not found for actionTrackingId : %s ", actionTrackingId));
            throw new RestException(HttpStatus.NOT_FOUND,
                    DatabaseErrorResponse.fromErrorCode(ErrorCause.CPV_DVU_DETAILS_NOT_FOUND));
        }
        return this.cpvDvuVerificationDetailsTransformer.convertDDBModelToDto(cpvDvuVerificationDetailsDDBModel);
    }
}
