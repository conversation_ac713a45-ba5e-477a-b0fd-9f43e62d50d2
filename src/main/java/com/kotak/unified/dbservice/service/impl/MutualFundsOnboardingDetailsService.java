package com.kotak.unified.dbservice.service.impl;

import com.kotak.unified.db.mf.MutualFundsOnboardingDetailsRequest;
import com.kotak.unified.db.request.mf.GetMutualFundsOnboardingDetailsRequest;
import com.kotak.unified.dbservice.enums.ErrorCause;
import com.kotak.unified.dbservice.exceptions.custom.RestException;
import com.kotak.unified.dbservice.model.exception.DatabaseErrorResponse;
import com.kotak.unified.dbservice.model.mf.MutualFundsOnboardingDetailsDDBModel;
import com.kotak.unified.dbservice.repository.MutualFundsOnboardingDetailsDDBRepository;
import com.kotak.unified.dbservice.transformer.MutualFundOnboardingDetailsTransformer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.services.dynamodb.model.ConditionalCheckFailedException;

import java.util.Objects;

@Service
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Slf4j
public class MutualFundsOnboardingDetailsService {
    private final MutualFundsOnboardingDetailsDDBRepository mutualFundsOnboardingDetailsDDBRepository;
    private final MutualFundOnboardingDetailsTransformer mutualFundOnboardingDetailsTransformer;

    public MutualFundsOnboardingDetailsRequest getMutualFundOnboardingDetails(String eventTrackingId) {
        MutualFundsOnboardingDetailsDDBModel mutualFundsOnboardingDetailsDDBModel = this.mutualFundsOnboardingDetailsDDBRepository
                .findByEventTrackingId(eventTrackingId);
        if (Objects.isNull(mutualFundsOnboardingDetailsDDBModel)) {
            log.warn("MutualFundOnboardingDetails db entry not found for eventTrackingId : {} ", eventTrackingId);
            throw new RestException(HttpStatus.NOT_FOUND,
                    DatabaseErrorResponse.fromErrorCode(ErrorCause.MF_ONBOARDING_STATUS_RECORD_NOT_FOUND));
        }
        return mutualFundOnboardingDetailsTransformer.convertDDBModelToResponseEntity(mutualFundsOnboardingDetailsDDBModel);
    }

    public MutualFundsOnboardingDetailsRequest getCrnByEventTypeEventStatusAndCreatedAtDesc(GetMutualFundsOnboardingDetailsRequest getMutualFundOnboardingDetailsRequest) {
        MutualFundsOnboardingDetailsDDBModel mutualFundsOnboardingDetailsDDBModel = this.mutualFundsOnboardingDetailsDDBRepository
                .findByCrnEventTypeEventStatusAndCreatedAtDesc(getMutualFundOnboardingDetailsRequest);
        if (Objects.isNull(mutualFundsOnboardingDetailsDDBModel)) {
            log.warn("MutualFundOnboardingDetails db entry not found for crn : {}, eventType : {}, eventStatus : {}", getMutualFundOnboardingDetailsRequest.getCrn(), getMutualFundOnboardingDetailsRequest.getEventType(), getMutualFundOnboardingDetailsRequest.getEventStatus());
            throw new RestException(HttpStatus.NOT_FOUND,
                    DatabaseErrorResponse.fromErrorCode(ErrorCause.MF_ONBOARDING_STATUS_RECORD_NOT_FOUND));
        }
        return mutualFundOnboardingDetailsTransformer.convertDDBModelToResponseEntity(mutualFundsOnboardingDetailsDDBModel);
    }


    public MutualFundsOnboardingDetailsRequest save(MutualFundsOnboardingDetailsRequest mutualFundOnboardingDetailsRequest) {
        try {
            MutualFundsOnboardingDetailsDDBModel mutualFundsOnboardingDetailsDDBModel = mutualFundOnboardingDetailsTransformer.convertResponseEntityToDDBModel(mutualFundOnboardingDetailsRequest);
            MutualFundsOnboardingDetailsDDBModel savedMutualFundsOnboardingDetailsDDBModel = mutualFundsOnboardingDetailsDDBRepository.save(mutualFundsOnboardingDetailsDDBModel);
            return mutualFundOnboardingDetailsTransformer.convertDDBModelToResponseEntity(savedMutualFundsOnboardingDetailsDDBModel);
        } catch (ConditionalCheckFailedException e) {
            log.error("ConditionalCheckFailedException occurred while saving MutualFundOnboardingDetails" +
                            " with eventTrackingId : {}, leadId : {} ",
                    mutualFundOnboardingDetailsRequest.getEventTrackingId(), mutualFundOnboardingDetailsRequest.getLeadTrackingNumber());
            throw new RestException(HttpStatus.BAD_REQUEST,
                    DatabaseErrorResponse.fromErrorCode(ErrorCause.DDB_ITEM_VERSION_MISMATCH));
        }
        catch (Exception e) {
            log.error("Exception occurred while saving MutualFundOnboardingDetails" +
                            " with eventTrackingId : {}, leadId : {} ",
                    mutualFundOnboardingDetailsRequest.getEventTrackingId(), mutualFundOnboardingDetailsRequest.getLeadTrackingNumber(), e);
            throw new RestException(HttpStatus.INTERNAL_SERVER_ERROR,
                    DatabaseErrorResponse.fromErrorCode(ErrorCause.INTERNAL_SERVER_ERROR));
        }
    }
}
