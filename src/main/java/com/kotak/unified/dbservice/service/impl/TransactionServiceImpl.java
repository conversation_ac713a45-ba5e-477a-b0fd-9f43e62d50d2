package com.kotak.unified.dbservice.service.impl;

import com.kotak.unified.db.response.TransactionResponse;
import com.kotak.unified.dbservice.enums.ErrorCause;
import com.kotak.unified.dbservice.exceptions.custom.RestException;
import com.kotak.unified.dbservice.model.exception.DatabaseErrorResponse;
import com.kotak.unified.dbservice.repository.TransactionFacade;
import com.kotak.unified.dbservice.service.TransactionService;
import com.kotak.unified.dbservice.transformer.TransactionTransformer;
import com.kotak.unified.orchestrator.common.dbmodels.Transaction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class TransactionServiceImpl implements TransactionService {
    private final TransactionFacade transactionFacade;

    private final TransactionTransformer transactionTransformer;

    @Autowired
    public TransactionServiceImpl(TransactionFacade transactionFacade, TransactionTransformer transactionTransformer) {
        this.transactionFacade = transactionFacade;
        this.transactionTransformer = transactionTransformer;
    }

    @Override
    public TransactionResponse getTransaction(String transactionId) {
        Transaction transaction = transactionFacade.findById(transactionId).orElseThrow(
                () -> new RestException(HttpStatus.NOT_FOUND, DatabaseErrorResponse.fromErrorCode(ErrorCause.TRANSACTION_NOT_FOUND))
        );
        return transactionTransformer.convertTransactionToResponse(transaction);
    }
}
