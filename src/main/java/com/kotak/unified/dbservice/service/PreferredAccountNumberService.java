package com.kotak.unified.dbservice.service;

import com.kotak.unified.dbinterface.models.PreferredAccountNumberDto;
import com.kotak.unified.dbservice.enums.ErrorCause;
import com.kotak.unified.dbservice.exceptions.custom.RestException;
import com.kotak.unified.dbservice.model.PreferredAccountNumberDDBModel;
import com.kotak.unified.dbservice.model.exception.DatabaseErrorResponse;
import com.kotak.unified.dbservice.repository.PreferredAccountNumberDDBRepository;
import com.kotak.unified.dbservice.transformer.PreferredAccountNumberTransformer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.services.dynamodb.model.ConditionalCheckFailedException;

@Service
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Slf4j
public class PreferredAccountNumberService {

    private final PreferredAccountNumberTransformer preferredAccountNumberTransformer;
    private final PreferredAccountNumberDDBRepository preferredAccountNumberDDBRepository;

    public PreferredAccountNumberDto savePreferredAccountNumberRecord(
            PreferredAccountNumberDto preferredAccountNumberDto) {
        try {
            //todo: let the caller handle version mismatch, work item #294299
            //get saved record, if exists
            PreferredAccountNumberDDBModel existingRecord = this.preferredAccountNumberDDBRepository
                    .getByPreferredAccountNumber(preferredAccountNumberDto.getPreferredAccountNumber());
            //get DDB model from request DTO
            PreferredAccountNumberDDBModel preferredAccountNumberDDBModel = this.preferredAccountNumberTransformer
                    .convertDtoToDDBModel(preferredAccountNumberDto);
            //update version from existing record, if not null.
            if (ObjectUtils.isNotEmpty(existingRecord)) {
                preferredAccountNumberDDBModel.setVersion(existingRecord.getVersion());
            }
            //save record
            PreferredAccountNumberDDBModel savedPreferredAccountNumber =
                    this.preferredAccountNumberDDBRepository.save(preferredAccountNumberDDBModel);
            //return DTO
            return this.preferredAccountNumberTransformer.convertDDBModelToDTO(savedPreferredAccountNumber);
        } catch (ConditionalCheckFailedException e) {
            log.warn("ConditionalCheckFailedException occurred while saving preferredAccountNumberDto " +
                             "for lead {}", preferredAccountNumberDto.getLeadTrackingNumber());
            throw new RestException(HttpStatus.BAD_REQUEST,
                                    DatabaseErrorResponse.fromErrorCode(ErrorCause.DDB_ITEM_VERSION_MISMATCH));
        }
    }

    public PreferredAccountNumberDto getPreferredAccountNumberRecord(String preferredAccountNumber) {
        PreferredAccountNumberDDBModel preferredAccountNumberDDBModel = this.preferredAccountNumberDDBRepository
                .getByPreferredAccountNumber(preferredAccountNumber);
        if (ObjectUtils.isEmpty(preferredAccountNumberDDBModel)) {
            log.warn("PreferredAccountNumber record not found for preferredAccountNumber : {}", preferredAccountNumber);
            throw new RestException(HttpStatus.NOT_FOUND,
                                    DatabaseErrorResponse.fromErrorCode(ErrorCause.PREFERRED_ACCOUNT_NUMBER_NOT_FOUND));
        }
        return this.preferredAccountNumberTransformer.convertDDBModelToDTO(preferredAccountNumberDDBModel);
    }
}
