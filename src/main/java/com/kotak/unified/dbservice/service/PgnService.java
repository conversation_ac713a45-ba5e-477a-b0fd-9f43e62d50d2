package com.kotak.unified.dbservice.service;

import com.kotak.unified.common.request.bcif.GetCrnDetailsRequest;
import com.kotak.unified.common.response.bcif.GetCrnDetailsResponse;
import com.kotak.unified.db.request.pgn.AssignPgnRequest;
import com.kotak.unified.db.request.pgn.InsertPgnRequest;
import com.kotak.unified.db.response.pgn.AssignPgnResponse;
import com.kotak.unified.db.response.pgn.InsertPgnResponse;
import com.kotak.unified.dbservice.accessor.BcifServiceAccessor;
import com.kotak.unified.dbservice.enums.PgnType;
import com.kotak.unified.dbservice.exceptions.CrnActivatedException;
import com.kotak.unified.dbservice.exceptions.EntityExistsException;
import com.kotak.unified.dbservice.exceptions.PgnAssignmentException;
import com.kotak.unified.dbservice.exceptions.PgnDepletedException;
import com.kotak.unified.dbservice.model.ExternalServiceExecutionDetails;
import com.kotak.unified.dbservice.model.PGNStatus;
import com.kotak.unified.dbservice.model.pgn.PgnDDBModel;
import com.kotak.unified.dbservice.repository.PgnDDBRepository;
import com.kotak.unified.dbservice.transformer.PgnTransformer;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.services.dynamodb.model.ConditionalCheckFailedException;
import software.amazon.awssdk.services.dynamodb.model.DynamoDbException;
import com.fasterxml.jackson.core.JsonProcessingException;

@Service
@Slf4j
@AllArgsConstructor
public class PgnService {

    private final PgnDDBRepository pgnDDBRepository;
    private final PgnTransformer pgnTransformer;
    private final BcifServiceAccessor bcifServiceAccessor;
    private final RetryTemplate retryTemplate;

    public AssignPgnResponse assignPgn(AssignPgnRequest assignPgnRequest) {
        PgnDDBModel existingPgnDdbModel;
        try {
            existingPgnDdbModel = pgnDDBRepository.findByLeadTrackingNumberAndPgnTypeAndPgnStatus(
                    assignPgnRequest.getLeadTrackingNumber(), assignPgnRequest.getPgnType(), PGNStatus.ASSIGNED.name());
        } catch (DynamoDbException e) {
            throw new PgnAssignmentException("Failed to query existing PGN assignment", e);
        }

        if (existingPgnDdbModel != null) {
            return pgnTransformer.convertPgnDdbModelToAssignPgnResponse(existingPgnDdbModel, true);
        }
        PgnDDBModel validPgn;
        try {
            validPgn = retryTemplate.execute(context -> getValidPgn(assignPgnRequest));
        } catch (CrnActivatedException | ConditionalCheckFailedException | PgnAssignmentException e) {
            throw new RuntimeException(String.format("Pgn assignment failed for lead id: %s, " +
                    "all retry attempts exhausted", assignPgnRequest.getLeadTrackingNumber()));
        }

        return pgnTransformer.convertPgnDdbModelToAssignPgnResponse(validPgn, false);
    }

    private PgnDDBModel getValidPgn(AssignPgnRequest assignPgnRequest) {
        PgnDDBModel assignedPgn;
        try {
            assignedPgn = pgnDDBRepository.findByPgnTypeAndPgnStatus(assignPgnRequest.getPgnType(), PGNStatus.UN_ASSIGNED.name());
        } catch (DynamoDbException e) {
            throw new PgnAssignmentException("Failed to query unassigned PGN", e);
        }
        
        if (assignedPgn == null) {
            String errorMessage = String.format("No PGN available to assign for PGN Type: %s", assignPgnRequest.getPgnType());
            throw new PgnDepletedException(errorMessage);
        }

        if (StringUtils.isNotBlank(assignedPgn.getLeadTrackingNumber())) {
            String errorMessage = String.format("Lead tracking number found in unassigned PGN record: %s, " +
                    "cannot use it for PGN assignment", assignedPgn);
            throw new PgnAssignmentException(errorMessage);
        }

        try {
            assignedPgn.setLeadTrackingNumber(assignPgnRequest.getLeadTrackingNumber());
            assignedPgn.setStatus(PGNStatus.ASSIGNED);
            assignedPgn = pgnDDBRepository.save(assignedPgn);
        } catch (ConditionalCheckFailedException e) {
            throw e;
        } catch (DynamoDbException e) {
            throw new PgnAssignmentException("Database operation failed during PGN assignment", e);
        } catch (Exception e) {
            throw new PgnAssignmentException("Unexpected error during PGN assignment", e);
        }
        
        boolean isCrnActivatedOrCheckFailed;
        try {
            isCrnActivatedOrCheckFailed = isCrnActivated(assignedPgn.getCrn(), assignPgnRequest.getLeadTrackingNumber());
        } catch (Exception e) {
            log.warn("CRN: {} activation check failed with exception message: {}", assignedPgn.getCrn(), e.getMessage());
            isCrnActivatedOrCheckFailed = true;
        }

        if (isCrnActivatedOrCheckFailed) {
            assignedPgn.setStatus(PGNStatus.ABORTED);
            try {
                pgnDDBRepository.save(assignedPgn);
            } catch (Exception e) {
                String errorMessage = String.format("PGN allocation failed for lead id %s, failed to update PGN status " +
                        "as ABORTED for CRN %s", assignPgnRequest.getLeadTrackingNumber(), assignedPgn.getCrn());
                throw new RuntimeException(errorMessage);
            }

            String errorMessage = String.format("Assigned CRN: %s , for lead id: %s was already activated or API call failed",
                    assignedPgn.getCrn(), assignPgnRequest.getLeadTrackingNumber());
            throw new CrnActivatedException(errorMessage);
        }

        return assignedPgn;
    }

    private Boolean isCrnActivated(String crn, String leadTrackingNumber) {
        ExternalServiceExecutionDetails<GetCrnDetailsRequest, GetCrnDetailsResponse> dynamicInfoResponse;
        try {
            dynamicInfoResponse = bcifServiceAccessor.getDynamicInfo(crn, leadTrackingNumber);
        } catch (Exception e) {
            String errorMessage = String.format("Exception occurred while calling getDynamicInfo for crn: %s with error message: %s",
                    crn, e.getMessage());
            throw new RuntimeException(errorMessage);
        }

        if (dynamicInfoResponse.getErrorResponse() != null) {
            String errorMessage = String.format("getDynamicInfo api failed for CRN: %s, with error response: %s",
                    crn, dynamicInfoResponse.getErrorResponse());
            throw new RuntimeException(errorMessage);
        }

        GetCrnDetailsResponse getCrnDetailsResponse = dynamicInfoResponse.getSuccessResponse();
        if (BooleanUtils.isNotTrue(getCrnDetailsResponse.getIsSuccess())) {
            String errorMessage = String.format("getDynamicsInfo inner response is failure for CRN: %s, with response: %s",
                    crn, getCrnDetailsResponse.getErrorMessage());
            throw new RuntimeException(errorMessage);
        }
        return getCrnDetailsResponse.getIsCrnActivated();
    }

    public InsertPgnResponse insertPgn(InsertPgnRequest insertPgnRequest) throws EntityExistsException {
        PgnDDBModel record = PgnDDBModel.builder()
                .crn(insertPgnRequest.getCrn())
                .pgnType(PgnType.valueOf(insertPgnRequest.getPgnType()))
                .pgnMetadata(pgnTransformer.convertRequestPgnMetadataToPgnMetadataModel(
                        insertPgnRequest.getPgnType(), insertPgnRequest.getPgnMetadata()))
                .status(PGNStatus.UN_ASSIGNED)
                .build();

        try {
            pgnDDBRepository.save(record);
        } catch (ConditionalCheckFailedException e) {
            String errorMessage = String.format("Record with CRN %s and PGN type: %s already exists in the DB",
                    insertPgnRequest.getCrn(), insertPgnRequest.getPgnType());
            throw new EntityExistsException(errorMessage, e);
        } catch (DynamoDbException e) {
            throw new PgnAssignmentException("Database operation failed during PGN insertion", e);
        } catch (Exception e) {
            String errorMessage = String.format("Exception occurred while calling getDynamicInfo for crn: %s with error message: %s",
                    "", e.getMessage());
            throw new RuntimeException(errorMessage);
        }

        return InsertPgnResponse.builder().success(true).build();
    }
}
