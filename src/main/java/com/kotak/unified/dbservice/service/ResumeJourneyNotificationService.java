package com.kotak.unified.dbservice.service;

import com.kotak.unified.db.model.CreateResumeJourneyNotificationStatusRecordRequest;
import com.kotak.unified.db.model.CreateResumeJourneyNotificationStatusRecordResponse;
import com.kotak.unified.db.model.GetResumeJourneyNotificationStatusRecordResponse;
import com.kotak.unified.db.model.UpdateResumeJourneyNotificationStatusRecordRequest;
import com.kotak.unified.db.model.UpdateResumeJourneyNotificationStatusRecordResponse;
import com.kotak.unified.dbservice.enums.ErrorCause;
import com.kotak.unified.dbservice.exceptions.custom.RestException;
import com.kotak.unified.dbservice.model.exception.DatabaseErrorResponse;
import com.kotak.unified.dbservice.repository.ResumeJourneyNotificationStatusRepository;
import com.kotak.unified.dbservice.transformer.ResumeJourneyNotificationTransformer;
import com.kotak.unified.orchestrator.common.dbmodels.resumenotification.ResumeJourneyNotificationStatus;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.services.dynamodb.model.ConditionalCheckFailedException;

@Slf4j
@Service
@AllArgsConstructor
public class ResumeJourneyNotificationService {

    @Autowired
    private final ResumeJourneyNotificationStatusRepository resumeJourneyNotificationStatusRepository;
    @Autowired
    private final ResumeJourneyNotificationTransformer resumeJourneyNotificationTransformer;

    /**
     * Create a new ResumeJourneyNotification object and insert it into the database.
     * @param createResumeJourneyNotificationStatusRecordRequest - ResumeJourneyNotificationStatusRecordRequest object containing the data to be inserted into the database.
     * @return CreateResumeJourneyNotificationStatusRecordResponse
     */
    public CreateResumeJourneyNotificationStatusRecordResponse createResumeJourneyNotificationStatusRecord(CreateResumeJourneyNotificationStatusRecordRequest createResumeJourneyNotificationStatusRecordRequest) {
        ResumeJourneyNotificationStatus resumeJourneyNotificationStatus = resumeJourneyNotificationTransformer
                .convertRequestToResumeJourneyNotificationStatus(createResumeJourneyNotificationStatusRecordRequest);
        try {
            resumeJourneyNotificationStatusRepository.putRecord(resumeJourneyNotificationStatus);
        } catch (ConditionalCheckFailedException e) {
            log.error("Encountered ConditionalCheckFailedException while creating ResumeJourneyNotificationRecord, " +
                            "ResumeJourneyNotification Record with leadTrackingNumber {} already exists",
                    createResumeJourneyNotificationStatusRecordRequest.getLeadTrackingNumber(), e);
            throw new RestException(
                    HttpStatus.CONFLICT,
                    DatabaseErrorResponse.fromErrorCode(ErrorCause.RESUME_NOTIFICATION_RECORD_ALREADY_EXISTS)
            );
        }
        return resumeJourneyNotificationTransformer.getCreateResumeJourneyNotificationStatusRecordResponse(true);
    }

    public GetResumeJourneyNotificationStatusRecordResponse getResumeJourneyNotificationStatusRecord(String leadTrackingNumber) {
        ResumeJourneyNotificationStatus resumeJourneyNotificationStatus = resumeJourneyNotificationStatusRepository.getRecord(leadTrackingNumber)
                .orElseThrow(() -> {
                    log.warn("ResumeJourneyNotificationStatus Record doesn't exists for leadTrackingNumber {} ", leadTrackingNumber);
                    return new RestException(
                            HttpStatus.NOT_FOUND,
                            DatabaseErrorResponse.fromErrorCode(ErrorCause.RESUME_NOTIFICATION_RECORD_NOT_FOUND)
                    );
                });
        return resumeJourneyNotificationTransformer.convertModelToGetResumeJourneyNotificationStatusRecordResponse(resumeJourneyNotificationStatus);
    }

    public UpdateResumeJourneyNotificationStatusRecordResponse updateResumeJourneyNotificationStatusRecord(String leadTrackingNumber, UpdateResumeJourneyNotificationStatusRecordRequest updateResumeJourneyNotificationStatusRecordRequest) {
        ResumeJourneyNotificationStatus resumeJourneyNotificationStatus = resumeJourneyNotificationStatusRepository.getRecord(leadTrackingNumber)
                .orElseThrow(() -> {
                    log.error("ResumeJourneyNotificationStatus Record doesn't exists for leadTrackingNumber {}", leadTrackingNumber);
                    return new RestException(
                            HttpStatus.NOT_FOUND,
                            DatabaseErrorResponse.fromErrorCode(ErrorCause.RESUME_NOTIFICATION_RECORD_NOT_FOUND)
                    );
                });

        resumeJourneyNotificationTransformer.updateResumeJourneyNotificationStatusDetails(resumeJourneyNotificationStatus, updateResumeJourneyNotificationStatusRecordRequest);

        try {
            resumeJourneyNotificationStatusRepository.updateRecord(resumeJourneyNotificationStatus);
        } catch (ConditionalCheckFailedException e) {
            log.error("Encountered ConditionalCheckFailedException while updating ResumeJourneyNotificationStatusRecord, " +
                            "for record with leadTrackingNumber {} and version {}",
                    leadTrackingNumber, resumeJourneyNotificationStatus.getVersion(), e);
            throw new RestException(
                    HttpStatus.CONFLICT,
                    DatabaseErrorResponse.fromErrorCode(ErrorCause.RESUME_NOTIFICATION_RECORD_VERSION_CONFLICT)
            );
        }

        return UpdateResumeJourneyNotificationStatusRecordResponse.builder()
                .isUpdateRecordSuccessful(true)
                .build();
    }
}
