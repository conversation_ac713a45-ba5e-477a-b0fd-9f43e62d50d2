package com.kotak.unified.dbservice.service.impl;

import com.kotak.unified.common.request.database.KycSchedulingRequest;
import com.kotak.unified.db.KycSchedulingDetailsResponse;
import com.kotak.unified.db.KycSchedulingResponse;
import com.kotak.unified.dbservice.enums.ErrorCause;
import com.kotak.unified.dbservice.exceptions.custom.RestException;
import com.kotak.unified.dbservice.model.exception.DatabaseErrorResponse;
import com.kotak.unified.dbservice.service.KycScheduleService;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Service
public class KycScheduleServiceImpl implements KycScheduleService {

    public KycScheduleServiceImpl() {
    }

    @Override
    public KycSchedulingResponse createKycSchedule(KycSchedulingRequest kycSchedulingRequest) {
        throw new RestException(HttpStatus.METHOD_NOT_ALLOWED,  DatabaseErrorResponse.fromErrorCode(ErrorCause.INPUT_VALIDATION_FAILED));
    }

    @Override
    public KycSchedulingResponse updateKycSchedule(KycSchedulingRequest kycSchedulingRequest) {
        throw new RestException(HttpStatus.METHOD_NOT_ALLOWED,  DatabaseErrorResponse.fromErrorCode(ErrorCause.INPUT_VALIDATION_FAILED));
    }

    @Override
    public KycSchedulingResponse getKycScheduleForLeadTrackingId(String leadTrackingId) {
        throw new RestException(HttpStatus.METHOD_NOT_ALLOWED,  DatabaseErrorResponse.fromErrorCode(ErrorCause.INPUT_VALIDATION_FAILED));
    }

    @Override
    public KycSchedulingDetailsResponse getKycSchedulesBasedOnTimestamp(Long timestamp, String operator) {
        throw new RestException(HttpStatus.METHOD_NOT_ALLOWED,  DatabaseErrorResponse.fromErrorCode(ErrorCause.INPUT_VALIDATION_FAILED));
    }
}
