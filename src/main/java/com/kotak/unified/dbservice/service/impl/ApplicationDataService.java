package com.kotak.unified.dbservice.service.impl;

import com.kotak.unified.common.exception.InvalidRequestException;
import com.kotak.unified.db.ApplicationData;
import com.kotak.unified.db.GetApplicationDataRequest;
import com.kotak.unified.dbservice.exceptions.EntityNotFoundException;
import com.kotak.unified.dbservice.repository.UserStatusFacade;
import com.kotak.unified.dbservice.transformer.ApplicationDataTransformer;
import com.kotak.unified.dbservice.transformer.ApplicationDataTransformerV2;
import com.kotak.unified.dbservice.transformer.factory.ApplicationDataTransformerFactory;
import com.kotak.unified.dbservice.transformer.factory.ApplicationDataTransformerFactoryV2;
import com.kotak.unified.dbservice.utils.ApplicationDataUtil;
import com.kotak.unified.orchestrator.common.dbmodels.UserStatus;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Slf4j
@Service
public class ApplicationDataService {

    private final UserStatusFacade userStatusFacade;
    private final ApplicationDataTransformerFactory applicationDataTransformerFactory;
    private final ApplicationDataTransformerFactoryV2 applicationDataTransformerFactoryV2;

    public ApplicationDataService(@NonNull UserStatusFacade userStatusFacade,
                                  @NonNull ApplicationDataTransformerFactory applicationDataTransformerFactory,
                                  @NonNull ApplicationDataTransformerFactoryV2 applicationDataTransformerFactoryV2) {
        this.userStatusFacade = userStatusFacade;
        this.applicationDataTransformerFactory = applicationDataTransformerFactory;
        this.applicationDataTransformerFactoryV2 = applicationDataTransformerFactoryV2;
    }

    public ApplicationData getApplicationData(@NonNull GetApplicationDataRequest getApplicationDataRequest,
                                              @NonNull String leadTrackingNumber)
        throws InvalidRequestException, EntityNotFoundException {
        validateGetApplicationDataRequest(getApplicationDataRequest, leadTrackingNumber);

        UserStatus userStatus = userStatusFacade.findByLeadTrackingNumber(leadTrackingNumber);
        validateNotNullUserStatus(userStatus, leadTrackingNumber);

        String product = ApplicationDataUtil.getProduct(userStatus);

        ApplicationDataTransformer applicationDataTransformer = applicationDataTransformerFactory.selectTransformer(product);

        return applicationDataTransformer
            .populateApplicationData(getApplicationDataRequest,
                        userStatus);
    }

    public ApplicationData getApplicationDataV2(@NonNull GetApplicationDataRequest getApplicationDataRequest,
                                                @NonNull String leadTrackingNumber)
            throws InvalidRequestException, EntityNotFoundException {
        validateGetApplicationDataRequest(getApplicationDataRequest, leadTrackingNumber);

        com.kotak.unified.orchestrator.common.dbmodels.ApplicationData applicationDataDDBModel =
                userStatusFacade.findApplicationDataByLeadTrackingNumber(leadTrackingNumber);
        validateNotNullApplicationData(applicationDataDDBModel, leadTrackingNumber);

        String product = ApplicationDataUtil.getProduct(applicationDataDDBModel);

        ApplicationDataTransformerV2 applicationDataTransformerV2 = applicationDataTransformerFactoryV2.selectTransformer(product);

        if (Objects.isNull(applicationDataTransformerV2)) {
            log.warn("ApplicationDataTransformer found null for the given leadTrackingNumber: {} of product type: {}",
                    leadTrackingNumber, product);
            throw new InvalidRequestException("Please provide leadTrackingNumber having a valid product type "
                    + leadTrackingNumber);
        }

        return applicationDataTransformerV2
                .populateApplicationData(getApplicationDataRequest,
                        applicationDataDDBModel);
    }

    private void validateNotNullUserStatus(UserStatus userStatus,
        String leadTrackingNumber)
        throws EntityNotFoundException {
        if(userStatus == null){
            log.warn("[ApplicationDataService -> getApplicationData] " +
                "Could not find application with the given applicationId {}", leadTrackingNumber);
            throw new EntityNotFoundException("Could not find application with the given applicationId " + leadTrackingNumber);
        }
    }

    private void validateGetApplicationDataRequest(
        GetApplicationDataRequest getApplicationDataRequest, String leadTrackingNumber)
        throws InvalidRequestException {
        if (getApplicationDataRequest.getDataFilters().size() == 0) {
            log.warn("[ApplicationDataService -> getApplicationData] Please provide onboarding data filters for which you require data for {}", leadTrackingNumber);
            throw new InvalidRequestException("Please provide onboarding data filters for which you require data for "
                    + leadTrackingNumber);
        }
    }

    private void validateNotNullApplicationData(com.kotak.unified.orchestrator.common.dbmodels.ApplicationData applicationDataDDBModel,
                                                String leadTrackingNumber)
            throws EntityNotFoundException {
        if(applicationDataDDBModel == null) {
            log.warn("[ApplicationDataService -> getApplicationDataV2] " +
                    "Could not find application with the given applicationId {}", leadTrackingNumber);
            throw new EntityNotFoundException("Could not find application with the given applicationId " + leadTrackingNumber);
        }
    }
}
