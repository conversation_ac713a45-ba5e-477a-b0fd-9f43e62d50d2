package com.kotak.unified.dbservice.service;

import com.kotak.unified.db.AccountPropagationExecutionDataResponse;
import com.kotak.unified.db.AsyncProcessDetailsResponse;
import com.kotak.unified.db.AsyncProcessExecutionDataResponse;
import com.kotak.unified.db.DormantAccountAsyncExecutionDataResponse;
import com.kotak.unified.db.EtbPostDeclarationsPropagationExecutionDataResponse;
import com.kotak.unified.db.EtbPostVideoKycPropagationExecutionDataResponse;
import com.kotak.unified.db.LeadJourneyExecutionDataResponse;
import com.kotak.unified.db.MerchantOnboardingPropagationExecutionDataResponse;
import com.kotak.unified.db.NRFullKycPropagationExecutionDataResponse;
import com.kotak.unified.db.UpgradeCustomerJourneyPropagationExecutionDataResponse;
import com.kotak.unified.db.VkycPropagationExecutionDataResponse;
import com.kotak.unified.dbservice.enums.AsyncExecutionDataProcessType;
import com.kotak.unified.dbservice.enums.ErrorCause;
import com.kotak.unified.dbservice.exceptions.custom.RestException;
import com.kotak.unified.dbservice.model.exception.DatabaseErrorResponse;
import com.kotak.unified.dbservice.repository.AsyncProcessFacade;
import com.kotak.unified.orchestrator.common.dbmodels.asyncprocessing.AccountPropagationExecutionData;
import com.kotak.unified.orchestrator.common.dbmodels.asyncprocessing.AsyncProcessDBModel;
import com.kotak.unified.orchestrator.common.dbmodels.asyncprocessing.AsyncProcessExecutionData;
import com.kotak.unified.orchestrator.common.dbmodels.asyncprocessing.AsyncProcessId;
import com.kotak.unified.orchestrator.common.dbmodels.asyncprocessing.DormantAccountAsyncExecutionData;
import com.kotak.unified.orchestrator.common.dbmodels.asyncprocessing.EtbPostDeclarationsPropagationExecutionData;
import com.kotak.unified.orchestrator.common.dbmodels.asyncprocessing.EtbPostVideoKycPropagationExecutionData;
import com.kotak.unified.orchestrator.common.dbmodels.asyncprocessing.LeadJourneyExecutionData;
import com.kotak.unified.orchestrator.common.dbmodels.asyncprocessing.MerchantOnBoardingWlPropagationExecutionData;
import com.kotak.unified.orchestrator.common.dbmodels.asyncprocessing.NRFullKycPropagationExecutionData;
import com.kotak.unified.orchestrator.common.dbmodels.asyncprocessing.UpgradeCustomerJourneyPropagationExecutionData;
import com.kotak.unified.orchestrator.common.dbmodels.asyncprocessing.VkycPropagationExecutionData;
import com.kotak.unified.orchestrator.library.sqsmodels.AsyncProcessType;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class AsyncProcessService {
    private final AsyncProcessFacade asyncProcessFacade;
    private final ModelMapper modelMapper;

    @Autowired
    public AsyncProcessService(AsyncProcessFacade asyncProcessFacade, ModelMapper modelMapper) {
        this.asyncProcessFacade = asyncProcessFacade;
        this.modelMapper = modelMapper;
    }

    public AsyncProcessExecutionDataResponse getAsyncProcessExecutionData(
            String leadTrackingNumber, AsyncExecutionDataProcessType asyncExecutionDataProcessType) {
        AsyncProcessType asyncProcessType = AsyncProcessType.valueOf(asyncExecutionDataProcessType.toString());
        AsyncProcessId processId = AsyncProcessId.builder()
                .leadTrackingNumber(leadTrackingNumber)
                .processType(asyncProcessType)
                .build();
        AsyncProcessDBModel asyncProcessDBModel = asyncProcessFacade.getByProcessId(processId);
        if (asyncProcessDBModel == null) {
            throw new RestException(HttpStatus.NOT_FOUND, DatabaseErrorResponse
                    .fromErrorCode(ErrorCause.ASYNC_PROCESS_STATUS_NOT_FOUND));
        }
        return geAsyncProcessExecutionDataResponse(asyncProcessDBModel.getAsyncProcessExecutionData());
    }

    public AsyncProcessDetailsResponse getAsyncProcessData(
            String leadTrackingNumber, String processType) {
        AsyncProcessType asyncProcessType;
        try {
            asyncProcessType = AsyncProcessType.valueOf(processType);
        } catch (IllegalArgumentException e) {
            throw new RestException(HttpStatus.NOT_FOUND, DatabaseErrorResponse
                    .fromErrorCode(ErrorCause.ASYNC_PROCESS_STATUS_NOT_FOUND));
        }
        AsyncProcessId processId = AsyncProcessId.builder()
                .leadTrackingNumber(leadTrackingNumber)
                .processType(asyncProcessType)
                .build();
        AsyncProcessDBModel asyncProcessDBModel = asyncProcessFacade.getByProcessId(processId);
        if (asyncProcessDBModel == null) {
            throw new RestException(HttpStatus.NOT_FOUND, DatabaseErrorResponse
                    .fromErrorCode(ErrorCause.ASYNC_PROCESS_STATUS_NOT_FOUND));
        }
        AsyncProcessDetailsResponse result = convertDBModelToResponseEntity(asyncProcessDBModel);
        result.setAsyncProcessExecutionData(geAsyncProcessExecutionDataResponse(asyncProcessDBModel.getAsyncProcessExecutionData()));
        return result;
    }

    private AsyncProcessDetailsResponse convertDBModelToResponseEntity(AsyncProcessDBModel asyncProcessDBModel) {
        return modelMapper.map(asyncProcessDBModel, AsyncProcessDetailsResponse.class);
    }

    private AsyncProcessExecutionDataResponse geAsyncProcessExecutionDataResponse(AsyncProcessExecutionData asyncProcessExecutionData) {

        if ((asyncProcessExecutionData instanceof EtbPostVideoKycPropagationExecutionData etbPostVideoKycPropagationExecutionData)) {
            return modelMapper.map(etbPostVideoKycPropagationExecutionData, EtbPostVideoKycPropagationExecutionDataResponse.class);
        } else if (asyncProcessExecutionData instanceof EtbPostDeclarationsPropagationExecutionData etbPostDeclarationsPropagationExecutionData) {
            return modelMapper.map(etbPostDeclarationsPropagationExecutionData, EtbPostDeclarationsPropagationExecutionDataResponse.class);
        } else if (asyncProcessExecutionData instanceof AccountPropagationExecutionData accountPropagationExecutionData ) {
            return modelMapper.map(accountPropagationExecutionData, AccountPropagationExecutionDataResponse.class);
        } else if (asyncProcessExecutionData instanceof NRFullKycPropagationExecutionData nrFullKycPropagationExecutionData) {
            return modelMapper.map(nrFullKycPropagationExecutionData, NRFullKycPropagationExecutionDataResponse.class);
        } else if (asyncProcessExecutionData instanceof DormantAccountAsyncExecutionData dormantAccountAsyncExecutionData) {
            return modelMapper.map(dormantAccountAsyncExecutionData, DormantAccountAsyncExecutionDataResponse.class);
        } else if (asyncProcessExecutionData instanceof VkycPropagationExecutionData vkycPropagationExecutionData) {
            return modelMapper.map(vkycPropagationExecutionData, VkycPropagationExecutionDataResponse.class);
        } else if (asyncProcessExecutionData instanceof LeadJourneyExecutionData leadJourneyExecutionData) {
            return modelMapper.map(leadJourneyExecutionData, LeadJourneyExecutionDataResponse.class);
        } else if (asyncProcessExecutionData instanceof UpgradeCustomerJourneyPropagationExecutionData upgradeCustomerJourneyPropagationExecutionData) {
            return modelMapper.map(upgradeCustomerJourneyPropagationExecutionData, UpgradeCustomerJourneyPropagationExecutionDataResponse.class);
        } else if (asyncProcessExecutionData instanceof MerchantOnBoardingWlPropagationExecutionData merchantOnboardingPropagationExecutionData) {
            return modelMapper.map(merchantOnboardingPropagationExecutionData, MerchantOnboardingPropagationExecutionDataResponse.class);
        }
        return null;
    }

}
