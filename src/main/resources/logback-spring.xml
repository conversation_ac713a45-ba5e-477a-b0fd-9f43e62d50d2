<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <appender class="ch.qos.logback.core.ConsoleAppender" name="Console">
    <encoder>
      <Pattern>${LOG_PATTERN}</Pattern>
    </encoder>
  </appender>
  <property name="LOG_PATTERN"
    value="%d{HH:mm:ss.SSS} [%thread] %-5level | %X{X-Correlation-ID} | %X{X-Request-ID} | %logger{36} - %msg | %ex{full}%n"/>
  <springProfile name="local">
    <logger level="DEBUG" name="com.kotak.unified.dbservice"/>
    <root level="DEBUG">
      <appender-ref ref="Console"/>
    </root>
  </springProfile>
  <springProfile name="uat">
    <logger level="DEBUG" name="com.kotak.unified.dbservice"/>
    <root level="WARN">
      <appender-ref ref="Console"/>
    </root>
  </springProfile>
  <springProfile name="prod">
    <logger level="INFO" name="com.kotak.unified.dbservice"/>
    <root level="ERROR">
      <appender-ref ref="Console"/>
    </root>
  </springProfile>
  <springProfile name="dr">
    <logger level="INFO" name="com.kotak.unified.dbservice"/>
    <root level="ERROR">
      <appender-ref ref="Console"/>
    </root>
  </springProfile>  
</configuration>