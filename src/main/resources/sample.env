BCIF_SERVICE_ENDPOINT_URL=http://localhost:4002;
spring.jackson.serialization.write_dates_as_timestamps=false;
spring.jackson.serialization.FAIL_ON_EMPTY_BEANS=false;
server.port=5011;
TRUST_ALL_CERTS=true;

AWS_ACCESS_KEY_ID=****;
AWS_SECRET_ACCESS_KEY=***;
AWS_REGION=ap-south-1;
UNIVERSAL_KYC_DETAILS_TABLE_NAME=UniversalKYCDetails-dev;
spring.profiles.active=local;
CA_PGN_TABLE_NAME=CAPreGeneratedCRNAccountNumber-dev;
RETRY_MAX_ATTEMPTS=3;
RESUME_JOURNEY_NOTIFICATION_STATUS_TABLE_NAME=ResumeJourneyNotificationStatus-dev;
RESUME_JOURNEY_NOTIFICATION_TRACK_NEXT_CHECK_TABLE_NAME=ResumeJourneyNotificationTrackNextCheck-dev;
ASYNC_PROCESS_DYNAMO_TABLE_NAME=UAO-AsyncProcess-dev;
API_RESPONSE_DETAILS_DYNAMO_TABLE_NAME=UAO-ApiResponseDetails-dev;
VIDEO_KYC_STATUS_DYNAMO_TABLE_NAME=UAO-VideoKycDetails-dev;
TRANSACTION_DYNAMO_TABLE_NAME=UAO-Transaction-dev;
USER_JOURNEY_STATUS_DYNAMO_TABLE_NAME=UAO-UserJourneyStatus-dev;
APPLICATION_DATA_DYNAMO_TABLE_NAME=UAO-ApplicationData-dev;
CPV_DVU_VERIFICATION_DETAILS_TABLE_NAME=UAO-CpvDvuVerificationDetails-dev;
DATA_CHANGE_NOTIFICATION_QUEUE_URL=https://sqs.ap-south-1.amazonaws.com/************/kotak-uao-data-change-notification;
UKYC_ALLOWED_JOURNEY_TYPES=SavingsAccount,SalaryAccount;
PGN_DYNAMO_TABLE_NAME=UAO-PGN-dev;
MF_ONBOARDING_DETAILS_TABLE_NAME=UAO-MutualFundsOnboardingDetails-dev;

# S3 Request/Response Logging Configuration (from orchestration service)
S3_REQUEST_RESPONSE_BUCKET_NAME=unified-onboarding-dev-s3-bucket;
S3_REQUEST_RESPONSE_LOG_FILEPATH=request-response;