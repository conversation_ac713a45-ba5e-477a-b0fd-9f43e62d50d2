server.servlet.context.path=/
server.port=8080
spring.jackson.serialization.write_dates_as_timestamps=false
spring.jackson.serialization.FAIL_ON_EMPTY_BEANS=false
bcif.BCIF_SERVICE_ENDPOINT_URL=${BCIF_SERVICE_ENDPOINT_URL}
TRUST_ALL_CERTS=false
dynamodb.UniversalKYCDetails.TableName=${UNIVERSAL_KYC_DETAILS_TABLE_NAME}
dynamodb.CAPreGeneratedNumber.TableName=${CA_PGN_TABLE_NAME}
retry.maxAttempts=${RETRY_MAX_ATTEMPTS}
dynamodb.ResumeJourneyNotificationStatus.TableName=${RESUME_JOURNEY_NOTIFICATION_STATUS_TABLE_NAME}
dynamodb.ResumeJourneyNotificationTrackNextCheck.TableName=${RESUME_JOURNEY_NOTIFICATION_TRACK_NEXT_CHECK_TABLE_NAME}
aws.dynamo.asyncProcessTableName=${ASYNC_PROCESS_DYNAMO_TABLE_NAME}
aws.dynamo.apiResponseDetailsTableName=${API_RESPONSE_DETAILS_DYNAMO_TABLE_NAME}
aws.dynamo.videoKycStatusTableName=${VIDEO_KYC_STATUS_DYNAMO_TABLE_NAME}
aws.dynamo.transactionTableName=${TRANSACTION_DYNAMO_TABLE_NAME}
aws.dynamo.userJourneyStatusTableName=${USER_JOURNEY_STATUS_DYNAMO_TABLE_NAME}
aws.dynamo.applicationDataTableName=${APPLICATION_DATA_DYNAMO_TABLE_NAME}
aws.dynamo.cpvDvuVerificationDetailsTableName=${CPV_DVU_VERIFICATION_DETAILS_TABLE_NAME}
aws.dynamo.pgnTableName=${PGN_DYNAMO_TABLE_NAME}
aws.dynamo.preferredAccountNumberTableName=${PREFERRED_ACCOUNT_NUMBER_TABLE_NAME}
ukycAllowedJourneyTypes=${UKYC_ALLOWED_JOURNEY_TYPES}
data.change.notification.queueUrl=${DATA_CHANGE_NOTIFICATION_QUEUE_URL}
aws.dynamo.mutualFundsOnboardingDetailsTableName=${MF_ONBOARDING_DETAILS_TABLE_NAME}

# S3 Request/Response Logging Configuration
AWS_REGION=${AWS_REGION:ap-south-1}
S3_REQUEST_RESPONSE_BUCKET_NAME=${S3_REQUEST_RESPONSE_BUCKET_NAME:unified-onboarding-dev-s3-bucket}
S3_REQUEST_RESPONSE_LOG_FILEPATH=${S3_REQUEST_RESPONSE_LOG_FILEPATH:request-response}

management.endpoints.web.exposure.include=prometheus,info,health,metrics
management.metrics.tags.application=kotak-uao-db-service
management.endpoint.prometheus.enabled=true
management.endpoints.web.base-path=/actuator
management.metrics.tags.environment=${ENVIRONMENT}
management.metrics.tags.eks_cluster=${OBSERVABILITY_ENVIRONMENT_TAG}
management.metrics.distribution.percentiles-histogram.http.server.requests: true
management.metrics.distribution.percentiles.http.server.requests: 0.5, 0.95, 0.99
