package builder;

import com.kotak.unified.common.enums.ComparisonOperator;
import com.kotak.unified.common.enums.ScheduleType;
import com.kotak.unified.common.request.Address;
import com.kotak.unified.common.request.database.KycSchedulingRequest;
import com.kotak.unified.db.AddressResponse;
import com.kotak.unified.db.KycSchedulingDetailsResponse;
import com.kotak.unified.db.KycSchedulingResponse;
import com.kotak.unified.dbservice.model.KycSchedule;

import java.time.Instant;
import java.util.List;

public final class KycScheduleTestDataBuilder {

    private static final String LEAD_TRACKING_NUMBER = "dummy-lead-tracking-number";

    private static final Long KYC_SCHEDULED_AT = Instant.now().toEpochMilli();

    private static final String LINE_1 = "dummy line 1";

    private static final String LINE_2 = "dummy line 2";

    private static final String LINE_3 = "dummy line 3";

    private static final String PINCODE = "dummy-pincode";

    private static final String CITY = "dummy-city";

    private static final String STATE = "dummy-state";
    private static final String RANDOM_COMPARISON_OPERATOR = "abc";

    public static String getLeadTrackingNumber() {
        return LEAD_TRACKING_NUMBER;
    }

    public static Long getKycScheduledAt() {
        return KYC_SCHEDULED_AT + 10_000_000;
    }

    public static Instant getKycScheduledAtInstant() {
        return Instant.ofEpochMilli(getKycScheduledAt());
    }

    public static String getLine1() {
        return LINE_1;
    }

    public static String getLine2() {
        return LINE_2;
    }

    public static String getLine3() {
        return LINE_3;
    }

    public static String getPincode() {
        return PINCODE;
    }

    public static String getCity() {
        return CITY;
    }

    private static String getState() {
        return STATE;
    }
    public static ComparisonOperator getGreaterThanOperator() { return ComparisonOperator.GT; }
    public static ComparisonOperator getLessThanOperator() { return ComparisonOperator.LT; }
    public static String getRandomComparisonOperator() {return RANDOM_COMPARISON_OPERATOR;}

    public static KycSchedulingRequest createDummyKycSchedulingRequest() {
        return KycSchedulingRequest
                .builder()
                .leadTrackingNumber(getLeadTrackingNumber())
                .scheduledAt(getKycScheduledAt())
                .address(createDummyAddressRequest())
                .scheduleType(ScheduleType.DOOR_TO_DOOR_KYC)
                .build();
    }

    public static KycSchedulingResponse createDummyKycSchedulingResponse() {
        return KycSchedulingResponse
                .builder()
                .leadTrackingNumber(getLeadTrackingNumber())
                .scheduledAt(getKycScheduledAt())
                .address(createDummyAddressResponse())
                .build();
    }

    public static KycSchedule createDummyKycSchedule() {
        return KycSchedule
                .builder()
                .leadTrackingId(getLeadTrackingNumber())
                .scheduledAt(getKycScheduledAtInstant())
                .address(createDummyAddress())
                .build();
    }

    public static Address createDummyAddressRequest() {
        return Address
                .builder()
                .line1(getLine1())
                .line2(getLine2())
                .line3(getLine3())
                .pincode(getPincode())
                .city(getCity())
                .state(getState())
                .build();
    }

    public static com.kotak.unified.dbservice.model.common.Address createDummyAddress() {
        return com.kotak.unified.dbservice.model.common.Address
                .builder()
                .line1(getLine1())
                .line2(getLine2())
                .line3(getLine3())
                .pincode(getPincode())
                .city(getCity())
                .state(getState())
                .build();
    }

    public static AddressResponse createDummyAddressResponse() {
        return AddressResponse
                .builder()
                .line1(getLine1())
                .line2(getLine2())
                .line3(getLine3())
                .pincode(getPincode())
                .city(getCity())
                .state(getState())
                .build();
    }

    public static KycSchedulingDetailsResponse createDummyKycSchedulingDetailsResponse() {
        return KycSchedulingDetailsResponse.builder()
                .kycSchedulingResponseList(List.of(KycScheduleTestDataBuilder.createDummyKycSchedulingResponse()))
                .build();
    }

    public static List<KycSchedule> createDummyKycScheduleList() {
        return List.of(KycScheduleTestDataBuilder.createDummyKycSchedule());
    }

    private KycScheduleTestDataBuilder() {}
}
