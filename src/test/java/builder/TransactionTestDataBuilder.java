package builder;

import com.kotak.unified.db.response.TransactionResponse;
import com.kotak.unified.orchestrator.common.dbmodels.FundsTransferStatus;
import com.kotak.unified.orchestrator.common.dbmodels.Transaction;

import java.time.Instant;

public final class TransactionTestDataBuilder {
    public static final String DUMMY_TRANSACTION_ID = "dummy-transaction-id";
    public static final String DUMMY_LEAD_TRACKING_NUMBER = "dummy-lead-tracking-number";
    public static final String DUMMY_STATUS = "dummy-status";
    public static final Instant NOW = Instant.now();
    public static final String DUMMY_VALUE = "dummy-value";

    public static Transaction createDummyTransaction() {
        return Transaction
                .builder()
                .txnId(DUMMY_TRANSACTION_ID)
                .leadTrackingNumber(DUMMY_LEAD_TRACKING_NUMBER)
                .status(DUMMY_STATUS)
                .fundsTransferStatus(FundsTransferStatus.COMPLETED)
                .createdAt(NOW)
                .mihPayId(DUMMY_VALUE)
                .mode(DUMMY_VALUE)
                .unmappedStatus(DUMMY_VALUE)
                .key(DUMMY_VALUE)
                .amount(DUMMY_VALUE)
                .discount(DUMMY_VALUE)
                .netAmountDebit(DUMMY_VALUE)
                .addedOn(DUMMY_VALUE)
                .productInfo(DUMMY_VALUE)
                .firstName(DUMMY_VALUE)
                .lastName(DUMMY_VALUE)
                .address1(DUMMY_VALUE)
                .address2(DUMMY_VALUE)
                .city(DUMMY_VALUE)
                .state(DUMMY_VALUE)
                .country(DUMMY_VALUE)
                .zipcode(DUMMY_VALUE)
                .email(DUMMY_VALUE)
                .phone(DUMMY_VALUE)
                .requestHash(DUMMY_VALUE)
                .responseHash(DUMMY_VALUE)
                .hashMatching(true)
                .field1(DUMMY_VALUE)
                .field2(DUMMY_VALUE)
                .field3(DUMMY_VALUE)
                .field4(DUMMY_VALUE)
                .field5(DUMMY_VALUE)
                .field6(DUMMY_VALUE)
                .field7(DUMMY_VALUE)
                .field8(DUMMY_VALUE)
                .field9(DUMMY_VALUE)
                .paymentSource(DUMMY_VALUE)
                .meCode(DUMMY_VALUE)
                .pgType(DUMMY_VALUE)
                .bankRefNum(DUMMY_VALUE)
                .bankCode(DUMMY_VALUE)
                .error(DUMMY_VALUE)
                .errorMessage(DUMMY_VALUE)
                .finacleProcessStatus(DUMMY_VALUE)
                .finacleTransactionDate(DUMMY_VALUE)
                .finacleProcessRemarks(DUMMY_VALUE)
                .finacleTransactionId(DUMMY_VALUE)
                .build();
    }

    public static TransactionResponse createDummyTransactionResponse() {
        return TransactionResponse
                .builder()
                .txnId(DUMMY_TRANSACTION_ID)
                .createdAt(Instant.now())
                .build();
    }

    private TransactionTestDataBuilder() {}
}
