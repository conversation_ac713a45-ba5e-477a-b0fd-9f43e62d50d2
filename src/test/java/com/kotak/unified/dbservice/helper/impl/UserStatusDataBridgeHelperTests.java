package com.kotak.unified.dbservice.helper.impl;

import com.kotak.unified.common.exception.DataBridgePublishingException;
import com.kotak.unified.databridgeinterface.enums.DataSource;
import com.kotak.unified.databridgeinterface.model.sqs.DataChangeNotificationMessage;
import com.kotak.unified.dbservice.accessor.DataChangeNotificationQueueAccessor;
import com.kotak.unified.orchestrator.common.dbmodels.UserStatus;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class UserStatusDataBridgeHelperTests {
    private static final String DATA_SOURCE_TRACKING_ID = "TEST_LEAD_ID";
    private UserStatusDataBridgeHelper userStatusDataBridgeHelper;
    @Mock
    private DataChangeNotificationQueueAccessor dataChangeNotificationQueueAccessor;

    @BeforeEach
    void setUp() {
        userStatusDataBridgeHelper = new UserStatusDataBridgeHelper(
                dataChangeNotificationQueueAccessor);
    }

    @Test
    @SneakyThrows
    void publishToDataBridge() {
        DataChangeNotificationMessage expectedDataChangeNotificationMessage = getDataChangeNotificationMessage();
        doNothing().when(dataChangeNotificationQueueAccessor).publishMessage(any());
        Assertions.assertDoesNotThrow(() -> userStatusDataBridgeHelper.publishToDataBridge(getUserStatus(), DataSource.USER_JOURNEY_STATUS));
        final ArgumentCaptor<DataChangeNotificationMessage> captor = ArgumentCaptor.forClass(DataChangeNotificationMessage.class);
        verify(dataChangeNotificationQueueAccessor, times(1)).publishMessage(captor.capture());
        DataChangeNotificationMessage actualDataChangeNotificationMessage = captor.getValue();
        Assertions.assertEquals(expectedDataChangeNotificationMessage, actualDataChangeNotificationMessage);
    }

    @Test
    @SneakyThrows
    void publishToDataBridgeHandlingException() {
        DataChangeNotificationMessage expectedDataChangeNotificationMessage = getDataChangeNotificationMessage();
        doThrow(new RuntimeException()).when(dataChangeNotificationQueueAccessor).publishMessage(any());
        UserStatus userStatus = getUserStatus();
        DataBridgePublishingException dpe = Assertions.assertThrows(DataBridgePublishingException.class,
                () -> userStatusDataBridgeHelper.publishToDataBridge(userStatus, DataSource.USER_JOURNEY_STATUS));
        final ArgumentCaptor<DataChangeNotificationMessage> captor = ArgumentCaptor.forClass(DataChangeNotificationMessage.class);
        verify(dataChangeNotificationQueueAccessor, times(1)).publishMessage(captor.capture());
        DataChangeNotificationMessage actualDataChangeNotificationMessage = captor.getValue();
        Assertions.assertEquals(expectedDataChangeNotificationMessage, actualDataChangeNotificationMessage);
        Assertions.assertEquals(userStatus.getLeadTrackingNumber(),dpe.getLeadTrackingNumber());
        Assertions.assertEquals(expectedDataChangeNotificationMessage.getDataSource().name(),dpe.getDataSourceType());
        Assertions.assertEquals(expectedDataChangeNotificationMessage.getDataSourceTrackingId(),dpe.getDataSourceTrackingId());
    }

    private DataChangeNotificationMessage getDataChangeNotificationMessage () {
        return DataChangeNotificationMessage.builder()
                .dataSource(DataSource.USER_JOURNEY_STATUS)
                .dataSourceTrackingId(DATA_SOURCE_TRACKING_ID)
                .build();
    }

    private UserStatus getUserStatus() {
        return UserStatus.builder().leadTrackingNumber(DATA_SOURCE_TRACKING_ID).build();
    }
}
