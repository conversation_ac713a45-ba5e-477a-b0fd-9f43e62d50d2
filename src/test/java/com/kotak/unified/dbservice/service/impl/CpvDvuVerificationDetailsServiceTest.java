package com.kotak.unified.dbservice.service.impl;

import com.kotak.unified.dbinterface.models.CpvDvuVerificationDetailsDto;
import com.kotak.unified.dbservice.TestUtils;
import com.kotak.unified.dbservice.exceptions.custom.RestException;
import com.kotak.unified.dbservice.model.cpv.CpvDvuVerificationDetailsDDBModel;
import com.kotak.unified.dbservice.repository.CpvDvuVerificationDetailsDDBRepository;
import com.kotak.unified.dbservice.transformer.CpvDvuVerificationDetailsTransformer;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import software.amazon.awssdk.services.dynamodb.model.ConditionalCheckFailedException;

@ExtendWith(MockitoExtension.class)
public class CpvDvuVerificationDetailsServiceTest {
    @Mock
    private CpvDvuVerificationDetailsTransformer cpvDvuVerificationDetailsTransformer;
    @Mock
    private CpvDvuVerificationDetailsDDBRepository cpvDvuVerificationDetailsDDBRepository;
    @InjectMocks
    private CpvDvuVerificationDetailsService cpvDvuVerificationDetailsService;

    @Test
    public void test_save_success() {
        CpvDvuVerificationDetailsDto cpvDvuVerificationDetailsDto = TestUtils.getMockedCpvDvuVerificationDetailsDto();
        CpvDvuVerificationDetailsDDBModel cpvDvuVerificationDetailsDDBModel = TestUtils.getMockedCpvDvuVerificationDetailsDDB();

        CpvDvuVerificationDetailsDto cpvDvuVerificationDetailsDtoUpdated = TestUtils.getMockedCpvDvuVerificationDetailsDto();
        cpvDvuVerificationDetailsDtoUpdated.setVersion(2);

        Mockito.when(cpvDvuVerificationDetailsTransformer.convertDtoToDDBModel(cpvDvuVerificationDetailsDto))
                .thenReturn(cpvDvuVerificationDetailsDDBModel);

        Mockito.when(cpvDvuVerificationDetailsDDBRepository.save(cpvDvuVerificationDetailsDDBModel))
                .thenReturn(cpvDvuVerificationDetailsDDBModel);

        Mockito.when(cpvDvuVerificationDetailsTransformer.convertDDBModelToDto(cpvDvuVerificationDetailsDDBModel))
                .thenReturn(cpvDvuVerificationDetailsDtoUpdated);
        CpvDvuVerificationDetailsDto actualDto = this.cpvDvuVerificationDetailsService.save(cpvDvuVerificationDetailsDto);
        Assertions.assertEquals(cpvDvuVerificationDetailsDtoUpdated, actualDto);
    }

    @Test
    public void test_save_conditional_check() {
        CpvDvuVerificationDetailsDto cpvDvuVerificationDetailsDto = TestUtils.getMockedCpvDvuVerificationDetailsDto();
        CpvDvuVerificationDetailsDDBModel cpvDvuVerificationDetailsDDBModel = TestUtils.getMockedCpvDvuVerificationDetailsDDB();

        Mockito.when(cpvDvuVerificationDetailsTransformer.convertDtoToDDBModel(cpvDvuVerificationDetailsDto))
                .thenReturn(cpvDvuVerificationDetailsDDBModel);

        Mockito.when(cpvDvuVerificationDetailsDDBRepository.save(cpvDvuVerificationDetailsDDBModel))
                .thenThrow(ConditionalCheckFailedException.builder().message("Invalid version").build());
        RestException re = Assertions.assertThrows(RestException.class, () ->
                this.cpvDvuVerificationDetailsService.save(cpvDvuVerificationDetailsDto));
        Assertions.assertEquals(HttpStatus.BAD_REQUEST, re.getHttpStatus());
        Assertions.assertEquals(1, re.getErrorResponseList().size());
        Assertions.assertEquals("DB021", re.getErrorResponseList().get(0).getErrorCode());
        Assertions.assertEquals("Mismatch in version of ddb item present in db and from request", re.getErrorResponseList().get(0).getErrorMessage());
    }

    @Test
    public void test_get_cpv_dvu_success() {
        CpvDvuVerificationDetailsDto cpvDvuVerificationDetailsDto = TestUtils.getMockedCpvDvuVerificationDetailsDto();
        CpvDvuVerificationDetailsDDBModel cpvDvuVerificationDetailsDDBModel = TestUtils.getMockedCpvDvuVerificationDetailsDDB();

        Mockito.when(cpvDvuVerificationDetailsDDBRepository
                        .getByActionTrackingId(cpvDvuVerificationDetailsDto.getActionTrackingId()))
                .thenReturn(cpvDvuVerificationDetailsDDBModel);
        Mockito.when(cpvDvuVerificationDetailsTransformer.convertDDBModelToDto(cpvDvuVerificationDetailsDDBModel))
                .thenReturn(cpvDvuVerificationDetailsDto);
        CpvDvuVerificationDetailsDto actual = this.cpvDvuVerificationDetailsService
                .getCpvDvuVerificationDetails(cpvDvuVerificationDetailsDto.getActionTrackingId());
        Assertions.assertEquals(cpvDvuVerificationDetailsDto, actual);
    }

    @Test
    public void test_get_cpv_dvu_not_present() {
        Mockito.when(cpvDvuVerificationDetailsDDBRepository.getByActionTrackingId("a1"))
                .thenReturn(null);
        RestException re = Assertions.assertThrows(RestException.class, () ->
                this.cpvDvuVerificationDetailsService.getCpvDvuVerificationDetails("a1"));
        Assertions.assertEquals(HttpStatus.NOT_FOUND, re.getHttpStatus());
        Assertions.assertEquals(1, re.getErrorResponseList().size());
        Assertions.assertEquals("DB020", re.getErrorResponseList().get(0).getErrorCode());
        Assertions.assertEquals("Cpv DVU verification details not found", re.getErrorResponseList().get(0).getErrorMessage());
    }
}
