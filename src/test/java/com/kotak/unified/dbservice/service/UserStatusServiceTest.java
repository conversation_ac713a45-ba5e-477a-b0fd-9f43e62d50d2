package com.kotak.unified.dbservice.service;

import com.google.gson.Gson;
import com.kotak.unified.db.AadhaarDetailsResponse;
import com.kotak.unified.db.DormantAccountJourneyMetadataResponse;
import com.kotak.unified.db.ExecutionDataResponse;
import com.kotak.unified.db.JourneyMetadataResponse;
import com.kotak.unified.db.PersonalLoanJourneyMetadataResponse;
import com.kotak.unified.db.SavingsAccountJourneyMetadataResponse;
import com.kotak.unified.db.UserStatusResponse;
import com.kotak.unified.db.mf.MutualFundsJourneyMetadataResponse;
import com.kotak.unified.db.response.EtbAccountDetailDto;
import com.kotak.unified.db.response.EtbDetailsDto;
import com.kotak.unified.dbservice.TestUtils;
import com.kotak.unified.dbservice.enums.ErrorCause;
import com.kotak.unified.dbservice.exceptions.custom.RestException;
import com.kotak.unified.dbservice.helper.impl.UserStatusDataBridgeHelper;
import com.kotak.unified.dbservice.model.exception.DatabaseErrorResponse;
import com.kotak.unified.dbservice.repository.UserStatusFacade;
import com.kotak.unified.dbservice.transformer.UserStatusTransformer;
import com.kotak.unified.enums.UserJourneyStatusField;
import com.kotak.unified.orchestrator.common.dbmodels.AadhaarDetails;
import com.kotak.unified.orchestrator.common.dbmodels.BankDetails;
import com.kotak.unified.orchestrator.common.dbmodels.BusinessLoanJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.DormantAccountActivationJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.EtbAccountDetail;
import com.kotak.unified.orchestrator.common.dbmodels.EtbDetails;
import com.kotak.unified.orchestrator.common.dbmodels.ExecutionData;
import com.kotak.unified.orchestrator.common.dbmodels.JourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.JourneyStatus;
import com.kotak.unified.orchestrator.common.dbmodels.MutualFundsJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.NRSavingsAccountJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.PersonalLoanJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.ProductDetails;
import com.kotak.unified.orchestrator.common.dbmodels.SavingsAccountJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.UserStatus;
import com.kotak.unified.request.PatchUserJourneyStatusRequest;
import com.kotak.unified.request.PatchUserJourneyStatusResponse;
import org.json.JSONException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.modelmapper.ModelMapper;
import org.modelmapper.convention.MatchingStrategies;
import org.skyscreamer.jsonassert.JSONAssert;
import org.springframework.http.HttpStatus;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;

@ExtendWith(MockitoExtension.class)
public class UserStatusServiceTest {
    private final UserStatusFacade userStatusFacade;
    private final UserStatusService userStatusService;
    private final UserStatusTransformer userStatusTransformer;
    private final UserStatusDataBridgeHelper userStatusDataBridgeHelper;
    private final Gson gson;

    UserStatusServiceTest() {
        this.userStatusFacade = Mockito.mock(UserStatusFacade.class);
        this.userStatusTransformer = Mockito.mock(UserStatusTransformer.class);
        this.userStatusDataBridgeHelper = Mockito.mock(UserStatusDataBridgeHelper.class);
        ModelMapper modelMapper = new ModelMapper();
        modelMapper.getConfiguration().setMatchingStrategy(MatchingStrategies.STRICT);
        this.userStatusService = new UserStatusService(userStatusFacade, modelMapper, userStatusTransformer, userStatusDataBridgeHelper);
        this.gson = new Gson();
    }

    @Test
    public void testGetUserStatusWithLeadTrackingNumberSuccess() throws JSONException {
        String lead = "123";
        UserStatus us = TestUtils.getUserStatusWithSavingsAccountJourneyMetadata();
        Mockito.when(this.userStatusFacade.findByLeadTrackingNumber(lead)).thenReturn(us);
        UserStatusResponse usr = this.userStatusService.getUserStatus(lead);
        this.validateUserStatusResponse(us, usr, true);
    }

    @Test
    public void testGetUserStatusWithLeadTrackingNumberWithPendingVerificationForNR() throws JSONException {
        String lead = "123";
        UserStatus us = UserStatus.builder().journeyStatus(JourneyStatus.PENDING_VERIFICATION)
                .journeyType("NRSavingsAccount")
                        .journeyMetadata(NRSavingsAccountJourneyMetadata.builder().build())
                .build();
        Mockito.when(this.userStatusFacade.findByLeadTrackingNumber(lead)).thenReturn(us);
        UserStatusResponse usr = this.userStatusService.getUserStatus(lead);
        Assertions.assertEquals(us.getJourneyStatus().toString(), usr.getJourneyStatus().toString());
    }

    @Test
    public void testGetUserStatusWithLeadTrackingNumberSuccessForPdl() throws JSONException {
        String lead = "123";
        UserStatus us = TestUtils.getUserStatusWithPaydayLoanJourneyMetadata();
        Mockito.when(this.userStatusFacade.findByLeadTrackingNumber(lead)).thenReturn(us);
        UserStatusResponse usr = this.userStatusService.getUserStatus(lead);
        Assertions.assertNotNull(usr.getJourneyMetadata());

    }


    @Test
    public void testGetUserStatusWithLeadTrackingNumberWithBusinessJourneyMetadataSuccess() throws JSONException {
        String lead = "123";
        UserStatus us = TestUtils.getUserStatusWithSavingsAccountJourneyMetadata();
        us.setJourneyMetadata(new BusinessLoanJourneyMetadata());
        Mockito.when(this.userStatusFacade.findByLeadTrackingNumber(lead)).thenReturn(us);
        UserStatusResponse usr = this.userStatusService.getUserStatus(lead);
        this.validateUserStatusResponse(us, usr, false);
    }

    @Test
    public void testGetUserStatusWithLeadTrackingNumberWithPersonalLoanJourneyMetadataSuccess() throws JSONException {
        String lead = "123";
        UserStatus us = TestUtils.getUserStatusWithPersonalLoanJourneyMetadata();
        Mockito.when(this.userStatusFacade.findByLeadTrackingNumber(lead)).thenReturn(us);
        UserStatusResponse usr = this.userStatusService.getUserStatus(lead);
        this.validateUserStatusResponse(us, usr, true);
    }

    @Test
    public void testGetUserStatusWithLeadTrackingNumberWithDormancyJourneyMetadataSuccess() throws JSONException {
        UserStatus us = TestUtils.getUserStatusWithDormancyJourneyMetadata();
        Mockito.when(this.userStatusFacade.findByLeadTrackingNumber(TestUtils.LEAD_ID)).thenReturn(us);
        UserStatusResponse usr = this.userStatusService.getUserStatus(us.getLeadTrackingNumber());
        this.validateUserStatusResponse(us, usr, true);
    }

    @Test
    public void testGetUserStatusWithLeadTrackingNumberWithInvalidJourneyMetadata() throws JSONException {
        String lead = "123";
        UserStatus us = TestUtils.getUserStatusWithSavingsAccountJourneyMetadata();
        us.setJourneyMetadata(null);
        Mockito.when(this.userStatusFacade.findByLeadTrackingNumber(lead)).thenReturn(us);
        UserStatusResponse usr = this.userStatusService.getUserStatus(lead);
        Assertions.assertNull(usr.getJourneyMetadata());
        this.validateUserStatusResponse(us, usr, false);
    }

    @Test
    public void testGetUserStatusWithMissingIdThrowsNotFoundException()
    {
        String lead = "123";
        Mockito.when(this.userStatusFacade.findByLeadTrackingNumber(lead)).thenReturn(null);
        RestException res = assertThrows(RestException.class,
                () -> this.userStatusService.getUserStatus(lead));
        Assertions.assertEquals(HttpStatus.NOT_FOUND, res.getHttpStatus());
        List<DatabaseErrorResponse> expected = new ArrayList<>();
        expected.add(DatabaseErrorResponse.fromErrorCode(ErrorCause.LEAD_TRACKING_NUMBER_NOT_FOUND));
        Assertions.assertEquals(expected, res.getErrorResponseList());
    }

    @Test
    public void testUpdateUserStatus_with_validLeadId_success() {
        String leadId = "validL1";
        UserStatus us =  UserStatus.builder().leadTrackingNumber(leadId).build();
        Mockito.when(userStatusFacade.findByLeadTrackingNumber(leadId)).thenReturn(us);
        Map<UserJourneyStatusField, Object> updates = new HashMap<>();
        updates.put(UserJourneyStatusField.RM_CODE,"RM112");
        UserStatus updated = UserStatus.builder().leadTrackingNumber(leadId)
                .journeyMetadata(SavingsAccountJourneyMetadata.builder()
                        .productDetails(ProductDetails.builder()
                                .rmCode("RM112")
                                .lgCode("LG112")
                                .lcCode("LC112")
                                .corpCode("CC112")
                                .lineOfBusiness("LOB112")
                                .build())
                        .bankDetails(BankDetails.builder().branchCode("BC112").build())
                        .build())
                .build();
        PatchUserJourneyStatusRequest pjs = PatchUserJourneyStatusRequest.builder().updates(updates).build();
        Mockito.when(userStatusTransformer.applyPatch(us,updates)).thenReturn(updated);
        Mockito.when(userStatusFacade.save(updated)).thenReturn(updated);

        PatchUserJourneyStatusResponse updatedResponse = this.userStatusService.updateUserStatus(leadId, pjs);
        Assertions.assertTrue(updatedResponse.getIsPatchSuccessful());
        Mockito.verify(this.userStatusDataBridgeHelper, Mockito.times(1))
                .publishToDataBridge(any(), any());
    }

    @Test
    public void testUpdateUserStatus_with_validLeadId_success_dataChangeNotificationFails() {
        String leadId = "validL1";
        UserStatus us =  UserStatus.builder().leadTrackingNumber(leadId).build();
        Mockito.when(userStatusFacade.findByLeadTrackingNumber(leadId)).thenReturn(us);
        Map<UserJourneyStatusField, Object> updates = new HashMap<>();
        updates.put(UserJourneyStatusField.RM_CODE,"RM112");
        UserStatus updated = UserStatus.builder().leadTrackingNumber(leadId)
                .journeyMetadata(SavingsAccountJourneyMetadata.builder()
                        .productDetails(ProductDetails.builder()
                                .rmCode("RM112")
                                .lgCode("LG112")
                                .lcCode("LC112")
                                .corpCode("CC112")
                                .lineOfBusiness("LOB112")
                                .build())
                        .bankDetails(BankDetails.builder().branchCode("BC112").build())
                        .build())
                .build();
        PatchUserJourneyStatusRequest pjs = PatchUserJourneyStatusRequest.builder().updates(updates).build();
        Mockito.when(userStatusTransformer.applyPatch(us,updates)).thenReturn(updated);
        Mockito.when(userStatusFacade.save(updated)).thenReturn(updated);
        doThrow(new RuntimeException("Exception")).when(userStatusDataBridgeHelper).publishToDataBridge(any(), any());

        PatchUserJourneyStatusResponse updatedResponse = this.userStatusService.updateUserStatus(leadId, pjs);
        Assertions.assertTrue(updatedResponse.getIsPatchSuccessful());
        Mockito.verify(this.userStatusDataBridgeHelper, Mockito.times(1))
                .publishToDataBridge(any(), any());
    }

    @Test
    public void testUpdateUserStatus_with_newLeadId_failure() {
        String leadId = "validL1";
        Mockito.when(userStatusFacade.findByLeadTrackingNumber(leadId)).thenReturn(null);
        RestException re = assertThrows(RestException.class,
                () -> this.userStatusService.updateUserStatus(leadId,null));
        Assertions.assertNotNull(re);
        Assertions.assertEquals(HttpStatus.NOT_FOUND, re.getHttpStatus());
        Mockito.verify(this.userStatusDataBridgeHelper, Mockito.times(0))
                .publishToDataBridge(any(), any());
    }

    private void validateUserStatusResponse(UserStatus us, UserStatusResponse usr
    , boolean validateJourneyMetadata) throws JSONException {
        Assertions.assertEquals(us.getLeadTrackingNumber(), usr.getLeadTrackingNumber());
        Assertions.assertEquals(us.getBankLeadTrackingNumber(), usr.getBankLeadTrackingNumber());
        Assertions.assertEquals(us.getPhoneNumber(), usr.getPhoneNumber());
        Assertions.assertEquals(us.getProductId(), usr.getProductId());
        Assertions.assertEquals(us.getPanNumber(), usr.getPanNumber());
        Assertions.assertEquals(us.getJourneyType(), usr.getJourneyType());
        Assertions.assertEquals(us.getUserIp(), usr.getUserIp());
        Assertions.assertEquals(us.getCreatedTime(), usr.getCreatedTime());
        Assertions.assertEquals(us.getLastModifiedTime(), usr.getLastModifiedTime());
        Assertions.assertEquals(us.getJourneyStatus().toString(), usr.getJourneyStatus().toString());
        Assertions.assertEquals(us.getJourneyStatusReasonCode(), usr.getJourneyStatusReasonCode());
        validateExecutionDataResponse(us.getExecutionData(), usr.getExecutionData());
        if (validateJourneyMetadata) {
        // JSON serialization throwing errors for classes with Instant fields.
        // Hence, validating each field separately for such classes - ExecutionData, AadhaarDetails
            validateJourneyMetadataResponse(us.getJourneyMetadata(), usr.getJourneyMetadata());
        }
    }

    private void validateExecutionDataResponse(ExecutionData expected, ExecutionDataResponse actual) {
        Assertions.assertEquals(expected.getIsFirstTimeAadhaarOTPSent(), actual.getIsFirstTimeAadhaarOTPSent());
        Assertions.assertEquals(expected.getOtpResendAttemptsLeft(), actual.getOtpResendAttemptsLeft());
        Assertions.assertEquals(expected.getOtpRetryAttemptsLeft(), actual.getOtpRetryAttemptsLeft());
        Assertions.assertEquals(expected.getAadhaarRetryLastAttemptedTime(), actual.getAadhaarRetryLastAttemptedTime());
        Assertions.assertEquals(expected.getNcifLastVerifiedTime(), actual.getNcifLastVerifiedTime());
        Assertions.assertEquals(expected.getIsFirstTimeEmailOTPSent(), actual.getIsFirstTimeEmailOTPSent());
        Assertions.assertEquals(expected.getEmailOTPResendAttempts(), actual.getEmailOTPResendAttempts());
        Assertions.assertEquals(expected.getEmailOTPWrongAttempts(), actual.getEmailOTPWrongAttempts());
        Assertions.assertEquals(expected.getEmailOTPRetryLastAttemptedTime(), actual.getEmailOTPRetryLastAttemptedTime());
        Assertions.assertEquals(expected.getEmailValidationRetryAttemptsLeft(), actual.getEmailValidationRetryAttemptsLeft());
        Assertions.assertEquals(expected.getIsMobileOtpAlreadySent(), actual.getIsMobileOtpAlreadySent());
        Assertions.assertEquals(expected.getMobileOTPResendAttempts(), actual.getMobileOTPResendAttempts());
        Assertions.assertEquals(expected.getMobileOTPWrongAttempts(), actual.getMobileOTPWrongAttempts());
        Assertions.assertEquals(expected.getIsPanAadhaarLinked(), actual.getIsPanAadhaarLinked());
        Assertions.assertEquals(expected.getOtpId(), actual.getOtpId());
        Assertions.assertEquals(expected.getIsNomineeSkipped(), actual.getIsNomineeSkipped());
        Assertions.assertEquals(expected.getVerificationUnblockTime(), actual.getVerificationUnblockTime());
        Assertions.assertEquals(expected.getIsInCompleteDOB(), actual.getIsInCompleteDOB());
        Assertions.assertEquals(expected.getAddFundsAttempts(), actual.getAddFundsAttempts());
        Assertions.assertEquals(expected.getIsEmailAddressVerified(), actual.getIsEmailAddressVerified());
        Assertions.assertEquals(expected.getAddFundsLastAttemptedTime(), actual.getAddFundsLastAttemptedTime());
    }

    private void validateJourneyMetadataResponse(JourneyMetadata ex, JourneyMetadataResponse ac) throws JSONException {
        if(ex instanceof SavingsAccountJourneyMetadata savingsAccountEx) {
            SavingsAccountJourneyMetadataResponse savingsAccountAc = (SavingsAccountJourneyMetadataResponse) ac;
            Assertions.assertEquals(savingsAccountEx.getCrn(), savingsAccountAc.getCrn());
            Assertions.assertEquals(savingsAccountEx.getAccountNumber(), savingsAccountAc.getAccountNumber());
            Assertions.assertEquals(savingsAccountEx.getAccountOpeningDate(), savingsAccountAc.getAccountOpeningDate());
            Assertions.assertEquals(savingsAccountEx.getActivMoneyOptIn(), savingsAccountAc.getActivMoneyOptIn());
            Assertions.assertEquals(savingsAccountEx.getEmailAddress(), savingsAccountAc.getEmailAddress());
            Assertions.assertEquals(savingsAccountEx.getIsCommunicationAddressSameAsAadhaarAddress(),
                    savingsAccountAc.getIsCommunicationAddressSameAsAadhaarAddress());
            Assertions.assertEquals(savingsAccountEx.getInitialPanNumber(), savingsAccountAc.getInitialPanNumber());

            validateObjectsUsingJsonSerialization(savingsAccountEx.getVkycDetails(), savingsAccountAc.getVkycDetails());
            validateObjectsUsingJsonSerialization(savingsAccountEx.getProductDetails(), savingsAccountAc.getProductDetails());
            validateObjectsUsingJsonSerialization(savingsAccountEx.getCommunicationAddress(), savingsAccountAc.getCommunicationAddress());
            validateObjectsUsingJsonSerialization(savingsAccountEx.getPanDetails(), savingsAccountAc.getPanDetails());
            validateObjectsUsingJsonSerialization(savingsAccountEx.getPersonalDetails(), savingsAccountAc.getPersonalDetails());
            validateObjectsUsingJsonSerialization(savingsAccountEx.getNomineeDetails(), savingsAccountAc.getNomineeDetails());
            validateAadhaarDetails(savingsAccountEx.getAadhaarDetails(), savingsAccountAc.getAadhaarDetails());
        } else if (ex instanceof PersonalLoanJourneyMetadata personalLoanEx) {
            PersonalLoanJourneyMetadataResponse personalLoanAc = (PersonalLoanJourneyMetadataResponse) ac;
            validateAadhaarDetails(personalLoanEx.getApplicantDetails().getAadhaarDetails(),
                    personalLoanAc.getApplicantDetails().getAadhaarDetails());
            validateObjectsUsingJsonSerialization(personalLoanEx.getApplicantDetails().getAdditionalDetails(),
                    personalLoanEx.getApplicantDetails().getAdditionalDetails());
            validateObjectsUsingJsonSerialization(personalLoanEx.getApplicantDetails().getCrnDetails(),
                    personalLoanAc.getApplicantDetails().getCrnDetails());
            validateObjectsUsingJsonSerialization(personalLoanEx.getApplicantDetails().getIncomeDetails(),
                    personalLoanAc.getApplicantDetails().getIncomeDetails());
            validateObjectsUsingJsonSerialization(personalLoanEx.getApplicantDetails().getPanDetails(),
                    personalLoanAc.getApplicantDetails().getPanDetails());
            validateObjectsUsingJsonSerialization(personalLoanEx.getApplicantDetails().getAddress(),
                    personalLoanAc.getApplicantDetails().getAddress());
            validateObjectsUsingJsonSerialization(personalLoanEx.getApplicantDetails().getVkycDetails(),
                    personalLoanAc.getApplicantDetails().getVkycDetails());
            validateObjectsUsingJsonSerialization(personalLoanEx.getLoanIntent(), personalLoanAc.getLoanIntent());
            validateObjectsUsingJsonSerialization(personalLoanEx.getProductDetails(), personalLoanAc.getProductDetails());

            Assertions.assertEquals(String.valueOf(personalLoanEx.getApplicantDetails().getCommunicationAddressSameAsAddress()),
                    String.valueOf(personalLoanAc.getApplicantDetails().getCommunicationAddressSameAsAddress()));
            Assertions.assertEquals(personalLoanEx.getApplicantDetails().getEmailId(),
                    personalLoanAc.getApplicantDetails().getEmailId());
            Assertions.assertEquals(String.valueOf(personalLoanEx.getPlApplicationMileStone()),
                    String.valueOf(personalLoanAc.getPlApplicationMileStone()));
            Assertions.assertEquals(personalLoanEx.getBlockedReasons(), personalLoanAc.getBlockedReasons());
        } else if (ex instanceof DormantAccountActivationJourneyMetadata dorExpected) {
            DormantAccountJourneyMetadataResponse dorActual = (DormantAccountJourneyMetadataResponse) ac;
            validateEtbDetails(dorExpected.getEtbDetails(), dorActual.getEtbDetails());
            validateEtbAccountDetails(dorExpected.getValidDormantAccounts(), dorActual.getValidDormantAccounts());
        }
        else if (ex instanceof MutualFundsJourneyMetadata mfJourneyMetadataExpected) {
            MutualFundsJourneyMetadataResponse mfJourneyMetadataActual = (MutualFundsJourneyMetadataResponse) ac;
            validateEtbDetails(mfJourneyMetadataExpected.getEtbDetails(), mfJourneyMetadataActual.getEtbDetails());
            validateEtbAccountDetails(mfJourneyMetadataExpected.getValidAccounts(), mfJourneyMetadataActual.getValidAccounts());
        }
    }

    private void validateEtbDetails(EtbDetails expectedEtbDetails, EtbDetailsDto actualEtbDetails) {
        if(expectedEtbDetails==null) return;
        if (expectedEtbDetails.getCrnList() != null) {
            for (int i = 0; i < expectedEtbDetails.getCrnList().size(); i++) {
                Assertions.assertEquals(expectedEtbDetails.getCrnList().get(i), actualEtbDetails.getCrnList().get(i));
            }
        }
        this.validateEtbAccountDetails(expectedEtbDetails.getEtbAccountDetailList(),
                actualEtbDetails.getEtbAccountDetailList());
    }

    private void validateEtbAccountDetails(List<EtbAccountDetail> expectedAccountDetails,
                                            List<EtbAccountDetailDto> actualAccountDetails) {
        if (expectedAccountDetails != null) {
            for (int i = 0; i < expectedAccountDetails.size(); i++) {
                EtbAccountDetail expectedAccountDetail = expectedAccountDetails.get(i);
                EtbAccountDetailDto actualAccountDetail = actualAccountDetails.get(i);
                Assertions.assertEquals(expectedAccountDetail.getAccountNumber(), actualAccountDetail.getAccountNumber());
                Assertions.assertEquals(expectedAccountDetail.getBranchCode(), actualAccountDetail.getBranchCode());
                Assertions.assertEquals(expectedAccountDetail.getSchemeCode(), actualAccountDetail.getSchemeCode());
            }
        }
    }

    private <T,K> void  validateObjectsUsingJsonSerialization(T expected, K actual) throws JSONException{
        String expectedString = gson.toJson(expected);
        org.json.JSONObject actualJson = new org.json.JSONObject(gson.toJson(actual));
        JSONAssert.assertEquals(expectedString, actualJson, false);
    }

    private void validateAadhaarDetails(AadhaarDetails ex, AadhaarDetailsResponse ac) throws JSONException {
        Assertions.assertEquals(ex.getCareOf(), ac.getCareOf());
        Assertions.assertEquals(ex.getCode(), ac.getCode());
        Assertions.assertEquals(ex.getDob(), ac.getDOB());
        Assertions.assertEquals(ex.getDistrict(), ac.getDistrict());
        Assertions.assertEquals(ex.getGender(), ac.getGender());
        Assertions.assertEquals(ex.getHouse(), ac.getHouse());
        Assertions.assertEquals(ex.getLandmark(), ac.getLandmark());
        Assertions.assertEquals(ex.getLocality(), ac.getLocality());
        Assertions.assertEquals(ex.getName(), ac.getName());
        Assertions.assertEquals(ex.getPhoto(), ac.getPhoto());
        Assertions.assertEquals(ex.getPincode(), ac.getPincode());
        Assertions.assertEquals(ex.getRefKey(), ac.getRefKey());
        validateObjectsUsingJsonSerialization(ex.getDerivedAadhaarDetails(), ac.getDerivedAadhaarDetails());
    }
}
