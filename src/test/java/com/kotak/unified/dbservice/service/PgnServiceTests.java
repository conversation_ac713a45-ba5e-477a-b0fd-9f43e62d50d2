package com.kotak.unified.dbservice.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.JsonParseException;
import com.kotak.unified.common.request.bcif.GetCrnDetailsRequest;
import com.kotak.unified.common.response.ErrorResponse;
import com.kotak.unified.common.response.bcif.GetCrnDetailsResponse;
import com.kotak.unified.db.request.pgn.AssignPgnRequest;
import com.kotak.unified.db.request.pgn.InsertPgnRequest;
import com.kotak.unified.db.response.pgn.AssignPgnResponse;
import com.kotak.unified.db.response.pgn.InsertPgnResponse;
import com.kotak.unified.dbservice.accessor.BcifServiceAccessor;
import com.kotak.unified.dbservice.config.RetryConfig;
import com.kotak.unified.dbservice.enums.PgnType;
import com.kotak.unified.dbservice.exceptions.EntityExistsException;
import com.kotak.unified.dbservice.exceptions.PgnDepletedException;
import com.kotak.unified.dbservice.exceptions.PgnAssignmentException;
import com.kotak.unified.dbservice.model.ExternalServiceExecutionDetails;
import com.kotak.unified.dbservice.model.PGNStatus;
import com.kotak.unified.dbservice.model.pgn.AccountNumSchemeCodePgnMetadata;
import com.kotak.unified.dbservice.model.pgn.PgnDDBModel;
import com.kotak.unified.dbservice.repository.PgnDDBRepository;
import com.kotak.unified.dbservice.transformer.PgnTransformer;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import software.amazon.awssdk.services.dynamodb.model.ConditionalCheckFailedException;
import software.amazon.awssdk.services.dynamodb.model.DynamoDbException;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class PgnServiceTests {

    private final PgnTransformer pgnTransformer;
    private final PgnDDBRepository pgnDDBRepository;
    private final BcifServiceAccessor bcifServiceAccessor;

    private final PgnService pgnService;
    private final String MOCK_ACCOUNT_NUMBER = "**********";
    private final String MOCK_SCHEME_CODE = "CSSAC";

    public PgnServiceTests() {
        this.pgnTransformer = mock();
        this.pgnDDBRepository = mock();
        this.bcifServiceAccessor = mock();

        this.pgnService = new PgnService(pgnDDBRepository, pgnTransformer,
                bcifServiceAccessor, new RetryConfig().retryTemplate());
    }

    @Test
    public void test_assignPgn_success_happyCase() throws JsonProcessingException {
        AssignPgnRequest assignPgnRequest = AssignPgnRequest.builder()
                .leadTrackingNumber("leadId")
                .pgnType("SAVINGS")
                .build();
        AccountNumSchemeCodePgnMetadata pgnMetadata = AccountNumSchemeCodePgnMetadata.builder()
                .accountNumber("**********")
                .schemeCode("CSSAC")
                .build();
        PgnDDBModel unAssignedPgn = getUnAssignedPgnDdbModel();
        when(pgnDDBRepository.findByLeadTrackingNumberAndPgnTypeAndPgnStatus(
                "leadId", "SAVINGS", "ASSIGNED")).thenReturn(null);

        //1 tc for throw exception for dynamodb exception

        when(pgnDDBRepository.findByPgnTypeAndPgnStatus("SAVINGS", "UN_ASSIGNED")).
                thenReturn(unAssignedPgn);

        PgnDDBModel assignedPgnDdbModel = getAssignedPgnDdbModel();

        when(pgnDDBRepository.save(unAssignedPgn)).thenReturn(assignedPgnDdbModel);

        GetCrnDetailsResponse getCrnDetailsResponse = GetCrnDetailsResponse.builder()
                .isCrnActivated(false)
                .isSuccess(true)
                .build();
        ExternalServiceExecutionDetails<GetCrnDetailsRequest, GetCrnDetailsResponse> dynamicInfoResponse =
                new ExternalServiceExecutionDetails<>(GetCrnDetailsRequest.builder().build(), getCrnDetailsResponse, null);
        when(bcifServiceAccessor.getDynamicInfo("********", "leadId")).thenReturn(dynamicInfoResponse);

        AssignPgnResponse expectedResponse = getAssignPgnResponse();
        when(pgnTransformer.convertPgnDdbModelToAssignPgnResponse(assignedPgnDdbModel, false)).thenReturn(expectedResponse);

        AssignPgnResponse assignPgnResponse = pgnService.assignPgn(assignPgnRequest);

        com.kotak.unified.db.pgn.AccountNumSchemeCodePgnMetadata responsePgnMetadata = (com.kotak.unified.db.pgn.AccountNumSchemeCodePgnMetadata) assignPgnResponse.getPgnMetadata();
        Assertions.assertEquals(false, assignPgnResponse.getPgnAlreadyAssigned());
        Assertions.assertEquals(pgnMetadata.getAccountNumber(), responsePgnMetadata.getAccountNumber());
        Assertions.assertEquals(pgnMetadata.getSchemeCode(), responsePgnMetadata.getSchemeCode());
        Assertions.assertEquals("SAVINGS", assignPgnResponse.getPgnType());
        Assertions.assertEquals("leadId", assignPgnResponse.getLeadTrackingNumber());
        verify(pgnDDBRepository).findByLeadTrackingNumberAndPgnTypeAndPgnStatus("leadId", "SAVINGS", "ASSIGNED");
        verify(pgnDDBRepository).findByPgnTypeAndPgnStatus("SAVINGS", "UN_ASSIGNED");
        verify(pgnDDBRepository).save(assignedPgnDdbModel);
        verify(bcifServiceAccessor).getDynamicInfo("********", "leadId");
    }

    @Test
    public void test_assignPgn_failure_existingPgnQueryDynamoDbException() throws JsonProcessingException {
        AssignPgnRequest assignPgnRequest = AssignPgnRequest.builder()
                .leadTrackingNumber("leadId")
                .pgnType("SAVINGS")
                .build();

        // Mock DynamoDbException for existing PGN query - as requested by mentor at line 68
        when(pgnDDBRepository.findByLeadTrackingNumberAndPgnTypeAndPgnStatus(
                "leadId", "SAVINGS", "ASSIGNED"))
                .thenThrow(DynamoDbException.builder().message("DynamoDB connection error").build());

        PgnAssignmentException exception = Assertions.assertThrows(PgnAssignmentException.class,
                () -> pgnService.assignPgn(assignPgnRequest));

        Assertions.assertEquals("Failed to query existing PGN assignment", exception.getMessage());
        Assertions.assertNotNull(exception.getCause());
        Assertions.assertTrue(exception.getCause() instanceof DynamoDbException);

        verify(pgnDDBRepository).findByLeadTrackingNumberAndPgnTypeAndPgnStatus("leadId", "SAVINGS", "ASSIGNED");
        verify(pgnDDBRepository, times(0)).findByPgnTypeAndPgnStatus(any(), any());
        verify(pgnDDBRepository, times(0)).save(any());
        verify(bcifServiceAccessor, times(0)).getDynamicInfo(any(), any());
    }

    @Test
    public void test_assignPgn_failure_bcifServiceJsonProcessingException() throws JsonProcessingException {
        AssignPgnRequest assignPgnRequest = AssignPgnRequest.builder()
                .leadTrackingNumber("leadId")
                .pgnType("SAVINGS")
                .build();

        when(pgnDDBRepository.findByLeadTrackingNumberAndPgnTypeAndPgnStatus(
                "leadId", "SAVINGS", "ASSIGNED")).thenReturn(null);

        when(pgnDDBRepository.findByPgnTypeAndPgnStatus("SAVINGS", "UN_ASSIGNED"))
                .thenReturn(getUnAssignedPgnDdbModel());

        PgnDDBModel assignedPgnDdbModel = getAssignedPgnDdbModel();
        when(pgnDDBRepository.save(any())).thenReturn(assignedPgnDdbModel);

        // Mock JsonProcessingException for external service call
        try {
            doThrow(new JsonParseException(null, "JSON parsing error"))
                    .when(bcifServiceAccessor).getDynamicInfo("********", "leadId");
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Test setup failed", e);
        }

        RuntimeException exception = Assertions.assertThrows(RuntimeException.class,
                () -> pgnService.assignPgn(assignPgnRequest));

        Assertions.assertEquals("Pgn assignment failed for lead id: leadId, all retry attempts exhausted", exception.getMessage());
        verify(pgnDDBRepository).findByLeadTrackingNumberAndPgnTypeAndPgnStatus("leadId", "SAVINGS", "ASSIGNED");
        verify(pgnDDBRepository, times(3)).findByPgnTypeAndPgnStatus("SAVINGS", "UN_ASSIGNED");
        verify(pgnDDBRepository, times(2)).save(any()); // 1 for assignment + 1 for ABORTED status, then retry finds same record
        verify(bcifServiceAccessor, times(1)).getDynamicInfo("********", "leadId"); // Only called once before CRN treated as activated
    }

    @Test
    public void test_assignPgn_success_PgnAlreadyAssigned() throws JsonProcessingException {
        AssignPgnRequest assignPgnRequest = AssignPgnRequest.builder()
                .leadTrackingNumber("leadId")
                .pgnType("SAVINGS")
                .build();

        AccountNumSchemeCodePgnMetadata pgnMetadata = AccountNumSchemeCodePgnMetadata.builder()
                .accountNumber("**********")
                .schemeCode("CSSAC")
                .build();

        PgnDDBModel assignedPgnDdbModel = getAssignedPgnDdbModel();
        when(pgnDDBRepository.findByLeadTrackingNumberAndPgnTypeAndPgnStatus(
                "leadId", "SAVINGS", "ASSIGNED")).thenReturn(assignedPgnDdbModel);

        AssignPgnResponse expectedResponse = getAssignPgnResponse();
        when(pgnTransformer.convertPgnDdbModelToAssignPgnResponse(assignedPgnDdbModel, true)).thenReturn(expectedResponse);

        AssignPgnResponse assignPgnResponse = pgnService.assignPgn(assignPgnRequest);
        assignPgnResponse.setPgnAlreadyAssigned(true);

        com.kotak.unified.db.pgn.AccountNumSchemeCodePgnMetadata responsePgnMetadata = (com.kotak.unified.db.pgn.AccountNumSchemeCodePgnMetadata) assignPgnResponse.getPgnMetadata();
        Assertions.assertEquals(true, assignPgnResponse.getPgnAlreadyAssigned());
        Assertions.assertEquals(pgnMetadata.getAccountNumber(), responsePgnMetadata.getAccountNumber());
        Assertions.assertEquals(pgnMetadata.getSchemeCode(), responsePgnMetadata.getSchemeCode());
        Assertions.assertEquals("SAVINGS", assignPgnResponse.getPgnType());
        Assertions.assertEquals("leadId", assignPgnResponse.getLeadTrackingNumber());
        verify(pgnDDBRepository).findByLeadTrackingNumberAndPgnTypeAndPgnStatus("leadId", "SAVINGS", "ASSIGNED");
        verify(pgnDDBRepository, times(0)).findByPgnTypeAndPgnStatus(any(), any());
        verify(pgnDDBRepository, times(0)).save(any());
        verify(bcifServiceAccessor, times(0)).getDynamicInfo(any(), any());
    }

    @Test
    public void test_assignPgn_failure_PgnDepleted() throws JsonProcessingException {
        AssignPgnRequest assignPgnRequest = AssignPgnRequest.builder()
                .leadTrackingNumber("leadId")
                .pgnType("SAVINGS")
                .build();
        when(pgnDDBRepository.findByLeadTrackingNumberAndPgnTypeAndPgnStatus(
                "leadId", "SAVINGS", "ASSIGNED")).
                thenReturn(null);

        when(pgnDDBRepository.findByPgnTypeAndPgnStatus("SAVINGS", "UN_ASSIGNED")).
                thenReturn(null);

        PgnDepletedException pgnDepletedException = Assertions.assertThrows(PgnDepletedException.class, () -> pgnService.assignPgn(assignPgnRequest));

        Assertions.assertEquals("No PGN available to assign for PGN Type: SAVINGS", pgnDepletedException.getMessage());
        verify(pgnDDBRepository).findByLeadTrackingNumberAndPgnTypeAndPgnStatus("leadId", "SAVINGS", "ASSIGNED");
        verify(pgnDDBRepository, times(1)).findByPgnTypeAndPgnStatus("SAVINGS", "UN_ASSIGNED");
        verify(pgnDDBRepository, times(0)).save(any());
        verify(bcifServiceAccessor, times(0)).getDynamicInfo(any(), any());
    }

    @Test
    public void test_assignPgn_failure_leadIdPresentInUnassignedLead_allTimes() throws JsonProcessingException {
        AssignPgnRequest assignPgnRequest = AssignPgnRequest.builder()
                .leadTrackingNumber("leadId")
                .pgnType("SAVINGS")
                .build();
        when(pgnDDBRepository.findByLeadTrackingNumberAndPgnTypeAndPgnStatus(
                "leadId", "SAVINGS", "ASSIGNED")).
                thenReturn(null);


        PgnDDBModel unAssignedPgn = getUnAssignedPgnDdbModel();
        unAssignedPgn.setLeadTrackingNumber("leadId");
        when(pgnDDBRepository.findByPgnTypeAndPgnStatus("SAVINGS", "UN_ASSIGNED")).
                thenReturn(unAssignedPgn);

        RuntimeException exception = Assertions.assertThrows(RuntimeException.class, () -> pgnService.assignPgn(assignPgnRequest));

        Assertions.assertEquals("Pgn assignment failed for lead id: leadId, " +
                "all retry attempts exhausted", exception.getMessage());
        verify(pgnDDBRepository).findByLeadTrackingNumberAndPgnTypeAndPgnStatus("leadId", "SAVINGS", "ASSIGNED");
        verify(pgnDDBRepository, times(3)).findByPgnTypeAndPgnStatus("SAVINGS", "UN_ASSIGNED");
        verify(pgnDDBRepository, times(0)).save(any());
        verify(bcifServiceAccessor, times(0)).getDynamicInfo(any(), any());
    }

    @Test
    public void test_assignPgn_success_leadIdPresentInUnassignedLead_once() throws JsonProcessingException {
        AssignPgnRequest assignPgnRequest = AssignPgnRequest.builder()
                .leadTrackingNumber("leadId")
                .pgnType("SAVINGS")
                .build();

        when(pgnDDBRepository.findByLeadTrackingNumberAndPgnTypeAndPgnStatus(
                "leadId", "SAVINGS", "ASSIGNED")).thenReturn(null);

        PgnDDBModel pgnWithOutLeadTrackingNumber = getUnAssignedPgnDdbModel();
        PgnDDBModel pgnWithLeadTrackingNumber = getUnAssignedPgnDdbModel();
        pgnWithLeadTrackingNumber.setLeadTrackingNumber("leadId");
        when(pgnDDBRepository.findByPgnTypeAndPgnStatus("SAVINGS", "UN_ASSIGNED")).
                thenReturn(pgnWithLeadTrackingNumber, pgnWithOutLeadTrackingNumber);

        PgnDDBModel assignedPgnDdbModel = getAssignedPgnDdbModel();
        when(pgnDDBRepository.save(pgnWithOutLeadTrackingNumber)).thenReturn(assignedPgnDdbModel);

        GetCrnDetailsResponse getCrnDetailsResponse = GetCrnDetailsResponse.builder()
                .isCrnActivated(false)
                .isSuccess(true)
                .build();
        ExternalServiceExecutionDetails<GetCrnDetailsRequest, GetCrnDetailsResponse> dynamicInfoResponse =
                new ExternalServiceExecutionDetails<>(GetCrnDetailsRequest.builder().build(), getCrnDetailsResponse, null);
        when(bcifServiceAccessor.getDynamicInfo("********", "leadId")).thenReturn(dynamicInfoResponse);

        AssignPgnResponse expectedResponse = getAssignPgnResponse();
        when(pgnTransformer.convertPgnDdbModelToAssignPgnResponse(assignedPgnDdbModel, false)).thenReturn(expectedResponse);

        AssignPgnResponse assignPgnResponse = pgnService.assignPgn(assignPgnRequest);

        com.kotak.unified.db.pgn.AccountNumSchemeCodePgnMetadata responsePgnMetadata = (com.kotak.unified.db.pgn.AccountNumSchemeCodePgnMetadata) assignPgnResponse.getPgnMetadata();
        Assertions.assertEquals(false, assignPgnResponse.getPgnAlreadyAssigned());
        Assertions.assertEquals(MOCK_ACCOUNT_NUMBER, responsePgnMetadata.getAccountNumber());
        Assertions.assertEquals(MOCK_SCHEME_CODE, responsePgnMetadata.getSchemeCode());
        Assertions.assertEquals("SAVINGS", assignPgnResponse.getPgnType());
        Assertions.assertEquals("leadId", assignPgnResponse.getLeadTrackingNumber());
        verify(pgnDDBRepository).findByLeadTrackingNumberAndPgnTypeAndPgnStatus("leadId", "SAVINGS", "ASSIGNED");
        verify(pgnDDBRepository, times(2)).findByPgnTypeAndPgnStatus("SAVINGS", "UN_ASSIGNED");
        verify(pgnDDBRepository, times(1)).save(any());
        verify(bcifServiceAccessor, times(1)).getDynamicInfo(any(), any());
    }

    @Test
    public void test_assignPgn_success_leadIdPresentInUnassignedLead_twice() throws JsonProcessingException {
        AssignPgnRequest assignPgnRequest = AssignPgnRequest.builder()
                .leadTrackingNumber("leadId")
                .pgnType("SAVINGS")
                .build();

        when(pgnDDBRepository.findByLeadTrackingNumberAndPgnTypeAndPgnStatus(
                "leadId", "SAVINGS", "ASSIGNED")).thenReturn(null);

        when(pgnDDBRepository.findByPgnTypeAndPgnStatus("SAVINGS", "UN_ASSIGNED")).thenReturn(null);

        PgnDDBModel pgnWithOutLeadTrackingNumber = getUnAssignedPgnDdbModel();
        PgnDDBModel pgnWithLeadTrackingNumber = getUnAssignedPgnDdbModel();
        pgnWithLeadTrackingNumber.setLeadTrackingNumber("leadId");
        when(pgnDDBRepository.findByPgnTypeAndPgnStatus("SAVINGS", "UN_ASSIGNED")).
                thenReturn(pgnWithLeadTrackingNumber, pgnWithLeadTrackingNumber, pgnWithOutLeadTrackingNumber);

        PgnDDBModel assignedPgnDdbModel = getAssignedPgnDdbModel();
        when(pgnDDBRepository.save(pgnWithOutLeadTrackingNumber)).thenReturn(assignedPgnDdbModel);

        GetCrnDetailsResponse getCrnDetailsResponse = GetCrnDetailsResponse.builder()
                .isCrnActivated(false)
                .isSuccess(true)
                .build();
        ExternalServiceExecutionDetails<GetCrnDetailsRequest, GetCrnDetailsResponse> dynamicInfoResponse =
                new ExternalServiceExecutionDetails<>(GetCrnDetailsRequest.builder().build(), getCrnDetailsResponse, null);
        when(bcifServiceAccessor.getDynamicInfo("********", "leadId")).thenReturn(dynamicInfoResponse);

        AssignPgnResponse expectedResponse = getAssignPgnResponse();
        when(pgnTransformer.convertPgnDdbModelToAssignPgnResponse(assignedPgnDdbModel, false)).thenReturn(expectedResponse);

        AssignPgnResponse assignPgnResponse = pgnService.assignPgn(assignPgnRequest);

        com.kotak.unified.db.pgn.AccountNumSchemeCodePgnMetadata responsePgnMetadata = (com.kotak.unified.db.pgn.AccountNumSchemeCodePgnMetadata) assignPgnResponse.getPgnMetadata();
        Assertions.assertEquals(false, assignPgnResponse.getPgnAlreadyAssigned());
        Assertions.assertEquals(MOCK_ACCOUNT_NUMBER, responsePgnMetadata.getAccountNumber());
        Assertions.assertEquals(MOCK_SCHEME_CODE, responsePgnMetadata.getSchemeCode());
        Assertions.assertEquals("SAVINGS", assignPgnResponse.getPgnType());
        Assertions.assertEquals("leadId", assignPgnResponse.getLeadTrackingNumber());
        verify(pgnDDBRepository).findByLeadTrackingNumberAndPgnTypeAndPgnStatus("leadId", "SAVINGS", "ASSIGNED");
        verify(pgnDDBRepository, times(3)).findByPgnTypeAndPgnStatus("SAVINGS", "UN_ASSIGNED");
        verify(pgnDDBRepository, times(1)).save(any());
        verify(bcifServiceAccessor, times(1)).getDynamicInfo(any(), any());
    }

    @Test
    public void test_assignPgn_failure_conditionalCheckedException_allTimes() throws JsonProcessingException {
        AssignPgnRequest assignPgnRequest = AssignPgnRequest.builder()
                .leadTrackingNumber("leadId")
                .pgnType("SAVINGS")
                .build();

        when(pgnDDBRepository.findByLeadTrackingNumberAndPgnTypeAndPgnStatus(
                "leadId", "SAVINGS", "ASSIGNED")).thenReturn(null);


        when(pgnDDBRepository.findByPgnTypeAndPgnStatus("SAVINGS", "UN_ASSIGNED"))
                .thenReturn(getUnAssignedPgnDdbModel(), getUnAssignedPgnDdbModel(), getUnAssignedPgnDdbModel());

        when(pgnDDBRepository.save(any()))
                .thenThrow(ConditionalCheckFailedException.builder().message("Mock exception").build());

        RuntimeException exception = Assertions.assertThrows(RuntimeException.class, () -> pgnService.assignPgn(assignPgnRequest));

        Assertions.assertEquals("Pgn assignment failed for lead id: leadId, all retry attempts exhausted", exception.getMessage());
        verify(pgnDDBRepository).findByLeadTrackingNumberAndPgnTypeAndPgnStatus("leadId", "SAVINGS", "ASSIGNED");
        verify(pgnDDBRepository, times(3)).findByPgnTypeAndPgnStatus("SAVINGS", "UN_ASSIGNED");
        verify(pgnDDBRepository, times(3)).save(any());
        verify(bcifServiceAccessor, times(0)).getDynamicInfo(any(), any());
    }

    @Test
    public void test_assignPgn_success_conditionalCheckedException_once() throws JsonProcessingException {
        AssignPgnRequest assignPgnRequest = AssignPgnRequest.builder()
                .leadTrackingNumber("leadId")
                .pgnType("SAVINGS")
                .build();

        when(pgnDDBRepository.findByLeadTrackingNumberAndPgnTypeAndPgnStatus(
                "leadId", "SAVINGS", "ASSIGNED")).thenReturn(null);

        when(pgnDDBRepository.findByPgnTypeAndPgnStatus("SAVINGS", "UN_ASSIGNED"))
                .thenReturn(getUnAssignedPgnDdbModel(), getUnAssignedPgnDdbModel());

        PgnDDBModel assignedPgnDdbModel = getAssignedPgnDdbModel();
        when(pgnDDBRepository.save(any()))
                .thenThrow(ConditionalCheckFailedException.builder().message("Mock exception").build())
                .thenReturn(assignedPgnDdbModel);

        GetCrnDetailsResponse getCrnDetailsResponse = GetCrnDetailsResponse.builder()
                .isCrnActivated(false)
                .isSuccess(true)
                .build();
        ExternalServiceExecutionDetails<GetCrnDetailsRequest, GetCrnDetailsResponse> dynamicInfoResponse =
                new ExternalServiceExecutionDetails<>(GetCrnDetailsRequest.builder().build(), getCrnDetailsResponse, null);
        when(bcifServiceAccessor.getDynamicInfo("********", "leadId")).thenReturn(dynamicInfoResponse);

        AssignPgnResponse expectedResponse = getAssignPgnResponse();
        when(pgnTransformer.convertPgnDdbModelToAssignPgnResponse(assignedPgnDdbModel, false)).thenReturn(expectedResponse);

        AssignPgnResponse assignPgnResponse = pgnService.assignPgn(assignPgnRequest);
        com.kotak.unified.db.pgn.AccountNumSchemeCodePgnMetadata responsePgnMetadata =
                (com.kotak.unified.db.pgn.AccountNumSchemeCodePgnMetadata) assignPgnResponse.getPgnMetadata();

        Assertions.assertEquals(false, assignPgnResponse.getPgnAlreadyAssigned());
        Assertions.assertEquals(MOCK_ACCOUNT_NUMBER, responsePgnMetadata.getAccountNumber());
        Assertions.assertEquals(MOCK_SCHEME_CODE, responsePgnMetadata.getSchemeCode());
        Assertions.assertEquals("SAVINGS", assignPgnResponse.getPgnType());
        Assertions.assertEquals("leadId", assignPgnResponse.getLeadTrackingNumber());
        verify(pgnDDBRepository).findByLeadTrackingNumberAndPgnTypeAndPgnStatus("leadId", "SAVINGS", "ASSIGNED");
        verify(pgnDDBRepository, times(2)).findByPgnTypeAndPgnStatus("SAVINGS", "UN_ASSIGNED");
        verify(pgnDDBRepository, times(2)).save(any());
        verify(bcifServiceAccessor, times(1)).getDynamicInfo(any(), any());
    }

    @Test
    public void test_assignPgn_failure_conditionalCheckedException_twice() throws JsonProcessingException {
        AssignPgnRequest assignPgnRequest = AssignPgnRequest.builder()
                .leadTrackingNumber("leadId")
                .pgnType("SAVINGS")
                .build();

        when(pgnDDBRepository.findByLeadTrackingNumberAndPgnTypeAndPgnStatus(
                "leadId", "SAVINGS", "ASSIGNED")).thenReturn(null);

        when(pgnDDBRepository.findByPgnTypeAndPgnStatus("SAVINGS", "UN_ASSIGNED"))
                .thenReturn(getUnAssignedPgnDdbModel(), getUnAssignedPgnDdbModel(), getUnAssignedPgnDdbModel());

        PgnDDBModel assignedPgnDdbModel = getAssignedPgnDdbModel();
        when(pgnDDBRepository.save(any()))
                .thenThrow(ConditionalCheckFailedException.builder().message("Mock exception").build())
                .thenThrow(ConditionalCheckFailedException.builder().message("Mock exception").build())
                .thenReturn(assignedPgnDdbModel);

        GetCrnDetailsResponse getCrnDetailsResponse = GetCrnDetailsResponse.builder()
                .isCrnActivated(false)
                .isSuccess(true)
                .build();
        ExternalServiceExecutionDetails<GetCrnDetailsRequest, GetCrnDetailsResponse> dynamicInfoResponse =
                new ExternalServiceExecutionDetails<>(GetCrnDetailsRequest.builder().build(), getCrnDetailsResponse, null);
        when(bcifServiceAccessor.getDynamicInfo("********", "leadId")).thenReturn(dynamicInfoResponse);

        AssignPgnResponse expectedResponse = getAssignPgnResponse();
        when(pgnTransformer.convertPgnDdbModelToAssignPgnResponse(assignedPgnDdbModel, false)).thenReturn(expectedResponse);

        AssignPgnResponse assignPgnResponse = pgnService.assignPgn(assignPgnRequest);
        com.kotak.unified.db.pgn.AccountNumSchemeCodePgnMetadata responsePgnMetadata =
                (com.kotak.unified.db.pgn.AccountNumSchemeCodePgnMetadata) assignPgnResponse.getPgnMetadata();

        Assertions.assertEquals(false, assignPgnResponse.getPgnAlreadyAssigned());
        Assertions.assertEquals(MOCK_ACCOUNT_NUMBER, responsePgnMetadata.getAccountNumber());
        Assertions.assertEquals(MOCK_SCHEME_CODE, responsePgnMetadata.getSchemeCode());
        Assertions.assertEquals("SAVINGS", assignPgnResponse.getPgnType());
        Assertions.assertEquals("leadId", assignPgnResponse.getLeadTrackingNumber());
        verify(pgnDDBRepository).findByLeadTrackingNumberAndPgnTypeAndPgnStatus("leadId", "SAVINGS", "ASSIGNED");
        verify(pgnDDBRepository, times(3)).findByPgnTypeAndPgnStatus("SAVINGS", "UN_ASSIGNED");
        verify(pgnDDBRepository, times(3)).save(any());
        verify(bcifServiceAccessor, times(1)).getDynamicInfo(any(), any());
    }

    @Test
    public void test_assignPgn_failure_dynamicInfoApiFailure() throws JsonProcessingException {
        AssignPgnRequest assignPgnRequest = AssignPgnRequest.builder()
                .leadTrackingNumber("leadId")
                .pgnType("SAVINGS")
                .build();

        when(pgnDDBRepository.findByLeadTrackingNumberAndPgnTypeAndPgnStatus(
                "leadId", "SAVINGS", "ASSIGNED")).thenReturn(null);

        when(pgnDDBRepository.findByPgnTypeAndPgnStatus("SAVINGS", "UN_ASSIGNED")).thenReturn(null);


        when(pgnDDBRepository.findByPgnTypeAndPgnStatus("SAVINGS", "UN_ASSIGNED")).
                thenReturn(getUnAssignedPgnDdbModel(), getUnAssignedPgnDdbModel(), getUnAssignedPgnDdbModel());

        PgnDDBModel assignedPgnDdbModel = getAssignedPgnDdbModel();
        when(pgnDDBRepository.save(any())).thenReturn(assignedPgnDdbModel);

        when(bcifServiceAccessor.getDynamicInfo("********", "leadId")).thenThrow(new RuntimeException("Test Exception"));

        RuntimeException runtimeException = Assertions.assertThrows(RuntimeException.class, () -> pgnService.assignPgn(assignPgnRequest));

        Assertions.assertEquals("Pgn assignment failed for lead id: leadId, " +
                "all retry attempts exhausted", runtimeException.getMessage());
        verify(pgnDDBRepository).findByLeadTrackingNumberAndPgnTypeAndPgnStatus("leadId", "SAVINGS", "ASSIGNED");
        verify(pgnDDBRepository, times(3)).findByPgnTypeAndPgnStatus("SAVINGS", "UN_ASSIGNED");
        verify(pgnDDBRepository, times(6)).save(any());
        verify(bcifServiceAccessor, times(3)).getDynamicInfo(any(), any());
    }

    @Test
    public void test_assignPgn_failure_dynamicInfoApiErrorResponseNonNull() throws JsonProcessingException {
        AssignPgnRequest assignPgnRequest = AssignPgnRequest.builder()
                .leadTrackingNumber("leadId")
                .pgnType("SAVINGS")
                .build();

        when(pgnDDBRepository.findByLeadTrackingNumberAndPgnTypeAndPgnStatus(
                "leadId", "SAVINGS", "ASSIGNED")).thenReturn(null);

        when(pgnDDBRepository.findByPgnTypeAndPgnStatus("SAVINGS", "UN_ASSIGNED")).thenReturn(null);

        when(pgnDDBRepository.findByPgnTypeAndPgnStatus("SAVINGS", "UN_ASSIGNED")).
                thenReturn(getUnAssignedPgnDdbModel(), getUnAssignedPgnDdbModel(), getUnAssignedPgnDdbModel());

        PgnDDBModel assignedPgnDdbModel = getAssignedPgnDdbModel();
        when(pgnDDBRepository.save(any())).thenReturn(assignedPgnDdbModel);

        ErrorResponse errorResponse = ErrorResponse.builder().errorCode("mock_code").errorMessage("mock error").build();
        ExternalServiceExecutionDetails<GetCrnDetailsRequest, GetCrnDetailsResponse> dynamicInfoResponse =
                new ExternalServiceExecutionDetails<>(GetCrnDetailsRequest.builder().build(), null, errorResponse);
        when(bcifServiceAccessor.getDynamicInfo("********", "leadId"))
                .thenReturn(dynamicInfoResponse);

        RuntimeException runtimeException = Assertions.assertThrows(RuntimeException.class, () -> pgnService.assignPgn(assignPgnRequest));

        Assertions.assertEquals("Pgn assignment failed for lead id: leadId, " +
                "all retry attempts exhausted", runtimeException.getMessage());
        verify(pgnDDBRepository).findByLeadTrackingNumberAndPgnTypeAndPgnStatus("leadId", "SAVINGS", "ASSIGNED");
        verify(pgnDDBRepository, times(3)).findByPgnTypeAndPgnStatus("SAVINGS", "UN_ASSIGNED");
        verify(pgnDDBRepository, times(6)).save(any());
        verify(bcifServiceAccessor, times(3)).getDynamicInfo(any(), any());
    }

    @Test
    public void test_assignPgn_failure_dynamicInfoApiResponseNotSuccess() throws JsonProcessingException {
        AssignPgnRequest assignPgnRequest = AssignPgnRequest.builder()
                .leadTrackingNumber("leadId")
                .pgnType("SAVINGS")
                .build();

        when(pgnDDBRepository.findByLeadTrackingNumberAndPgnTypeAndPgnStatus(
                "leadId", "SAVINGS", "ASSIGNED")).thenReturn(null);

        when(pgnDDBRepository.findByPgnTypeAndPgnStatus("SAVINGS", "UN_ASSIGNED")).thenReturn(null);

        when(pgnDDBRepository.findByPgnTypeAndPgnStatus("SAVINGS", "UN_ASSIGNED")).
                thenReturn(getUnAssignedPgnDdbModel(), getUnAssignedPgnDdbModel(), getUnAssignedPgnDdbModel());

        PgnDDBModel assignedPgnDdbModel = getAssignedPgnDdbModel();
        when(pgnDDBRepository.save(any())).thenReturn(assignedPgnDdbModel);

        GetCrnDetailsResponse getCrnDetailsResponse = GetCrnDetailsResponse.builder().isCrnActivated(false).build();
        ExternalServiceExecutionDetails<GetCrnDetailsRequest, GetCrnDetailsResponse> dynamicInfoResponse =
                new ExternalServiceExecutionDetails<>(GetCrnDetailsRequest.builder().build(), getCrnDetailsResponse, null);
        when(bcifServiceAccessor.getDynamicInfo("********", "leadId"))
                .thenReturn(dynamicInfoResponse);

        RuntimeException runtimeException = Assertions.assertThrows(RuntimeException.class, () -> pgnService.assignPgn(assignPgnRequest));

        Assertions.assertEquals("Pgn assignment failed for lead id: leadId, " +
                "all retry attempts exhausted", runtimeException.getMessage());
        verify(pgnDDBRepository).findByLeadTrackingNumberAndPgnTypeAndPgnStatus("leadId", "SAVINGS", "ASSIGNED");
        verify(pgnDDBRepository, times(3)).findByPgnTypeAndPgnStatus("SAVINGS", "UN_ASSIGNED");
        verify(pgnDDBRepository, times(6)).save(any());
        verify(bcifServiceAccessor, times(3)).getDynamicInfo(any(), any());
    }

    @Test
    public void test_assignPgn_failure_crnAlreadyActivated() throws JsonProcessingException {
        AssignPgnRequest assignPgnRequest = AssignPgnRequest.builder()
                .leadTrackingNumber("leadId")
                .pgnType("SAVINGS")
                .build();

        when(pgnDDBRepository.findByLeadTrackingNumberAndPgnTypeAndPgnStatus(
                "leadId", "SAVINGS", "ASSIGNED")).thenReturn(null);

        when(pgnDDBRepository.findByPgnTypeAndPgnStatus("SAVINGS", "UN_ASSIGNED")).thenReturn(null);

        when(pgnDDBRepository.findByPgnTypeAndPgnStatus("SAVINGS", "UN_ASSIGNED")).
                thenReturn(getUnAssignedPgnDdbModel(), getUnAssignedPgnDdbModel(), getUnAssignedPgnDdbModel());

        PgnDDBModel assignedPgnDdbModel = getAssignedPgnDdbModel();
        when(pgnDDBRepository.save(any())).thenReturn(assignedPgnDdbModel);

        GetCrnDetailsResponse getCrnDetailsResponse = GetCrnDetailsResponse.builder().isSuccess(true).isCrnActivated(true).build();
        ExternalServiceExecutionDetails<GetCrnDetailsRequest, GetCrnDetailsResponse> dynamicInfoResponse =
                new ExternalServiceExecutionDetails<>(GetCrnDetailsRequest.builder().build(), getCrnDetailsResponse, null);
        when(bcifServiceAccessor.getDynamicInfo("********", "leadId"))
                .thenReturn(dynamicInfoResponse);

        RuntimeException runtimeException = Assertions.assertThrows(RuntimeException.class, () -> pgnService.assignPgn(assignPgnRequest));

        Assertions.assertEquals("Pgn assignment failed for lead id: leadId, " +
                "all retry attempts exhausted", runtimeException.getMessage());
        verify(pgnDDBRepository).findByLeadTrackingNumberAndPgnTypeAndPgnStatus("leadId", "SAVINGS", "ASSIGNED");
        verify(pgnDDBRepository, times(3)).findByPgnTypeAndPgnStatus("SAVINGS", "UN_ASSIGNED");
        verify(pgnDDBRepository, times(6)).save(any());
        verify(bcifServiceAccessor, times(3)).getDynamicInfo(any(), any());
    }

    @Test
    public void test_assignPgn_Success_crnAlreadyActivated_once() throws JsonProcessingException {
        AssignPgnRequest assignPgnRequest = AssignPgnRequest.builder()
                .leadTrackingNumber("leadId")
                .pgnType("SAVINGS")
                .build();

        when(pgnDDBRepository.findByLeadTrackingNumberAndPgnTypeAndPgnStatus(
                "leadId", "SAVINGS", "ASSIGNED")).thenReturn(null);

        when(pgnDDBRepository.findByPgnTypeAndPgnStatus("SAVINGS", "UN_ASSIGNED")).thenReturn(null);

        when(pgnDDBRepository.findByPgnTypeAndPgnStatus("SAVINGS", "UN_ASSIGNED")).
                thenReturn(getUnAssignedPgnDdbModel(), getUnAssignedPgnDdbModel());

        PgnDDBModel assignedPgnDdbModel = getAssignedPgnDdbModel();
        when(pgnDDBRepository.save(any())).thenReturn(assignedPgnDdbModel);

        GetCrnDetailsResponse getCrnDetailsResponse1 = GetCrnDetailsResponse.builder().isSuccess(true).isCrnActivated(true).build();
        ExternalServiceExecutionDetails<GetCrnDetailsRequest, GetCrnDetailsResponse> dynamicInfoResponse1 =
                new ExternalServiceExecutionDetails<>(GetCrnDetailsRequest.builder().build(), getCrnDetailsResponse1, null);

        GetCrnDetailsResponse getCrnDetailsResponse2 = GetCrnDetailsResponse.builder().isSuccess(true).isCrnActivated(false).build();
        ExternalServiceExecutionDetails<GetCrnDetailsRequest, GetCrnDetailsResponse> dynamicInfoResponse2 =
                new ExternalServiceExecutionDetails<>(GetCrnDetailsRequest.builder().build(), getCrnDetailsResponse2, null);

        when(bcifServiceAccessor.getDynamicInfo("********", "leadId"))
                .thenReturn(dynamicInfoResponse1, dynamicInfoResponse2);

        AssignPgnResponse expectedResponse = getAssignPgnResponse();
        when(pgnTransformer.convertPgnDdbModelToAssignPgnResponse(assignedPgnDdbModel, false)).thenReturn(expectedResponse);

        AssignPgnResponse assignPgnResponse = pgnService.assignPgn(assignPgnRequest);
        com.kotak.unified.db.pgn.AccountNumSchemeCodePgnMetadata responsePgnMetadata =
                (com.kotak.unified.db.pgn.AccountNumSchemeCodePgnMetadata) assignPgnResponse.getPgnMetadata();

        Assertions.assertEquals(false, assignPgnResponse.getPgnAlreadyAssigned());
        Assertions.assertEquals(MOCK_ACCOUNT_NUMBER, responsePgnMetadata.getAccountNumber());
        Assertions.assertEquals(MOCK_SCHEME_CODE, responsePgnMetadata.getSchemeCode());
        Assertions.assertEquals("SAVINGS", assignPgnResponse.getPgnType());
        Assertions.assertEquals("leadId", assignPgnResponse.getLeadTrackingNumber());
        verify(pgnDDBRepository).findByLeadTrackingNumberAndPgnTypeAndPgnStatus("leadId", "SAVINGS", "ASSIGNED");
        verify(pgnDDBRepository, times(2)).findByPgnTypeAndPgnStatus("SAVINGS", "UN_ASSIGNED");
        verify(pgnDDBRepository, times(3)).save(any());
        verify(bcifServiceAccessor, times(2)).getDynamicInfo(any(), any());
    }

    @Test
    public void test_insertPgn_success_happyCase() throws EntityExistsException {
        InsertPgnRequest insertPgnRequest = getInsertPgnRequest();
        AccountNumSchemeCodePgnMetadata pgnMetadata = AccountNumSchemeCodePgnMetadata.builder()
                .schemeCode(MOCK_SCHEME_CODE)
                .accountNumber(MOCK_ACCOUNT_NUMBER)
                .build();

        PgnDDBModel pgnDDBModel = PgnDDBModel.builder()
                .crn("********")
                .pgnType(PgnType.SAVINGS)
                .pgnMetadata(pgnMetadata)
                .status(PGNStatus.UN_ASSIGNED)
                .build();

        when(pgnDDBRepository.save(any())).thenReturn(pgnDDBModel);

        InsertPgnResponse insertPgnResponse = pgnService.insertPgn(insertPgnRequest);
        Assertions.assertTrue(insertPgnResponse.getSuccess());
        verify(pgnDDBRepository).save(any());
    }

    @Test
    public void test_insertPgn_failure_conditionalCheckedExceptions() {
        InsertPgnRequest insertPgnRequest = getInsertPgnRequest();

        when(pgnDDBRepository.save(any()))
                .thenThrow(ConditionalCheckFailedException.builder().message("Test exception").build());

        EntityExistsException entityExistsException = Assertions.assertThrows(EntityExistsException.class,
                () -> pgnService.insertPgn(insertPgnRequest));

        Assertions.assertEquals("Record with CRN ******** and PGN type: SAVINGS already exists in the DB",
                entityExistsException.getMessage());
        verify(pgnDDBRepository).save(any());
    }

    private InsertPgnRequest getInsertPgnRequest() {
        com.kotak.unified.db.pgn.AccountNumSchemeCodePgnMetadata pgnMetadata = com.kotak.unified.db.pgn.
                AccountNumSchemeCodePgnMetadata.builder().accountNumber(MOCK_ACCOUNT_NUMBER).schemeCode(MOCK_SCHEME_CODE).build();
        return InsertPgnRequest.builder().crn("********").pgnType("SAVINGS").pgnMetadata(pgnMetadata).build();
    }

    private PgnDDBModel getUnAssignedPgnDdbModel() {
        AccountNumSchemeCodePgnMetadata pgnMetadata = AccountNumSchemeCodePgnMetadata.builder()
                .accountNumber("**********")
                .schemeCode("CSSAC")
                .build();
        return PgnDDBModel.builder()
                .crn("********")
                .pgnType(PgnType.SAVINGS)
                .pgnMetadata(pgnMetadata)
                .leadTrackingNumberAndStatus("#UN_ASSIGNED")
                .status(PGNStatus.UN_ASSIGNED)
                .build();
    }

    private PgnDDBModel getAssignedPgnDdbModel() {
        AccountNumSchemeCodePgnMetadata pgnMetadata = AccountNumSchemeCodePgnMetadata.builder()
                .accountNumber(MOCK_ACCOUNT_NUMBER)
                .schemeCode(MOCK_SCHEME_CODE)
                .build();
        return PgnDDBModel.builder()
                .crn("********")
                .pgnType(PgnType.SAVINGS)
                .pgnMetadata(pgnMetadata)
                .leadTrackingNumber("leadId")
                .leadTrackingNumberAndStatus("#UN_ASSIGNED")
                .status(PGNStatus.ASSIGNED)
                .build();
    }

    private AssignPgnResponse getAssignPgnResponse() {
        com.kotak.unified.db.pgn.AccountNumSchemeCodePgnMetadata accountNumSchemeCodePgnMetadata = com.kotak.unified.db.pgn
                .AccountNumSchemeCodePgnMetadata.builder().schemeCode(MOCK_SCHEME_CODE).accountNumber(MOCK_ACCOUNT_NUMBER).build();
        return AssignPgnResponse.builder()
                .pgnAlreadyAssigned(false)
                .crn("********")
                .pgnType("SAVINGS")
                .leadTrackingNumber("leadId")
                .pgnMetadata(accountNumSchemeCodePgnMetadata)
                .build();
    }

}
