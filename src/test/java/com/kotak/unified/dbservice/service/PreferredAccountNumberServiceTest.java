package com.kotak.unified.dbservice.service;

import static com.kotak.unified.dbservice.utils.Constants.SOFT_BLOCK;
import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;

import com.kotak.unified.dbinterface.models.PreferredAccountNumberDto;
import com.kotak.unified.dbservice.exceptions.custom.RestException;
import com.kotak.unified.dbservice.model.PreferredAccountNumberDDBModel;
import com.kotak.unified.dbservice.repository.PreferredAccountNumberDDBRepository;
import com.kotak.unified.dbservice.transformer.PreferredAccountNumberTransformer;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import software.amazon.awssdk.services.dynamodb.model.ConditionalCheckFailedException;

@ExtendWith(MockitoExtension.class)
public class PreferredAccountNumberServiceTest {

    @Mock
    private PreferredAccountNumberTransformer preferredAccountNumberTransformer;

    @Mock
    private PreferredAccountNumberDDBRepository preferredAccountNumberDDBRepository;

    @InjectMocks
    private PreferredAccountNumberService preferredAccountNumberService;

    private PreferredAccountNumberDto preferredAccountNumberDto;
    private PreferredAccountNumberDDBModel preferredAccountNumberDDBModel;

    @BeforeEach
    public void setUp() {
        preferredAccountNumberDto = PreferredAccountNumberDto.builder()
                .preferredAccountNumber("**********")
                .leadTrackingNumber("lead123")
                .blockStatus(SOFT_BLOCK)
                .build();

        preferredAccountNumberDDBModel = PreferredAccountNumberDDBModel.builder()
                .preferredAccountNumber("**********")
                .leadTrackingNumber("lead123")
                .blockStatus(SOFT_BLOCK)
                .build();
    }

    @Test
    public void testSavePreferredAccountNumberRecord_Success() {
        // Arrange
        when(preferredAccountNumberTransformer.convertDtoToDDBModel(preferredAccountNumberDto))
                .thenReturn(preferredAccountNumberDDBModel);
        when(preferredAccountNumberDDBRepository.save(preferredAccountNumberDDBModel))
                .thenReturn(preferredAccountNumberDDBModel);
        when(preferredAccountNumberTransformer.convertDDBModelToDTO(preferredAccountNumberDDBModel))
                .thenReturn(preferredAccountNumberDto);

        // Act
        PreferredAccountNumberDto result = preferredAccountNumberService.savePreferredAccountNumberRecord(preferredAccountNumberDto);

        // Assert
        assertNotNull(result);
        assertEquals(preferredAccountNumberDto, result);
    }

    @Test
    public void testSavePreferredAccountNumberRecord_ConditionalCheckFailed() {
        // Arrange
        when(preferredAccountNumberTransformer.convertDtoToDDBModel(preferredAccountNumberDto))
                .thenReturn(preferredAccountNumberDDBModel);
        when(preferredAccountNumberDDBRepository.save(preferredAccountNumberDDBModel))
                .thenThrow(ConditionalCheckFailedException.builder().message("Invalid version").build());

        // Act & Assert
        RestException exception = assertThrows(RestException.class, () ->
                preferredAccountNumberService.savePreferredAccountNumberRecord(preferredAccountNumberDto));
        assertEquals(HttpStatus.BAD_REQUEST, exception.getHttpStatus());
        assertEquals("DB021", exception.getErrorResponseList().get(0).getErrorCode());
        assertEquals("Mismatch in version of ddb item present in db and from request", exception.getErrorResponseList().get(0).getErrorMessage());
    }

    @Test
    public void testGetPreferredAccountNumberRecord_Success() {
        // Arrange
        when(preferredAccountNumberDDBRepository.getByPreferredAccountNumber("**********"))
                .thenReturn(preferredAccountNumberDDBModel);
        when(preferredAccountNumberTransformer.convertDDBModelToDTO(preferredAccountNumberDDBModel))
                .thenReturn(preferredAccountNumberDto);

        // Act
        PreferredAccountNumberDto result = preferredAccountNumberService.getPreferredAccountNumberRecord("**********");

        // Assert
        assertNotNull(result);
        assertEquals(preferredAccountNumberDto, result);
    }

    @Test
    public void testGetPreferredAccountNumberRecord_NotFound() {
        // Arrange
        when(preferredAccountNumberDDBRepository.getByPreferredAccountNumber("**********"))
                .thenReturn(null);

        // Act & Assert
        RestException exception = assertThrows(RestException.class, () ->
                preferredAccountNumberService.getPreferredAccountNumberRecord("**********"));
        assertEquals(HttpStatus.NOT_FOUND, exception.getHttpStatus());
        assertEquals("DB022", exception.getErrorResponseList().get(0).getErrorCode());
        assertEquals("Preferred account number not found", exception.getErrorResponseList().get(0).getErrorMessage());
    }
}