package com.kotak.unified.dbservice.service.impl;

import com.kotak.unified.common.exception.InvalidRequestException;
import com.kotak.unified.db.ApplicationData;
import com.kotak.unified.db.GetApplicationDataRequest;
import com.kotak.unified.db.PLApplicationData;
import com.kotak.unified.db.response.DormantAccountActivationApplicationData;
import com.kotak.unified.dbservice.exceptions.EntityNotFoundException;
import com.kotak.unified.dbservice.repository.UserStatusFacade;
import com.kotak.unified.dbservice.transformer.ApplicationDataTransformer;
import com.kotak.unified.dbservice.transformer.factory.ApplicationDataTransformerFactory;
import com.kotak.unified.dbservice.transformer.factory.ApplicationDataTransformerFactoryV2;
import com.kotak.unified.orchestrator.common.dbmodels.PersonalLoanJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.UserStatus;
import com.kotak.unified.orchestrator.common.dbmodels.assets.PLProductDetails;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.List;

import static com.kotak.unified.db.ApplicationDataFilter.PHONE_NUMBER;
import static com.kotak.unified.dbservice.utils.Constants.DORMANT_ACCOUNT_ACTIVATION;
import static com.kotak.unified.dbservice.utils.Constants.DORMANT_ACCOUNT_ACTIVATION_GRAPH_NAME;
import static com.kotak.unified.dbservice.utils.Constants.PERSONAL_LOAN;
import static com.kotak.unified.dbservice.utils.Constants.PERSONAL_LOAN_GRAPH_NAME;
import static java.util.Collections.EMPTY_LIST;
import static org.junit.Assert.assertThrows;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class ApplicationDataServiceTest {

  private static final String LEAD_TRACKING_NUMBER = "l1";
  private final UserStatusFacade userStatusFacade;
  private final ApplicationDataTransformerFactory applicationDataTransformerFactory;
  private final ApplicationDataTransformerFactoryV2 applicationDataTransformerFactoryV2;
  private final ApplicationDataTransformer applicationDataTransformer;
  private final ApplicationDataService applicationDataService;
  private final UserStatus userStatus;
  private final GetApplicationDataRequest request;
  private final PersonalLoanJourneyMetadata journeyMetadata;

  ApplicationDataServiceTest() {
    this.userStatusFacade = Mockito.mock(UserStatusFacade.class);
    this.applicationDataTransformerFactory = Mockito.mock(
        ApplicationDataTransformerFactory.class);
    this.applicationDataTransformerFactoryV2 = Mockito.mock(
            ApplicationDataTransformerFactoryV2.class);
    this.applicationDataService = new ApplicationDataService(
        userStatusFacade,
        applicationDataTransformerFactory,
        applicationDataTransformerFactoryV2);
    this.applicationDataTransformer = Mockito.mock(
        ApplicationDataTransformer.class);
    this.userStatus = Mockito.mock(
        UserStatus.class);
    this.request = Mockito.mock(
        GetApplicationDataRequest.class);
    this.journeyMetadata = Mockito.mock(
        PersonalLoanJourneyMetadata.class);
  }

  @Test
  void testGetApplicationDataHappyCase() {
    final ApplicationData applicationData = getApplicationData();
    setDependencyExpectationsHappyCase();
    setDataExpectationsHappyCase(applicationData);
    ApplicationData response = callService();
    Assertions.assertEquals(applicationData, response);
    verifyDependencyExpectationsHappyCase();
  }

  @Test
  void testGetApplicationDataHappyCaseDormantAccountActivation() throws InvalidRequestException, EntityNotFoundException {
    final ApplicationData applicationData = DormantAccountActivationApplicationData.builder().build();
    setDependencyExpectationsHappyCaseDormantAccountActivation();
    setDataExpectationsHappyCaseDormantAccountActivation(applicationData);
    ApplicationData response = callService();
    Assertions.assertEquals(applicationData, response);
    verifyDependencyExpectationsHappyCaseDormantAccountActivation();
  }


  @Test
  void testGetApplicationDataEmptyDataFilter() {
    when(request.getDataFilters()).thenReturn(EMPTY_LIST);
    Exception exception = assertThrows(InvalidRequestException.class,
        this::callService);
    Assertions.assertEquals(
        "Please provide onboarding data filters for which you require data for l1",
        exception.getMessage());
  }

  @Test
  void testGetApplicationDataNonExistingApplicationId() {
    when(request.getDataFilters()).thenReturn(List.of(PHONE_NUMBER));
    when(userStatusFacade.findByLeadTrackingNumber(
        LEAD_TRACKING_NUMBER)).thenReturn(
        null);
    Throwable exception = assertThrows(EntityNotFoundException.class,
        this::callService);
    Assertions.assertEquals(
        "Could not find application with the given applicationId l1",
        exception.getMessage());
  }

  @Test
  void testGetApplicationDataMissingProductDetails() {
    when(request.getDataFilters()).thenReturn(List.of(PHONE_NUMBER));
    when(userStatus.getLeadTrackingNumber()).thenReturn(LEAD_TRACKING_NUMBER);
    when(userStatus.getJourneyType()).thenReturn(PERSONAL_LOAN_GRAPH_NAME);
    when(userStatus.getJourneyMetadata()).thenReturn(journeyMetadata);
    when(journeyMetadata.getProductDetails()).thenReturn(null);
    when(userStatusFacade.findByLeadTrackingNumber(
        LEAD_TRACKING_NUMBER)).thenReturn(
        userStatus);
    Exception exception = assertThrows(InvalidRequestException.class,
        this::callService);
    Assertions.assertEquals(
        "No product exist for the applicationId l1",
        exception.getMessage());
  }

  @Test
  void testGetApplicationDataMissingProductName() {
    when(request.getDataFilters()).thenReturn(List.of(PHONE_NUMBER));
    when(userStatus.getLeadTrackingNumber()).thenReturn(LEAD_TRACKING_NUMBER);
    when(userStatus.getJourneyType()).thenReturn(PERSONAL_LOAN_GRAPH_NAME);
    when(userStatus.getJourneyMetadata()).thenReturn(journeyMetadata);
    when(journeyMetadata.getProductDetails()).thenReturn(
        getProductDetails(null));
    when(userStatusFacade.findByLeadTrackingNumber(
        LEAD_TRACKING_NUMBER)).thenReturn(
        userStatus);
    Exception exception = assertThrows(InvalidRequestException.class,
        this::callService);
    Assertions.assertEquals(
        "No product exist for the applicationId l1",
        exception.getMessage());
  }

  @Test
  void testGetApplicationDataV2EmptyDataFilter() {
    when(request.getDataFilters()).thenReturn(EMPTY_LIST);
    Exception exception = assertThrows(InvalidRequestException.class,
            this::callServiceV2);
    Assertions.assertEquals(
            "Please provide onboarding data filters for which you require data for l1",
            exception.getMessage());
  }

  @SneakyThrows
  private ApplicationData callService() {
    return applicationDataService.getApplicationData(
        request, LEAD_TRACKING_NUMBER);
  }

  @SneakyThrows
  private void setDataExpectationsHappyCase(
      final ApplicationData applicationData) {
    final PLProductDetails productDetails = getProductDetails(PERSONAL_LOAN);
    when(request.getDataFilters()).thenReturn(List.of(PHONE_NUMBER));
    when(userStatus.getJourneyType()).thenReturn(PERSONAL_LOAN_GRAPH_NAME);
    when(userStatus.getJourneyMetadata()).thenReturn(journeyMetadata);
    when(journeyMetadata.getProductDetails()).thenReturn(productDetails);
    when(
        applicationDataTransformer.populateApplicationData(
            request, userStatus)).thenReturn(applicationData);
  }


  private void setDataExpectationsHappyCaseDormantAccountActivation(
          final ApplicationData applicationData) throws InvalidRequestException, EntityNotFoundException {
    when(request.getDataFilters()).thenReturn(List.of(PHONE_NUMBER));
    when(userStatus.getJourneyType()).thenReturn(DORMANT_ACCOUNT_ACTIVATION_GRAPH_NAME);
    when(applicationDataTransformer.populateApplicationData(
                    request, userStatus)).thenReturn(applicationData);
  }

  private void setDependencyExpectationsHappyCase() {
    when(userStatusFacade.findByLeadTrackingNumber(
        LEAD_TRACKING_NUMBER)).thenReturn(
        userStatus);
    when(
        applicationDataTransformerFactory.selectTransformer(
            PERSONAL_LOAN)).thenReturn(applicationDataTransformer);
  }

  private void setDependencyExpectationsHappyCaseDormantAccountActivation() {
    when(userStatusFacade.findByLeadTrackingNumber(
            LEAD_TRACKING_NUMBER)).thenReturn(
            userStatus);
    when(
            applicationDataTransformerFactory.selectTransformer(
                    DORMANT_ACCOUNT_ACTIVATION)).thenReturn(applicationDataTransformer);
  }

  private void verifyDependencyExpectationsHappyCase() {
    verify(userStatusFacade, times(1)).findByLeadTrackingNumber(
        LEAD_TRACKING_NUMBER);
    verify(applicationDataTransformerFactory, times(1)).selectTransformer(
        PERSONAL_LOAN);
  }

  private void verifyDependencyExpectationsHappyCaseDormantAccountActivation() {
    verify(userStatusFacade, times(1)).findByLeadTrackingNumber(
            LEAD_TRACKING_NUMBER);
    verify(applicationDataTransformerFactory, times(1)).selectTransformer(
            DORMANT_ACCOUNT_ACTIVATION);
  }

  @SneakyThrows
  private ApplicationData callServiceV2() {
    return applicationDataService.getApplicationDataV2(
            request, LEAD_TRACKING_NUMBER);
  }

  private ApplicationData getApplicationData() {
    return PLApplicationData.builder().build();
  }

  private PLProductDetails getProductDetails(final String productName) {
    return PLProductDetails.builder().productName(productName).build();
  }
}