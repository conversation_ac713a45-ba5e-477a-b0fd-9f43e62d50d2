package com.kotak.unified.dbservice.service.impl;

import builder.KycScheduleTestDataBuilder;
import com.kotak.unified.common.request.database.KycSchedulingRequest;
import com.kotak.unified.dbservice.exceptions.custom.RestException;
import com.kotak.unified.dbservice.service.KycScheduleService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;

import static org.junit.jupiter.api.Assertions.assertThrows;

class KycScheduleServiceImplTest {


    private final KycScheduleService kycScheduleService;

    KycScheduleServiceImplTest() {
        this.kycScheduleService = new KycScheduleServiceImpl();
    }

    @Test
    void createKycSchedule_MethodNotAllowed() {
        KycSchedulingRequest kycSchedulingRequest = KycScheduleTestDataBuilder.createDummyKycSchedulingRequest();
        RestException restException = assertThrows(RestException.class, () -> kycScheduleService.createKycSchedule(kycSchedulingRequest));

        Assertions.assertEquals(HttpStatus.METHOD_NOT_ALLOWED, restException.getHttpStatus());
    }

    @Test
    void updateKycSchedule_MethodNotAllowed() {
        KycSchedulingRequest kycSchedulingRequest = KycScheduleTestDataBuilder.createDummyKycSchedulingRequest();
        RestException restException = assertThrows(RestException.class, () -> kycScheduleService.updateKycSchedule(kycSchedulingRequest));

        Assertions.assertEquals(HttpStatus.METHOD_NOT_ALLOWED, restException.getHttpStatus());
    }

    @Test
    void getKycScheduleForLeadTrackingId_MethodNotAllowed() {
        String leadTrackingId = KycScheduleTestDataBuilder.getLeadTrackingNumber();

        RestException restException = assertThrows(RestException.class, () -> kycScheduleService.getKycScheduleForLeadTrackingId(leadTrackingId));

        Assertions.assertEquals(HttpStatus.METHOD_NOT_ALLOWED, restException.getHttpStatus());
    }

    @Test
    void getKycSchedules_MethodNotAllowed() {
        Long timestamp = KycScheduleTestDataBuilder.getKycScheduledAt();
        String operator = KycScheduleTestDataBuilder.getRandomComparisonOperator();
        RestException restException = assertThrows(RestException.class, () -> kycScheduleService.getKycSchedulesBasedOnTimestamp(timestamp, operator));
        Assertions.assertEquals(HttpStatus.METHOD_NOT_ALLOWED, restException.getHttpStatus());
    }

//    @Test
//    void createKycSchedule_Success1() {
//        KycSchedulingRequest kycSchedulingRequest = KycScheduleTestDataBuilder.createDummyKycSchedulingRequest();
//        KycSchedulingResponse kycSchedulingResponse = KycScheduleTestDataBuilder.createDummyKycSchedulingResponse();
//        KycSchedule kycSchedule = KycScheduleTestDataBuilder.createDummyKycSchedule();
//
//        when(kycScheduleTransformer.convertRequestToModel(any(KycSchedulingRequest.class))).thenReturn(kycSchedule);
//        when(kycScheduleRepository.save(any(KycSchedule.class))).thenReturn(kycSchedule);
//        when(kycScheduleTransformer.convertModelToResponse(any(KycSchedule.class))).thenReturn(kycSchedulingResponse);
//
//        KycSchedulingResponse actualKycScheduleResponse = kycScheduleService.createKycSchedule(kycSchedulingRequest);
//
//        Assertions.assertEquals(kycSchedulingResponse, actualKycScheduleResponse);
//        verify(kycScheduleTransformer, times(1)).convertRequestToModel(kycSchedulingRequest);
//        verify(kycScheduleRepository, times(1)).save(kycSchedule);
//        verify(kycScheduleTransformer, times(1)).convertModelToResponse(kycSchedule);
//    }
//
//    @Test
//    void createKycSchedule_Db_Exception() {
//        KycSchedulingRequest kycSchedulingRequest = KycScheduleTestDataBuilder.createDummyKycSchedulingRequest();
//        KycSchedule kycSchedule = KycScheduleTestDataBuilder.createDummyKycSchedule();
//
//        when(kycScheduleTransformer.convertRequestToModel(any(KycSchedulingRequest.class))).thenReturn(kycSchedule);
//        when(kycScheduleRepository.save(any(KycSchedule.class))).thenThrow(new DuplicateKeyException(""));
//
//        RestException restException = assertThrows(RestException.class, () -> kycScheduleService.createKycSchedule(kycSchedulingRequest));
//
//        Assertions.assertEquals(HttpStatus.CONFLICT, restException.getHttpStatus());
//        verify(kycScheduleTransformer, times(1)).convertRequestToModel(kycSchedulingRequest);
//        verify(kycScheduleRepository, times(1)).save(kycSchedule);
//        verify(kycScheduleTransformer, times(0)).convertModelToResponse(any(KycSchedule.class));
//    }
//
//    @Test
//    void updateKycSchedule_Success() {
//        KycSchedulingRequest kycSchedulingRequest = KycScheduleTestDataBuilder.createDummyKycSchedulingRequest();
//        KycSchedulingResponse kycSchedulingResponse = KycScheduleTestDataBuilder.createDummyKycSchedulingResponse();
//        KycSchedule kycSchedule = KycScheduleTestDataBuilder.createDummyKycSchedule();
//        Optional<KycSchedule> optionalKycSchedule = Optional.of(kycSchedule);
//        String leadTrackingId = kycSchedulingRequest.getLeadTrackingNumber();
//
//        when(kycScheduleRepository.findKycScheduleByLeadTrackingId(anyString())).thenReturn(optionalKycSchedule);
//        doNothing().when(kycScheduleTransformer).updateKycSchedule(any(KycSchedule.class), any(KycSchedulingRequest.class));
//        when(kycScheduleRepository.save(any(KycSchedule.class))).thenReturn(kycSchedule);
//        when(kycScheduleTransformer.convertModelToResponse(any(KycSchedule.class))).thenReturn(kycSchedulingResponse);
//
//        KycSchedulingResponse actualKycScheduleResponse = kycScheduleService.updateKycSchedule(kycSchedulingRequest);
//
//        Assertions.assertEquals(kycSchedulingResponse, actualKycScheduleResponse);
//        verify(kycScheduleRepository, times(1)).findKycScheduleByLeadTrackingId(leadTrackingId);
//        verify(kycScheduleTransformer, times(1)).updateKycSchedule(kycSchedule, kycSchedulingRequest);
//        verify(kycScheduleRepository, times(1)).save(kycSchedule);
//        verify(kycScheduleTransformer, times(1)).convertModelToResponse(kycSchedule);
//    }
//
//    @Test
//    void updateKycSchedule_Not_Found_Exception() {
//        KycSchedulingRequest kycSchedulingRequest = KycScheduleTestDataBuilder.createDummyKycSchedulingRequest();
//        Optional<KycSchedule> optionalKycSchedule = Optional.empty();
//        String leadTrackingId = kycSchedulingRequest.getLeadTrackingNumber();
//
//        when(kycScheduleRepository.findKycScheduleByLeadTrackingId(anyString())).thenReturn(optionalKycSchedule);
//
//        RestException restException = assertThrows(RestException.class, () -> kycScheduleService.updateKycSchedule(kycSchedulingRequest));
//
//        Assertions.assertEquals(HttpStatus.NOT_FOUND, restException.getHttpStatus());
//        verify(kycScheduleRepository, times(1)).findKycScheduleByLeadTrackingId(leadTrackingId);
//    }
//
//    @Test
//    void getKycScheduleForLeadTrackingId_Success() {
//        String leadTrackingId = KycScheduleTestDataBuilder.getLeadTrackingNumber();
//        KycSchedule kycSchedule = KycScheduleTestDataBuilder.createDummyKycSchedule();
//        KycSchedulingResponse kycSchedulingResponse = KycScheduleTestDataBuilder.createDummyKycSchedulingResponse();
//
//        when(kycScheduleRepository.findKycScheduleByLeadTrackingId(anyString())).thenReturn(Optional.of(kycSchedule));
//        when(kycScheduleTransformer.convertModelToResponse(kycSchedule)).thenReturn(kycSchedulingResponse);
//
//        KycSchedulingResponse kycScheduleForLeadTrackingId = kycScheduleService.getKycScheduleForLeadTrackingId(leadTrackingId);
//
//        Assertions.assertEquals(kycSchedulingResponse, kycScheduleForLeadTrackingId);
//        verify(kycScheduleRepository, times(1)).findKycScheduleByLeadTrackingId(leadTrackingId);
//        verify(kycScheduleTransformer, times(1)).convertModelToResponse(kycSchedule);
//    }
//
//    @Test
//    void getKycScheduleForLeadTrackingId_Failure() {
//        String leadTrackingId = KycScheduleTestDataBuilder.getLeadTrackingNumber();
//
//        when(kycScheduleRepository.findKycScheduleByLeadTrackingId(anyString())).thenReturn(Optional.empty());
//
//        RestException restException = assertThrows(RestException.class, () -> kycScheduleService.getKycScheduleForLeadTrackingId(leadTrackingId));
//
//        Assertions.assertEquals(HttpStatus.NOT_FOUND, restException.getHttpStatus());
//        verify(kycScheduleRepository, times(1)).findKycScheduleByLeadTrackingId(leadTrackingId);
//    }
//
//    @Test
//    void getKycSchedulesForGreaterThanGivenTimestamp() {
//        Long timestamp = KycScheduleTestDataBuilder.getKycScheduledAt();
//        ComparisonOperator comparisonOperator = KycScheduleTestDataBuilder.getGreaterThanOperator();
//        List<KycSchedule> kycSchedules = KycScheduleTestDataBuilder.createDummyKycScheduleList();
//        KycSchedulingDetailsResponse kycSchedulingDetailsResponse = KycScheduleTestDataBuilder.createDummyKycSchedulingDetailsResponse();
//
//        when(kycScheduleRepository.findKycScheduleByScheduledAtGreaterThan(any())).thenReturn(kycSchedules);
//        when(kycScheduleTransformer.convertModelToResponse(kycSchedules)).thenReturn(kycSchedulingDetailsResponse);
//
//        KycSchedulingDetailsResponse kycSchedulesForGreaterThanGivenTimestamp = kycScheduleService.getKycSchedulesBasedOnTimestamp(timestamp, comparisonOperator.toString());
//
//        Assertions.assertEquals(kycSchedulingDetailsResponse, kycSchedulesForGreaterThanGivenTimestamp);
//        verify(kycScheduleRepository, times(1)).findKycScheduleByScheduledAtGreaterThan(Instant.ofEpochMilli(timestamp));
//        verify(kycScheduleTransformer, times(1)).convertModelToResponse(kycSchedules);
//    }
//
//    @Test
//    void getKycSchedulesForLessThanGivenTimestamp() {
//        Long timestamp = KycScheduleTestDataBuilder.getKycScheduledAt();
//        ComparisonOperator comparisonOperator = KycScheduleTestDataBuilder.getLessThanOperator();
//        List<KycSchedule> kycSchedules = KycScheduleTestDataBuilder.createDummyKycScheduleList();
//        KycSchedulingDetailsResponse kycSchedulingDetailsResponse = KycScheduleTestDataBuilder.createDummyKycSchedulingDetailsResponse();
//
//        when(kycScheduleRepository.findKycScheduleByScheduledAtLessThan(any())).thenReturn(kycSchedules);
//        when(kycScheduleTransformer.convertModelToResponse(kycSchedules)).thenReturn(kycSchedulingDetailsResponse);
//
//        KycSchedulingDetailsResponse kycSchedulesForGreaterThanGivenTimestamp = kycScheduleService.getKycSchedulesBasedOnTimestamp(timestamp, comparisonOperator.toString());
//
//        Assertions.assertEquals(kycSchedulingDetailsResponse, kycSchedulesForGreaterThanGivenTimestamp);
//        verify(kycScheduleRepository, times(1)).findKycScheduleByScheduledAtLessThan(Instant.ofEpochMilli(timestamp));
//        verify(kycScheduleTransformer, times(1)).convertModelToResponse(kycSchedules);
//    }
//
//    @Test
//    void getKycSchedulesTrowsRestException() {
//        Long timestamp = KycScheduleTestDataBuilder.getKycScheduledAt();
//        String operator = KycScheduleTestDataBuilder.getRandomComparisonOperator();
//        List<KycSchedule> kycSchedules = KycScheduleTestDataBuilder.createDummyKycScheduleList();
//        KycSchedulingDetailsResponse kycSchedulingDetailsResponse = KycScheduleTestDataBuilder.createDummyKycSchedulingDetailsResponse();
//
//        when(kycScheduleRepository.findKycScheduleByScheduledAtLessThan(any())).thenReturn(kycSchedules);
//        when(kycScheduleTransformer.convertModelToResponse(kycSchedules)).thenReturn(kycSchedulingDetailsResponse);
//
//        assertThrows(RestException.class,  ()->kycScheduleService.getKycSchedulesBasedOnTimestamp(timestamp, operator));
//
//        verify(kycScheduleRepository, times(0)).findKycScheduleByScheduledAtLessThan(Instant.ofEpochMilli(timestamp));
//        verify(kycScheduleTransformer, times(0)).convertModelToResponse(kycSchedules);
//    }


}