package com.kotak.unified.dbservice.service;

import com.kotak.unified.db.model.ActiveScheduledNotificationNudgeDetailsDTO;
import com.kotak.unified.db.model.CancelledNotificationNudgeDetailsDTO;
import com.kotak.unified.db.model.CreateResumeJourneyNotificationStatusRecordRequest;
import com.kotak.unified.db.model.CreateResumeJourneyNotificationStatusRecordResponse;
import com.kotak.unified.db.model.GetResumeJourneyNotificationStatusRecordResponse;
import com.kotak.unified.db.model.RecipientTypeDTO;
import com.kotak.unified.db.model.UpdateResumeJourneyNotificationStatusRecordRequest;
import com.kotak.unified.db.model.UpdateResumeJourneyNotificationStatusRecordResponse;
import com.kotak.unified.dbservice.exceptions.custom.RestException;
import com.kotak.unified.dbservice.repository.ResumeJourneyNotificationStatusRepository;
import com.kotak.unified.dbservice.transformer.ResumeJourneyNotificationTransformer;
import com.kotak.unified.orchestrator.common.dbmodels.resumenotification.ActiveScheduledNotificationNudgeDetails;
import com.kotak.unified.orchestrator.common.dbmodels.resumenotification.RecipientType;
import com.kotak.unified.orchestrator.common.dbmodels.resumenotification.ResumeJourneyNotificationStatus;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import software.amazon.awssdk.services.dynamodb.model.ConditionalCheckFailedException;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static com.kotak.unified.dbservice.TestUtils.LEAD_ID;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ResumeJourneyNotificationServiceTest {

    @Mock
    private ResumeJourneyNotificationStatusRepository resumeJourneyNotificationStatusRepository;
    private ResumeJourneyNotificationService resumeJourneyNotificationService;
    private final ResumeJourneyNotificationTransformer resumeJourneyNotificationTransformer = new ResumeJourneyNotificationTransformer();
    private static Instant instant1 = Instant.ofEpochSecond(1720017971);
    private static Instant instant2 = Instant.ofEpochSecond(1720017882);
    @BeforeEach
    void setUp() {
        resumeJourneyNotificationService = new ResumeJourneyNotificationService(
                resumeJourneyNotificationStatusRepository, resumeJourneyNotificationTransformer);
    }

    @Test
    void test_createResumeJourneyNotificationStatusRecord_success() {
        CreateResumeJourneyNotificationStatusRecordRequest createResumeJourneyNotificationStatusRecordRequest =
                CreateResumeJourneyNotificationStatusRecordRequest.builder()
                        .leadTrackingNumber(LEAD_ID)
                        .latestScheduledNotificationNudgeCreatedAt(instant1)
                        .activeScheduledNotificationNudgeDetailsList(getActiveScheduledNotificationNudgeDetailsList())
                        .cancelledScheduledNotificationNudgeDetailsList(new ArrayList<>())
                        .build();

        ArgumentCaptor<ResumeJourneyNotificationStatus> resumeJourneyNotificationStatusArgumentCaptor = ArgumentCaptor
                .forClass(ResumeJourneyNotificationStatus.class);
        Mockito.doNothing().when(resumeJourneyNotificationStatusRepository).putRecord(resumeJourneyNotificationStatusArgumentCaptor.capture());
        CreateResumeJourneyNotificationStatusRecordResponse createResumeJourneyNotificationStatusRecordResponse = resumeJourneyNotificationService
                .createResumeJourneyNotificationStatusRecord(createResumeJourneyNotificationStatusRecordRequest);
        Assertions.assertTrue(createResumeJourneyNotificationStatusRecordResponse.getIsCreateRecordSuccessful());

        ResumeJourneyNotificationStatus resumeJourneyNotificationStatus = resumeJourneyNotificationStatusArgumentCaptor.getValue();
        ActiveScheduledNotificationNudgeDetails activeScheduledNotificationNudgeDetails = resumeJourneyNotificationStatus.getActiveScheduledNotificationNudgeDetailsList().get(0);
        ActiveScheduledNotificationNudgeDetailsDTO activeScheduledNotificationNudgeDetailsDTO = getActiveScheduledNotificationNudgeDetailsList().get(0);
        Assertions.assertEquals(resumeJourneyNotificationStatus.getLeadTrackingNumber(), LEAD_ID);
        Assertions.assertEquals(resumeJourneyNotificationStatus.getLatestScheduledNotificationNudgeCreatedAt(), instant1);
        Assertions.assertEquals(resumeJourneyNotificationStatus.getCancelledScheduledNotificationNudgeDetailsList(), new ArrayList<>());
        Assertions.assertEquals(activeScheduledNotificationNudgeDetailsDTO.getFlowId(), activeScheduledNotificationNudgeDetails.getFlowId());
        Assertions.assertEquals(activeScheduledNotificationNudgeDetailsDTO.getRunId(), activeScheduledNotificationNudgeDetails.getRunId());
        Assertions.assertEquals(activeScheduledNotificationNudgeDetails.getRecipientType(),
                RecipientType.valueOf(activeScheduledNotificationNudgeDetailsDTO.getRecipientType().name()));
    }

    @Test
    void test_createResumeJourneyNotificationStatusRecord_throws409RestException() {
        CreateResumeJourneyNotificationStatusRecordRequest createResumeJourneyNotificationStatusRecordRequest =
                CreateResumeJourneyNotificationStatusRecordRequest.builder()
                        .leadTrackingNumber(LEAD_ID)
                        .latestScheduledNotificationNudgeCreatedAt(instant1)
                        .activeScheduledNotificationNudgeDetailsList(getActiveScheduledNotificationNudgeDetailsList())
                        .cancelledScheduledNotificationNudgeDetailsList(new ArrayList<>())
                        .build();

        ArgumentCaptor<ResumeJourneyNotificationStatus> resumeJourneyNotificationStatusArgumentCaptor = ArgumentCaptor
                .forClass(ResumeJourneyNotificationStatus.class);

        doThrow(ConditionalCheckFailedException.class)
                .when(resumeJourneyNotificationStatusRepository)
                .putRecord(resumeJourneyNotificationStatusArgumentCaptor.capture());
        RestException restException = Assertions.assertThrows(RestException.class, () -> {
            resumeJourneyNotificationService.createResumeJourneyNotificationStatusRecord(createResumeJourneyNotificationStatusRecordRequest);
        });
        Assertions.assertEquals(HttpStatus.CONFLICT, restException.getHttpStatus());
    }

    @Test
    void test_updateResumeJourneyNotificationStatusRecord_success() {
        ResumeJourneyNotificationStatus resumeJourneyNotificationStatus = ResumeJourneyNotificationStatus.builder()
                .activeScheduledNotificationNudgeDetailsList(new ArrayList<>())
                .cancelledScheduledNotificationNudgeDetailsList(new ArrayList<>())
                .leadTrackingNumber(LEAD_ID)
                .latestScheduledNotificationNudgeCreatedAt(instant1)
                .build();

        UpdateResumeJourneyNotificationStatusRecordRequest updateResumeJourneyNotificationStatusRecordRequest =
                UpdateResumeJourneyNotificationStatusRecordRequest.builder()
                .activeScheduledNotificationNudgeDetailsList(getActiveScheduledNotificationNudgeDetailsList())
                .cancelledScheduledNotificationNudgeDetailsList(getCancelledScheduledNotificationNudgeDetailsList())
                .latestScheduledNotificationNudgeCreatedAt(instant1)
                .build();

        when(resumeJourneyNotificationStatusRepository.getRecord(LEAD_ID))
                .thenReturn(Optional.ofNullable(resumeJourneyNotificationStatus));
        ArgumentCaptor<ResumeJourneyNotificationStatus> resumeJourneyNotificationStatusArgumentCaptor = ArgumentCaptor.forClass(ResumeJourneyNotificationStatus.class);
        doNothing().when(resumeJourneyNotificationStatusRepository).updateRecord(resumeJourneyNotificationStatusArgumentCaptor.capture());

        UpdateResumeJourneyNotificationStatusRecordResponse updateResumeJourneyNotificationStatusRecordResponse = resumeJourneyNotificationService
                .updateResumeJourneyNotificationStatusRecord(LEAD_ID, updateResumeJourneyNotificationStatusRecordRequest);
        ResumeJourneyNotificationStatus resumeJourneyNotificationStatusUpdated = resumeJourneyNotificationStatusArgumentCaptor.getValue();

        Mockito.verify(resumeJourneyNotificationStatusRepository).getRecord(LEAD_ID);
        Assertions.assertTrue(updateResumeJourneyNotificationStatusRecordResponse.getIsUpdateRecordSuccessful());
        Assertions.assertEquals(updateResumeJourneyNotificationStatusRecordRequest.getActiveScheduledNotificationNudgeDetailsList(),
                resumeJourneyNotificationTransformer.convertModelToActiveScheduledNotificationNudgeDetailsDTOList(resumeJourneyNotificationStatusUpdated.getActiveScheduledNotificationNudgeDetailsList()));
        Assertions.assertEquals(updateResumeJourneyNotificationStatusRecordRequest.getCancelledScheduledNotificationNudgeDetailsList(),
                resumeJourneyNotificationTransformer.convertModelToScheduledNotificationNudgeDetailsDTOList(resumeJourneyNotificationStatusUpdated.getCancelledScheduledNotificationNudgeDetailsList()));

    }

    @Test
    void test_updateResumeJourneyNotificationStatusRecord_throws404RestException() {
        when(resumeJourneyNotificationStatusRepository.getRecord(LEAD_ID))
                .thenReturn(Optional.empty());

        UpdateResumeJourneyNotificationStatusRecordRequest updateResumeJourneyNotificationStatusRecordRequest =
                UpdateResumeJourneyNotificationStatusRecordRequest.builder()
                        .activeScheduledNotificationNudgeDetailsList(getActiveScheduledNotificationNudgeDetailsList())
                        .cancelledScheduledNotificationNudgeDetailsList(getCancelledScheduledNotificationNudgeDetailsList())
                        .latestScheduledNotificationNudgeCreatedAt(instant1)
                        .build();

        RestException restException = Assertions.assertThrows(RestException.class,
                () -> resumeJourneyNotificationService
                        .updateResumeJourneyNotificationStatusRecord(LEAD_ID, updateResumeJourneyNotificationStatusRecordRequest));
        Assertions.assertEquals(HttpStatus.NOT_FOUND, restException.getHttpStatus());
    }

    @Test
    void test_updateResumeJourneyNotificationStatusRecord_throws409RestException() {
        ResumeJourneyNotificationStatus resumeJourneyNotificationStatus = ResumeJourneyNotificationStatus.builder()
                .activeScheduledNotificationNudgeDetailsList(new ArrayList<>())
                .cancelledScheduledNotificationNudgeDetailsList(new ArrayList<>())
                .leadTrackingNumber(LEAD_ID)
                .latestScheduledNotificationNudgeCreatedAt(instant1)
                .build();

        UpdateResumeJourneyNotificationStatusRecordRequest updateResumeJourneyNotificationStatusRecordRequest =
                UpdateResumeJourneyNotificationStatusRecordRequest.builder()
                        .activeScheduledNotificationNudgeDetailsList(getActiveScheduledNotificationNudgeDetailsList())
                        .cancelledScheduledNotificationNudgeDetailsList(getCancelledScheduledNotificationNudgeDetailsList())
                        .latestScheduledNotificationNudgeCreatedAt(instant1)
                        .build();


        when(resumeJourneyNotificationStatusRepository.getRecord(LEAD_ID))
                .thenReturn(Optional.ofNullable(resumeJourneyNotificationStatus));
        ArgumentCaptor<ResumeJourneyNotificationStatus> resumeJourneyNotificationStatusArgumentCaptor = ArgumentCaptor.forClass(ResumeJourneyNotificationStatus.class);
        doThrow(ConditionalCheckFailedException.class).when(resumeJourneyNotificationStatusRepository).updateRecord(resumeJourneyNotificationStatusArgumentCaptor.capture());

        RestException restException = Assertions.assertThrows(RestException.class, () -> resumeJourneyNotificationService
                .updateResumeJourneyNotificationStatusRecord(LEAD_ID, updateResumeJourneyNotificationStatusRecordRequest));
        Assertions.assertEquals(HttpStatus.CONFLICT, restException.getHttpStatus());
    }

    @Test
    public void test_getResumeJourneyNotificationStatusRecord_success() {
        ResumeJourneyNotificationStatus resumeJourneyNotificationStatus = ResumeJourneyNotificationStatus.builder()
                .activeScheduledNotificationNudgeDetailsList(new ArrayList<>())
                .cancelledScheduledNotificationNudgeDetailsList(new ArrayList<>())
                .leadTrackingNumber(LEAD_ID)
                .latestScheduledNotificationNudgeCreatedAt(instant1)
                .build();

        when(resumeJourneyNotificationStatusRepository.getRecord(LEAD_ID))
                .thenReturn(Optional.ofNullable(resumeJourneyNotificationStatus));

        GetResumeJourneyNotificationStatusRecordResponse getResumeJourneyNotificationStatusRecordResponse = resumeJourneyNotificationService
                .getResumeJourneyNotificationStatusRecord(LEAD_ID);

        Mockito.verify(resumeJourneyNotificationStatusRepository).getRecord(LEAD_ID);
        Assertions.assertEquals(resumeJourneyNotificationStatus.getLeadTrackingNumber(), getResumeJourneyNotificationStatusRecordResponse.getLeadTrackingNumber());
        Assertions.assertEquals(resumeJourneyNotificationStatus.getLatestScheduledNotificationNudgeCreatedAt(), resumeJourneyNotificationStatus.getLatestScheduledNotificationNudgeCreatedAt());
        Assertions.assertEquals(resumeJourneyNotificationStatus.getActiveScheduledNotificationNudgeDetailsList(),
                resumeJourneyNotificationTransformer.convertDTOToActiveScheduledNotificationNudgeDetailsList(getResumeJourneyNotificationStatusRecordResponse.getActiveScheduledNotificationNudgeDetailsList()));
        Assertions.assertEquals(resumeJourneyNotificationStatus.getCancelledScheduledNotificationNudgeDetailsList(),
                resumeJourneyNotificationTransformer.convertDTOToScheduledNotificationNudgeDetailsList(getResumeJourneyNotificationStatusRecordResponse.getCancelledScheduledNotificationNudgeDetailsList()));

    }

    @Test
    public void test_getResumeJourneyNotificationStatusRecord_throws404RestException() {
        when(resumeJourneyNotificationStatusRepository.getRecord(LEAD_ID))
                .thenReturn(Optional.empty());

        RestException restException = Assertions.assertThrows(RestException.class,
                () -> resumeJourneyNotificationService.getResumeJourneyNotificationStatusRecord(LEAD_ID));

        Mockito.verify(resumeJourneyNotificationStatusRepository).getRecord(LEAD_ID);
        Assertions.assertEquals(HttpStatus.NOT_FOUND, restException.getHttpStatus());
    }

    private List<ActiveScheduledNotificationNudgeDetailsDTO> getActiveScheduledNotificationNudgeDetailsList() {
        List<ActiveScheduledNotificationNudgeDetailsDTO> activeScheduledNotificationNudgeDetailsDTOList = new ArrayList<>();
        ActiveScheduledNotificationNudgeDetailsDTO activeScheduledNotificationNudgeDetailsDTO = ActiveScheduledNotificationNudgeDetailsDTO
                .builder()
                .flowId("Flow1")
                .runId("Run1")
                .recipientType(RecipientTypeDTO.CRN)
                .build();
        activeScheduledNotificationNudgeDetailsDTOList.add(activeScheduledNotificationNudgeDetailsDTO);
        return activeScheduledNotificationNudgeDetailsDTOList;
    }

    private List<CancelledNotificationNudgeDetailsDTO> getCancelledScheduledNotificationNudgeDetailsList() {
        List<CancelledNotificationNudgeDetailsDTO> cancelledNotificationNudgeDetailsDTOList = new ArrayList<>();
        CancelledNotificationNudgeDetailsDTO cancelledNotificationNudgeDetailsDTO = CancelledNotificationNudgeDetailsDTO
                .builder()
                .flowId("Flow2")
                .runId("Run2")
                .recipientType(RecipientTypeDTO.CRN)
                .build();
        cancelledNotificationNudgeDetailsDTOList.add(cancelledNotificationNudgeDetailsDTO);
        return cancelledNotificationNudgeDetailsDTOList;
    }
}