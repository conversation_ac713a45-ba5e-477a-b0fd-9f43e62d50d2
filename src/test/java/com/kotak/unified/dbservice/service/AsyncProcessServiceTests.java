package com.kotak.unified.dbservice.service;

import com.kotak.unified.db.AccountPropagationExecutionDataResponse;
import com.kotak.unified.db.AsyncProcessDetailsResponse;
import com.kotak.unified.db.AsyncProcessExecutionDataResponse;
import com.kotak.unified.db.LeadJourneyExecutionDataResponse;
import com.kotak.unified.dbservice.enums.AsyncExecutionDataProcessType;
import com.kotak.unified.dbservice.exceptions.custom.RestException;
import com.kotak.unified.dbservice.repository.AsyncProcessFacade;
import com.kotak.unified.orchestrator.common.dbmodels.asyncprocessing.AccountPropagationExecutionData;
import com.kotak.unified.orchestrator.common.dbmodels.asyncprocessing.AsyncProcessDBModel;
import com.kotak.unified.orchestrator.common.dbmodels.asyncprocessing.AsyncProcessId;
import com.kotak.unified.orchestrator.common.dbmodels.asyncprocessing.LeadJourneyExecutionData;
import com.kotak.unified.orchestrator.library.sqsmodels.AsyncProcessType;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.modelmapper.ModelMapper;
import org.modelmapper.convention.MatchingStrategies;

import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;


@ExtendWith(MockitoExtension.class)
public class AsyncProcessServiceTests {
    private final AsyncProcessFacade asyncProcessFacade;
    private final AsyncProcessService asyncProcessService;

    private static final String TEST_LEAD_TRACKING_NUMBER = "123";

    AsyncProcessServiceTests() {
        this.asyncProcessFacade = Mockito.mock(AsyncProcessFacade.class);
        ModelMapper modelMapper = new ModelMapper();
        modelMapper.getConfiguration().setMatchingStrategy(MatchingStrategies.STRICT);
        this.asyncProcessService = new AsyncProcessService(asyncProcessFacade, modelMapper);
    }

    @Test
    public void getAsyncProcessExecutionData_Success() {
        AsyncProcessId processId = AsyncProcessId.builder()
                .leadTrackingNumber(TEST_LEAD_TRACKING_NUMBER)
                .processType(AsyncProcessType.ACCOUNT_PROPAGATION)
                .build();
        AccountPropagationExecutionData accountPropagationExecutionData = AccountPropagationExecutionData.builder()
                .isPhoneNumberUpiIdAvailable(true)
                .build();
        AsyncProcessDBModel asyncProcessDBModel = AsyncProcessDBModel.builder()
                .processId(processId)
                .asyncProcessExecutionData(accountPropagationExecutionData)
                .build();
        when(asyncProcessFacade.getByProcessId(Mockito.any())).thenReturn(asyncProcessDBModel);
        AsyncProcessExecutionDataResponse asyncProcessExecutionDataResponse = asyncProcessService.getAsyncProcessExecutionData(
                TEST_LEAD_TRACKING_NUMBER, AsyncExecutionDataProcessType.ACCOUNT_PROPAGATION);
        AccountPropagationExecutionDataResponse accountPropagationExecutionDataResponse =
                (AccountPropagationExecutionDataResponse) asyncProcessExecutionDataResponse;

        Assertions.assertNotNull(accountPropagationExecutionDataResponse);
        Assertions.assertTrue(accountPropagationExecutionDataResponse.getIsPhoneNumberUpiIdAvailable());

    }

    @Test
    public void getAsyncProcessExecutionData_LeadPropagation_Success() {
        AsyncProcessId processId = AsyncProcessId.builder()
                .leadTrackingNumber(TEST_LEAD_TRACKING_NUMBER)
                .processType(AsyncProcessType.HOME_LOAN_PROPAGATION)
                .build();
        LeadJourneyExecutionData accountPropagationExecutionData = LeadJourneyExecutionData.builder()
                .leadId("123")
                .build();
        AsyncProcessDBModel asyncProcessDBModel = AsyncProcessDBModel.builder()
                .processId(processId)
                .asyncProcessExecutionData(accountPropagationExecutionData)
                .build();
        when(asyncProcessFacade.getByProcessId(Mockito.any())).thenReturn(asyncProcessDBModel);
        AsyncProcessExecutionDataResponse asyncProcessExecutionDataResponse = asyncProcessService.getAsyncProcessExecutionData(
                TEST_LEAD_TRACKING_NUMBER, AsyncExecutionDataProcessType.HOME_LOAN_PROPAGATION);
        LeadJourneyExecutionDataResponse leadJourneyExecutionDataResponse =
                (LeadJourneyExecutionDataResponse) asyncProcessExecutionDataResponse;

        Assertions.assertNotNull(leadJourneyExecutionDataResponse);
        Assertions.assertEquals("123",leadJourneyExecutionDataResponse.getLeadID());

    }
    @Test
    public void getAsyncProcessExecutionData_ReturnsNull() {
        AsyncProcessId processId = AsyncProcessId.builder()
                .leadTrackingNumber(TEST_LEAD_TRACKING_NUMBER)
                .processType(AsyncProcessType.ACCOUNT_PROPAGATION)
                .build();
        AsyncProcessDBModel asyncProcessDBModel = AsyncProcessDBModel.builder()
                .processId(processId)
                .build();
        when(asyncProcessFacade.getByProcessId(Mockito.any())).thenReturn(asyncProcessDBModel);
        AsyncProcessExecutionDataResponse asyncProcessExecutionDataResponse = asyncProcessService.getAsyncProcessExecutionData(
                TEST_LEAD_TRACKING_NUMBER, AsyncExecutionDataProcessType.ACCOUNT_PROPAGATION);
        Assertions.assertNull(asyncProcessExecutionDataResponse);
    }

    @Test
    public void getAsyncProcessExecutionData__LeadGeneration_ReturnsNull() {
        AsyncProcessId processId = AsyncProcessId.builder()
                .leadTrackingNumber(TEST_LEAD_TRACKING_NUMBER)
                .processType(AsyncProcessType.HOME_LOAN_PROPAGATION)
                .build();
        AsyncProcessDBModel asyncProcessDBModel = AsyncProcessDBModel.builder()
                .processId(processId)
                .build();
        when(asyncProcessFacade.getByProcessId(Mockito.any())).thenReturn(asyncProcessDBModel);
        AsyncProcessExecutionDataResponse leadJourneyExecutionDataResponse = asyncProcessService.getAsyncProcessExecutionData(
                TEST_LEAD_TRACKING_NUMBER, AsyncExecutionDataProcessType.HOME_LOAN_PROPAGATION);
        Assertions.assertNull(leadJourneyExecutionDataResponse);
    }

    @Test
    public void getAsyncProcessExecutionData_throwsRestException() {
        when(asyncProcessFacade.getByProcessId(Mockito.any())).thenReturn(null);
        Assertions.assertThrows(RestException.class, () -> asyncProcessService.getAsyncProcessExecutionData(
                TEST_LEAD_TRACKING_NUMBER, AsyncExecutionDataProcessType.ACCOUNT_PROPAGATION));
    }

    @Test
    public void getAsyncProcessExecutionData_LeadGenerationthrowsRestException() {
        when(asyncProcessFacade.getByProcessId(Mockito.any())).thenReturn(null);
        Assertions.assertThrows(RestException.class, () -> asyncProcessService.getAsyncProcessExecutionData(
                TEST_LEAD_TRACKING_NUMBER, AsyncExecutionDataProcessType.HOME_LOAN_PROPAGATION));
    }


    @Test
    public void getAsyncProcessData_Success() {
        AsyncProcessId processId = AsyncProcessId.builder()
                .leadTrackingNumber(TEST_LEAD_TRACKING_NUMBER)
                .processType(AsyncProcessType.ACCOUNT_PROPAGATION)
                .build();
        AccountPropagationExecutionData accountPropagationExecutionData = AccountPropagationExecutionData.builder()
                .isPhoneNumberUpiIdAvailable(true)
                .build();
        AsyncProcessDBModel asyncProcessDBModel = AsyncProcessDBModel.builder()
                .processId(processId)
                .asyncProcessExecutionData(accountPropagationExecutionData)
                .build();
        when(asyncProcessFacade.getByProcessId(Mockito.any())).thenReturn(asyncProcessDBModel);
        AsyncProcessDetailsResponse asyncProcessDetailsResponse = asyncProcessService.getAsyncProcessData(
                TEST_LEAD_TRACKING_NUMBER, AsyncExecutionDataProcessType.ACCOUNT_PROPAGATION.name());

        verify(asyncProcessFacade, times(1)).getByProcessId(Mockito.any());
        Assertions.assertNotNull(asyncProcessDetailsResponse);
        Assertions.assertEquals(asyncProcessDBModel.getProcessId().getLeadTrackingNumber(), asyncProcessDetailsResponse.getProcessId().getLeadTrackingNumber());
        Assertions.assertEquals(asyncProcessDBModel.getProcessId().getProcessType().name(), asyncProcessDetailsResponse.getProcessId().getProcessType());
        Assertions.assertEquals(((AccountPropagationExecutionData) asyncProcessDBModel.getAsyncProcessExecutionData()).getIsPhoneNumberUpiIdAvailable(),
                ((AccountPropagationExecutionDataResponse)  asyncProcessDetailsResponse.getAsyncProcessExecutionData()).getIsPhoneNumberUpiIdAvailable());
    }

    @Test
    public void getAsyncProcessData_throwsRestException() {
        when(asyncProcessFacade.getByProcessId(Mockito.any())).thenReturn(null);
        Assertions.assertThrows(RestException.class, () -> asyncProcessService.getAsyncProcessData(
                TEST_LEAD_TRACKING_NUMBER, AsyncExecutionDataProcessType.ACCOUNT_PROPAGATION.name()));
        verify(asyncProcessFacade, times(1)).getByProcessId(Mockito.any());
    }

    @Test
    public void getAsyncProcessData_LeadGeneration_throwsRestException() {
        when(asyncProcessFacade.getByProcessId(Mockito.any())).thenReturn(null);
        Assertions.assertThrows(RestException.class, () -> asyncProcessService.getAsyncProcessData(
                TEST_LEAD_TRACKING_NUMBER, AsyncExecutionDataProcessType.HOME_LOAN_PROPAGATION.name()));
        verify(asyncProcessFacade, times(1)).getByProcessId(Mockito.any());
    }

    @Test
    public void getAsyncProcessData_throwsRestExceptionForInvalidProcessType() {
        Assertions.assertThrows(RestException.class, () -> asyncProcessService.getAsyncProcessData(
                TEST_LEAD_TRACKING_NUMBER,"Dummy_process_type"));
        verify(asyncProcessFacade, times(0)).getByProcessId(Mockito.any());
    }
}
