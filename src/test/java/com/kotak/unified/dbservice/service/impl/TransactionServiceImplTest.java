package com.kotak.unified.dbservice.service.impl;

import builder.TransactionTestDataBuilder;
import com.kotak.unified.db.response.TransactionResponse;
import com.kotak.unified.dbservice.enums.ErrorCause;
import com.kotak.unified.dbservice.exceptions.custom.RestException;
import com.kotak.unified.dbservice.repository.TransactionFacade;
import com.kotak.unified.dbservice.service.TransactionService;
import com.kotak.unified.dbservice.transformer.TransactionTransformer;
import com.kotak.unified.orchestrator.common.dbmodels.Transaction;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;

import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class TransactionServiceImplTest {

    private final TransactionFacade transactionFacade;

    private final TransactionTransformer transactionTransformer;

    private final TransactionService transactionService;

    TransactionServiceImplTest() {
        this.transactionFacade = Mockito.mock(TransactionFacade.class);
        this.transactionTransformer = Mockito.mock(TransactionTransformer.class);
        this.transactionService = new TransactionServiceImpl(transactionFacade, transactionTransformer);
    }

    @Test
    void testSuccessGetTransaction() {
        Transaction dummyTransaction = TransactionTestDataBuilder.createDummyTransaction();
        TransactionResponse dummyTransactionResponse = TransactionTestDataBuilder.createDummyTransactionResponse();
        String dummyTransactionId = TransactionTestDataBuilder.DUMMY_TRANSACTION_ID;

        when(transactionFacade.findById(anyString())).thenReturn(Optional.of(dummyTransaction));
        when(transactionTransformer.convertTransactionToResponse(any(Transaction.class))).thenReturn(dummyTransactionResponse);

        TransactionResponse transaction = transactionService.getTransaction(dummyTransactionId);

        Assertions.assertEquals(dummyTransactionId, transaction.getTxnId());
        Assertions.assertEquals(dummyTransactionResponse, transaction);

        verify(transactionFacade, times(1)).findById(dummyTransactionId);
        verify(transactionTransformer, times(1)).convertTransactionToResponse(dummyTransaction);
    }

    @Test
    void testFailureGetTransaction() {
        String dummyTransactionId = TransactionTestDataBuilder.DUMMY_TRANSACTION_ID;

        when(transactionFacade.findById(anyString())).thenReturn(Optional.empty());

        RestException restException = Assertions.assertThrows(RestException.class, () -> transactionService.getTransaction(dummyTransactionId));

        Assertions.assertEquals(HttpStatus.NOT_FOUND, restException.getHttpStatus());
        Assertions.assertFalse(restException.getErrorResponseList().isEmpty());
        Assertions.assertEquals(1, restException.getErrorResponseList().size());
        Assertions.assertEquals(ErrorCause.TRANSACTION_NOT_FOUND.getCode(), restException.getErrorResponseList().get(0).getErrorCode());
        Assertions.assertEquals(ErrorCause.TRANSACTION_NOT_FOUND.getMessage(), restException.getErrorResponseList().get(0).getErrorMessage());

        verify(transactionFacade, times(1)).findById(dummyTransactionId);
    }
}