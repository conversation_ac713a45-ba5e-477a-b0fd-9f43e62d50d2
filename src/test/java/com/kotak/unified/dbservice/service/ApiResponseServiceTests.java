package com.kotak.unified.dbservice.service;

import com.kotak.unified.db.ApiResponseDetailsDto;
import com.kotak.unified.dbservice.exceptions.custom.RestException;
import com.kotak.unified.dbservice.repository.ApiResponseDetailsDDBRepository;
import com.kotak.unified.orchestrator.common.dbmodels.asyncprocessing.ApiResponseDetailsDDBModel;
import com.kotak.unified.orchestrator.library.sqsmodels.ApiName;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;


@ExtendWith(MockitoExtension.class)
public class ApiResponseServiceTests {
    private final ApiResponseDetailsDDBRepository apiResponseDetailsDDBRepository;
    private final ApiResponseService apiResponseService;

    private static final String TEST_LEAD_TRACKING_NUMBER = "123";

    ApiResponseServiceTests() {
        this.apiResponseDetailsDDBRepository = Mockito.mock(ApiResponseDetailsDDBRepository.class);
        this.apiResponseService = new ApiResponseService(apiResponseDetailsDDBRepository);
    }

    @Test
    public void getApiResponseDetails_Success() {
        ApiResponseDetailsDDBModel apiResponseDetailsDDBModel = ApiResponseDetailsDDBModel.builder()
                .apiName(ApiName.BUREAU_PAN_PROFILE)
                .content("content")
                .leadTrackingNumber(TEST_LEAD_TRACKING_NUMBER)
                .build();
        when(apiResponseDetailsDDBRepository.getByLeadTrackingNumberAndApiName(TEST_LEAD_TRACKING_NUMBER, ApiName.BUREAU_PAN_PROFILE)).thenReturn(apiResponseDetailsDDBModel);
        ApiResponseDetailsDto apiResponseDetailsDto = apiResponseService.getApiResponseData(
                TEST_LEAD_TRACKING_NUMBER, ApiName.BUREAU_PAN_PROFILE.toString());
        Assertions.assertNotNull(apiResponseDetailsDto);
        Assertions.assertEquals("content", apiResponseDetailsDto.getContent());

    }

    @Test
    public void getApiResponseDetails_Null() {
        ApiResponseDetailsDDBModel apiResponseDetailsDDBModel = null;
        when(apiResponseDetailsDDBRepository.getByLeadTrackingNumberAndApiName(TEST_LEAD_TRACKING_NUMBER, ApiName.BUREAU_PAN_PROFILE)).thenReturn(apiResponseDetailsDDBModel);
        RestException exception = assertThrows(RestException.class, ()-> apiResponseService.getApiResponseData(
                TEST_LEAD_TRACKING_NUMBER, ApiName.BUREAU_PAN_PROFILE.toString()));
        Assertions.assertEquals("DB023", exception.getErrorResponseList().get(0).getErrorCode());
        Assertions.assertEquals("Api Response not found", exception.getErrorResponseList().get(0).getErrorMessage());
        assertEquals(HttpStatus.NOT_FOUND, exception.getHttpStatus());
    }

    @Test
    public void getApiResponseDetails_BadRequest() {
        RestException exception = assertThrows(RestException.class, ()-> apiResponseService.getApiResponseData(
                TEST_LEAD_TRACKING_NUMBER, "RANDOM"));
        Assertions.assertEquals("DB024", exception.getErrorResponseList().get(0).getErrorCode());
        Assertions.assertEquals("Invalid ApiName is provided", exception.getErrorResponseList().get(0).getErrorMessage());
        assertEquals(HttpStatus.BAD_REQUEST, exception.getHttpStatus());
    }
}
