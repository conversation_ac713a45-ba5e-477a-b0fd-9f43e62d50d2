package com.kotak.unified.dbservice.service;

import com.kotak.unified.db.vkycStatusResponse.VideoKycDetailsResponse;
import com.kotak.unified.db.vkycStatusResponse.VkycStatusResponse;
import com.kotak.unified.dbservice.exceptions.custom.RestException;
import com.kotak.unified.dbservice.repository.VideoKYCStatusFacade;
import com.kotak.unified.orchestrator.common.dbmodels.vkycStatusModel.VideoKycDetails;
import com.kotak.unified.orchestrator.common.dbmodels.vkycStatusModel.VkycStatus;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.modelmapper.ModelMapper;

import java.time.Instant;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class VideoKycDetailsServiceTests {
    private VideoKycDetailsService videoKycDetailsService;
    @Mock
    private VideoKYCStatusFacade videoKYCStatusFacade;
    private final String TRACKING_ID = "dummyTrackingID";

    private final String VKYC_APPROVED_STATUS = "Approved";
    private final String TEST_LEAD_ID = "TEST_LEAD_ID";
    private final String VKYC_TRACKING_ID = "VKYC_TRACKING_ID";
    private final String TEST_INSTANT = Instant.now().toString();


    @BeforeEach
    public void setUp() {
        videoKycDetailsService = new VideoKycDetailsService(videoKYCStatusFacade, new ModelMapper());
    }

     /*

    @Test
    void saveVideoDetailSuccess() {
        VideoKycDetailsResponse videoKycDetailsRequest = getVideoKycDetailsApprovedResponse();
        VideoKycDetails videoKycDetails = getVideoKycDetails();

        when(videoKYCStatusFacade.save(any(VideoKycDetails.class))).thenReturn(videoKycDetails);

        VideoKycDetailsResponse videoKycDetailsResponse = videoKycDetailsService.save(videoKycDetailsRequest);

        verify(videoKYCStatusFacade, times(1)).save(any(VideoKycDetails.class));
        Assertions.assertEquals(videoKycDetailsRequest.getLeadTrackingNumber(), videoKycDetailsResponse.getLeadTrackingNumber());
        Assertions.assertEquals(videoKycDetailsRequest.getVkycStatusList(), videoKycDetailsResponse.getVkycStatusList());
        Assertions.assertEquals(videoKycDetailsRequest.getTrackingId(), videoKycDetailsResponse.getTrackingId());
    }


    @Test
    public void test_getTopByLeadTrackingNumberOrderByCreatedAtDesc() {
        when(videoKYCStatusFacade.findTopByLeadTrackingNumberOrderByCreatedAtDesc(TRACKING_ID)).thenReturn(getVideoKycDetails());

        VideoKycDetailsResponse videoKycDetailsResponse = videoKycDetailsService.getTopByLeadTrackingNumberOrderByCreatedAtDesc(TRACKING_ID);

        verify(videoKYCStatusFacade, times(1)).findTopByLeadTrackingNumberOrderByCreatedAtDesc(TRACKING_ID);
        Assertions.assertEquals(videoKycDetailsResponse.getTrackingId(), VKYC_TRACKING_ID);
    }*/

    @Test
    public void test_getTopByLeadTrackingNumberOrderByCreatedAtDesc_error() {
        when(videoKYCStatusFacade.findTopByLeadTrackingNumberOrderByCreatedAtDesc(VKYC_TRACKING_ID)).thenReturn(null);

        Assertions.assertThrows(RestException.class, () -> videoKycDetailsService.getTopByLeadTrackingNumberOrderByCreatedAtDesc(VKYC_TRACKING_ID));
        verify(videoKYCStatusFacade, times(1)).findTopByLeadTrackingNumberOrderByCreatedAtDesc(VKYC_TRACKING_ID);
    }

    /*@Test
    public void test_getTopByLeadTrackingNumberAndLatestStatusOrderByCreatedAt_success() {
        VideoKycDetailsResponse videoKycDetailsExpectedResponse = getVideoKycDetailsApprovedResponse();
        when(videoKYCStatusFacade.findTopByLeadTrackingNumberAndLatestStatusOrderByCreatedAt(TRACKING_ID, VKYC_APPROVED_STATUS)).thenReturn(getVideoKycDetails());

        VideoKycDetailsResponse videoKycDetailsResponse = videoKycDetailsService.getTopByLeadTrackingNumberAndLatestStatusOrderByCreatedAt(TRACKING_ID, VKYC_APPROVED_STATUS);

        verify(videoKYCStatusFacade, times(1)).findTopByLeadTrackingNumberAndLatestStatusOrderByCreatedAt(TRACKING_ID, VKYC_APPROVED_STATUS);

        Assertions.assertEquals(videoKycDetailsExpectedResponse.getLeadTrackingNumber(), videoKycDetailsResponse.getLeadTrackingNumber());
        Assertions.assertEquals(videoKycDetailsExpectedResponse.getVkycStatusList(), videoKycDetailsResponse.getVkycStatusList());
        Assertions.assertEquals(videoKycDetailsExpectedResponse.getTrackingId(), videoKycDetailsResponse.getTrackingId());
    }*/

    @Test
    public void test_getTopByLeadTrackingNumberAndLatestStatusOrderByCreatedAt_null() {
        when(videoKYCStatusFacade.findTopByLeadTrackingNumberAndLatestStatusOrderByCreatedAt(TRACKING_ID, VKYC_APPROVED_STATUS)).thenReturn(null);
        Assertions.assertThrows(RestException.class, () -> videoKycDetailsService.getTopByLeadTrackingNumberAndLatestStatusOrderByCreatedAt(TRACKING_ID, VKYC_APPROVED_STATUS));

        verify(videoKYCStatusFacade, times(1)).findTopByLeadTrackingNumberAndLatestStatusOrderByCreatedAt(TRACKING_ID, VKYC_APPROVED_STATUS);
    }


    /*@Test
    void getVideoKycDetailsSuccess() {
        VideoKycDetailsResponse expectedVideoKycDetailsResponse = getVideoKycDetailsApprovedResponse();
        when(videoKYCStatusFacade.findByTrackingId(VKYC_TRACKING_ID)).thenReturn(getVideoKycDetails());
        VideoKycDetailsResponse videoKycDetailsResponse = videoKycDetailsService.getVideoKycDetails(VKYC_TRACKING_ID);
        Assertions.assertEquals(expectedVideoKycDetailsResponse.getLeadTrackingNumber(), videoKycDetailsResponse.getLeadTrackingNumber());
        Assertions.assertEquals(expectedVideoKycDetailsResponse.getVkycStatusList(), videoKycDetailsResponse.getVkycStatusList());
        Assertions.assertEquals(expectedVideoKycDetailsResponse.getTrackingId(), videoKycDetailsResponse.getTrackingId());
    }*/

    @Test
    void getVideoKycDetailsExceptionWhenNull() {
        when(videoKYCStatusFacade.findByTrackingId(VKYC_TRACKING_ID)).thenReturn(null);
        Assertions.assertThrows(RestException.class, () -> videoKycDetailsService.getVideoKycDetails(VKYC_TRACKING_ID));
    }

    private VideoKycDetails getVideoKycDetails() {
        return VideoKycDetails.builder()
                .trackingId(VKYC_TRACKING_ID)
                .latestStatus(VKYC_APPROVED_STATUS)
                .leadTrackingNumber(TEST_LEAD_ID)
                .vkycStatusList(List.of(VkycStatus.builder()
                        .status(VKYC_APPROVED_STATUS)
                        .auditorId("123")
                        .auditorActionDate(TEST_INSTANT)
                        .build()))
                .build();
    }

    private VideoKycDetailsResponse getVideoKycDetailsApprovedResponse() {
        return VideoKycDetailsResponse.builder()
                .leadTrackingNumber(TEST_LEAD_ID)
                .latestStatus(VKYC_APPROVED_STATUS)
                .trackingId(VKYC_TRACKING_ID)
                .vkycStatusList(List.of(VkycStatusResponse.builder()
                        .status(VKYC_APPROVED_STATUS)
                        .auditorId("123")
                        .auditorActionDate(TEST_INSTANT)
                        .build()))
                .build();
    }

    @Test
    public void getTopByLeadTrackingNumberAndLatestStatusOrderByCreatedAtNullParam() {
        assertThrows(NullPointerException.class, () ->
                videoKycDetailsService.getTopByLeadTrackingNumberAndLatestStatusOrderByCreatedAt(null, null));
    }

    @Test
    public void getTopByLeadTrackingNumberOrderByCreatedAtDescNullParam() {
        assertThrows(NullPointerException.class, () ->
                videoKycDetailsService.getTopByLeadTrackingNumberOrderByCreatedAtDesc(null));
    }
    @Test
    public void saveNullParam() {
        assertThrows(NullPointerException.class, () -> videoKycDetailsService.save(null));
    }

    @Test
    public void getVideoKycDetailsNullParam() {
        assertThrows(NullPointerException.class, () ->
                videoKycDetailsService.getVideoKycDetails(null));
    }
}
