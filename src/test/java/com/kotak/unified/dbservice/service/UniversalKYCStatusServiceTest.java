package com.kotak.unified.dbservice.service;

import com.kotak.unified.db.model.CreateUKYCRecordRequest;
import com.kotak.unified.db.model.CreateUKYCRecordResponse;
import com.kotak.unified.db.model.GetUKYCRecordResponse;
import com.kotak.unified.db.model.KYCStatusDTO;
import com.kotak.unified.db.model.UKYCChannelStatusDTO;
import com.kotak.unified.db.model.UpdateUKYCRecordRequest;
import com.kotak.unified.db.model.UpdateUKYCRecordResponse;
import com.kotak.unified.dbservice.TestUtils;
import com.kotak.unified.dbservice.exceptions.custom.RestException;
import com.kotak.unified.dbservice.repository.UniversalKYCDetailsRepository;
import com.kotak.unified.dbservice.transformer.UniversalKYCDetailsTransformer;
import com.kotak.unified.dbservice.validator.UniversalKYCStatusAPIRequestValidator;
import com.kotak.unified.orchestrator.common.dbmodels.ukycStatusModel.KYCChannel;
import com.kotak.unified.orchestrator.common.dbmodels.ukycStatusModel.KYCStatus;
import com.kotak.unified.orchestrator.common.dbmodels.ukycStatusModel.UKYCChannelStatus;
import com.kotak.unified.orchestrator.common.dbmodels.ukycStatusModel.UKYCStatus;
import com.kotak.unified.orchestrator.common.dbmodels.ukycStatusModel.UniversalKYCDetails;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.test.util.ReflectionTestUtils;
import software.amazon.awssdk.services.dynamodb.model.ConditionalCheckFailedException;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

import static com.kotak.unified.dbservice.TestUtils.JOURNEY_TYPE;
import static com.kotak.unified.dbservice.TestUtils.TEST_AADHAAR_REF_KEY;
import static com.kotak.unified.dbservice.TestUtils.TEST_ACTION_TRACKING_ID;
import static com.kotak.unified.dbservice.TestUtils.TEST_ACTOR;
import static com.kotak.unified.dbservice.TestUtils.TEST_APPLICATION_ID;
import static com.kotak.unified.dbservice.TestUtils.TEST_CLIENT_ID;
import static com.kotak.unified.dbservice.TestUtils.TEST_JOURNEY_TYPE;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;


@ExtendWith(MockitoExtension.class)
class UniversalKYCStatusServiceTest {

    @Mock
    private UniversalKYCDetailsRepository universalKYCDetailsRepository;
    private UniversalKYCStatusService universalKYCStatusService;
    private final UniversalKYCDetailsTransformer universalKYCDetailsTransformer = new UniversalKYCDetailsTransformer();
    @InjectMocks
    private UniversalKYCStatusAPIRequestValidator universalKYCStatusAPIRequestValidator;

    @BeforeEach
    void setUp() {
        universalKYCStatusService = new UniversalKYCStatusService(
                universalKYCDetailsRepository, universalKYCDetailsTransformer);
        ReflectionTestUtils.setField(universalKYCStatusAPIRequestValidator, "ukycAllowedJourneyTypes" , "SavingsAccount,SalaryAccount");
    }

    @Test
    public void test_createUKYCRecord_success() {
        CreateUKYCRecordRequest createUKYCRecordRequest = TestUtils.getCreateUKYCRecordRequestWithVideoKYC();
        ArgumentCaptor<UniversalKYCDetails> universalKYCDetailsArgumentCaptor = ArgumentCaptor
                .forClass(UniversalKYCDetails.class);
        Mockito.doNothing().when(universalKYCDetailsRepository).putRecord(universalKYCDetailsArgumentCaptor.capture());
        CreateUKYCRecordResponse createUKYCRecordResponse = universalKYCStatusService
                .createUKYCRecord(createUKYCRecordRequest);
        Assertions.assertTrue(createUKYCRecordResponse.getIsCreateRecordSuccessful());

        UniversalKYCDetails universalKYCDetails = universalKYCDetailsArgumentCaptor.getValue();
        universalKYCDetails.setJourneyType(JOURNEY_TYPE);
        Assertions.assertEquals(TEST_AADHAAR_REF_KEY, universalKYCDetails.getAadhaarRefKey());
        Assertions.assertEquals(TEST_APPLICATION_ID, universalKYCDetails.getApplicationId());
        Assertions.assertEquals(TEST_CLIENT_ID, universalKYCDetails.getClientId());
        Assertions.assertNull(universalKYCDetails.getCrn());
        Assertions.assertEquals(UKYCStatus.INITIATED, universalKYCDetails.getUkycStatus());
        Assertions.assertEquals(JOURNEY_TYPE, universalKYCDetails.getJourneyType());
        Assertions.assertEquals(TEST_ACTOR, universalKYCDetails.getCreatedBy());

        List<UKYCChannelStatus> ukycChannelStatusList = universalKYCDetails.getUkycChannelStatusList();
        Assertions.assertEquals(1, ukycChannelStatusList.size());
        UKYCChannelStatus ukycChannelStatus = ukycChannelStatusList.get(0);
        Assertions.assertEquals(KYCStatus.INITIATED, ukycChannelStatus.getKycStatus());
        Assertions.assertEquals(KYCChannel.VIDEO_KYC, ukycChannelStatus.getKycChannel());
        Assertions.assertEquals(TEST_ACTION_TRACKING_ID, ukycChannelStatus.getActionTrackingId());
        Assertions.assertNull(ukycChannelStatus.getKycMetadata());
    }

    @Test
    public void test_createUKYCRecord_throws409RestException() {
        CreateUKYCRecordRequest createUKYCRecordRequest = TestUtils.getCreateUKYCRecordRequestWithVideoKYC();
        ArgumentCaptor<UniversalKYCDetails> universalKYCDetailsArgumentCaptor = ArgumentCaptor
                .forClass(UniversalKYCDetails.class);
        doThrow(ConditionalCheckFailedException.class)
                .when(universalKYCDetailsRepository)
                .putRecord(universalKYCDetailsArgumentCaptor.capture());
        RestException restException = Assertions.assertThrows(RestException.class, () -> {
            universalKYCStatusService.createUKYCRecord(createUKYCRecordRequest);
        });
        Assertions.assertEquals(HttpStatus.CONFLICT, restException.getHttpStatus());
    }

    @Test
    public void test_getUKYCRecord_success() {
        UniversalKYCDetails universalKYCDetails = TestUtils.getUniversalKYCDetailsWithVideoKYC();
        when(universalKYCDetailsRepository.getRecord(TEST_AADHAAR_REF_KEY, TEST_APPLICATION_ID))
                .thenReturn(Optional.ofNullable(universalKYCDetails));

        GetUKYCRecordResponse getUKYCRecordResponse = universalKYCStatusService
                .getUKYCRecord(TEST_AADHAAR_REF_KEY, TEST_APPLICATION_ID);

        Mockito.verify(universalKYCDetailsRepository).getRecord(TEST_AADHAAR_REF_KEY, TEST_APPLICATION_ID);
        Assertions.assertEquals(universalKYCDetails.getCrn(), getUKYCRecordResponse.getCrn());
        Assertions.assertEquals(universalKYCDetails.getClientId(), getUKYCRecordResponse.getClientId());
        Assertions.assertEquals(universalKYCDetails.getAadhaarRefKey(), getUKYCRecordResponse.getAadhaarRefKey());
        Assertions.assertEquals(universalKYCDetails.getUkycStatus().toString(),
                getUKYCRecordResponse.getUkycStatus().toString());
        Assertions.assertEquals(universalKYCDetails.getJourneyType(), getUKYCRecordResponse.getJourneyType());
        UKYCChannelStatusDTO ukycChannelStatusDTO = getUKYCRecordResponse.getUkycChannelStatusList().get(0);
        UKYCChannelStatus ukycChannelStatus = universalKYCDetails.getUkycChannelStatusList().get(0);
        Assertions.assertEquals(ukycChannelStatus.getKycStatus(),
                universalKYCDetailsTransformer.convertDTOToKYCStatus(ukycChannelStatusDTO.getKycStatus()));
        Assertions.assertEquals(ukycChannelStatus.getKycChannel(),
                universalKYCDetailsTransformer.convertDTOToKYCChannel(ukycChannelStatusDTO.getKycChannel()));

        Assertions.assertEquals(ukycChannelStatus.getActionTrackingId(), ukycChannelStatusDTO.getActionTrackingId());
        Assertions.assertEquals(ukycChannelStatus.getKycMetadata(),
                universalKYCDetailsTransformer.convertDTOToKYCMetadata(
                        ukycChannelStatusDTO.getKycMetadata(), ukycChannelStatusDTO.getKycChannel())
        );
    }

    @Test
    public void test_getUKYCRecord_throws404RestException() {
        when(universalKYCDetailsRepository.getRecord(TEST_AADHAAR_REF_KEY, TEST_APPLICATION_ID))
                .thenReturn(Optional.empty());

        RestException restException = Assertions.assertThrows(RestException.class,
                () -> universalKYCStatusService.getUKYCRecord(TEST_AADHAAR_REF_KEY, TEST_APPLICATION_ID));

        Mockito.verify(universalKYCDetailsRepository).getRecord(TEST_AADHAAR_REF_KEY, TEST_APPLICATION_ID);
        Assertions.assertEquals(HttpStatus.NOT_FOUND, restException.getHttpStatus());
    }

    @Test
    public void test_updateUKYCRecord_success() {
        UniversalKYCDetails universalKYCDetails = TestUtils.getUniversalKYCDetailsWithNoKYCChannel();
        universalKYCDetails.setJourneyType("SavingsAccount");
        UpdateUKYCRecordRequest updateUKYCRecordRequest = TestUtils.getUpdateUKYCRecordRequestWithVideoKYC();
        updateUKYCRecordRequest.setKycStatus(KYCStatusDTO.COMPLETED);

        when(universalKYCDetailsRepository.getRecord(TEST_AADHAAR_REF_KEY, TEST_APPLICATION_ID))
                .thenReturn(Optional.ofNullable(universalKYCDetails));
        ArgumentCaptor<UniversalKYCDetails> universalKYCDetailsArgumentCaptor = ArgumentCaptor.forClass(UniversalKYCDetails.class);
        doNothing().when(universalKYCDetailsRepository).updateRecord(universalKYCDetailsArgumentCaptor.capture());

        UpdateUKYCRecordResponse updateUKYCRecordResponse = universalKYCStatusService
                .updateUKYCRecord(TEST_AADHAAR_REF_KEY, TEST_APPLICATION_ID, updateUKYCRecordRequest);
        UniversalKYCDetails universalKYCDetailsUpdated = universalKYCDetailsArgumentCaptor.getValue();

        Mockito.verify(universalKYCDetailsRepository).getRecord(TEST_AADHAAR_REF_KEY, TEST_APPLICATION_ID);
        Assertions.assertTrue(updateUKYCRecordResponse.getIsUpdateRecordSuccessful());
        Assertions.assertEquals(updateUKYCRecordRequest.getCrn(), universalKYCDetailsUpdated.getCrn());
        Assertions.assertEquals(updateUKYCRecordRequest.getActor(), universalKYCDetailsUpdated.getLastModifiedBy());
        Assertions.assertEquals(
                universalKYCDetailsTransformer.convertDTOToUKYCStatus(updateUKYCRecordRequest.getKycStatus()),
                universalKYCDetailsUpdated.getUkycStatus()
        );
        Assertions.assertEquals(
                universalKYCDetailsTransformer.convertDTOToKYCStatus(updateUKYCRecordRequest.getKycStatus()),
                universalKYCDetailsUpdated.getUkycChannelStatusList().get(0).getKycStatus()
        );
        Assertions.assertEquals(
                universalKYCDetailsTransformer.convertDTOToKYCChannel(updateUKYCRecordRequest.getKycChannel()),
                universalKYCDetailsUpdated.getUkycChannelStatusList().get(0).getKycChannel()
        );
        Assertions.assertEquals(
                universalKYCDetailsTransformer.convertDTOToKYCMetadata(
                        updateUKYCRecordRequest.getKycMetadata(), updateUKYCRecordRequest.getKycChannel()),
                universalKYCDetailsUpdated.getUkycChannelStatusList().get(0).getKycMetadata()
        );
    }

    @Test
    public void test_updateUKYCRecord_addNewChannelStatus() {
        UniversalKYCDetails universalKYCDetails = TestUtils.getUniversalKYCDetailsWithNoKYCChannel();
        universalKYCDetails.setUkycChannelStatusList(null);
        universalKYCDetails.setJourneyType("SavingsAccount");
        UpdateUKYCRecordRequest updateUKYCRecordRequest = TestUtils.getUpdateUKYCRecordRequestWithVideoKYC();
        updateUKYCRecordRequest.setKycStatus(KYCStatusDTO.COMPLETED);

        when(universalKYCDetailsRepository.getRecord(TEST_AADHAAR_REF_KEY, TEST_APPLICATION_ID))
                .thenReturn(Optional.of(universalKYCDetails));
        ArgumentCaptor<UniversalKYCDetails> universalKYCDetailsArgumentCaptor = ArgumentCaptor.forClass(UniversalKYCDetails.class);
        doNothing().when(universalKYCDetailsRepository).updateRecord(universalKYCDetailsArgumentCaptor.capture());

        UpdateUKYCRecordResponse updateUKYCRecordResponse = universalKYCStatusService
                .updateUKYCRecord(TEST_AADHAAR_REF_KEY, TEST_APPLICATION_ID, updateUKYCRecordRequest);
        UniversalKYCDetails universalKYCDetailsUpdated = universalKYCDetailsArgumentCaptor.getValue();

        Mockito.verify(universalKYCDetailsRepository).getRecord(TEST_AADHAAR_REF_KEY, TEST_APPLICATION_ID);
        Assertions.assertTrue(updateUKYCRecordResponse.getIsUpdateRecordSuccessful());
        Assertions.assertEquals(updateUKYCRecordRequest.getCrn(), universalKYCDetailsUpdated.getCrn());
        Assertions.assertEquals(updateUKYCRecordRequest.getActor(), universalKYCDetailsUpdated.getLastModifiedBy());
        Assertions.assertEquals(
                universalKYCDetailsTransformer.convertDTOToUKYCStatus(updateUKYCRecordRequest.getKycStatus()),
                universalKYCDetailsUpdated.getUkycStatus()
        );
        Assertions.assertEquals(
                universalKYCDetailsTransformer.convertDTOToKYCStatus(updateUKYCRecordRequest.getKycStatus()),
                universalKYCDetailsUpdated.getUkycChannelStatusList().get(0).getKycStatus()
        );
        Assertions.assertEquals(
                universalKYCDetailsTransformer.convertDTOToKYCChannel(updateUKYCRecordRequest.getKycChannel()),
                universalKYCDetailsUpdated.getUkycChannelStatusList().get(0).getKycChannel()
        );
        Assertions.assertEquals(
                universalKYCDetailsTransformer.convertDTOToKYCMetadata(
                        updateUKYCRecordRequest.getKycMetadata(), updateUKYCRecordRequest.getKycChannel()),
                universalKYCDetailsUpdated.getUkycChannelStatusList().get(0).getKycMetadata()
        );
    }

    @Test
    public void test_updateUKYCRecord_updateExistingChannelStatus() {
        UniversalKYCDetails universalKYCDetails = TestUtils.getUniversalKYCDetailsWithVideoKYC();
        universalKYCDetails.setJourneyType("SavingsAccount");
        UpdateUKYCRecordRequest updateUKYCRecordRequest = TestUtils.getUpdateUKYCRecordRequestWithVideoKYC();
        updateUKYCRecordRequest.setCrn(null);
        updateUKYCRecordRequest.setKycStatus(KYCStatusDTO.COMPLETED);
        updateUKYCRecordRequest.setCompletionTime(Instant.now());

        when(universalKYCDetailsRepository.getRecord(TEST_AADHAAR_REF_KEY, TEST_APPLICATION_ID))
                .thenReturn(Optional.of(universalKYCDetails));
        ArgumentCaptor<UniversalKYCDetails> universalKYCDetailsArgumentCaptor = ArgumentCaptor.forClass(UniversalKYCDetails.class);
        doNothing().when(universalKYCDetailsRepository).updateRecord(universalKYCDetailsArgumentCaptor.capture());

        UpdateUKYCRecordResponse updateUKYCRecordResponse = universalKYCStatusService
                .updateUKYCRecord(TEST_AADHAAR_REF_KEY, TEST_APPLICATION_ID, updateUKYCRecordRequest);
        UniversalKYCDetails universalKYCDetailsUpdated = universalKYCDetailsArgumentCaptor.getValue();

        Mockito.verify(universalKYCDetailsRepository).getRecord(TEST_AADHAAR_REF_KEY, TEST_APPLICATION_ID);
        Assertions.assertTrue(updateUKYCRecordResponse.getIsUpdateRecordSuccessful());
        Assertions.assertEquals(updateUKYCRecordRequest.getCrn(), universalKYCDetailsUpdated.getCrn());
        Assertions.assertEquals(updateUKYCRecordRequest.getActor(), universalKYCDetailsUpdated.getLastModifiedBy());
        Assertions.assertEquals(
                universalKYCDetailsTransformer.convertDTOToUKYCStatus(updateUKYCRecordRequest.getKycStatus()),
                universalKYCDetailsUpdated.getUkycStatus()
        );
        Assertions.assertEquals(
                universalKYCDetailsTransformer.convertDTOToKYCStatus(updateUKYCRecordRequest.getKycStatus()),
                universalKYCDetailsUpdated.getUkycChannelStatusList().get(0).getKycStatus()
        );
        Assertions.assertEquals(
                universalKYCDetailsTransformer.convertDTOToKYCChannel(updateUKYCRecordRequest.getKycChannel()),
                universalKYCDetailsUpdated.getUkycChannelStatusList().get(0).getKycChannel()
        );
        Assertions.assertEquals(
                universalKYCDetailsTransformer.convertDTOToKYCMetadata(
                        updateUKYCRecordRequest.getKycMetadata(), updateUKYCRecordRequest.getKycChannel()),
                universalKYCDetailsUpdated.getUkycChannelStatusList().get(0).getKycMetadata()
        );
    }

    @Test
    public void test_updateUKYCRecord_throws404RestException() {
        when(universalKYCDetailsRepository.getRecord(TEST_AADHAAR_REF_KEY, TEST_APPLICATION_ID))
                .thenReturn(Optional.empty());
        UpdateUKYCRecordRequest updateUKYCRecordRequest = TestUtils.getUpdateUKYCRecordRequestWithVideoKYC();
        RestException restException = Assertions.assertThrows(RestException.class,
                () -> universalKYCStatusService
                        .updateUKYCRecord(TEST_AADHAAR_REF_KEY, TEST_APPLICATION_ID, updateUKYCRecordRequest));
        Assertions.assertEquals(HttpStatus.NOT_FOUND, restException.getHttpStatus());
    }

    @Test
    public void test_updateUKYCRecord_throws409RestException() {
        UniversalKYCDetails universalKYCDetails = TestUtils.getUniversalKYCDetailsWithNoKYCChannel();
        universalKYCDetails.setJourneyType("SavingsAccount");
        UpdateUKYCRecordRequest updateUKYCRecordRequest = TestUtils.getUpdateUKYCRecordRequestWithVideoKYC();
        updateUKYCRecordRequest.setKycStatus(KYCStatusDTO.COMPLETED);

        when(universalKYCDetailsRepository.getRecord(TEST_AADHAAR_REF_KEY, TEST_APPLICATION_ID))
                .thenReturn(Optional.ofNullable(universalKYCDetails));
        ArgumentCaptor<UniversalKYCDetails> universalKYCDetailsArgumentCaptor = ArgumentCaptor.forClass(UniversalKYCDetails.class);
        doThrow(ConditionalCheckFailedException.class).when(universalKYCDetailsRepository).updateRecord(universalKYCDetailsArgumentCaptor.capture());

        RestException restException = Assertions.assertThrows(RestException.class, () -> universalKYCStatusService
                .updateUKYCRecord(TEST_AADHAAR_REF_KEY, TEST_APPLICATION_ID, updateUKYCRecordRequest));
        Assertions.assertEquals(HttpStatus.CONFLICT, restException.getHttpStatus());
    }

}