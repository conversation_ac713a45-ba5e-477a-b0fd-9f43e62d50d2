package com.kotak.unified.dbservice.service.impl;

import com.kotak.unified.db.mf.MutualFundsOnboardingDetailsRequest;
import com.kotak.unified.db.request.mf.GetMutualFundsOnboardingDetailsRequest;
import com.kotak.unified.dbservice.TestUtils;
import com.kotak.unified.dbservice.exceptions.custom.RestException;
import com.kotak.unified.dbservice.model.mf.MutualFundsOnboardingDetailsDDBModel;
import com.kotak.unified.dbservice.repository.MutualFundsOnboardingDetailsDDBRepository;
import com.kotak.unified.dbservice.transformer.MutualFundOnboardingDetailsTransformer;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import software.amazon.awssdk.services.dynamodb.model.ConditionalCheckFailedException;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class MutualFundsOnboardingDetailsServiceTest {
    @Mock
    private MutualFundOnboardingDetailsTransformer mutualFundOnboardingDetailsTransformer;
    @Mock
    private MutualFundsOnboardingDetailsDDBRepository mutualFundsOnboardingDetailsDDBRepository;
    @InjectMocks
    private MutualFundsOnboardingDetailsService mutualFundsOnboardingDetailsService;

    @Test
    public void test_save_success() {
        MutualFundsOnboardingDetailsRequest mutualFundOnboardingDetailsRequest = TestUtils.getMockedMutualFundOnboardingDetailsRequest();
        MutualFundsOnboardingDetailsDDBModel mutualFundsOnboardingDetailsDDBModel = TestUtils.getMockedMutualFundOnboardingDetailsDDBModel();
        when(mutualFundOnboardingDetailsTransformer.convertResponseEntityToDDBModel(mutualFundOnboardingDetailsRequest))
                .thenReturn(mutualFundsOnboardingDetailsDDBModel);
        when(mutualFundsOnboardingDetailsDDBRepository.save(mutualFundsOnboardingDetailsDDBModel))
                .thenReturn(mutualFundsOnboardingDetailsDDBModel);
        when(mutualFundOnboardingDetailsTransformer.convertDDBModelToResponseEntity(mutualFundsOnboardingDetailsDDBModel))
                .thenReturn(mutualFundOnboardingDetailsRequest);
        MutualFundsOnboardingDetailsRequest response = this.mutualFundsOnboardingDetailsService.save(mutualFundOnboardingDetailsRequest);
        Assertions.assertEquals(mutualFundOnboardingDetailsRequest, response);
        verify(mutualFundOnboardingDetailsTransformer, times(1)).convertResponseEntityToDDBModel(any(MutualFundsOnboardingDetailsRequest.class));
        verify(mutualFundsOnboardingDetailsDDBRepository, times(1)).save(any(MutualFundsOnboardingDetailsDDBModel.class));
        verify(mutualFundOnboardingDetailsTransformer, times(1)).convertDDBModelToResponseEntity(any(MutualFundsOnboardingDetailsDDBModel.class));
    }

    @Test
    public void test_save_failure() {
        MutualFundsOnboardingDetailsRequest mutualFundOnboardingDetailsRequest = TestUtils.getMockedMutualFundOnboardingDetailsRequest();
        MutualFundsOnboardingDetailsDDBModel mutualFundsOnboardingDetailsDDBModel = TestUtils.getMockedMutualFundOnboardingDetailsDDBModel();
        when(mutualFundOnboardingDetailsTransformer.convertResponseEntityToDDBModel(mutualFundOnboardingDetailsRequest))
                .thenReturn(mutualFundsOnboardingDetailsDDBModel);
        doThrow(ConditionalCheckFailedException.class)
                .when(mutualFundsOnboardingDetailsDDBRepository)
                .save(mutualFundsOnboardingDetailsDDBModel);
        RestException re = Assertions.assertThrows(RestException.class, () -> mutualFundsOnboardingDetailsService.save(mutualFundOnboardingDetailsRequest));
        Assertions.assertEquals(HttpStatus.BAD_REQUEST, re.getHttpStatus());
        Assertions.assertEquals(1, re.getErrorResponseList().size());
        Assertions.assertEquals("DB021", re.getErrorResponseList().get(0).getErrorCode());
        Assertions.assertEquals("Mismatch in version of ddb item present in db and from request", re.getErrorResponseList().get(0).getErrorMessage());
        verify(mutualFundOnboardingDetailsTransformer, times(1)).convertResponseEntityToDDBModel(mutualFundOnboardingDetailsRequest);
    }

    @Test
    public void test_save_failure_Exception() {
        MutualFundsOnboardingDetailsRequest mutualFundOnboardingDetailsRequest = TestUtils.getMockedMutualFundOnboardingDetailsRequest();
        MutualFundsOnboardingDetailsDDBModel mutualFundsOnboardingDetailsDDBModel = TestUtils.getMockedMutualFundOnboardingDetailsDDBModel();
        when(mutualFundOnboardingDetailsTransformer.convertResponseEntityToDDBModel(mutualFundOnboardingDetailsRequest))
                .thenReturn(mutualFundsOnboardingDetailsDDBModel);
        doThrow(RuntimeException.class)
                .when(mutualFundsOnboardingDetailsDDBRepository)
                .save(mutualFundsOnboardingDetailsDDBModel);
        RestException re = Assertions.assertThrows(RestException.class, () -> mutualFundsOnboardingDetailsService.save(mutualFundOnboardingDetailsRequest));
        Assertions.assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, re.getHttpStatus());
        Assertions.assertEquals(1, re.getErrorResponseList().size());
        Assertions.assertEquals("DB001", re.getErrorResponseList().get(0).getErrorCode());
        Assertions.assertEquals("Internal Server Error", re.getErrorResponseList().get(0).getErrorMessage());
        verify(mutualFundOnboardingDetailsTransformer, times(1)).convertResponseEntityToDDBModel(mutualFundOnboardingDetailsRequest);
    }

    @Test
    public void test_findByEventTrackingId_getMutualFundOnboardingDetails_success() {
        MutualFundsOnboardingDetailsRequest mutualFundOnboardingDetailsRequest = TestUtils.getMockedMutualFundOnboardingDetailsRequest();
        MutualFundsOnboardingDetailsDDBModel mutualFundsOnboardingDetailsDDBModel = TestUtils.getMockedMutualFundOnboardingDetailsDDBModel();
        when(mutualFundsOnboardingDetailsDDBRepository.findByEventTrackingId("dummyEventTrackingId"))
                .thenReturn(mutualFundsOnboardingDetailsDDBModel);
        when(mutualFundOnboardingDetailsTransformer.convertDDBModelToResponseEntity(mutualFundsOnboardingDetailsDDBModel))
                .thenReturn(mutualFundOnboardingDetailsRequest);
        MutualFundsOnboardingDetailsRequest actualDto = this.mutualFundsOnboardingDetailsService.getMutualFundOnboardingDetails("dummyEventTrackingId");
        Assertions.assertEquals(mutualFundOnboardingDetailsRequest, actualDto);
        verify(mutualFundOnboardingDetailsTransformer, times(1)).convertDDBModelToResponseEntity(any(MutualFundsOnboardingDetailsDDBModel.class));
        verify(mutualFundsOnboardingDetailsDDBRepository, times(1)).findByEventTrackingId(any(String.class));
    }

    @Test
    public void test_findByEventTrackingId_getMutualFundOnboardingDetails_failure() {
        when(mutualFundsOnboardingDetailsDDBRepository.findByEventTrackingId("dummyEventTrackingId"))
                .thenReturn(null);
        RestException re = Assertions.assertThrows(RestException.class, () ->
                mutualFundsOnboardingDetailsService.getMutualFundOnboardingDetails("dummyEventTrackingId"));
        Assertions.assertEquals(HttpStatus.NOT_FOUND, re.getHttpStatus());
        Assertions.assertEquals(1, re.getErrorResponseList().size());
        Assertions.assertEquals("DB022", re.getErrorResponseList().get(0).getErrorCode());
        Assertions.assertEquals("MutualFundOnboardingDetails Record not found", re.getErrorResponseList().get(0).getErrorMessage());
        verify(mutualFundsOnboardingDetailsDDBRepository, times(1)).findByEventTrackingId(any(String.class));
    }

    @Test
    public void test_findByEventTrackingId_getCrnByEventTypeEventStatusAndCreatedAtDesc_success() {
        MutualFundsOnboardingDetailsRequest mutualFundOnboardingDetailsDto = TestUtils.getMockedMutualFundOnboardingDetailsRequest();
        MutualFundsOnboardingDetailsDDBModel mutualFundsOnboardingDetailsDDBModel = TestUtils.getMockedMutualFundOnboardingDetailsDDBModel();
        GetMutualFundsOnboardingDetailsRequest getMutualFundsOnboardingDetailsRequest = GetMutualFundsOnboardingDetailsRequest.builder().crn("dummyCrn").eventStatus("dummyEventStatus").eventType("dummyEventType").build();
        when(mutualFundsOnboardingDetailsDDBRepository.findByCrnEventTypeEventStatusAndCreatedAtDesc(getMutualFundsOnboardingDetailsRequest))
                .thenReturn(mutualFundsOnboardingDetailsDDBModel);
        when(mutualFundOnboardingDetailsTransformer.convertDDBModelToResponseEntity(mutualFundsOnboardingDetailsDDBModel))
                .thenReturn(mutualFundOnboardingDetailsDto);
        MutualFundsOnboardingDetailsRequest actualDto = this.mutualFundsOnboardingDetailsService.getCrnByEventTypeEventStatusAndCreatedAtDesc(getMutualFundsOnboardingDetailsRequest);
        Assertions.assertEquals(mutualFundOnboardingDetailsDto, actualDto);
        verify(mutualFundsOnboardingDetailsDDBRepository, times(1)).findByCrnEventTypeEventStatusAndCreatedAtDesc(any(GetMutualFundsOnboardingDetailsRequest.class));
        verify(mutualFundOnboardingDetailsTransformer, times(1)).convertDDBModelToResponseEntity(any(MutualFundsOnboardingDetailsDDBModel.class));
    }

    @Test
    public void test_findByEventTrackingId_getCrnByEventTypeEventStatusAndCreatedAtDesc_failure() {
        GetMutualFundsOnboardingDetailsRequest getMutualFundOnboardingDetailsRequest = GetMutualFundsOnboardingDetailsRequest.builder().crn("dummyCrn").eventStatus("dummyEventStatus").eventType("dummyEventType").build();
        when(mutualFundsOnboardingDetailsDDBRepository.findByCrnEventTypeEventStatusAndCreatedAtDesc(getMutualFundOnboardingDetailsRequest))
                .thenReturn(null);
        RestException re = Assertions.assertThrows(RestException.class, () ->
                mutualFundsOnboardingDetailsService.getCrnByEventTypeEventStatusAndCreatedAtDesc(getMutualFundOnboardingDetailsRequest));
        Assertions.assertEquals(HttpStatus.NOT_FOUND, re.getHttpStatus());
        Assertions.assertEquals(1, re.getErrorResponseList().size());
        Assertions.assertEquals("DB022", re.getErrorResponseList().get(0).getErrorCode());
        Assertions.assertEquals("MutualFundOnboardingDetails Record not found", re.getErrorResponseList().get(0).getErrorMessage());
        verify(mutualFundsOnboardingDetailsDDBRepository, times(1)).findByCrnEventTypeEventStatusAndCreatedAtDesc(any(GetMutualFundsOnboardingDetailsRequest.class));
    }
}
