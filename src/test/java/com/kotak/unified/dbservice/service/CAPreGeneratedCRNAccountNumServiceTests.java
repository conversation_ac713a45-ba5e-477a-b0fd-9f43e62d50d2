package com.kotak.unified.dbservice.service;

import com.kotak.unified.db.request.AssignCACRNAndAccountNumberRequest;
import com.kotak.unified.db.request.InsertCACrnAndAccountNumberRequest;
import com.kotak.unified.db.response.AssignCACRNAndAccountNumberResponse;
import com.kotak.unified.dbservice.exceptions.EntityExistsException;
import com.kotak.unified.dbservice.exceptions.PregeneratedCRNAccountNumDepletedException;
import com.kotak.unified.dbservice.model.CAPreGeneratedCRNAccountNumber;
import com.kotak.unified.dbservice.model.PGNStatus;
import com.kotak.unified.enums.CAJourneyType;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import software.amazon.awssdk.awscore.exception.AwsServiceException;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;

@ExtendWith(MockitoExtension.class)
public class CAPreGeneratedCRNAccountNumServiceTests {

    @Mock
    com.kotak.unified.dbservice.repository.CAPreGeneratedCRNAccountNumRepository CAPreGeneratedCRNAccountNumRepository;

    CAPreGeneratedCRNAccountNumService caPreGeneratedCRNAccountNumService;

    @BeforeEach
    public void setUp() {
        caPreGeneratedCRNAccountNumService = new CAPreGeneratedCRNAccountNumService(CAPreGeneratedCRNAccountNumRepository);
    }

    @Test
    void test_insertCrnAndAccountNumber_AccountNumberExists() {
        InsertCACrnAndAccountNumberRequest insertRequest = InsertCACrnAndAccountNumberRequest.builder()
                .crn("c1")
                .accountNumber("a1")
                .entityCrn("c2")
                .journeyType(CAJourneyType.BUSINESS_NON_PREFERRED_CURRENT_ACCOUNT)
                .build();

        Mockito.when(CAPreGeneratedCRNAccountNumRepository.doesAccountNumberExists(insertRequest.getAccountNumber())).thenReturn(true);
        Assertions.assertThrows(EntityExistsException.class, ()-> caPreGeneratedCRNAccountNumService.insertCrnAndAccountNumber(insertRequest));
        Mockito.verify(CAPreGeneratedCRNAccountNumRepository, Mockito.times(1)).doesAccountNumberExists(insertRequest.getAccountNumber());
    }

    @Test
    void test_insertCrnAndAccountNumber_AusCrnExists() {
        InsertCACrnAndAccountNumberRequest insertRequest = InsertCACrnAndAccountNumberRequest.builder()
                .crn("c1")
                .accountNumber("a1")
                .entityCrn("c2")
                .journeyType(CAJourneyType.BUSINESS_NON_PREFERRED_CURRENT_ACCOUNT)
                .build();

        Mockito.when(CAPreGeneratedCRNAccountNumRepository.doesAccountNumberExists(insertRequest.getAccountNumber())).thenReturn(false);
        Mockito.when(CAPreGeneratedCRNAccountNumRepository.doesAusCrnExists(insertRequest.getCrn())).thenReturn(true);

        Assertions.assertThrows(EntityExistsException.class, ()-> caPreGeneratedCRNAccountNumService.insertCrnAndAccountNumber(insertRequest));
        Mockito.verify(CAPreGeneratedCRNAccountNumRepository, Mockito.times(1)).doesAusCrnExists(insertRequest.getCrn());
    }

    @Test
    void test_insertCrnAndAccountNumber_EntityCrnExists() {
        InsertCACrnAndAccountNumberRequest insertRequest = InsertCACrnAndAccountNumberRequest.builder()
                .crn("c1")
                .accountNumber("a1")
                .entityCrn("c2")
                .journeyType(CAJourneyType.BUSINESS_NON_PREFERRED_CURRENT_ACCOUNT)
                .build();

        Mockito.when(CAPreGeneratedCRNAccountNumRepository.doesAccountNumberExists(insertRequest.getAccountNumber())).thenReturn(false);
        Mockito.when(CAPreGeneratedCRNAccountNumRepository.doesAusCrnExists(insertRequest.getCrn())).thenReturn(false);
        Mockito.when(CAPreGeneratedCRNAccountNumRepository.doesEntityCrnExists(insertRequest.getEntityCrn())).thenReturn(true);

        Assertions.assertThrows(EntityExistsException.class, ()-> caPreGeneratedCRNAccountNumService.insertCrnAndAccountNumber(insertRequest));
        Mockito.verify(CAPreGeneratedCRNAccountNumRepository, Mockito.times(1)).doesEntityCrnExists(insertRequest.getEntityCrn());
    }

    @Test
    void test_insertCrnAndAccountNumber_DynamoException() throws EntityExistsException {
        InsertCACrnAndAccountNumberRequest insertRequest = InsertCACrnAndAccountNumberRequest.builder()
                .crn("c1")
                .accountNumber("a1")
                .entityCrn("c2")
                .journeyType(CAJourneyType.BUSINESS_NON_PREFERRED_CURRENT_ACCOUNT)
                .build();

        Mockito.when(CAPreGeneratedCRNAccountNumRepository.doesAccountNumberExists(insertRequest.getAccountNumber())).thenReturn(false);
        Mockito.when(CAPreGeneratedCRNAccountNumRepository.doesAusCrnExists(insertRequest.getCrn())).thenReturn(false);
        Mockito.when(CAPreGeneratedCRNAccountNumRepository.doesEntityCrnExists(insertRequest.getEntityCrn())).thenReturn(false);

        Mockito.doThrow(AwsServiceException.builder().message("DB_ERROR").build()).when(CAPreGeneratedCRNAccountNumRepository).insertRecord(any());

        Assertions.assertThrows(AwsServiceException.class, ()-> caPreGeneratedCRNAccountNumService.insertCrnAndAccountNumber(insertRequest));
        Mockito.verify(CAPreGeneratedCRNAccountNumRepository, Mockito.times(1)).doesEntityCrnExists(insertRequest.getEntityCrn());
    }


    @Test
    void test_insertCrnAndAccountNumber_Success() throws EntityExistsException {
        InsertCACrnAndAccountNumberRequest insertRequest = InsertCACrnAndAccountNumberRequest.builder()
                .crn("c1")
                .accountNumber("a1")
                .entityCrn("c2")
                .journeyType(CAJourneyType.BUSINESS_NON_PREFERRED_CURRENT_ACCOUNT)
                .build();

        Mockito.when(CAPreGeneratedCRNAccountNumRepository.doesAccountNumberExists(insertRequest.getAccountNumber())).thenReturn(false);
        Mockito.when(CAPreGeneratedCRNAccountNumRepository.doesAusCrnExists(insertRequest.getCrn())).thenReturn(false);
        Mockito.when(CAPreGeneratedCRNAccountNumRepository.doesEntityCrnExists(insertRequest.getEntityCrn())).thenReturn(false);

        caPreGeneratedCRNAccountNumService.insertCrnAndAccountNumber(insertRequest);

        Mockito.verify(CAPreGeneratedCRNAccountNumRepository, Mockito.times(1)).doesAccountNumberExists(insertRequest.getAccountNumber());
        Mockito.verify(CAPreGeneratedCRNAccountNumRepository, Mockito.times(1)).doesAusCrnExists(insertRequest.getCrn());
        Mockito.verify(CAPreGeneratedCRNAccountNumRepository, Mockito.times(1)).doesEntityCrnExists(insertRequest.getEntityCrn());
    }

    @Test
    public void assignCRNAndAccountNumber_success()
            throws PregeneratedCRNAccountNumDepletedException, EntityExistsException {

        AssignCACRNAndAccountNumberRequest request = AssignCACRNAndAccountNumberRequest
                .builder()
                .leadTrackingNumber("l1")
                .journeyType(CAJourneyType.BUSINESS_NON_PREFERRED_CURRENT_ACCOUNT)
                .build();

        Mockito.when(CAPreGeneratedCRNAccountNumRepository.doesLeadTrackingNumberExists("l1"))
                .thenReturn(false);

        CAPreGeneratedCRNAccountNumber pgnAssigned = CAPreGeneratedCRNAccountNumber.builder()
                .accountNumber("a1")
                .leadTrackingNumber("l1")
                .crn("c1")
                .entityCrn("c2")
                .status(PGNStatus.ASSIGNED)
                .journeyType(CAJourneyType.BUSINESS_NON_PREFERRED_CURRENT_ACCOUNT)
                .build();

        Mockito.when(CAPreGeneratedCRNAccountNumRepository
                        .assignPregeneratedCRNAccountNum(request.getLeadTrackingNumber(), request.getJourneyType()))
                .thenReturn(pgnAssigned);

        AssignCACRNAndAccountNumberResponse response =
                caPreGeneratedCRNAccountNumService.assignAccountNumber(request);

        Assertions.assertNotNull(response);
        Assertions.assertEquals(pgnAssigned.getAccountNumber(), response.getAccountNumber());
        Assertions.assertEquals(pgnAssigned.getCrn(), response.getCrn());
        Assertions.assertEquals(pgnAssigned.getEntityCrn(), response.getEntityCrn());
        Assertions.assertEquals(pgnAssigned.getJourneyType(), response.getJourneyType());
    }

    @Test
    public void assignCRNAndAccountNumberCRNAlreadyExists() {

        AssignCACRNAndAccountNumberRequest request = AssignCACRNAndAccountNumberRequest
                .builder()
                .leadTrackingNumber("l1")
                .journeyType(CAJourneyType.BUSINESS_NON_PREFERRED_CURRENT_ACCOUNT)
                .build();

        Mockito.when(CAPreGeneratedCRNAccountNumRepository.doesLeadTrackingNumberExists("l1"))
                .thenReturn(true);

        assertThrows(EntityExistsException.class, () -> caPreGeneratedCRNAccountNumService.assignAccountNumber(request));
    }

}
