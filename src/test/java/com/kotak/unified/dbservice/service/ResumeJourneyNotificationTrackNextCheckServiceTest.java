package com.kotak.unified.dbservice.service;

import com.kotak.unified.db.model.ActiveScheduledNotificationNudgeDetailsDTO;
import com.kotak.unified.db.model.CreateResumeJourneyNotificationStatusRecordRequest;
import com.kotak.unified.db.model.CreateResumeJourneyNotificationStatusRecordResponse;
import com.kotak.unified.db.model.CreateResumeJourneyNotificationTrackNextCheckRecordRequest;
import com.kotak.unified.db.model.CreateResumeJourneyNotificationTrackNextCheckRecordResponse;
import com.kotak.unified.db.model.GetResumeJourneyNotificationStatusRecordResponse;
import com.kotak.unified.db.model.GetResumeJourneyNotificationTrackNextCheckRecordResponse;
import com.kotak.unified.db.model.UpdateResumeJourneyNotificationStatusRecordRequest;
import com.kotak.unified.db.model.UpdateResumeJourneyNotificationStatusRecordResponse;
import com.kotak.unified.db.model.UpdateResumeJourneyNotificationTrackNextCheckRecordRequest;
import com.kotak.unified.db.model.UpdateResumeJourneyNotificationTrackNextCheckRecordResponse;
import com.kotak.unified.dbservice.exceptions.custom.RestException;
import com.kotak.unified.dbservice.repository.ResumeJourneyNotificationStatusRepository;
import com.kotak.unified.dbservice.repository.ResumeJourneyNotificationTrackNextCheckRepository;
import com.kotak.unified.dbservice.transformer.ResumeJourneyNotificationTrackNextCheckTransformer;
import com.kotak.unified.dbservice.transformer.ResumeJourneyNotificationTransformer;
import com.kotak.unified.orchestrator.common.dbmodels.resumenotification.ActiveScheduledNotificationNudgeDetails;
import com.kotak.unified.orchestrator.common.dbmodels.resumenotification.RecipientType;
import com.kotak.unified.orchestrator.common.dbmodels.resumenotification.ResumeJourneyNotificationStatus;
import com.kotak.unified.orchestrator.common.dbmodels.resumenotification.ResumeJourneyNotificationTrackNextCheck;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import software.amazon.awssdk.services.dynamodb.model.ConditionalCheckFailedException;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Optional;

import static com.kotak.unified.dbservice.TestUtils.LEAD_ID;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ResumeJourneyNotificationTrackNextCheckServiceTest {

    @Mock
    private ResumeJourneyNotificationTrackNextCheckRepository resumeJourneyNotificationTrackNextCheckRepository;
    private ResumeJourneyNotificationTrackNextCheckService resumeJourneyNotificationTrackNextCheckService;
    private final ResumeJourneyNotificationTrackNextCheckTransformer resumeJourneyNotificationTrackNextCheckTransformer = new ResumeJourneyNotificationTrackNextCheckTransformer();
    private static Instant instant1 = Instant.ofEpochSecond(1720017971);
    private static Instant instant2 = Instant.ofEpochSecond(1720017882);
    @BeforeEach
    void setUp() {
        resumeJourneyNotificationTrackNextCheckService = new ResumeJourneyNotificationTrackNextCheckService(
                resumeJourneyNotificationTrackNextCheckRepository, resumeJourneyNotificationTrackNextCheckTransformer);
    }

    @Test
    void test_createResumeJourneyNotificationTrackNextCheckRecord_success() {
        CreateResumeJourneyNotificationTrackNextCheckRecordRequest createResumeJourneyNotificationTrackNextCheckRecordRequest =
                CreateResumeJourneyNotificationTrackNextCheckRecordRequest.builder()
                        .leadTrackingNumber(LEAD_ID)
                        .nextCheckTime(instant1)
                        .build();

        ArgumentCaptor<ResumeJourneyNotificationTrackNextCheck> resumeJourneyNotificationTrackNextCheckArgumentCaptor = ArgumentCaptor
                .forClass(ResumeJourneyNotificationTrackNextCheck.class);
        Mockito.doNothing().when(resumeJourneyNotificationTrackNextCheckRepository).putRecord(resumeJourneyNotificationTrackNextCheckArgumentCaptor.capture());
        CreateResumeJourneyNotificationTrackNextCheckRecordResponse createResumeJourneyNotificationTrackNextCheckRecordResponse = resumeJourneyNotificationTrackNextCheckService
                .createResumeJourneyNotificationTrackNextCheckRecord(createResumeJourneyNotificationTrackNextCheckRecordRequest);
        Assertions.assertTrue(createResumeJourneyNotificationTrackNextCheckRecordResponse.getIsCreateRecordSuccessful());

        ResumeJourneyNotificationTrackNextCheck resumeJourneyNotificationTrackNextCheck = resumeJourneyNotificationTrackNextCheckArgumentCaptor.getValue();
        Assertions.assertEquals(resumeJourneyNotificationTrackNextCheck.getLeadTrackingNumber(), LEAD_ID);
        Assertions.assertEquals(resumeJourneyNotificationTrackNextCheck.getNextCheckTime(), instant1);
    }

    @Test
    void test_createResumeJourneyNotificationTrackNextCheckRecord_throws409RestException() {
        CreateResumeJourneyNotificationTrackNextCheckRecordRequest createResumeJourneyNotificationTrackNextCheckRecordRequest =
                CreateResumeJourneyNotificationTrackNextCheckRecordRequest.builder()
                        .leadTrackingNumber(LEAD_ID)
                        .nextCheckTime(instant1)
                        .build();

        ArgumentCaptor<ResumeJourneyNotificationTrackNextCheck> resumeJourneyNotificationTrackNextCheckArgumentCaptor = ArgumentCaptor
                .forClass(ResumeJourneyNotificationTrackNextCheck.class);

        doThrow(ConditionalCheckFailedException.class)
                .when(resumeJourneyNotificationTrackNextCheckRepository)
                .putRecord(resumeJourneyNotificationTrackNextCheckArgumentCaptor.capture());
        RestException restException = Assertions.assertThrows(RestException.class, () -> {
            resumeJourneyNotificationTrackNextCheckService.createResumeJourneyNotificationTrackNextCheckRecord(createResumeJourneyNotificationTrackNextCheckRecordRequest);
        });
        Assertions.assertEquals(HttpStatus.CONFLICT, restException.getHttpStatus());
    }

    @Test
    void test_updateResumeJourneyNotificationTrackNextCheckRecord_success() {
        ResumeJourneyNotificationTrackNextCheck resumeJourneyNotificationTrackNextCheck = ResumeJourneyNotificationTrackNextCheck.builder()
                .leadTrackingNumber(LEAD_ID)
                .nextCheckTime(instant1)
                .build();

        UpdateResumeJourneyNotificationTrackNextCheckRecordRequest updateResumeJourneyNotificationTrackNextCheckRecordRequest =
                UpdateResumeJourneyNotificationTrackNextCheckRecordRequest.builder()
                        .nextCheckTime(instant2)
                        .build();

        when(resumeJourneyNotificationTrackNextCheckRepository.getRecord(LEAD_ID))
                .thenReturn(Optional.ofNullable(resumeJourneyNotificationTrackNextCheck));
        ArgumentCaptor<ResumeJourneyNotificationTrackNextCheck> resumeJourneyNotificationTrackNextCheckArgumentCaptor = ArgumentCaptor.forClass(ResumeJourneyNotificationTrackNextCheck.class);
        doNothing().when(resumeJourneyNotificationTrackNextCheckRepository).updateRecord(resumeJourneyNotificationTrackNextCheckArgumentCaptor.capture());

        UpdateResumeJourneyNotificationTrackNextCheckRecordResponse updateResumeJourneyNotificationTrackNextCheckRecordResponse = resumeJourneyNotificationTrackNextCheckService
                .updateResumeJourneyNotificationTrackNextCheckRecord(LEAD_ID, updateResumeJourneyNotificationTrackNextCheckRecordRequest);
        ResumeJourneyNotificationTrackNextCheck resumeJourneyNotificationTrackNextCheckUpdated = resumeJourneyNotificationTrackNextCheckArgumentCaptor.getValue();

        Mockito.verify(resumeJourneyNotificationTrackNextCheckRepository).getRecord(LEAD_ID);
        Assertions.assertTrue(updateResumeJourneyNotificationTrackNextCheckRecordResponse.getIsUpdateRecordSuccessful());
        Assertions.assertEquals(updateResumeJourneyNotificationTrackNextCheckRecordRequest.getNextCheckTime(),
                resumeJourneyNotificationTrackNextCheckUpdated.getNextCheckTime());
    }


    @Test
    void test_updateResumeJourneyNotificationTrackNextCheckRecord_throws404RestException() {
        when(resumeJourneyNotificationTrackNextCheckRepository.getRecord(LEAD_ID))
                .thenReturn(Optional.empty());

        UpdateResumeJourneyNotificationTrackNextCheckRecordRequest updateResumeJourneyNotificationTrackNextCheckRecordRequest =
                UpdateResumeJourneyNotificationTrackNextCheckRecordRequest.builder()
                        .nextCheckTime(instant2)
                        .build();

        RestException restException = Assertions.assertThrows(RestException.class,
                () -> resumeJourneyNotificationTrackNextCheckService
                        .updateResumeJourneyNotificationTrackNextCheckRecord(LEAD_ID, updateResumeJourneyNotificationTrackNextCheckRecordRequest));
        Assertions.assertEquals(HttpStatus.NOT_FOUND, restException.getHttpStatus());
    }

    @Test
    void test_updateResumeJourneyNotificationTrackNextCheckRecord_throws409RestException() {
        ResumeJourneyNotificationTrackNextCheck resumeJourneyNotificationTrackNextCheck = ResumeJourneyNotificationTrackNextCheck.builder()
                .leadTrackingNumber(LEAD_ID)
                .nextCheckTime(instant1)
                .build();

        UpdateResumeJourneyNotificationTrackNextCheckRecordRequest updateResumeJourneyNotificationTrackNextCheckRecordRequest =
                UpdateResumeJourneyNotificationTrackNextCheckRecordRequest.builder()
                        .nextCheckTime(instant2)
                        .build();

        when(resumeJourneyNotificationTrackNextCheckRepository.getRecord(LEAD_ID))
                .thenReturn(Optional.ofNullable(resumeJourneyNotificationTrackNextCheck));

        ArgumentCaptor<ResumeJourneyNotificationTrackNextCheck> resumeJourneyNotificationTrackNextCheckArgumentCaptor = ArgumentCaptor.forClass(ResumeJourneyNotificationTrackNextCheck.class);
        doThrow(ConditionalCheckFailedException.class).when(resumeJourneyNotificationTrackNextCheckRepository).updateRecord(resumeJourneyNotificationTrackNextCheckArgumentCaptor.capture());

        RestException restException = Assertions.assertThrows(RestException.class, () -> resumeJourneyNotificationTrackNextCheckService
                .updateResumeJourneyNotificationTrackNextCheckRecord(LEAD_ID, updateResumeJourneyNotificationTrackNextCheckRecordRequest));
        Assertions.assertEquals(HttpStatus.CONFLICT, restException.getHttpStatus());
    }

    @Test
    public void test_getResumeJourneyNotificationTrackNextCheckRecord_success() {
        ResumeJourneyNotificationTrackNextCheck resumeJourneyNotificationTrackNextCheck = ResumeJourneyNotificationTrackNextCheck.builder()
                .leadTrackingNumber(LEAD_ID)
                .nextCheckTime(instant1)
                .build();

        when(resumeJourneyNotificationTrackNextCheckRepository.getRecord(LEAD_ID))
                .thenReturn(Optional.ofNullable(resumeJourneyNotificationTrackNextCheck));

        GetResumeJourneyNotificationTrackNextCheckRecordResponse getResumeJourneyNotificationTrackNextCheckRecordResponse = resumeJourneyNotificationTrackNextCheckService
                .getResumeJourneyNotificationTrackNextCheckRecord(LEAD_ID);

        Mockito.verify(resumeJourneyNotificationTrackNextCheckRepository).getRecord(LEAD_ID);
        Assertions.assertEquals(resumeJourneyNotificationTrackNextCheck.getLeadTrackingNumber(), getResumeJourneyNotificationTrackNextCheckRecordResponse.getLeadTrackingNumber());
        Assertions.assertEquals(resumeJourneyNotificationTrackNextCheck.getNextCheckTime(), getResumeJourneyNotificationTrackNextCheckRecordResponse.getNextCheckTime());
    }

    @Test
    public void test_getResumeJourneyNotificationTrackNextCheckRecord_throws404RestException() {
        when(resumeJourneyNotificationTrackNextCheckRepository.getRecord(LEAD_ID))
                .thenReturn(Optional.empty());

        RestException restException = Assertions.assertThrows(RestException.class,
                () -> resumeJourneyNotificationTrackNextCheckService.getResumeJourneyNotificationTrackNextCheckRecord(LEAD_ID));

        Mockito.verify(resumeJourneyNotificationTrackNextCheckRepository).getRecord(LEAD_ID);
        Assertions.assertEquals(HttpStatus.NOT_FOUND, restException.getHttpStatus());
    }
}