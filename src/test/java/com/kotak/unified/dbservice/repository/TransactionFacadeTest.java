package com.kotak.unified.dbservice.repository;

import com.kotak.unified.orchestrator.common.dbmodels.Transaction;
import com.kotak.unified.orchestrator.common.dbmodels.TransactionDDBModel;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class TransactionFacadeTest {
    @Mock
    private TransactionDDBRepository transactionDDBRepository;

    private TransactionFacade transactionFacade;

    String txnId = "abc";

    String leadTrackingNumber = "123";

    Transaction transaction = getTransaction();

    TransactionDDBModel transactionDDBModel = getTransactionDDBModel();

    @BeforeEach
    public void setUp() {
        transactionFacade = new TransactionFacade(transactionDDBRepository);
    }


    @Test
    public void testFindById_DDBEnabled() {
        when(transactionDDBRepository.findTransactionByTxnId(txnId)).thenReturn(transactionDDBModel);

        Optional<Transaction> result = transactionFacade.findById(txnId);
        assertEquals(transaction, result.get());

        verify(transactionDDBRepository, times(1)).findTransactionByTxnId(txnId);
    }

    @Test
    public void testFindById_DDBEnabled_Null() {
        when(transactionDDBRepository.findTransactionByTxnId(txnId)).thenReturn(null);

        Optional<Transaction> result = transactionFacade.findById(txnId);
        assertEquals(Optional.empty(), result);

        verify(transactionDDBRepository, times(1)).findTransactionByTxnId(txnId);
    }
    @Test
    public void testFindById_DDBEnabled_exception() {
        when(transactionDDBRepository.findTransactionByTxnId(txnId)).thenThrow(new RuntimeException());

        assertThrows(RuntimeException.class,
                () -> transactionFacade.findById(txnId));

        verify(transactionDDBRepository, times(1)).findTransactionByTxnId(txnId);
    }

    private TransactionDDBModel getTransactionDDBModel() {
        return TransactionDDBModel.builder()
                .txnId(txnId)
                .leadTrackingNumber(leadTrackingNumber)
                .build();
    }

    private Transaction getTransaction() {
        return Transaction.builder()
                .txnId(txnId)
                .leadTrackingNumber(leadTrackingNumber)
                .build();
    }
}
