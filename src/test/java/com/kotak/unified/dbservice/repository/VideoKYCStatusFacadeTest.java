package com.kotak.unified.dbservice.repository;

import com.kotak.unified.orchestrator.common.dbmodels.vkycStatusModel.VideoKycDetails;
import com.kotak.unified.orchestrator.common.dbmodels.vkycStatusModel.VideoKycDetailsDDBModel;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class VideoKYCStatusFacadeTest {
    @Mock
    private VideoKycStatusDDBRepository videoKycStatusDDBRepository;

    private VideoKYCStatusFacade videoKYCStatusFacade;

    Instant createdAt = Instant.now();

    Instant lastModifiedAt = Instant.now();

    String leadTrackingNumber = "123";

    String status = "PENDING";

    String trackingId = "ABC";

    VideoKycDetails videoKycDetails = getVideoKycDetail();


    VideoKycDetailsDDBModel videoKycDetailsDDBModel = getVideoKycDetailsDDBModel();

    VideoKycDetailsDDBModel videoKycDetailsDDBModelWithoutLastModifiedAt = getVideoKycDetailsDDBModelWithoutLastModifiedAt();

    @BeforeEach
    public void setUp() {
        videoKYCStatusFacade = new VideoKYCStatusFacade(videoKycStatusDDBRepository);
    }

    @Test
    public void  testSaveWithDynamoEnabled() {
        when(videoKycStatusDDBRepository.save(videoKycDetailsDDBModelWithoutLastModifiedAt))
                .thenReturn(videoKycDetailsDDBModelWithoutLastModifiedAt);

        VideoKycDetails savedVideoKycDetails = videoKYCStatusFacade.save(videoKycDetails);

        verify(videoKycStatusDDBRepository, times(1)).save(videoKycDetailsDDBModelWithoutLastModifiedAt);
        assertNotNull(savedVideoKycDetails.getLastModifiedAt());
    }

    @Test
    public void  testSaveWithDynamoEnabledExceptionInDynamo() {
        when(videoKycStatusDDBRepository.save(videoKycDetailsDDBModelWithoutLastModifiedAt)).thenThrow(new RuntimeException());

        Assertions.assertThrows(RuntimeException.class, () ->videoKYCStatusFacade.save(videoKycDetails));

        verify(videoKycStatusDDBRepository, times(1)).save(videoKycDetailsDDBModelWithoutLastModifiedAt);
    }

    @Test
    public void  testFindTopByLeadTrackingNumberOrderByCreatedAtDesc_ddbEnabled() {
        when(videoKycStatusDDBRepository.findVideoKycDetailsDDBModelsByLeadTrackingNumber(leadTrackingNumber))
                .thenReturn(Collections.singletonList(videoKycDetailsDDBModel));

        VideoKycDetails result = videoKYCStatusFacade.findTopByLeadTrackingNumberOrderByCreatedAtDesc(leadTrackingNumber);

        assertEquals(videoKycDetails, result);
        assertNotNull(result.getLastModifiedAt());

        verify(videoKycStatusDDBRepository, times(1)).findVideoKycDetailsDDBModelsByLeadTrackingNumber(leadTrackingNumber);
    }

    @Test
    public void  testFindTopByLeadTrackingNumberOrderByCreatedAtDesc_ddbEnabled_exception() {
        when(videoKycStatusDDBRepository.findVideoKycDetailsDDBModelsByLeadTrackingNumber(leadTrackingNumber))
                .thenThrow(new RuntimeException());

        Assertions.assertThrows(RuntimeException.class,
                () -> videoKYCStatusFacade.findTopByLeadTrackingNumberOrderByCreatedAtDesc(leadTrackingNumber));

        verify(videoKycStatusDDBRepository, times(1)).findVideoKycDetailsDDBModelsByLeadTrackingNumber(leadTrackingNumber);
    }

    @Test
    public void  testFindTopByLeadTrackingNumberOrderByCreatedAtDesc_ddbEnabled_null() {
        when(videoKycStatusDDBRepository.findVideoKycDetailsDDBModelsByLeadTrackingNumber(leadTrackingNumber))
                .thenReturn(null);

        VideoKycDetails result = videoKYCStatusFacade.findTopByLeadTrackingNumberOrderByCreatedAtDesc(leadTrackingNumber);

        Assertions.assertNull(result);

        verify(videoKycStatusDDBRepository, times(1)).findVideoKycDetailsDDBModelsByLeadTrackingNumber(leadTrackingNumber);
    }

    @Test
    public void  testFindTopByLeadTrackingNumberOrderByCreatedAtDesc_ddbEnabled_empty() {
        when(videoKycStatusDDBRepository.findVideoKycDetailsDDBModelsByLeadTrackingNumber(leadTrackingNumber))
                .thenReturn(new ArrayList<>());

        VideoKycDetails result = videoKYCStatusFacade.findTopByLeadTrackingNumberOrderByCreatedAtDesc(leadTrackingNumber);

        Assertions.assertNull(result);

        verify(videoKycStatusDDBRepository, times(1)).findVideoKycDetailsDDBModelsByLeadTrackingNumber(leadTrackingNumber);
    }

    @Test
    public void findTopByLeadTrackingNumberAndLatestStatusOrderByCreatedAtNullParam() {
        assertThrows(NullPointerException.class, () ->
                videoKYCStatusFacade.findTopByLeadTrackingNumberAndLatestStatusOrderByCreatedAt(null, null));
    }

    @Test
    public void  testFindByTrackingId_DDBEnabled() {
        when(videoKycStatusDDBRepository.findByTrackingId(trackingId)).thenReturn(videoKycDetailsDDBModel);

        VideoKycDetails result = videoKYCStatusFacade.findByTrackingId(trackingId);

        assertEquals(videoKycDetails, result);

        verify(videoKycStatusDDBRepository, times(1)).findByTrackingId(trackingId);

    }

    @Test
    public void  testFindByTrackingId_DDBEnabled_exception() {
        when(videoKycStatusDDBRepository.findByTrackingId(trackingId)).thenThrow(new RuntimeException());

        Assertions.assertThrows(RuntimeException.class, () -> videoKYCStatusFacade.findByTrackingId(trackingId));

        verify(videoKycStatusDDBRepository, times(1)).findByTrackingId(trackingId);

    }

    @Test
    public void  testFindByTrackingId_DDBEnabled_null() {
        when(videoKycStatusDDBRepository.findByTrackingId(trackingId)).thenReturn(null);

        VideoKycDetails result = videoKYCStatusFacade.findByTrackingId(trackingId);

        assertNull(result);

        verify(videoKycStatusDDBRepository, times(1)).findByTrackingId(trackingId);

    }

    @Test
    public void  testFindTopByLeadTrackingNumberAndLatestStatusOrderByCreatedAt_DDBEnabled() {
        when(videoKycStatusDDBRepository.findTopByLeadTrackingNumberAndLatestStatusOrderByCreatedAt(leadTrackingNumber, status)).thenReturn(videoKycDetailsDDBModel);

        VideoKycDetails result = videoKYCStatusFacade.findTopByLeadTrackingNumberAndLatestStatusOrderByCreatedAt(leadTrackingNumber, status);

        assertEquals(videoKycDetails, result);

        verify(videoKycStatusDDBRepository, times(1)).findTopByLeadTrackingNumberAndLatestStatusOrderByCreatedAt(leadTrackingNumber, status);
    }

    @Test
    public void  testFindTopByLeadTrackingNumberAndLatestStatusOrderByCreatedAt_DDBEnabled_exception() {
        when(videoKycStatusDDBRepository.findTopByLeadTrackingNumberAndLatestStatusOrderByCreatedAt(leadTrackingNumber, status))
                .thenThrow(new RuntimeException());

       Assertions.assertThrows(RuntimeException.class,
               () -> videoKYCStatusFacade.findTopByLeadTrackingNumberAndLatestStatusOrderByCreatedAt(leadTrackingNumber, status));

        verify(videoKycStatusDDBRepository, times(1)).findTopByLeadTrackingNumberAndLatestStatusOrderByCreatedAt(leadTrackingNumber, status);
    }

    @Test
    public void  testFindTopByLeadTrackingNumberAndLatestStatusOrderByCreatedAt_DDBEnabled_null() {
        when(videoKycStatusDDBRepository.findTopByLeadTrackingNumberAndLatestStatusOrderByCreatedAt(leadTrackingNumber, status)).thenReturn(null);

        VideoKycDetails result = videoKYCStatusFacade.findTopByLeadTrackingNumberAndLatestStatusOrderByCreatedAt(leadTrackingNumber, status);

        assertNull(result);

        verify(videoKycStatusDDBRepository, times(1)).findTopByLeadTrackingNumberAndLatestStatusOrderByCreatedAt(leadTrackingNumber, status);
    }

    private VideoKycDetails getVideoKycDetail() {
        return VideoKycDetails.builder()
                .trackingId(trackingId)
                .leadTrackingNumber(leadTrackingNumber)
                .latestStatus(status)
                .createdAt(createdAt)
                .lastModifiedAt(lastModifiedAt)
                .build();
    }

    private VideoKycDetailsDDBModel getVideoKycDetailsDDBModel() {
        return VideoKycDetailsDDBModel.builder()
                .trackingId(trackingId)
                .leadTrackingNumber(leadTrackingNumber)
                .latestStatus(status)
                .createdAt(createdAt)
                .lastModifiedAt(lastModifiedAt)
                .build();
    }

    private VideoKycDetailsDDBModel getVideoKycDetailsDDBModelWithoutLastModifiedAt() {
        return VideoKycDetailsDDBModel.builder()
                .trackingId(trackingId)
                .leadTrackingNumber(leadTrackingNumber)
                .latestStatus(status)
                .createdAt(createdAt)
                .build();
    }
}

