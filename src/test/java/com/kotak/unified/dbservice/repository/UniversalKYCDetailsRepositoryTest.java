package com.kotak.unified.dbservice.repository;


import com.kotak.unified.dbservice.repository.impl.UniversalKYCDetailsRepositoryImpl;
import com.kotak.unified.orchestrator.common.dbmodels.ukycStatusModel.UniversalKYCDetails;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;

import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;


@ExtendWith(MockitoExtension.class)
class UniversalKYCDetailsRepositoryTest {

    @Mock
    private DynamoDbEnhancedClient dynamoDbEnhancedClient;

    @Mock
    private DynamoDbTable<UniversalKYCDetails> dynamoDbTable;

    private UniversalKYCDetailsRepository universalKYCDetailsRepository;

    @BeforeEach
    void setUp() {
        when(dynamoDbEnhancedClient.table(Mockito.anyString(), Mockito.<TableSchema<UniversalKYCDetails>>any())).thenReturn(dynamoDbTable);
        universalKYCDetailsRepository = new UniversalKYCDetailsRepositoryImpl(dynamoDbEnhancedClient, "testTable");
    }

    @Test
    void updateRecord() {
        UniversalKYCDetails universalKYCDetails = new UniversalKYCDetails();
        universalKYCDetailsRepository.updateRecord(universalKYCDetails);
        verify(dynamoDbTable, times(1)).updateItem(universalKYCDetails);
    }

    @Test
    void putRecord() {
        UniversalKYCDetails universalKYCDetails = new UniversalKYCDetails();
        universalKYCDetailsRepository.putRecord(universalKYCDetails);
        verify(dynamoDbTable, times(1)).putItem(universalKYCDetails);
    }

    @Test
    void getRecord() {
        String aadhaarRefKey = "testKey";
        String applicationId = "testId";
        universalKYCDetailsRepository.getRecord(aadhaarRefKey, applicationId);
        verify(dynamoDbTable, times(1)).getItem(Key.builder().partitionValue(aadhaarRefKey).sortValue(applicationId).build());
    }
}
