package com.kotak.unified.dbservice.repository;

import com.kotak.unified.dbservice.TestUtils;
import com.kotak.unified.dbservice.config.DynamoAWSConfiguration;
import com.kotak.unified.dbservice.model.cpv.CpvDvuVerificationDetailsDDBModel;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;

@ExtendWith(MockitoExtension.class)
public class CpvDvuVerificationDetailsDDBRepositoryTest {

    private DynamoDbEnhancedClient dynamoDbEnhancedClient;

    private DynamoAWSConfiguration dynamoAWSConfiguration;

    private DynamoDbTable<CpvDvuVerificationDetailsDDBModel> dynamoDbTable;

    private CpvDvuVerificationDetailsDDBRepository cpvDvuVerificationDetailsDDBRepository;

    public CpvDvuVerificationDetailsDDBRepositoryTest() {
        dynamoAWSConfiguration = Mockito.mock(DynamoAWSConfiguration.class);
        dynamoDbEnhancedClient = Mockito.mock(DynamoDbEnhancedClient.class);
        dynamoDbTable = Mockito.mock();
        Mockito.when(dynamoAWSConfiguration.getCpvDvuVerificationDetailsTableName()).thenReturn("UAO-cpvDvuVerificationDetails-dev");
        Mockito.when(dynamoDbEnhancedClient.table("UAO-cpvDvuVerificationDetails-dev",
                TableSchema.fromBean(CpvDvuVerificationDetailsDDBModel.class))).thenReturn(dynamoDbTable);
        cpvDvuVerificationDetailsDDBRepository = new CpvDvuVerificationDetailsDDBRepository(dynamoDbEnhancedClient,
                dynamoAWSConfiguration);
    }

    @Test
    public void test_save() {
        CpvDvuVerificationDetailsDDBModel cpvDvuVerificationDetailsDDBModel = TestUtils.getMockedCpvDvuVerificationDetailsDDB();
        Mockito.when(dynamoDbTable.updateItem(cpvDvuVerificationDetailsDDBModel)).thenReturn(cpvDvuVerificationDetailsDDBModel);
        CpvDvuVerificationDetailsDDBModel returnedDDBModel = this.cpvDvuVerificationDetailsDDBRepository.save(cpvDvuVerificationDetailsDDBModel);
        validateDDBModel(cpvDvuVerificationDetailsDDBModel, returnedDDBModel);
    }

    @Test
    public void test_get() {
        CpvDvuVerificationDetailsDDBModel cpvDvuVerificationDetailsDDBModel = TestUtils.getMockedCpvDvuVerificationDetailsDDB();
        Mockito.when(dynamoDbTable.getItem(Mockito.any(Key.class))).thenReturn(cpvDvuVerificationDetailsDDBModel);
        CpvDvuVerificationDetailsDDBModel returnedDDBModel = this.cpvDvuVerificationDetailsDDBRepository.getByActionTrackingId("a1");
        validateDDBModel(cpvDvuVerificationDetailsDDBModel, returnedDDBModel);
    }

    private void validateDDBModel(CpvDvuVerificationDetailsDDBModel expectedDDBModel,
                                  CpvDvuVerificationDetailsDDBModel actualDDBModel) {
        Assertions.assertEquals(expectedDDBModel.getLeadTrackingNumber(), actualDDBModel.getLeadTrackingNumber());
        Assertions.assertEquals(expectedDDBModel.getActionTrackingId(), actualDDBModel.getActionTrackingId());
        Assertions.assertEquals(expectedDDBModel.getCpvCode(), actualDDBModel.getCpvCode());
        Assertions.assertEquals(expectedDDBModel.getDvuCode(), actualDDBModel.getDvuCode());
        Assertions.assertEquals(expectedDDBModel.getLatestStatus(), actualDDBModel.getLatestStatus());
        Assertions.assertEquals(expectedDDBModel.getLastStatusEventRecordedAt(), actualDDBModel.getLastStatusEventRecordedAt());
        Assertions.assertEquals(expectedDDBModel.getVersion(), actualDDBModel.getVersion());
        Assertions.assertEquals(expectedDDBModel.getCreatedAt(), actualDDBModel.getCreatedAt());
        Assertions.assertEquals(expectedDDBModel.getLastModifiedAt(), actualDDBModel.getLastModifiedAt());
    }
}
