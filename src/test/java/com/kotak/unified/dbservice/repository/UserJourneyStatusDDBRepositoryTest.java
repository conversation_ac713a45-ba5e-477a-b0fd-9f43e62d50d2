package com.kotak.unified.dbservice.repository;

import com.kotak.unified.dbservice.TestUtils;
import com.kotak.unified.dbservice.config.DynamoAWSConfiguration;
import com.kotak.unified.orchestrator.common.dbmodels.JourneyStatus;
import com.kotak.unified.orchestrator.common.dbmodels.Status;
import com.kotak.unified.orchestrator.common.dbmodels.StepStatus;
import com.kotak.unified.orchestrator.common.dbmodels.UserJourneyStatusDDBModel;
import com.kotak.unified.orchestrator.common.dbmodels.UserStepStatus;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;

import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.kotak.unified.dbservice.TestUtils.SAVINGS_ACCOUNT_GRAPH_NAME;
import static com.kotak.unified.dbservice.TestUtils.TEST_LAST_UPDATED_TIME;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class UserJourneyStatusDDBRepositoryTest {
    @Mock
    private DynamoDbEnhancedClient dynamoDbEnhancedClient;

    @Mock
    private DynamoDbTable<UserJourneyStatusDDBModel> dynamoDbTable;

    @Mock
    private DynamoAWSConfiguration dynamoAWSConfiguration;

    private UserJourneyStatusDDBRepository repository;

    @BeforeEach
    void setUp() {
        when(dynamoAWSConfiguration.getUserJourneyStatusTableName()).thenReturn("testTable");
        when(dynamoDbEnhancedClient.table(Mockito.anyString(), Mockito.<TableSchema<UserJourneyStatusDDBModel>>any())).thenReturn(dynamoDbTable);
        repository = new UserJourneyStatusDDBRepository(dynamoDbEnhancedClient, dynamoAWSConfiguration);
    }

    @Test
    void save_happyCase() {
        UserJourneyStatusDDBModel userJourneyStatusDDBModel = getMockUserJourneyStatusDDBModel();
        when(dynamoDbTable.updateItem(userJourneyStatusDDBModel)).thenReturn(userJourneyStatusDDBModel);
        Assertions.assertEquals(userJourneyStatusDDBModel, repository.save(userJourneyStatusDDBModel));
        verify(dynamoDbTable, times(1)).updateItem(userJourneyStatusDDBModel);
    }

    @Test
    void save_exception() {
        UserJourneyStatusDDBModel userJourneyStatusDDBModel = getMockUserJourneyStatusDDBModel();
        when(dynamoDbTable.updateItem(userJourneyStatusDDBModel)).thenThrow(new RuntimeException("Error"));
        Assertions.assertThrows(RuntimeException.class, () -> repository.save(userJourneyStatusDDBModel));
        verify(dynamoDbTable, times(1)).updateItem(userJourneyStatusDDBModel);
    }

    @Test
    void findByLeadTrackingNumber_happyCase() {
        Key mockKey = Key.builder()
                .partitionValue(TestUtils.LEAD_ID)
                .build();
        UserJourneyStatusDDBModel userJourneyStatusDDBModel = getMockUserJourneyStatusDDBModel();
        when(dynamoDbTable.getItem(mockKey)).thenReturn(userJourneyStatusDDBModel);
        Assertions.assertEquals(userJourneyStatusDDBModel, repository.findByLeadTrackingNumber(TestUtils.LEAD_ID));
        verify(dynamoDbTable, times(1)).getItem(mockKey);
    }

    @Test
    void findByLeadTrackingNumber_exception() {
        Key mockKey = Key.builder()
                .partitionValue(TestUtils.LEAD_ID)
                .build();
        when(dynamoDbTable.getItem(mockKey)).thenThrow(new RuntimeException("Error"));
        Assertions.assertThrows(RuntimeException.class, () -> repository.findByLeadTrackingNumber(TestUtils.LEAD_ID));
        verify(dynamoDbTable, times(1)).getItem(mockKey);
    }


    private UserJourneyStatusDDBModel getMockUserJourneyStatusDDBModel() {
        List<StepStatus> phoneNumberStepStatus = new ArrayList<>();
        StepStatus getPhoneNumber = StepStatus.builder()
                .name("GetPhoneNumber")
                .status(Status.COMPLETED)
                .lastUpdatedTime(TEST_LAST_UPDATED_TIME)
                .build();
        phoneNumberStepStatus.add(getPhoneNumber);
        StepStatus phoneNumber = StepStatus.builder()
                .name("PhoneNumber")
                .status(Status.COMPLETED)
                .lastUpdatedTime(TEST_LAST_UPDATED_TIME)
                .stepsStatus(phoneNumberStepStatus)
                .build();


        List<StepStatus> aadhaarPanVerificationStepStatus = new ArrayList<>();
        StepStatus checkAadhaarDetails = StepStatus.builder()
                .name("CheckAadhaarDetails")
                .status(Status.COMPLETED)
                .lastUpdatedTime(TEST_LAST_UPDATED_TIME)
                .build();
        StepStatus getAadhaarPanDetails = StepStatus.builder()
                .name("GetAadhaarPanDetails")
                .status(Status.COMPLETED)
                .lastUpdatedTime(TEST_LAST_UPDATED_TIME)
                .build();
        StepStatus aadhaarOtpVerification = StepStatus.builder()
                .name("AadhaarOtpVerification")
                .status(Status.INITIATED)
                .lastUpdatedTime(TEST_LAST_UPDATED_TIME)
                .build();
        aadhaarPanVerificationStepStatus.add(checkAadhaarDetails);
        aadhaarPanVerificationStepStatus.add(getAadhaarPanDetails);
        aadhaarPanVerificationStepStatus.add(aadhaarOtpVerification);
        StepStatus aadhaarPanVerification = StepStatus.builder()
                .name("AadhaarPanVerification")
                .status(Status.INITIATED)
                .lastUpdatedTime(TEST_LAST_UPDATED_TIME)
                .stepsStatus(aadhaarPanVerificationStepStatus)
                .build();

        List<StepStatus> graphStepsStatus = new ArrayList<>();
        graphStepsStatus.add(phoneNumber);
        graphStepsStatus.add(aadhaarPanVerification);

        UserStepStatus userStepStatus = UserStepStatus.builder()
                .version("1.0")
                .status(Status.INITIATED)
                .isAsyncUIStep(false)
                .lastUpdatedTime(Instant.now())
                .stepsStatus(graphStepsStatus)
                .build();
        Map<String, UserStepStatus> graphStatus = new HashMap<>();
        graphStatus.put(SAVINGS_ACCOUNT_GRAPH_NAME, userStepStatus);

        return UserJourneyStatusDDBModel.builder()
                .leadTrackingNumber(TestUtils.LEAD_ID)
                .phoneNumber(TestUtils.PHONE_NUMBER)
                .crn(TestUtils.TEST_CRN)
                .journeyType(SAVINGS_ACCOUNT_GRAPH_NAME)
                .createdAt(TEST_LAST_UPDATED_TIME)
                .status(graphStatus)
                .journeyStatus(JourneyStatus.ON_HOLD)
                .build();
    }
}