package com.kotak.unified.dbservice.repository;

import com.kotak.unified.dbservice.repository.impl.ResumeJourneyNotificationStatusImpl;
import com.kotak.unified.orchestrator.common.dbmodels.resumenotification.ResumeJourneyNotificationStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;

import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ResumeJourneyNotificationStatusRepositoryTest {

    @Mock
    private DynamoDbEnhancedClient dynamoDbEnhancedClient;

    @Mock
    private DynamoDbTable<ResumeJourneyNotificationStatus> dynamoDbTable;

    private ResumeJourneyNotificationStatusRepository resumeJourneyNotificationStatusRepository;
    @BeforeEach
    void setUp() {
        when(dynamoDbEnhancedClient.table(Mockito.anyString(), Mockito.<TableSchema<ResumeJourneyNotificationStatus>>any())).thenReturn(dynamoDbTable);
        resumeJourneyNotificationStatusRepository = new ResumeJourneyNotificationStatusImpl(dynamoDbEnhancedClient, "testTable");
    }

    @Test
    void updateRecord() {
        ResumeJourneyNotificationStatus resumeJourneyNotificationStatus = new ResumeJourneyNotificationStatus();
        resumeJourneyNotificationStatusRepository.updateRecord(resumeJourneyNotificationStatus);
        verify(dynamoDbTable, times(1)).updateItem(resumeJourneyNotificationStatus);
    }

    @Test
    void getRecord() {
        String leadTrackingNumber = "1234";
        resumeJourneyNotificationStatusRepository.getRecord(leadTrackingNumber);
        verify(dynamoDbTable, times(1)).getItem(Key.builder().partitionValue(leadTrackingNumber).build());
    }

    @Test
    void putRecord() {
        ResumeJourneyNotificationStatus resumeJourneyNotificationStatus = new ResumeJourneyNotificationStatus();
        resumeJourneyNotificationStatusRepository.putRecord(resumeJourneyNotificationStatus);
        verify(dynamoDbTable, times(1)).putItem(resumeJourneyNotificationStatus);
    }
}