package com.kotak.unified.dbservice.repository;

import com.kotak.unified.dbservice.config.DynamoAWSConfiguration;
import com.kotak.unified.dbservice.enums.PgnType;
import com.kotak.unified.dbservice.model.PGNStatus;
import com.kotak.unified.dbservice.model.pgn.AccountNumSchemeCodePgnMetadata;
import com.kotak.unified.dbservice.model.pgn.PgnDDBModel;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import software.amazon.awssdk.core.pagination.sync.SdkIterable;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbIndex;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;
import software.amazon.awssdk.enhanced.dynamodb.model.Page;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryEnhancedRequest;

import java.util.Arrays;
import java.util.List;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class PgnDDBRepositoryTest {


    @Mock
    private DynamoDbEnhancedClient dynamoDbEnhancedClient;

    @Mock
    private DynamoAWSConfiguration dynamoAWSConfiguration;

    @Mock
    private DynamoDbTable<PgnDDBModel> pgnDynamoDbTable;

    private PgnDDBRepository pgnDDBRepository;

    @BeforeEach
    void setUp() {
        when(dynamoAWSConfiguration.getPgnTableName()).thenReturn("testTable");
        when(dynamoDbEnhancedClient.table(Mockito.anyString(), Mockito.<TableSchema<PgnDDBModel>>any())).thenReturn(pgnDynamoDbTable);
        pgnDDBRepository = new PgnDDBRepository(dynamoDbEnhancedClient, dynamoAWSConfiguration);
    }

    @Test
    public void test_save_happyCase() {
        PgnDDBModel pgnDDBModel = getAssignedPgnDdbModel();
        when(pgnDynamoDbTable.updateItem(pgnDDBModel)).thenReturn(pgnDDBModel);
        PgnDDBModel actualResponse = pgnDDBRepository.save(pgnDDBModel);
        Assertions.assertEquals(pgnDDBModel, actualResponse);

        verify(pgnDynamoDbTable).updateItem(pgnDDBModel);
    }

    @Test
    public void test_findByLeadTrackingNumberAndPgnTypeAndPgnStatus_success_happyCase() {
        DynamoDbIndex<PgnDDBModel> statusIndex = mock();
        QueryEnhancedRequest queryEnhancedRequest = QueryEnhancedRequest.builder()
                .queryConditional(QueryConditional.keyEqualTo(
                        Key.builder().partitionValue("SAVINGS").sortValue("leadId#ASSIGNED").build()
                ))
                .build();
        SdkIterable<Page<PgnDDBModel>> mockSdkIterable = mock(SdkIterable.class);
        Page<PgnDDBModel> mockPage = mock(Page.class);
        PgnDDBModel expectedResponse = getAssignedPgnDdbModel();
        when(statusIndex.query(queryEnhancedRequest)).thenReturn(mockSdkIterable);
        when(mockSdkIterable.stream()).thenReturn(Arrays.stream(new Page[]{mockPage}));
        when(mockPage.items()).thenReturn(List.of(expectedResponse));
        when(pgnDynamoDbTable.index("typeAndStatusIndex")).thenReturn(statusIndex);

        PgnDDBModel actualResponse = pgnDDBRepository.findByLeadTrackingNumberAndPgnTypeAndPgnStatus(
                "leadId", "SAVINGS", "ASSIGNED");

        Assertions.assertEquals(expectedResponse, actualResponse);
        verify(pgnDynamoDbTable).index("typeAndStatusIndex");
        verify(mockPage, times(2)).items();
        verify(mockSdkIterable).stream();
        verify(statusIndex).query(queryEnhancedRequest);
    }

    @Test
    public void test_findByLeadTrackingNumberAndPgnTypeAndPgnStatus_emptyStream() {
        DynamoDbIndex<PgnDDBModel> statusIndex = mock();
        QueryEnhancedRequest queryEnhancedRequest = QueryEnhancedRequest.builder()
                .queryConditional(QueryConditional.keyEqualTo(
                        Key.builder().partitionValue("SAVINGS").sortValue("leadId#ASSIGNED").build()
                ))
                .build();
        SdkIterable<Page<PgnDDBModel>> mockSdkIterable = mock(SdkIterable.class);
        when(statusIndex.query(queryEnhancedRequest)).thenReturn(mockSdkIterable);
        when(mockSdkIterable.stream()).thenReturn(Arrays.stream(new Page[]{}));
        when(pgnDynamoDbTable.index("typeAndStatusIndex")).thenReturn(statusIndex);

        PgnDDBModel actualResponse = pgnDDBRepository.findByLeadTrackingNumberAndPgnTypeAndPgnStatus(
                "leadId", "SAVINGS", "ASSIGNED");

        Assertions.assertNull(actualResponse);
        verify(pgnDynamoDbTable).index("typeAndStatusIndex");
        verify(mockSdkIterable).stream();
        verify(statusIndex).query(queryEnhancedRequest);
    }

    @Test
    public void test_findByPgnTypeAndPgnStatus_success_happyCase() {
        DynamoDbIndex<PgnDDBModel> statusIndex = mock();
        QueryEnhancedRequest queryEnhancedRequest = QueryEnhancedRequest.builder()
                .queryConditional(QueryConditional.keyEqualTo(
                        Key.builder().partitionValue("SAVINGS").sortValue("#UN_ASSIGNED").build()
                ))
                .build();
        SdkIterable<Page<PgnDDBModel>> mockSdkIterable = mock(SdkIterable.class);
        Page<PgnDDBModel> mockPage = mock(Page.class);
        PgnDDBModel expectedResponse = getUnAssignedPgnDdbModel();
        when(statusIndex.query(queryEnhancedRequest)).thenReturn(mockSdkIterable);
        when(mockSdkIterable.stream()).thenReturn(Arrays.stream(new Page[]{mockPage}));
        when(mockPage.items()).thenReturn(List.of(expectedResponse));
        when(pgnDynamoDbTable.index("typeAndStatusIndex")).thenReturn(statusIndex);

        PgnDDBModel actualResponse = pgnDDBRepository.findByPgnTypeAndPgnStatus(
                "SAVINGS", "UN_ASSIGNED");

        Assertions.assertEquals(expectedResponse, actualResponse);
        verify(pgnDynamoDbTable).index("typeAndStatusIndex");
        verify(mockPage, times(2)).items();
        verify(mockSdkIterable).stream();
        verify(statusIndex).query(queryEnhancedRequest);
    }

    @Test
    public void test_findByPgnTypeAndPgnStatus_emptyStream() {
        DynamoDbIndex<PgnDDBModel> statusIndex = mock();
        QueryEnhancedRequest queryEnhancedRequest = QueryEnhancedRequest.builder()
                .queryConditional(QueryConditional.keyEqualTo(
                        Key.builder().partitionValue("SAVINGS").sortValue("#UN_ASSIGNED").build()
                ))
                .build();
        SdkIterable<Page<PgnDDBModel>> mockSdkIterable = mock(SdkIterable.class);
        when(statusIndex.query(queryEnhancedRequest)).thenReturn(mockSdkIterable);
        when(mockSdkIterable.stream()).thenReturn(Arrays.stream(new Page[]{}));
        when(pgnDynamoDbTable.index("typeAndStatusIndex")).thenReturn(statusIndex);

        PgnDDBModel actualResponse = pgnDDBRepository.findByPgnTypeAndPgnStatus(
                "SAVINGS", "UN_ASSIGNED");

        Assertions.assertNull(actualResponse);
        verify(pgnDynamoDbTable).index("typeAndStatusIndex");
        verify(mockSdkIterable).stream();
        verify(statusIndex).query(queryEnhancedRequest);
    }

    private PgnDDBModel getAssignedPgnDdbModel() {
        AccountNumSchemeCodePgnMetadata accountNumSchemeCodePgnMetadata = AccountNumSchemeCodePgnMetadata.builder()
                .accountNumber("**********").schemeCode("CSSAC").build();
        return PgnDDBModel.builder()
                .crn("********")
                .leadTrackingNumber("leadId")
                .pgnType(PgnType.SAVINGS)
                .status(PGNStatus.ASSIGNED)
                .pgnMetadata(accountNumSchemeCodePgnMetadata)
                .leadTrackingNumberAndStatus("leadId#ASSIGNED")
                .build();
    }

    private PgnDDBModel getUnAssignedPgnDdbModel() {
        AccountNumSchemeCodePgnMetadata accountNumSchemeCodePgnMetadata = AccountNumSchemeCodePgnMetadata.builder()
                .accountNumber("**********").schemeCode("CSSAC").build();
        return PgnDDBModel.builder()
                .crn("********")
                .pgnType(PgnType.SAVINGS)
                .status(PGNStatus.UN_ASSIGNED)
                .pgnMetadata(accountNumSchemeCodePgnMetadata)
                .leadTrackingNumberAndStatus("#UN_ASSIGNED")
                .build();
    }

}
