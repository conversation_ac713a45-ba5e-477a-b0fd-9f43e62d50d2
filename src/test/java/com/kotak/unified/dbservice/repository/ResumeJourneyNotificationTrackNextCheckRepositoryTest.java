package com.kotak.unified.dbservice.repository;

import com.kotak.unified.dbservice.repository.impl.ResumeJourneyNotificationTrackNextCheckRepositoryImpl;
import com.kotak.unified.orchestrator.common.dbmodels.resumenotification.ResumeJourneyNotificationTrackNextCheck;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;

import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ResumeJourneyNotificationTrackNextCheckRepositoryTest {

    @Mock
    private DynamoDbEnhancedClient dynamoDbEnhancedClient;

    @Mock
    private DynamoDbTable<ResumeJourneyNotificationTrackNextCheck> dynamoDbTable;

    private ResumeJourneyNotificationTrackNextCheckRepository resumeJourneyNotificationTrackNextCheckRepository;
    @BeforeEach
    void setUp() {
        when(dynamoDbEnhancedClient.table(Mockito.anyString(), Mockito.<TableSchema<ResumeJourneyNotificationTrackNextCheck>>any())).thenReturn(dynamoDbTable);
        resumeJourneyNotificationTrackNextCheckRepository = new ResumeJourneyNotificationTrackNextCheckRepositoryImpl(dynamoDbEnhancedClient, "testTable");
    }

    @Test
    void updateRecord() {
        ResumeJourneyNotificationTrackNextCheck resumeJourneyNotificationTrackNextCheck = new ResumeJourneyNotificationTrackNextCheck();
        resumeJourneyNotificationTrackNextCheckRepository.updateRecord(resumeJourneyNotificationTrackNextCheck);
        verify(dynamoDbTable, times(1)).updateItem(resumeJourneyNotificationTrackNextCheck);
    }

    @Test
    void getRecord() {
        String leadTrackingNumber = "1234";
        resumeJourneyNotificationTrackNextCheckRepository.getRecord(leadTrackingNumber);
        verify(dynamoDbTable, times(1)).getItem(Key.builder().partitionValue(leadTrackingNumber).build());
    }

    @Test
    void putRecord() {
        ResumeJourneyNotificationTrackNextCheck resumeJourneyNotificationTrackNextCheck = new ResumeJourneyNotificationTrackNextCheck();
        resumeJourneyNotificationTrackNextCheckRepository.putRecord(resumeJourneyNotificationTrackNextCheck);
        verify(dynamoDbTable, times(1)).putItem(resumeJourneyNotificationTrackNextCheck);
    }
}