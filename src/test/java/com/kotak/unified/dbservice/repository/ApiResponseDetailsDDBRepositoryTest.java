package com.kotak.unified.dbservice.repository;

import com.kotak.unified.dbservice.config.DynamoAWSConfiguration;
import com.kotak.unified.orchestrator.common.dbmodels.asyncprocessing.ApiResponseDetailsDDBModel;
import com.kotak.unified.orchestrator.library.sqsmodels.ApiName;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ApiResponseDetailsDDBRepositoryTest {

    @Mock
    private DynamoDbEnhancedClient dynamoDbEnhancedClient;

    @Mock
    private DynamoDbTable<ApiResponseDetailsDDBModel> dynamoDbTable;

    private ApiResponseDetailsDDBRepository apiResponseDetailsDDBRepository;

    ApiResponseDetailsDDBModel apiResponseDetailsDDBModel = new ApiResponseDetailsDDBModel();
    String leadTrackingNumber = "123";

    @BeforeEach
    public void setUp() {
        DynamoAWSConfiguration awsConfiguration = new DynamoAWSConfiguration();
        awsConfiguration.setApiResponseDetailsTableName("apiResponse");
        when(dynamoDbEnhancedClient.table(Mockito.anyString(), Mockito.<TableSchema<ApiResponseDetailsDDBModel>>any())).thenReturn(dynamoDbTable);
        apiResponseDetailsDDBRepository = new ApiResponseDetailsDDBRepository(dynamoDbEnhancedClient, awsConfiguration);
    }

    @Test
    public void  testGetByLeadTrackingNumberAndApiName() {
        when(dynamoDbTable.getItem(Key.builder().partitionValue(leadTrackingNumber).sortValue(ApiName.BUREAU_PAN_PROFILE.toString()).build())).thenReturn(apiResponseDetailsDDBModel);

        ApiResponseDetailsDDBModel result = apiResponseDetailsDDBRepository.getByLeadTrackingNumberAndApiName(leadTrackingNumber, ApiName.BUREAU_PAN_PROFILE);

        assertEquals(apiResponseDetailsDDBModel, result);

        verify(dynamoDbTable, times(1)).getItem(Key.builder().partitionValue(leadTrackingNumber).sortValue(ApiName.BUREAU_PAN_PROFILE.toString()).build());

    }

    @Test
    public void testSave() {
        apiResponseDetailsDDBRepository.save(apiResponseDetailsDDBModel);
        verify(dynamoDbTable, times(1)).updateItem(apiResponseDetailsDDBModel);
    }
}
