package com.kotak.unified.dbservice.repository;

import com.kotak.unified.dbservice.config.DynamoAWSConfiguration;
import com.kotak.unified.orchestrator.common.dbmodels.TransactionDDBModel;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class TransactionDDBRepositoryTest {

    @Mock
    private DynamoDbEnhancedClient dynamoDbEnhancedClient;

    @Mock
    private DynamoDbTable<TransactionDDBModel> dynamoDbTable;

    private TransactionDDBRepository transactionDDBRepository;

    TransactionDDBModel transactionDDBModel = new TransactionDDBModel();
    String txnId = "abc";
    @BeforeEach
    public void setUp() {
        DynamoAWSConfiguration awsConfiguration = new DynamoAWSConfiguration();
        awsConfiguration.setTransactionTableName("transaction");
        when(dynamoDbEnhancedClient.table(Mockito.anyString(), Mockito.<TableSchema<TransactionDDBModel>>any())).thenReturn(dynamoDbTable);
        transactionDDBRepository = new TransactionDDBRepository(dynamoDbEnhancedClient, awsConfiguration);
    }

    @Test
    public void  testFindTransactionByTxnId() {
        when(dynamoDbTable.getItem(Key.builder().partitionValue(txnId).build())).thenReturn(transactionDDBModel);

        TransactionDDBModel result = transactionDDBRepository.findTransactionByTxnId(txnId);

        assertEquals(transactionDDBModel, result);

        verify(dynamoDbTable, times(1)).getItem(Key.builder().partitionValue(txnId).build());

    }
}

