package com.kotak.unified.dbservice.repository;

import com.kotak.unified.orchestrator.common.dbmodels.asyncprocessing.AsyncProcessDBModel;
import com.kotak.unified.orchestrator.common.dbmodels.asyncprocessing.AsyncProcessDDBModel;
import com.kotak.unified.orchestrator.common.dbmodels.asyncprocessing.AsyncProcessId;
import com.kotak.unified.orchestrator.library.sqsmodels.AsyncProcessType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class AsyncProcessFacadeTest {
    @Mock
    private AsyncProcessDDBRepository asyncProcessDDBRepository;
    private AsyncProcessFacade asyncProcessFacade;
    String leadTrackingNumber = "123";

    AsyncProcessType processType = AsyncProcessType.ACCOUNT_PROPAGATION;

    AsyncProcessId asyncProcessId = AsyncProcessId.builder().leadTrackingNumber(leadTrackingNumber).processType(processType).build();

    AsyncProcessDBModel asyncProcessDBModel = getAsyncProcessDBModel();

    AsyncProcessDDBModel asyncProcessDDBModel = getAsyncProcessDDBModel();

    @BeforeEach
    public void setUp() {
        asyncProcessFacade = new AsyncProcessFacade(asyncProcessDDBRepository);
    }

    @Test
    public void  testGetByProcessIdThroughDDB_success() {
        when(asyncProcessDDBRepository.getByLeadTrackingNumberAndProcessType(
                asyncProcessId.getLeadTrackingNumber(), asyncProcessId.getProcessType()))
                .thenReturn(asyncProcessDDBModel);

        AsyncProcessDBModel result = asyncProcessFacade.getByProcessId(asyncProcessId);

        assertEquals(asyncProcessDBModel, result);

        verify(asyncProcessDDBRepository, times(1)).getByLeadTrackingNumberAndProcessType(
                asyncProcessId.getLeadTrackingNumber(), asyncProcessId.getProcessType());

    }

    @Test
    public void  testGetByProcessIdThroughDDB_null_success() {
        when(asyncProcessDDBRepository.getByLeadTrackingNumberAndProcessType(
                asyncProcessId.getLeadTrackingNumber(), asyncProcessId.getProcessType()))
                .thenReturn(null);

        AsyncProcessDBModel result = asyncProcessFacade.getByProcessId(asyncProcessId);

        assertNull(result);

        verify(asyncProcessDDBRepository, times(1)).getByLeadTrackingNumberAndProcessType(
                asyncProcessId.getLeadTrackingNumber(), asyncProcessId.getProcessType());

    }

    @Test
    public void  testGetByProcessIdThroughDDB_failed_throwException() {
        when(asyncProcessDDBRepository.getByLeadTrackingNumberAndProcessType(
                asyncProcessId.getLeadTrackingNumber(), asyncProcessId.getProcessType()))
                .thenThrow(new RuntimeException());

        assertThrows(RuntimeException.class,
                () -> asyncProcessFacade.getByProcessId(asyncProcessId));

        verify(asyncProcessDDBRepository, times(1)).getByLeadTrackingNumberAndProcessType(
                asyncProcessId.getLeadTrackingNumber(), asyncProcessId.getProcessType());

    }

    private AsyncProcessDBModel getAsyncProcessDBModel() {
        return AsyncProcessDBModel.builder()
                .processId(asyncProcessId)
                .build();
    }

    private AsyncProcessDDBModel getAsyncProcessDDBModel() {
        return AsyncProcessDDBModel.builder()
                .leadTrackingNumber(leadTrackingNumber)
                .processType(processType)
                .build();
    }
}