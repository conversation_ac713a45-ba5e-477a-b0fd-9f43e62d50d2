package com.kotak.unified.dbservice.repository;

import static com.kotak.unified.dbservice.utils.Constants.SOFT_BLOCK;
import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;

import com.kotak.unified.dbservice.config.DynamoAWSConfiguration;
import com.kotak.unified.dbservice.model.PreferredAccountNumberDDBModel;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;
import software.amazon.awssdk.enhanced.dynamodb.Key;

@ExtendWith(MockitoExtension.class)
public class PreferredAccountNumberDDBRepositoryTest {

    @Mock
    private DynamoDbEnhancedClient dynamoDbEnhancedClient;

    @Mock
    private DynamoAWSConfiguration dynamoAWSConfiguration;

    @Mock
    private DynamoDbTable<PreferredAccountNumberDDBModel> dynamoDbTable;

    private PreferredAccountNumberDDBRepository preferredAccountNumberDDBRepository;

    private PreferredAccountNumberDDBModel preferredAccountNumberDDBModel;

    @BeforeEach
    public void setUp() {
        preferredAccountNumberDDBModel = PreferredAccountNumberDDBModel.builder()
                .preferredAccountNumber("**********")
                .leadTrackingNumber("123")
                .blockStatus(SOFT_BLOCK)
                .build();

        when(dynamoAWSConfiguration.getPreferredAccountNumberTableName()).thenReturn("PreferredAccountNumberTable");
        when(dynamoDbEnhancedClient.table("PreferredAccountNumberTable",
                                          TableSchema.fromBean(PreferredAccountNumberDDBModel.class))).thenReturn(dynamoDbTable);

        preferredAccountNumberDDBRepository = new PreferredAccountNumberDDBRepository(dynamoDbEnhancedClient, dynamoAWSConfiguration);
    }

    @Test
    public void testSave() {
        // Arrange
        when(dynamoDbTable.updateItem(preferredAccountNumberDDBModel)).thenReturn(preferredAccountNumberDDBModel);

        // Act
        PreferredAccountNumberDDBModel returnedDDBModel = preferredAccountNumberDDBRepository.save(preferredAccountNumberDDBModel);

        // Assert
        assertNotNull(returnedDDBModel);
        assertEquals(preferredAccountNumberDDBModel.getPreferredAccountNumber(), returnedDDBModel.getPreferredAccountNumber());
        assertEquals(preferredAccountNumberDDBModel.getBlockStatus(), returnedDDBModel.getBlockStatus());
    }

    @Test
    public void testGetByPreferredAccountNumber() {
        // Arrange
        when(dynamoDbTable.getItem(any(Key.class))).thenReturn(preferredAccountNumberDDBModel);

        // Act
        PreferredAccountNumberDDBModel returnedDDBModel = preferredAccountNumberDDBRepository.getByPreferredAccountNumber("**********");

        // Assert
        assertNotNull(returnedDDBModel);
        assertEquals(preferredAccountNumberDDBModel.getPreferredAccountNumber(), returnedDDBModel.getPreferredAccountNumber());
        assertEquals(preferredAccountNumberDDBModel.getBlockStatus(), returnedDDBModel.getBlockStatus());
    }
}