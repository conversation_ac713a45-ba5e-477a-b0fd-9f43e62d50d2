package com.kotak.unified.dbservice.repository;

import com.kotak.unified.dbservice.TestUtils;
import com.kotak.unified.dbservice.transformer.DDBModelTransformer;
import com.kotak.unified.orchestrator.common.dbmodels.Address;
import com.kotak.unified.orchestrator.common.dbmodels.ApplicationData;
import com.kotak.unified.orchestrator.common.dbmodels.ApplicationDataDDBModel;
import com.kotak.unified.orchestrator.common.dbmodels.DormantAccountActivationJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.ExecutionData;
import com.kotak.unified.orchestrator.common.dbmodels.JourneyStatus;
import com.kotak.unified.orchestrator.common.dbmodels.Status;
import com.kotak.unified.orchestrator.common.dbmodels.UserJourneyStatusDDBModel;
import com.kotak.unified.orchestrator.common.dbmodels.UserStatus;
import com.kotak.unified.orchestrator.common.dbmodels.UserStepStatus;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.kotak.unified.dbservice.TestUtils.SAVINGS_ACCOUNT_GRAPH_NAME;
import static com.kotak.unified.dbservice.TestUtils.TEST_LAST_UPDATED_TIME;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class UserStatusFacadeTest {

    @Mock
    private UserJourneyStatusDDBRepository userJourneyStatusDDBRepository;

    @Mock
    private ApplicationDataDDBRepository applicationDataDDBRepository;

    @Mock
    private DDBModelTransformer ddbModelTransformer;

    @InjectMocks
    private UserStatusFacade userStatusFacade;

    @Test
    void findByLeadTrackingNumber_ddbReadEnabled_happyCase() {
        UserStatus mockUserStatus = UserStatus.builder()
                .leadTrackingNumber(TestUtils.LEAD_ID)
                .journeyType(SAVINGS_ACCOUNT_GRAPH_NAME)
                .build();
        UserJourneyStatusDDBModel mockUserJourneyStatus = getMockUserJourneyStatusDDBModel();
        ApplicationDataDDBModel mockApplicationData = getMockApplicationDataDDBModel();
        when(userJourneyStatusDDBRepository.findByLeadTrackingNumber(TestUtils.LEAD_ID)).thenReturn(mockUserJourneyStatus);
        when(applicationDataDDBRepository.findByApplicationId(TestUtils.LEAD_ID)).thenReturn(mockApplicationData);
        when(ddbModelTransformer.getUserStatusFromUserJourneyStatusAndApplicationData(mockUserJourneyStatus, mockApplicationData)).thenReturn(mockUserStatus);
        Assertions.assertEquals(mockUserStatus, userStatusFacade.findByLeadTrackingNumber(TestUtils.LEAD_ID));
        verify(userJourneyStatusDDBRepository, times(1)).findByLeadTrackingNumber(anyString());
        verify(applicationDataDDBRepository, times(1)).findByApplicationId(anyString());
        verify(ddbModelTransformer, times(1)).getUserStatusFromUserJourneyStatusAndApplicationData(any(), any());
    }

    @Test
    void findByLeadTrackingNumber_ddbReadEnabled_applicationData_null() {
        UserStatus mockUserStatus = UserStatus.builder()
                .leadTrackingNumber(TestUtils.LEAD_ID)
                .journeyType(SAVINGS_ACCOUNT_GRAPH_NAME)
                .build();
        UserJourneyStatusDDBModel mockUserJourneyStatus = getMockUserJourneyStatusDDBModel();
        when(userJourneyStatusDDBRepository.findByLeadTrackingNumber(TestUtils.LEAD_ID)).thenReturn(mockUserJourneyStatus);
        when(applicationDataDDBRepository.findByApplicationId(TestUtils.LEAD_ID)).thenReturn(null);
        when(ddbModelTransformer.getUserStatusFromUserJourneyStatusAndApplicationData(mockUserJourneyStatus, null)).thenReturn(mockUserStatus);
        Assertions.assertEquals(mockUserStatus, userStatusFacade.findByLeadTrackingNumber(TestUtils.LEAD_ID));
        verify(userJourneyStatusDDBRepository, times(1)).findByLeadTrackingNumber(anyString());
        verify(applicationDataDDBRepository, times(1)).findByApplicationId(anyString());
        verify(ddbModelTransformer, times(1)).getUserStatusFromUserJourneyStatusAndApplicationData(any(), any());
    }

    @Test
    void findByLeadTrackingNumber_ddbReadEnabled_userJourneyStatus_null() {
        when(userJourneyStatusDDBRepository.findByLeadTrackingNumber(TestUtils.LEAD_ID)).thenReturn(null);
        Assertions.assertNull(userStatusFacade.findByLeadTrackingNumber(TestUtils.LEAD_ID));
        verify(userJourneyStatusDDBRepository, times(1)).findByLeadTrackingNumber(anyString());
        verify(applicationDataDDBRepository, never()).findByApplicationId(anyString());
    }

    @Test
    void findByLeadTrackingNumber_exception_throwException() {
        when(userJourneyStatusDDBRepository.findByLeadTrackingNumber(TestUtils.LEAD_ID)).thenThrow(new RuntimeException("Error"));
        Assertions.assertThrows(RuntimeException.class, () -> userStatusFacade.findByLeadTrackingNumber(TestUtils.LEAD_ID));
        verify(userJourneyStatusDDBRepository, times(1)).findByLeadTrackingNumber(anyString());
    }

    @Test
    void findByLeadTrackingNumber_null_leadTrackingNumber() {
        Assertions.assertThrows(NullPointerException.class, () -> userStatusFacade.findByLeadTrackingNumber(null));
    }

    @Test
    void findApplicationDataByLeadTrackingNumber_happyCase() {
        ApplicationData mockApplicationData = ApplicationData.builder().applicationTrackingId(TestUtils.LEAD_ID).build();
        ApplicationDataDDBModel mockApplicationDataDDBModel = getMockApplicationDataDDBModel();
        when(applicationDataDDBRepository.findByApplicationId(TestUtils.LEAD_ID)).thenReturn(mockApplicationDataDDBModel);
        when(ddbModelTransformer.getApplicationDataFromApplicationDataDDBModel(mockApplicationDataDDBModel)).thenReturn(mockApplicationData);
        Assertions.assertEquals(mockApplicationData, userStatusFacade.findApplicationDataByLeadTrackingNumber(TestUtils.LEAD_ID));
        verify(applicationDataDDBRepository, times(1)).findByApplicationId(anyString());
        verify(ddbModelTransformer, times(1)).getApplicationDataFromApplicationDataDDBModel(any());
    }

    @Test
    void findApplicationDataByLeadTrackingNumber_null() {
        when(applicationDataDDBRepository.findByApplicationId(TestUtils.LEAD_ID)).thenReturn(null);
        Assertions.assertThrows(RuntimeException.class, () -> userStatusFacade.findApplicationDataByLeadTrackingNumber(TestUtils.LEAD_ID));
        verify(applicationDataDDBRepository, times(1)).findByApplicationId(anyString());
    }


    @Test
    void findApplicationDataByLeadTrackingNumber_exception() {
        when(applicationDataDDBRepository.findByApplicationId(TestUtils.LEAD_ID)).thenThrow(new RuntimeException());
        Assertions.assertThrows(RuntimeException.class, () -> userStatusFacade.findApplicationDataByLeadTrackingNumber(TestUtils.LEAD_ID));
        verify(applicationDataDDBRepository, times(1)).findByApplicationId(anyString());
    }

    @Test
    void save_ddbWriteEnabled() {
        UserStatus mockUserStatus = UserStatus.builder()
                .leadTrackingNumber(TestUtils.LEAD_ID)
                .crn(TestUtils.TEST_CRN)
                .journeyType(SAVINGS_ACCOUNT_GRAPH_NAME)
                .build();
        UserJourneyStatusDDBModel mockUserJourneyStatus = getMockUserJourneyStatusDDBModel();
        UserJourneyStatusDDBModel updatedMockUserJourneyStatus = getMockUserJourneyStatusDDBModel();
        updatedMockUserJourneyStatus.setVersion(2);

        ApplicationDataDDBModel mockApplicationData = getMockApplicationDataDDBModel();
        ApplicationDataDDBModel updatedMockApplicationData = getMockApplicationDataDDBModel();
        updatedMockApplicationData.setVersion(2);

        when(ddbModelTransformer.getUserJourneyStatusDDBModelFromUserStatus(mockUserStatus)).thenReturn(mockUserJourneyStatus);
        when(ddbModelTransformer.getApplicationDataDDBModelFromUserStatus(mockUserStatus)).thenReturn(mockApplicationData);

        when(userJourneyStatusDDBRepository.save(mockUserJourneyStatus)).thenReturn(updatedMockUserJourneyStatus);
        when(applicationDataDDBRepository.save(mockApplicationData)).thenReturn(updatedMockApplicationData);

        doNothing().when(ddbModelTransformer).updateUserStatus(mockUserStatus, updatedMockUserJourneyStatus, updatedMockApplicationData);

        Assertions.assertEquals(mockUserStatus, userStatusFacade.save(mockUserStatus));
        verify(ddbModelTransformer, times(1)).getUserJourneyStatusDDBModelFromUserStatus(any());
        verify(ddbModelTransformer, times(1)).getApplicationDataDDBModelFromUserStatus(any());
        verify(ddbModelTransformer, times(1)).updateUserStatus(any(), any(), any());
        verify(userJourneyStatusDDBRepository, times(1)).save(any());
        verify(applicationDataDDBRepository, times(1)).save(any());
    }

    @Test
    void save_applicationDataDDBRepository_throws_exception() {
        UserStatus mockUserStatus = UserStatus.builder()
                .leadTrackingNumber(TestUtils.LEAD_ID)
                .crn(TestUtils.TEST_CRN)
                .journeyType(SAVINGS_ACCOUNT_GRAPH_NAME)
                .build();
        UserJourneyStatusDDBModel mockUserJourneyStatus = getMockUserJourneyStatusDDBModel();
        UserJourneyStatusDDBModel updatedMockUserJourneyStatus = getMockUserJourneyStatusDDBModel();
        updatedMockUserJourneyStatus.setVersion(2);

        ApplicationDataDDBModel mockApplicationData = getMockApplicationDataDDBModel();
        ApplicationDataDDBModel updatedMockApplicationData = getMockApplicationDataDDBModel();
        updatedMockApplicationData.setVersion(2);

        when(ddbModelTransformer.getUserJourneyStatusDDBModelFromUserStatus(mockUserStatus)).thenReturn(mockUserJourneyStatus);
        when(ddbModelTransformer.getApplicationDataDDBModelFromUserStatus(mockUserStatus)).thenReturn(mockApplicationData);
        when(applicationDataDDBRepository.save(mockApplicationData)).thenThrow(new RuntimeException("Error"));

        Assertions.assertThrows(RuntimeException.class, ()-> userStatusFacade.save(mockUserStatus));

        verify(ddbModelTransformer, times(1)).getUserJourneyStatusDDBModelFromUserStatus(any());
        verify(ddbModelTransformer, times(1)).getApplicationDataDDBModelFromUserStatus(any());
        verify(ddbModelTransformer, times(0)).updateUserStatus(any(), any(), any());
        verify(userJourneyStatusDDBRepository, never()).save(any());
        verify(applicationDataDDBRepository, times(1)).save(any());
    }

    @Test
    void save_null_userStatus() {
        Assertions.assertThrows(NullPointerException.class, () -> userStatusFacade.save(null));
    }

    private UserJourneyStatusDDBModel getMockUserJourneyStatusDDBModel() {

        UserStepStatus userStepStatus = UserStepStatus.builder()
                .version("1.0")
                .status(Status.INITIATED)
                .isAsyncUIStep(false)
                .lastUpdatedTime(Instant.now())
                .stepsStatus(List.of())
                .build();
        Map<String, UserStepStatus> graphStatus = new HashMap<>();
        graphStatus.put(TestUtils.SAVINGS_ACCOUNT_GRAPH_NAME, userStepStatus);

        return UserJourneyStatusDDBModel.builder()
                .leadTrackingNumber(TestUtils.LEAD_ID)
                .phoneNumber(TestUtils.PHONE_NUMBER)
                .crn(TestUtils.TEST_CRN)
                .journeyType(TestUtils.SAVINGS_ACCOUNT_GRAPH_NAME)
                .createdAt(TEST_LAST_UPDATED_TIME)
                .status(graphStatus)
                .journeyStatus(JourneyStatus.ACCOUNT_OPENED)
                .build();
    }

    private ApplicationDataDDBModel getMockApplicationDataDDBModel() {
        return ApplicationDataDDBModel.builder()
                .applicationTrackingId(TestUtils.LEAD_ID)
                .journeyMetadata(DormantAccountActivationJourneyMetadata.builder()
                        .communicationAddress(Address.builder()
                                .state("Delhi")
                                .city("New Delhi")
                                .pincode("112233")
                                .build())
                        .build())
                .executionData(ExecutionData.builder()
                        .ncifLastVerifiedTime(TEST_LAST_UPDATED_TIME)
                        .isMobileOtpAlreadySent(true)
                        .otpResendAttemptsLeft(2)
                        .build())
                .crn(TestUtils.TEST_CRN)
                .userIp(TestUtils.USER_IP)
                .applicationType(TestUtils.SAVINGS_ACCOUNT_GRAPH_NAME)
                .version(1)
                .build();
    }
}