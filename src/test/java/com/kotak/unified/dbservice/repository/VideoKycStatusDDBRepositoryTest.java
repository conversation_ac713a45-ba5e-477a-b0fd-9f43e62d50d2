package com.kotak.unified.dbservice.repository;

import com.kotak.unified.dbservice.config.DynamoAWSConfiguration;
import com.kotak.unified.orchestrator.common.dbmodels.vkycStatusModel.VideoKycDetailsDDBModel;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import software.amazon.awssdk.core.pagination.sync.SdkIterable;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbIndex;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;
import software.amazon.awssdk.enhanced.dynamodb.model.Page;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryEnhancedRequest;

import java.time.Instant;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class VideoKycStatusDDBRepositoryTest {
    @Mock
    private DynamoDbEnhancedClient dynamoDbEnhancedClient;

    @Mock
    private DynamoDbTable<VideoKycDetailsDDBModel> dynamoDbTable;

    @Mock
    private DynamoDbIndex<VideoKycDetailsDDBModel> leadTrackingNumberIndex;

    private VideoKycStatusDDBRepository videoKycStatusDDBRepository;

    Instant createdAt = Instant.now();

    String leadTrackingNumber = "123";

    String status = "PENDING";

    String trackingId = "ABC";

    VideoKycDetailsDDBModel videoKycDetailsDDBModel = getVideoKycDetailsDDBModel();

    @BeforeEach
    public void setUp() {
        DynamoAWSConfiguration awsConfiguration = new DynamoAWSConfiguration();
        awsConfiguration.setVideoKycStatusTableName("videoKycDetails");
        when(dynamoDbEnhancedClient.table(Mockito.anyString(), Mockito.<TableSchema<VideoKycDetailsDDBModel>>any())).thenReturn(dynamoDbTable);
        videoKycStatusDDBRepository = new VideoKycStatusDDBRepository(dynamoDbEnhancedClient, awsConfiguration);
    }

    @Test
    public void  testSave() {
        when(dynamoDbTable.updateItem(videoKycDetailsDDBModel)).thenReturn(videoKycDetailsDDBModel);

        videoKycStatusDDBRepository.save(videoKycDetailsDDBModel);

        verify(dynamoDbTable, times(1)).updateItem(videoKycDetailsDDBModel);

    }

    @Test
    public void  testFindByTrackingId() {
        when(dynamoDbTable.getItem(Key.builder().partitionValue(trackingId).build())).thenReturn(videoKycDetailsDDBModel);

        VideoKycDetailsDDBModel result = videoKycStatusDDBRepository.findByTrackingId(trackingId);

        assertEquals(videoKycDetailsDDBModel, result);

        verify(dynamoDbTable, times(1)).getItem(Key.builder().partitionValue(trackingId).build());

    }

    @Test
    public void  test_findVideoKycDetailsDDBModelsByLeadTrackingNumber() {
        QueryEnhancedRequest request = QueryEnhancedRequest.builder()
                .queryConditional(QueryConditional.keyEqualTo(Key.builder().partitionValue(leadTrackingNumber)
                        .build())).build();

        when(dynamoDbTable.index("LeadTrackingNumberIndex")).thenReturn(leadTrackingNumberIndex);
        SdkIterable<Page<VideoKycDetailsDDBModel>> mockIterable = mock(SdkIterable.class);
        Page<VideoKycDetailsDDBModel> mockPage = mock(Page.class);
        when(mockPage.items()).thenReturn(Arrays.asList(videoKycDetailsDDBModel));
        when(mockIterable.stream()).thenReturn(Arrays.stream(new Page[]{mockPage}));
        when(leadTrackingNumberIndex.query(request)).thenReturn(mockIterable);

        List<VideoKycDetailsDDBModel> result = videoKycStatusDDBRepository.findVideoKycDetailsDDBModelsByLeadTrackingNumber(leadTrackingNumber);
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(videoKycDetailsDDBModel, result.get(0));

        verify(dynamoDbTable, times(1)).index(anyString());
        verify(mockPage, times(1)).items();
        verify(mockIterable, times(1)).stream();
        verify(leadTrackingNumberIndex, times(1)).query(any(QueryEnhancedRequest.class));
    }

    @Test
    public void  test_findVideoKycDetailsDDBModelsByLeadTrackingNumber_emptyStream() {
        QueryEnhancedRequest request = QueryEnhancedRequest.builder()
                .queryConditional(QueryConditional.keyEqualTo(Key.builder().partitionValue(leadTrackingNumber)
                        .build())).build();

        when(dynamoDbTable.index("LeadTrackingNumberIndex")).thenReturn(leadTrackingNumberIndex);
        SdkIterable<Page<VideoKycDetailsDDBModel>> mockIterable = mock(SdkIterable.class);
        when(mockIterable.stream()).thenReturn(Arrays.stream(new Page[]{}));
        when(leadTrackingNumberIndex.query(request)).thenReturn(mockIterable);

        List<VideoKycDetailsDDBModel> result = videoKycStatusDDBRepository.findVideoKycDetailsDDBModelsByLeadTrackingNumber(leadTrackingNumber);
        assertNotNull(result);
        assertEquals(0, result.size());

        verify(dynamoDbTable, times(1)).index(anyString());
        verify(mockIterable, times(1)).stream();
        verify(leadTrackingNumberIndex, times(1)).query(any(QueryEnhancedRequest.class));
    }

    @Test
    void test_findTopByLeadTrackingNumberAndLatestStatusOrderByCreatedAt_happyCase() {
        String latestStatusPrefix = status + "#";

        QueryEnhancedRequest request = QueryEnhancedRequest.builder()
                .queryConditional(QueryConditional.sortBeginsWith(Key.builder().partitionValue(leadTrackingNumber)
                        .sortValue(latestStatusPrefix)
                        .build()))
                .limit(1)
                .scanIndexForward(false)
                .build();
        when(dynamoDbTable.index("LeadTrackingNumberIndex")).thenReturn(leadTrackingNumberIndex);
        SdkIterable<Page<VideoKycDetailsDDBModel>> mockIterable = mock(SdkIterable.class);
        Page<VideoKycDetailsDDBModel> mockPage = mock(Page.class);
        when(mockPage.items()).thenReturn(Arrays.asList(videoKycDetailsDDBModel));
        when(mockIterable.stream()).thenReturn(Arrays.stream(new Page[]{mockPage}));
        when(leadTrackingNumberIndex.query(request)).thenReturn(mockIterable);

        VideoKycDetailsDDBModel result = videoKycStatusDDBRepository.findTopByLeadTrackingNumberAndLatestStatusOrderByCreatedAt(
                leadTrackingNumber, status);

        assertEquals(videoKycDetailsDDBModel, result);

        verify(dynamoDbTable, times(1)).index(anyString());
        verify(mockPage, times(2)).items();
        verify(mockIterable, times(1)).stream();
        verify(leadTrackingNumberIndex, times(1)).query(any(QueryEnhancedRequest.class));
    }

    @Test
    void test_findTopByLeadTrackingNumberAndLatestStatusOrderByCreatedAt_emptyStream() {
        String latestStatusPrefix = status + "#";

        QueryEnhancedRequest request = QueryEnhancedRequest.builder()
                .queryConditional(QueryConditional.sortBeginsWith(Key.builder().partitionValue(leadTrackingNumber)
                        .sortValue(latestStatusPrefix)
                        .build()))
                .limit(1)
                .scanIndexForward(false)
                .build();
        when(dynamoDbTable.index("LeadTrackingNumberIndex")).thenReturn(leadTrackingNumberIndex);
        SdkIterable<Page<VideoKycDetailsDDBModel>> mockIterable = mock(SdkIterable.class);
        when(mockIterable.stream()).thenReturn(Arrays.stream(new Page[]{}));
        when(leadTrackingNumberIndex.query(request)).thenReturn(mockIterable);

        VideoKycDetailsDDBModel result = videoKycStatusDDBRepository.findTopByLeadTrackingNumberAndLatestStatusOrderByCreatedAt(
                leadTrackingNumber, status);

        assertNull(result);

        verify(dynamoDbTable, times(1)).index(anyString());
        verify(mockIterable, times(1)).stream();
        verify(leadTrackingNumberIndex, times(1)).query(any(QueryEnhancedRequest.class));
    }

    private VideoKycDetailsDDBModel getVideoKycDetailsDDBModel() {
        return VideoKycDetailsDDBModel.builder()
                .trackingId(trackingId)
                .leadTrackingNumber(leadTrackingNumber)
                .latestStatus(status)
                .createdAt(createdAt)
                .build();
    }
}