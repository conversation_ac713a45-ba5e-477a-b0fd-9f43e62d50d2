package com.kotak.unified.dbservice.repository;

import com.kotak.unified.dbservice.config.DynamoAWSConfiguration;
import com.kotak.unified.orchestrator.common.dbmodels.asyncprocessing.AsyncProcessDDBModel;
import com.kotak.unified.orchestrator.library.sqsmodels.AsyncProcessType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class AsyncProcessDDBRepositoryTest {

    @Mock
    private DynamoDbEnhancedClient dynamoDbEnhancedClient;

    @Mock
    private DynamoDbTable<AsyncProcessDDBModel> dynamoDbTable;

    private AsyncProcessDDBRepository asyncProcessDDBRepository;

    AsyncProcessDDBModel asyncProcessDDBModel = new AsyncProcessDDBModel();
    String leadTrackingNumber = "123";

    AsyncProcessType asyncProcessType = AsyncProcessType.ACCOUNT_PROPAGATION;
    @BeforeEach
    public void setUp() {
        DynamoAWSConfiguration awsConfiguration = new DynamoAWSConfiguration();
        awsConfiguration.setAsyncProcessTableName("asyncProcess");
        when(dynamoDbEnhancedClient.table(Mockito.anyString(), Mockito.<TableSchema<AsyncProcessDDBModel>>any())).thenReturn(dynamoDbTable);
        asyncProcessDDBRepository = new AsyncProcessDDBRepository(dynamoDbEnhancedClient, awsConfiguration);
    }

    @Test
    public void  testGetByLeadTrackingNumberAndProcessType() {
        when(dynamoDbTable.getItem(Key.builder().partitionValue(leadTrackingNumber).sortValue(asyncProcessType.name()).build())).thenReturn(asyncProcessDDBModel);

        AsyncProcessDDBModel result = asyncProcessDDBRepository.getByLeadTrackingNumberAndProcessType(leadTrackingNumber, asyncProcessType);

        assertEquals(asyncProcessDDBModel, result);

        verify(dynamoDbTable, times(1)).getItem(Key.builder().partitionValue(leadTrackingNumber).sortValue(asyncProcessType.name()).build());

    }
}
