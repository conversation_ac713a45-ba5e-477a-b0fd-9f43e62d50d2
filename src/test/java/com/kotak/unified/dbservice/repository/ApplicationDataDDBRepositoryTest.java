package com.kotak.unified.dbservice.repository;

import com.kotak.unified.dbservice.TestUtils;
import com.kotak.unified.dbservice.config.DynamoAWSConfiguration;
import com.kotak.unified.orchestrator.common.dbmodels.Address;
import com.kotak.unified.orchestrator.common.dbmodels.ApplicationDataDDBModel;
import com.kotak.unified.orchestrator.common.dbmodels.ExecutionData;
import com.kotak.unified.orchestrator.common.dbmodels.SavingsAccountJourneyMetadata;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;

import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ApplicationDataDDBRepositoryTest {
    @Mock
    private DynamoDbEnhancedClient dynamoDbEnhancedClient;

    @Mock
    private DynamoDbTable<ApplicationDataDDBModel> dynamoDbTable;

    @Mock
    private DynamoAWSConfiguration dynamoAWSConfiguration;

    private ApplicationDataDDBRepository repository;

    @BeforeEach
    void setUp() {
        when(dynamoAWSConfiguration.getApplicationDataTableName()).thenReturn("testTable");
        when(dynamoDbEnhancedClient.table(Mockito.anyString(), Mockito.<TableSchema<ApplicationDataDDBModel>>any())).thenReturn(dynamoDbTable);
        repository = new ApplicationDataDDBRepository(dynamoDbEnhancedClient, dynamoAWSConfiguration);
    }

    @Test
    void findByApplicationId_happyCase() {
        Key mockKey = Key.builder()
                .partitionValue(TestUtils.LEAD_ID)
                .build();
        ApplicationDataDDBModel applicationDataDDBModel = getMockApplicationDataDDBModel();
        when(dynamoDbTable.getItem(mockKey)).thenReturn(applicationDataDDBModel);
        Assertions.assertEquals(applicationDataDDBModel, repository.findByApplicationId(TestUtils.LEAD_ID));
        verify(dynamoDbTable, times(1)).getItem(mockKey);
    }

    @Test
    void findByApplicationId_exception() {
        Key mockKey = Key.builder()
                .partitionValue(TestUtils.LEAD_ID)
                .build();
        when(dynamoDbTable.getItem(mockKey)).thenThrow(new RuntimeException("Error"));
        Assertions.assertThrows(RuntimeException.class, () -> repository.findByApplicationId(TestUtils.LEAD_ID));
        verify(dynamoDbTable, times(1)).getItem(mockKey);
    }

    @Test
    void save_happyCase() {
        ApplicationDataDDBModel applicationDataDDBModel = getMockApplicationDataDDBModel();
        when(dynamoDbTable.updateItem(applicationDataDDBModel)).thenReturn(applicationDataDDBModel);
        Assertions.assertEquals(applicationDataDDBModel, repository.save(applicationDataDDBModel));
        verify(dynamoDbTable, times(1)).updateItem(applicationDataDDBModel);
    }

    @Test
    void save_exception() {
        ApplicationDataDDBModel applicationDataDDBModel = getMockApplicationDataDDBModel();
        when(dynamoDbTable.updateItem(applicationDataDDBModel)).thenThrow(new RuntimeException("Error"));
        Assertions.assertThrows(RuntimeException.class, () -> repository.save(applicationDataDDBModel));
        verify(dynamoDbTable, times(1)).updateItem(applicationDataDDBModel);
    }

    private ApplicationDataDDBModel getMockApplicationDataDDBModel() {
        return ApplicationDataDDBModel.builder()
                .applicationTrackingId(TestUtils.LEAD_ID)
                .journeyMetadata(SavingsAccountJourneyMetadata.builder()
                        .communicationAddress(Address.builder()
                                .state("Delhi")
                                .city("New Delhi")
                                .pincode("112233")
                                .build())
                        .build())
                .executionData(ExecutionData.builder()
                        .ncifLastVerifiedTime(TestUtils.TEST_LAST_UPDATED_TIME)
                        .isMobileOtpAlreadySent(true)
                        .otpResendAttemptsLeft(2)
                        .build())
                .crn(TestUtils.TEST_CRN)
                .userIp(TestUtils.USER_IP)
                .applicationType(TestUtils.SAVINGS_ACCOUNT_GRAPH_NAME)
                .build();
    }
}