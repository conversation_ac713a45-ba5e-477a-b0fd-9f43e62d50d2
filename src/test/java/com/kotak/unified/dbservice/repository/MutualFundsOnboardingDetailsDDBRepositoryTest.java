package com.kotak.unified.dbservice.repository;


import com.kotak.unified.db.request.mf.GetMutualFundsOnboardingDetailsRequest;
import com.kotak.unified.dbservice.TestUtils;
import com.kotak.unified.dbservice.config.DynamoAWSConfiguration;
import com.kotak.unified.dbservice.model.mf.MutualFundsOnboardingDetailsDDBModel;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import software.amazon.awssdk.core.pagination.sync.SdkIterable;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbIndex;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;
import software.amazon.awssdk.enhanced.dynamodb.model.Page;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryEnhancedRequest;

import java.util.Arrays;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class MutualFundsOnboardingDetailsDDBRepositoryTest {
    private DynamoDbEnhancedClient dynamoDbEnhancedClient;

    DynamoDbTable<MutualFundsOnboardingDetailsDDBModel> dynamoDbTable;

    private DynamoAWSConfiguration dynamoAWSConfiguration;

    private MutualFundsOnboardingDetailsDDBRepository repository;

    @Mock
    private DynamoDbIndex<MutualFundsOnboardingDetailsDDBModel> crnIndex;


    @BeforeEach
    void setUp() {

        dynamoAWSConfiguration = Mockito.mock(DynamoAWSConfiguration.class);
        dynamoDbEnhancedClient = Mockito.mock(DynamoDbEnhancedClient.class);
        dynamoDbTable = Mockito.mock();
        when(dynamoAWSConfiguration.getMutualFundsOnboardingDetailsTableName()).thenReturn("UAO-MutualFundOnboardingDetails-dev");
        when(dynamoDbEnhancedClient.table("UAO-MutualFundOnboardingDetails-dev",
                TableSchema.fromBean(MutualFundsOnboardingDetailsDDBModel.class))).thenReturn(dynamoDbTable);
        repository = new MutualFundsOnboardingDetailsDDBRepository(dynamoDbEnhancedClient, dynamoAWSConfiguration);
        verify(dynamoAWSConfiguration, times(1)).getMutualFundsOnboardingDetailsTableName();
        verify(dynamoDbEnhancedClient, times(1)).table(any(String.class), any(TableSchema.class));
    }

    @Test
    public void test_save() {
        MutualFundsOnboardingDetailsDDBModel mutualFundsOnboardingDetailsDDBModel = TestUtils.getMockedMutualFundOnboardingDetailsDDBModel();
        when(dynamoDbTable.updateItem(mutualFundsOnboardingDetailsDDBModel)).thenReturn(mutualFundsOnboardingDetailsDDBModel);
        MutualFundsOnboardingDetailsDDBModel returnedDDBModel = this.repository.save(mutualFundsOnboardingDetailsDDBModel);
        validateDDBModel(mutualFundsOnboardingDetailsDDBModel, returnedDDBModel);
        verify(dynamoDbTable, times(1)).updateItem(any(MutualFundsOnboardingDetailsDDBModel.class));
    }

    @Test
    public void test_findByEventTrackingId() {
        MutualFundsOnboardingDetailsDDBModel mutualFundsOnboardingDetailsDDBModel = TestUtils.getMockedMutualFundOnboardingDetailsDDBModel();
        when(dynamoDbTable.getItem(any(Key.class))).thenReturn(mutualFundsOnboardingDetailsDDBModel);
        MutualFundsOnboardingDetailsDDBModel returnedDDBModel = this.repository.findByEventTrackingId("eventTrackingId");
        validateDDBModel(mutualFundsOnboardingDetailsDDBModel, returnedDDBModel);
        verify(dynamoDbTable, times(1)).getItem(any(Key.class));
    }

    @Test
    public void  test_findByCrnAndLatestEventStatusOrderByCreatedAtDesc() {
        MutualFundsOnboardingDetailsDDBModel mutualFundsOnboardingDetailsDDBModel = TestUtils.getMockedMutualFundOnboardingDetailsDDBModel();
        GetMutualFundsOnboardingDetailsRequest getMutualFundOnboardingDetailsRequest = GetMutualFundsOnboardingDetailsRequest.builder().crn("dummyCrn").eventStatus("INITIATED").eventType("TRANSACTION_READY").build();
        QueryEnhancedRequest request = QueryEnhancedRequest.builder()
                .queryConditional(QueryConditional.keyEqualTo(Key.builder()
                        .partitionValue("dummyCrn")
                        .build()))
                .scanIndexForward(false)
                .build();

        when(dynamoDbTable.index("CRNIndex")).thenReturn(crnIndex);
        SdkIterable<Page<MutualFundsOnboardingDetailsDDBModel>> mockIterable = mock(SdkIterable.class);
        Page<MutualFundsOnboardingDetailsDDBModel> mockPage = mock(Page.class);
        when(mockPage.items()).thenReturn(Collections.singletonList(mutualFundsOnboardingDetailsDDBModel));
        when(mockIterable.stream()).thenReturn(Arrays.stream(new Page[]{mockPage}));
        when(crnIndex.query(request)).thenReturn(mockIterable);

        MutualFundsOnboardingDetailsDDBModel result =  repository.findByCrnEventTypeEventStatusAndCreatedAtDesc(getMutualFundOnboardingDetailsRequest);
        assertNotNull(result);
        assertEquals(mutualFundsOnboardingDetailsDDBModel, result);

        verify(dynamoDbTable, times(1)).index(anyString());
        verify(mockPage, times(3)).items();
        verify(mockIterable, times(1)).stream();
        verify(crnIndex, times(1)).query(any(QueryEnhancedRequest.class));
    }

    @Test
    public void  test_findByCrnAndLatestEventStatusOrderByCreatedAtDesc_Case_Filtered_Out() {
        MutualFundsOnboardingDetailsDDBModel mutualFundsOnboardingDetailsDDBModel = TestUtils.getMockedMutualFundOnboardingDetailsDDBModel();
        GetMutualFundsOnboardingDetailsRequest getMutualFundOnboardingDetailsRequest = GetMutualFundsOnboardingDetailsRequest.builder().crn("dummyCrn").eventStatus("COMPLETED").eventType("TRANSACTION_READY").build();
        QueryEnhancedRequest request = QueryEnhancedRequest.builder()
                .queryConditional(QueryConditional.keyEqualTo(Key.builder()
                        .partitionValue("dummyCrn")
                        .build()))
                .scanIndexForward(false)
                .build();

        when(dynamoDbTable.index("CRNIndex")).thenReturn(crnIndex);
        SdkIterable<Page<MutualFundsOnboardingDetailsDDBModel>> mockIterable = mock(SdkIterable.class);
        Page<MutualFundsOnboardingDetailsDDBModel> mockPage = mock(Page.class);
        when(mockPage.items()).thenReturn(Collections.singletonList(mutualFundsOnboardingDetailsDDBModel));
        when(mockIterable.stream()).thenReturn(Arrays.stream(new Page[]{mockPage}));
        when(crnIndex.query(request)).thenReturn(mockIterable);

        MutualFundsOnboardingDetailsDDBModel result =  repository.findByCrnEventTypeEventStatusAndCreatedAtDesc(getMutualFundOnboardingDetailsRequest);
        assertNull(result);

        verify(dynamoDbTable, times(1)).index(anyString());
        verify(mockPage, times(1)).items();
        verify(mockIterable, times(1)).stream();
        verify(crnIndex, times(1)).query(any(QueryEnhancedRequest.class));
    }

    @Test
    public void  test_findByCrnAndLatestEventStatusOrderByCreatedAtDesc_Case_No_Element() {
        MutualFundsOnboardingDetailsDDBModel mutualFundsOnboardingDetailsDDBModel = TestUtils.getMockedMutualFundOnboardingDetailsDDBModel();
        GetMutualFundsOnboardingDetailsRequest getMutualFundOnboardingDetailsRequest = GetMutualFundsOnboardingDetailsRequest.builder().crn("dummyCrn").eventStatus("INITIATED").eventType("TRANSACTION_READY").build();
        QueryEnhancedRequest request = QueryEnhancedRequest.builder()
                .queryConditional(QueryConditional.keyEqualTo(Key.builder()
                        .partitionValue("dummyCrn")
                        .build()))
                .scanIndexForward(false)
                .build();

        when(dynamoDbTable.index("CRNIndex")).thenReturn(crnIndex);
        SdkIterable<Page<MutualFundsOnboardingDetailsDDBModel>> mockIterable = mock(SdkIterable.class);
        Page<MutualFundsOnboardingDetailsDDBModel> mockPage = mock(Page.class);
        when(mockPage.items()).thenReturn(null);
        when(mockIterable.stream()).thenReturn(Arrays.stream(new Page[]{mockPage}));
        when(crnIndex.query(request)).thenReturn(mockIterable);

        MutualFundsOnboardingDetailsDDBModel result =  repository.findByCrnEventTypeEventStatusAndCreatedAtDesc(getMutualFundOnboardingDetailsRequest);
        assertNull(result);

        verify(dynamoDbTable, times(1)).index(anyString());
        verify(mockPage, times(1)).items();
        verify(mockIterable, times(1)).stream();
        verify(crnIndex, times(1)).query(any(QueryEnhancedRequest.class));
    }

    @Test
    public void  test_findByCrnAndLatestEventStatusOrderByCreatedAtDesc_case_no_pag() {
        MutualFundsOnboardingDetailsDDBModel mutualFundsOnboardingDetailsDDBModel = TestUtils.getMockedMutualFundOnboardingDetailsDDBModel();
        GetMutualFundsOnboardingDetailsRequest getMutualFundOnboardingDetailsRequest = GetMutualFundsOnboardingDetailsRequest.builder().crn("dummyCrn").eventStatus("INITIATED").eventType("TRANSACTION_READY").build();
        QueryEnhancedRequest request = QueryEnhancedRequest.builder()
                .queryConditional(QueryConditional.keyEqualTo(Key.builder()
                        .partitionValue("dummyCrn")
                        .build()))
                .scanIndexForward(false)
                .build();

        when(dynamoDbTable.index("CRNIndex")).thenReturn(crnIndex);
        SdkIterable<Page<MutualFundsOnboardingDetailsDDBModel>> mockIterable = mock(SdkIterable.class);
        Page<MutualFundsOnboardingDetailsDDBModel> mockPage = mock(Page.class);
        when(mockPage.items()).thenReturn(null);
        when(mockIterable.stream()).thenReturn(Arrays.stream(new Page[]{mockPage}));
        when(crnIndex.query(request)).thenReturn(mockIterable);

        MutualFundsOnboardingDetailsDDBModel result =  repository.findByCrnEventTypeEventStatusAndCreatedAtDesc(getMutualFundOnboardingDetailsRequest);
        assertNull(result);

        verify(dynamoDbTable, times(1)).index(anyString());
        verify(mockPage, times(1)).items();
        verify(mockIterable, times(1)).stream();
        verify(crnIndex, times(1)).query(any(QueryEnhancedRequest.class));
    }

    private void validateDDBModel(MutualFundsOnboardingDetailsDDBModel expectedDbModel, MutualFundsOnboardingDetailsDDBModel actualDBModel) {
        Assertions.assertEquals(expectedDbModel.getLeadTrackingNumber(), actualDBModel.getLeadTrackingNumber());
        Assertions.assertEquals(expectedDbModel.getEventTrackingId(), actualDBModel.getEventTrackingId());
        Assertions.assertEquals(expectedDbModel.getLatestEventStatus(), actualDBModel.getLatestEventStatus());
        Assertions.assertEquals(expectedDbModel.getCrn(), actualDBModel.getCrn());
        Assertions.assertEquals(expectedDbModel.getEventMetadata(), actualDBModel.getEventMetadata());
        Assertions.assertEquals(expectedDbModel.getEventType(), actualDBModel.getEventType());
        Assertions.assertEquals(expectedDbModel.getLatestEventStatus(), actualDBModel.getLatestEventStatus());
        Assertions.assertEquals(expectedDbModel.getVersion(), actualDBModel.getVersion());
        Assertions.assertEquals(expectedDbModel.getCreatedAt(), actualDBModel.getCreatedAt());
        Assertions.assertEquals(expectedDbModel.getLastModifiedAt(), actualDBModel.getLastModifiedAt());
    }
}
