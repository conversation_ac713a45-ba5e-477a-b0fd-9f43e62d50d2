package com.kotak.unified.dbservice.controller;

import com.kotak.unified.common.response.wrapper.ResponseWrapperWithTypeSet;
import com.kotak.unified.db.ApiResponseDetailsDto;
import com.kotak.unified.dbservice.service.ApiResponseService;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import javax.ws.rs.BadRequestException;

import static org.junit.jupiter.api.Assertions.assertThrows;

public class ApiResponseDetailsControllerTest {

  private final ApiResponseDetailsController apiResponseDetailsController;
  private final ApiResponseService apiResponseService;

  private static final String LEAD_TRACKING_NUMBER = "l1";


  ApiResponseDetailsControllerTest() {
    this.apiResponseService = Mockito.mock(ApiResponseService.class);
    apiResponseDetailsController = new ApiResponseDetailsController(
            apiResponseService);
  }

  @SneakyThrows
  @Test
  public void testGetApiResponseDetailsHappyCase() {
    ApiResponseDetailsDto response = ApiResponseDetailsDto.builder().build();
    Mockito.when(apiResponseService.getApiResponseData(LEAD_TRACKING_NUMBER, "BUREAU_PAN_PROFILE"))
        .thenReturn(response);
    ResponseEntity<ResponseWrapperWithTypeSet<ApiResponseDetailsDto>> expectedResponse = new ResponseEntity<>(ResponseWrapperWithTypeSet.success(response), HttpStatus.OK);
    ResponseEntity<ResponseWrapperWithTypeSet<ApiResponseDetailsDto>> responseEntity = apiResponseDetailsController.getApiResponseData(LEAD_TRACKING_NUMBER, "BUREAU_PAN_PROFILE");
    Assertions.assertNotNull(responseEntity);
    Assertions.assertEquals(HttpStatus.OK.value(),
            responseEntity.getStatusCode().value());
    Assertions.assertEquals(expectedResponse.getBody().getData().toString(),
            responseEntity.getBody().getData().toString());
    Mockito.verify(apiResponseService, Mockito.times(1))
            .getApiResponseData(LEAD_TRACKING_NUMBER, "BUREAU_PAN_PROFILE");
  }

  @SneakyThrows
  @Test
  public void testGetApiResponseDetailsErrorCase() {
    ApiResponseDetailsDto response = ApiResponseDetailsDto.builder().build();
    Mockito.when(apiResponseService.getApiResponseData(LEAD_TRACKING_NUMBER, "BUREAU_PAN_PROFILE"))
            .thenThrow(new BadRequestException("Invalid ApiName"));
    ResponseEntity<ResponseWrapperWithTypeSet<ApiResponseDetailsDto>> expectedResponse = new ResponseEntity<>(ResponseWrapperWithTypeSet.success(response), HttpStatus.OK);
    BadRequestException exception = assertThrows(BadRequestException.class, ()-> apiResponseDetailsController.getApiResponseData(LEAD_TRACKING_NUMBER, "BUREAU_PAN_PROFILE"));
    Assertions.assertEquals("Invalid ApiName", exception.getMessage());
  }
}
