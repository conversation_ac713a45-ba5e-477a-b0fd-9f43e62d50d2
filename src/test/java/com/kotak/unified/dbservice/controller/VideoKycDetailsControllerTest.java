package com.kotak.unified.dbservice.controller;

import com.kotak.unified.common.response.wrapper.ResponseWrapper;
import com.kotak.unified.db.vkycStatusResponse.VideoKycDetailsResponse;
import com.kotak.unified.db.vkycStatusResponse.VkycStatusResponse;
import com.kotak.unified.dbservice.TestUtils;
import com.kotak.unified.dbservice.service.VideoKycDetailsService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.time.Instant;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class VideoKycDetailsControllerTest {
    private final VideoKycDetailsController videoKycDetailsController;

    private final VideoKycDetailsService videoKycDetailsService;

    private String VKYC_APPROVED_STATUS = "Approved";
    private String TEST_LEAD_ID = "TEST_LEAD_ID";
    private String VKYC_TRACKING_ID = "VKYC_TRACKING_ID";
    private String TEST_INSTANT = Instant.now().toString();

    VideoKycDetailsControllerTest() {
        this.videoKycDetailsService = Mockito.mock(VideoKycDetailsService.class);
        this.videoKycDetailsController = new VideoKycDetailsController(videoKycDetailsService);
    }

    @Test
    void getVideoKycDetailsSuccess() {
        VideoKycDetailsResponse videoKycDetailsResponse = getVideoKycDetailsApprovedResponse();
        when(videoKycDetailsService.getVideoKycDetails(VKYC_TRACKING_ID)).thenReturn(videoKycDetailsResponse);
        ResponseEntity<ResponseWrapper<VideoKycDetailsResponse>> response = videoKycDetailsController.getVideoKycDetails(VKYC_TRACKING_ID);
        Assertions.assertEquals(HttpStatus.OK.value(), response.getStatusCode().value());
        verify(videoKycDetailsService, times(1)).getVideoKycDetails(VKYC_TRACKING_ID);
        Assertions.assertEquals(videoKycDetailsResponse, response.getBody().getData());
    }

    @Test
    void getAllVideoKycDetailsSuccess() {
        ResponseEntity<ResponseWrapper<List<VideoKycDetailsResponse>>> response = videoKycDetailsController.getAllVideoKycDetails(TestUtils.LEAD_ID);

        Assertions.assertEquals(HttpStatus.METHOD_NOT_ALLOWED.value(), response.getStatusCode().value());
    }

    @Test
    void saveVideoKycDetailsSuccess() {
        VideoKycDetailsResponse videoKycDetailsRequest = VideoKycDetailsResponse.builder().build();
        ResponseEntity<ResponseWrapper<VideoKycDetailsResponse>> response = videoKycDetailsController.saveVideoKycDetails(videoKycDetailsRequest);
        Assertions.assertEquals(HttpStatus.OK.value(), response.getStatusCode().value());
        verify(videoKycDetailsService, times(1)).save(videoKycDetailsRequest);
    }

    @Test
    void getTopByLeadTrackingNumberOrderByCreatedAtDescSuccess() {
        VideoKycDetailsResponse videoKycDetailsResponse = getVideoKycDetailsApprovedResponse();
        when(videoKycDetailsService.getTopByLeadTrackingNumberOrderByCreatedAtDesc("123")).thenReturn(videoKycDetailsResponse);
        ResponseEntity<ResponseWrapper<VideoKycDetailsResponse>> response = videoKycDetailsController.getTopByLeadTrackingNumberOrderByCreatedAtDesc("123");
        Assertions.assertEquals(HttpStatus.OK.value(), response.getStatusCode().value());
        verify(videoKycDetailsService, times(1)).getTopByLeadTrackingNumberOrderByCreatedAtDesc("123");
        Assertions.assertEquals(videoKycDetailsResponse, response.getBody().getData());
    }

    @Test
    void getTopByLeadTrackingNumberAndLatestStatusOrderByCreatedAtSuccess() {
        VideoKycDetailsResponse videoKycDetailsResponse = getVideoKycDetailsApprovedResponse();
        when(videoKycDetailsService.getTopByLeadTrackingNumberAndLatestStatusOrderByCreatedAt("123", "Approved")).thenReturn(videoKycDetailsResponse);
        ResponseEntity<ResponseWrapper<VideoKycDetailsResponse>> response = videoKycDetailsController
                .getTopByLeadTrackingNumberAndLatestStatusOrderByCreatedAt("123", "Approved");
        Assertions.assertEquals(HttpStatus.OK.value(), response.getStatusCode().value());
        verify(videoKycDetailsService, times(1)).getTopByLeadTrackingNumberAndLatestStatusOrderByCreatedAt("123", "Approved");
        Assertions.assertEquals(videoKycDetailsResponse, response.getBody().getData());
    }

    private VideoKycDetailsResponse getVideoKycDetailsApprovedResponse() {
        return VideoKycDetailsResponse.builder()
                .leadTrackingNumber(TEST_LEAD_ID)
                .latestStatus(VKYC_APPROVED_STATUS)
                .trackingId(VKYC_TRACKING_ID)
                .vkycStatusList(List.of(VkycStatusResponse.builder()
                        .status(VKYC_APPROVED_STATUS)
                        .auditorId("123")
                        .auditorActionDate(TEST_INSTANT)
                        .build()))
                .build();
    }
    @Test
    public void getVideoKycDetailsNullParam() {
        assertThrows(NullPointerException.class, () ->
                videoKycDetailsController.getVideoKycDetails(null));
    }
    @Test
    public void getAllVideoKycDetailsNullParam() {
        assertThrows(NullPointerException.class, () ->
                videoKycDetailsController.getAllVideoKycDetails(null));
    }
    @Test
    public void saveVideoKycDetailsNullParam() {
        assertThrows(NullPointerException.class, () ->
                videoKycDetailsController.saveVideoKycDetails(null));
    }
    @Test
    public void getTopByLeadTrackingNumberOrderByCreatedAtDescNullParam() {
        assertThrows(NullPointerException.class, () ->
                videoKycDetailsController.getTopByLeadTrackingNumberOrderByCreatedAtDesc(null));
    }
    @Test
    public void getTopByLeadTrackingNumberAndLatestStatusOrderByCreatedAtNullParam() {
        assertThrows(NullPointerException.class, () ->
                videoKycDetailsController.getTopByLeadTrackingNumberAndLatestStatusOrderByCreatedAt(null, null));
    }
}
