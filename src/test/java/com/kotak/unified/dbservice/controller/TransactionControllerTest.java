package com.kotak.unified.dbservice.controller;

import com.kotak.unified.common.response.wrapper.ResponseWrapper;
import com.kotak.unified.db.response.TransactionResponse;
import com.kotak.unified.dbservice.TestUtils;
import com.kotak.unified.dbservice.service.TransactionService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.List;

class TransactionControllerTest {
    private final TransactionService transactionService;

    private final TransactionController transactionController;

    TransactionControllerTest() {
        this.transactionService = Mockito.mock(TransactionService.class);
        this.transactionController = new TransactionController(transactionService);
    }

    @Test
    void getTransactionsForLeadTrackingNumberSuccess() {
        ResponseEntity<ResponseWrapper<List<TransactionResponse>>> response = transactionController.getTransactionsForLeadTrackingNumber(TestUtils.LEAD_ID);
        Assertions.assertEquals(HttpStatus.METHOD_NOT_ALLOWED.value(), response.getStatusCode().value());
    }
}