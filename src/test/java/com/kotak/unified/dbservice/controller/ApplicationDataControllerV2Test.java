package com.kotak.unified.dbservice.controller;

import com.kotak.unified.common.exception.InvalidRequestException;
import com.kotak.unified.common.response.ErrorResponse;
import com.kotak.unified.db.ApplicationData;
import com.kotak.unified.db.ApplicationDataFilter;
import com.kotak.unified.db.GetApplicationDataRequest;
import com.kotak.unified.db.PLApplicationData;
import com.kotak.unified.dbservice.exceptions.EntityNotFoundException;
import com.kotak.unified.dbservice.service.impl.ApplicationDataService;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.List;

import static com.kotak.unified.db.ApplicationDataFilter.PHONE_NUMBER;

public class ApplicationDataControllerV2Test {

    private final ApplicationDataControllerV2 applicationDataControllerV2;
    private final ApplicationDataService applicationDataService;

    private static final String LEAD_TRACKING_NUMBER = "l1";

    ApplicationDataControllerV2Test() {
        this.applicationDataService = Mockito.mock(ApplicationDataService.class);
        applicationDataControllerV2 = new ApplicationDataControllerV2(
                applicationDataService);
    }

    @SneakyThrows
    @Test
    public void testGetApplicationDataHappyCase() {
        final ApplicationData response = getApplicationData();
        final GetApplicationDataRequest request = getGetApplicationDataRequest();
        Mockito.when(
                        applicationDataService.getApplicationDataV2(request,
                                LEAD_TRACKING_NUMBER))
                .thenReturn(response);
        final ResponseEntity<?> expectedResponse = new ResponseEntity<>(response,
                HttpStatus.OK);
        validateResponse(request, expectedResponse, HttpStatus.OK);
    }

    @SneakyThrows
    @Test
    public void testGetApplicationDataInvalidRequestException() {
        final GetApplicationDataRequest request = getGetApplicationDataRequest();
        final String errorMessage = "Invalid request.";
        final ErrorResponse errorResponse = ErrorResponse.builder()
                .errorCode("INVALID_REQUEST").errorMessage(errorMessage).build();
        Mockito.when(
                        applicationDataService.getApplicationDataV2(request,
                                LEAD_TRACKING_NUMBER))
                .thenThrow(new InvalidRequestException(errorMessage));
        ResponseEntity<?> expectedResponse = new ResponseEntity<>(errorResponse,
                HttpStatus.BAD_REQUEST);
        validateResponse(request, expectedResponse, HttpStatus.BAD_REQUEST);
    }

    @SneakyThrows
    @Test
    public void testGetApplicationDataEntityNotFoundException() {
        final GetApplicationDataRequest request = getGetApplicationDataRequest();
        final String errorMessage = "Not found.";
        final ErrorResponse errorResponse = ErrorResponse.builder()
                .errorCode("NOT_FOUND").errorMessage(errorMessage).build();
        Mockito.when(
                        applicationDataService.getApplicationDataV2(request,
                                LEAD_TRACKING_NUMBER))
                .thenThrow(new EntityNotFoundException(errorMessage));
        ResponseEntity<?> expectedResponse = new ResponseEntity<>(errorResponse,
                HttpStatus.NOT_FOUND);
        validateResponse(request, expectedResponse, HttpStatus.NOT_FOUND);
    }

    @SneakyThrows
    @Test
    public void testGetApplicationDataException() {
        final GetApplicationDataRequest request = getGetApplicationDataRequest();
        final String errorMessage = "Unknown error.";
        final ErrorResponse errorResponse = ErrorResponse.builder()
                .errorCode("UNKNOWN_ERROR").errorMessage(errorMessage).build();
        Mockito.when(
                        applicationDataService.getApplicationDataV2(request,
                                LEAD_TRACKING_NUMBER))
                .thenThrow(new RuntimeException(errorMessage));
        ResponseEntity<?> expectedResponse = new ResponseEntity<>(errorResponse,
                HttpStatus.INTERNAL_SERVER_ERROR);
        validateResponse(request, expectedResponse,
                HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @SneakyThrows
    private void validateResponse(final GetApplicationDataRequest request,
                                  final ResponseEntity<?> expectedResponse, final HttpStatus status) {
        final ResponseEntity<?> responseEntity = applicationDataControllerV2.getApplicationData(
                LEAD_TRACKING_NUMBER, request);
        Assertions.assertNotNull(responseEntity);
        Assertions.assertEquals(status.value(),
                responseEntity.getStatusCode().value());
        Assertions.assertEquals(expectedResponse.getBody().toString(),
                responseEntity.getBody().toString());
        Mockito.verify(applicationDataService, Mockito.times(1))
                .getApplicationDataV2(request, LEAD_TRACKING_NUMBER);
    }

    private ApplicationData getApplicationData() {
        return PLApplicationData.builder().build();
    }

    private GetApplicationDataRequest getGetApplicationDataRequest() {
        final List<ApplicationDataFilter> dataFilters = List.of(PHONE_NUMBER);
        return GetApplicationDataRequest.builder().dataFilters(dataFilters).build();
    }
}
