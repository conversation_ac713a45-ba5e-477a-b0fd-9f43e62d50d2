package com.kotak.unified.dbservice.controller;

import com.kotak.unified.common.response.wrapper.ResponseWrapper;
import com.kotak.unified.db.UserStatusResponse;
import com.kotak.unified.db.request.GetFilteredUserStatusRequest;
import com.kotak.unified.dbservice.TestUtils;
import com.kotak.unified.dbservice.service.UserStatusService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.modelmapper.ModelMapper;
import org.modelmapper.convention.MatchingStrategies;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class UserStatusControllerTest {
    private final UserStatusController userStatusController;
    private final UserStatusService userStatusService;

    private final ModelMapper modelMapper;

    UserStatusControllerTest()
    {
        this.userStatusService = Mockito.mock(UserStatusService.class);
        this.userStatusController = new UserStatusController(userStatusService);
        modelMapper = new ModelMapper();
        modelMapper.getConfiguration().setMatchingStrategy(MatchingStrategies.STRICT);
    }

    @Test
    public void testGetUserStatus()
    {
        String leadTrackingNumber = "l1";
        UserStatusResponse usr = TestUtils.getUserStatusResponseWithSavingsAccountJourneyMetadata();
        Mockito.when(this.userStatusService.getUserStatus(leadTrackingNumber)).thenReturn(usr);
        ResponseEntity<ResponseWrapper<UserStatusResponse>> response = this.userStatusController.getUserStatus(leadTrackingNumber);

        Mockito.verify(this.userStatusService, Mockito.times(1)).getUserStatus(leadTrackingNumber);
        Assertions.assertEquals(HttpStatus.OK.value(), response.getStatusCode().value());
        assertThat(response.getBody().getData()).usingRecursiveComparison().isEqualTo(usr);
    }

    @Test
    public void testGetFilteredUserStatus()
    {
        GetFilteredUserStatusRequest getFilteredUserStatusRequest = GetFilteredUserStatusRequest.builder()
                .phoneNumber("***********")
                .creationStartTime(0L)
                .creationEndTime(1L)
                .crn(TestUtils.TEST_CRN)
                .build();

        ResponseEntity<ResponseWrapper<List<UserStatusResponse>>> response = this.userStatusController.getFilteredUserStatus(getFilteredUserStatusRequest);
        Assertions.assertEquals(HttpStatus.METHOD_NOT_ALLOWED.value(), response.getStatusCode().value());
    }
}
