package com.kotak.unified.dbservice.controller;

import com.kotak.unified.common.response.wrapper.ResponseWrapper;
import com.kotak.unified.common.response.wrapper.ResponseWrapperWithTypeSet;
import com.kotak.unified.db.AccountPropagationExecutionDataResponse;
import com.kotak.unified.db.AsyncProcessDetailsResponse;
import com.kotak.unified.db.AsyncProcessExecutionDataResponse;
import com.kotak.unified.db.AsyncProcessIdResponse;
import com.kotak.unified.db.AsyncProcessStatusResponse;
import com.kotak.unified.db.AsyncProcessStepResponse;
import com.kotak.unified.db.AsyncProcessTypes;
import com.kotak.unified.dbservice.enums.AsyncExecutionDataProcessType;
import com.kotak.unified.dbservice.enums.ErrorCause;
import com.kotak.unified.dbservice.exceptions.custom.RestException;
import com.kotak.unified.dbservice.model.exception.DatabaseErrorResponse;
import com.kotak.unified.dbservice.service.AsyncProcessService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.MethodArgumentNotValidException;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class AsyncProcessControllerTests {

    private final AsyncProcessController asyncProcessController;
    private final AsyncProcessService asyncProcessService;

    private static final String TEST_LEAD_TRACKING_ID = "123";

    AsyncProcessControllerTests()
    {
        this.asyncProcessService = Mockito.mock(AsyncProcessService.class);
        this.asyncProcessController = new AsyncProcessController(asyncProcessService);
    }

    @Test
    public void getFilteredAsyncProcess() {

        ResponseEntity<ResponseWrapper<List<AsyncProcessDetailsResponse>>> response = this.asyncProcessController.getFilteredAsyncProcess("123","ACCOUNT_PROPAGATION","FAILED", 12345L, 67890L);

        Assertions.assertEquals(HttpStatus.METHOD_NOT_ALLOWED.value(), response.getStatusCode().value());
    }

    @Test
    public void getAsyncProcessExecutionData_ReturnsPayload() {
        AsyncProcessExecutionDataResponse executionDataResponse = AccountPropagationExecutionDataResponse.builder()
                .isPhoneNumberUpiIdAvailable(true)
                .build();

        Mockito.when(this.asyncProcessService.getAsyncProcessExecutionData(
                    TEST_LEAD_TRACKING_ID, AsyncExecutionDataProcessType.ACCOUNT_PROPAGATION)
                )
                .thenReturn(executionDataResponse);
        ResponseEntity<ResponseWrapperWithTypeSet<AsyncProcessExecutionDataResponse>> response =
                asyncProcessController.getAsyncProcessExecutionData(
                        TEST_LEAD_TRACKING_ID,
                        AsyncExecutionDataProcessType.ACCOUNT_PROPAGATION.toString());

        Mockito.verify(this.asyncProcessService, Mockito.times(1))
                .getAsyncProcessExecutionData(
                        TEST_LEAD_TRACKING_ID,AsyncExecutionDataProcessType.ACCOUNT_PROPAGATION);
        Assertions.assertEquals(HttpStatus.OK.value(), response.getStatusCode().value());
        Assertions.assertEquals(executionDataResponse, response.getBody().getData());
    }

    @Test
    public void getAsyncProcessExecutionData_ReturnsNull() {

        Mockito.when(this.asyncProcessService.getAsyncProcessExecutionData(
                        TEST_LEAD_TRACKING_ID, AsyncExecutionDataProcessType.ACCOUNT_PROPAGATION)
                ).thenReturn(null);
        ResponseEntity<ResponseWrapperWithTypeSet<AsyncProcessExecutionDataResponse>> response =
                asyncProcessController.getAsyncProcessExecutionData(
                        TEST_LEAD_TRACKING_ID,
                        AsyncExecutionDataProcessType.ACCOUNT_PROPAGATION.toString());

        Mockito.verify(this.asyncProcessService, Mockito.times(1))
                .getAsyncProcessExecutionData(TEST_LEAD_TRACKING_ID, AsyncExecutionDataProcessType.ACCOUNT_PROPAGATION);
        Assertions.assertEquals(HttpStatus.OK.value(), response.getStatusCode().value());
        Assertions.assertNull(response.getBody().getData());
    }

    @Test
    public void getAsyncProcessExecutionData_ReturnsNotFound() {
        Mockito.when(this.asyncProcessService.getAsyncProcessExecutionData(
                TEST_LEAD_TRACKING_ID, AsyncExecutionDataProcessType.ACCOUNT_PROPAGATION)
        ).thenThrow(new RestException(HttpStatus.NOT_FOUND, DatabaseErrorResponse
                .fromErrorCode(ErrorCause.ASYNC_PROCESS_STATUS_NOT_FOUND)));
        ResponseEntity<ResponseWrapperWithTypeSet<AsyncProcessExecutionDataResponse>> response =
                asyncProcessController.getAsyncProcessExecutionData(
                        TEST_LEAD_TRACKING_ID,
                        AsyncExecutionDataProcessType.ACCOUNT_PROPAGATION.toString());

        Mockito.verify(this.asyncProcessService, Mockito.times(1))
                .getAsyncProcessExecutionData(TEST_LEAD_TRACKING_ID, AsyncExecutionDataProcessType.ACCOUNT_PROPAGATION);
        Assertions.assertEquals(HttpStatus.NOT_FOUND.value(), response.getStatusCode().value());
        Assertions.assertEquals(ErrorCause.ASYNC_PROCESS_STATUS_NOT_FOUND.getMessage(), response.getBody().getErrors().get(0).getErrorMessage());
        Assertions.assertNull(response.getBody().getData());
    }

    @Test
    public void getAsyncProcessExecutionData_ReturnsNPE() {
        Mockito.when(this.asyncProcessService.getAsyncProcessExecutionData(
                TEST_LEAD_TRACKING_ID, AsyncExecutionDataProcessType.ACCOUNT_PROPAGATION)
        ).thenThrow(new NullPointerException("Error"));
        NullPointerException exception = Assertions.assertThrows(NullPointerException.class, ()->
                asyncProcessController.getAsyncProcessExecutionData(
                TEST_LEAD_TRACKING_ID,
                AsyncExecutionDataProcessType.ACCOUNT_PROPAGATION.toString()));

        Mockito.verify(this.asyncProcessService, Mockito.times(1))
                .getAsyncProcessExecutionData(TEST_LEAD_TRACKING_ID, AsyncExecutionDataProcessType.ACCOUNT_PROPAGATION);
    }

    @Test
    public void getAsyncProcessExecutionData_ReturnsError() {

        Assertions.assertThrows(MethodArgumentNotValidException.class, () -> asyncProcessController.getAsyncProcessExecutionData(
                TEST_LEAD_TRACKING_ID,
                "DUMMY_PROCESS_TYPE"
        ));
        Mockito.verify(asyncProcessService,Mockito.times(0)).getAsyncProcessExecutionData(
                TEST_LEAD_TRACKING_ID,
                AsyncExecutionDataProcessType.ACCOUNT_PROPAGATION);

    }

    @Test
    public void getAsyncProcessData_ReturnsPayload() {
        AsyncProcessDetailsResponse asyncProcessDetailsResponse = AsyncProcessDetailsResponse.builder()
                .build();

        Mockito.when(this.asyncProcessService.getAsyncProcessData(
                        TEST_LEAD_TRACKING_ID, AsyncExecutionDataProcessType.ACCOUNT_PROPAGATION.name())
                )
                .thenReturn(asyncProcessDetailsResponse);
        ResponseEntity<ResponseWrapper<AsyncProcessDetailsResponse>> response =
                asyncProcessController.getAsyncProcessData(
                        TEST_LEAD_TRACKING_ID,
                        AsyncExecutionDataProcessType.ACCOUNT_PROPAGATION.toString());

        Mockito.verify(this.asyncProcessService, Mockito.times(1))
                .getAsyncProcessData(
                        TEST_LEAD_TRACKING_ID,AsyncExecutionDataProcessType.ACCOUNT_PROPAGATION.name());
        Assertions.assertEquals(HttpStatus.OK.value(), response.getStatusCode().value());
        Assertions.assertEquals(asyncProcessDetailsResponse, response.getBody().getData());
    }

    @Test
    public void getAsyncProcessData_ReturnsNull() {
        Mockito.when(this.asyncProcessService.getAsyncProcessData(
                TEST_LEAD_TRACKING_ID, AsyncExecutionDataProcessType.ACCOUNT_PROPAGATION.name())
        ).thenReturn(null);
        ResponseEntity<ResponseWrapper<AsyncProcessDetailsResponse>> response =
                asyncProcessController.getAsyncProcessData(
                        TEST_LEAD_TRACKING_ID,
                        AsyncExecutionDataProcessType.ACCOUNT_PROPAGATION.toString());

        Mockito.verify(this.asyncProcessService, Mockito.times(1))
                .getAsyncProcessData(TEST_LEAD_TRACKING_ID, AsyncExecutionDataProcessType.ACCOUNT_PROPAGATION.name());
        Assertions.assertEquals(HttpStatus.OK.value(), response.getStatusCode().value());
        Assertions.assertNull(response.getBody().getData());
    }

    @Test
    public void getAsyncProcessData_ReturnsError() {
        Mockito.when(asyncProcessService.getAsyncProcessData(TEST_LEAD_TRACKING_ID, "DUMMY_PROCESS_TYPE"))
                .thenThrow(new RestException(HttpStatus.NOT_FOUND, DatabaseErrorResponse
                        .fromErrorCode(ErrorCause.ASYNC_PROCESS_STATUS_NOT_FOUND)));

        Assertions.assertThrows(RestException.class, () -> asyncProcessController.getAsyncProcessData(
                TEST_LEAD_TRACKING_ID,
                "DUMMY_PROCESS_TYPE"
        ));
        Mockito.verify(asyncProcessService,Mockito.times(0)).getAsyncProcessData(
                TEST_LEAD_TRACKING_ID,
                AsyncExecutionDataProcessType.ACCOUNT_PROPAGATION.name());

    }
}
