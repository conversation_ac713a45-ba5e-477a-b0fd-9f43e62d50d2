package com.kotak.unified.dbservice.controller;

import com.kotak.unified.db.model.CreateResumeJourneyNotificationStatusRecordRequest;
import com.kotak.unified.db.model.CreateResumeJourneyNotificationStatusRecordResponse;
import com.kotak.unified.db.model.GetResumeJourneyNotificationStatusRecordResponse;
import com.kotak.unified.db.model.UpdateResumeJourneyNotificationStatusRecordRequest;
import com.kotak.unified.db.model.UpdateResumeJourneyNotificationStatusRecordResponse;
import com.kotak.unified.dbservice.service.ResumeJourneyNotificationService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import static com.kotak.unified.dbservice.TestUtils.LEAD_ID;

@ExtendWith(MockitoExtension.class)
class ResumeJourneyNotificationControllerTest {

    @Mock
    private ResumeJourneyNotificationService resumeJourneyNotificationService;
    private ResumeJourneyNotificationController resumeJourneyNotificationController;

    @BeforeEach
    void setUp() {
        resumeJourneyNotificationController = new ResumeJourneyNotificationController((resumeJourneyNotificationService));
    }

    @Test
    void test_createResumeJourneyNotificationStatus_success() {
        CreateResumeJourneyNotificationStatusRecordRequest createResumeJourneyNotificationStatusRecordRequest = new CreateResumeJourneyNotificationStatusRecordRequest();
        CreateResumeJourneyNotificationStatusRecordResponse createResumeJourneyNotificationStatusRecordResponse = new CreateResumeJourneyNotificationStatusRecordResponse();
        Mockito.when(resumeJourneyNotificationService.createResumeJourneyNotificationStatusRecord(createResumeJourneyNotificationStatusRecordRequest)).thenReturn(createResumeJourneyNotificationStatusRecordResponse);
        ResponseEntity<CreateResumeJourneyNotificationStatusRecordResponse> response = resumeJourneyNotificationController.createResumeJourneyNotificationStatus(createResumeJourneyNotificationStatusRecordRequest);
        Assertions.assertEquals(HttpStatus.OK, response.getStatusCode());
        Assertions.assertEquals(createResumeJourneyNotificationStatusRecordResponse, response.getBody());
    }

    @Test
    void test_getResumeJourneyNotificationStatusRecord_success() {
        GetResumeJourneyNotificationStatusRecordResponse getResumeJourneyNotificationStatusRecordResponse = new GetResumeJourneyNotificationStatusRecordResponse();
        Mockito.when(resumeJourneyNotificationService.getResumeJourneyNotificationStatusRecord(LEAD_ID))
                .thenReturn(getResumeJourneyNotificationStatusRecordResponse);
        ResponseEntity<GetResumeJourneyNotificationStatusRecordResponse> response = resumeJourneyNotificationController
                .getResumeJourneyNotificationStatusRecord(LEAD_ID);

        Assertions.assertEquals(HttpStatus.OK, response.getStatusCode());
        Assertions.assertEquals(getResumeJourneyNotificationStatusRecordResponse, response.getBody());
    }

    @Test
    void test_updateResumeJourneyNotificationStatusRecord_success() {
        UpdateResumeJourneyNotificationStatusRecordRequest updateResumeJourneyNotificationStatusRecordRequest = new UpdateResumeJourneyNotificationStatusRecordRequest();
        UpdateResumeJourneyNotificationStatusRecordResponse updateResumeJourneyNotificationStatusRecordResponse = new UpdateResumeJourneyNotificationStatusRecordResponse();
        Mockito.when(resumeJourneyNotificationService.updateResumeJourneyNotificationStatusRecord(LEAD_ID, updateResumeJourneyNotificationStatusRecordRequest))
                .thenReturn(updateResumeJourneyNotificationStatusRecordResponse);
        ResponseEntity<UpdateResumeJourneyNotificationStatusRecordResponse> response = resumeJourneyNotificationController
                .updateResumeJourneyNotificationStatusRecord(LEAD_ID, updateResumeJourneyNotificationStatusRecordRequest);

        Assertions.assertEquals(HttpStatus.OK, response.getStatusCode());
        Assertions.assertEquals(updateResumeJourneyNotificationStatusRecordResponse, response.getBody());
    }
}