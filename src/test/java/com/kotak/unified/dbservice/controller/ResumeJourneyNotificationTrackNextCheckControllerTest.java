package com.kotak.unified.dbservice.controller;

import com.kotak.unified.db.model.CreateResumeJourneyNotificationTrackNextCheckRecordRequest;
import com.kotak.unified.db.model.CreateResumeJourneyNotificationTrackNextCheckRecordResponse;
import com.kotak.unified.db.model.GetResumeJourneyNotificationTrackNextCheckRecordResponse;
import com.kotak.unified.db.model.UpdateResumeJourneyNotificationTrackNextCheckRecordRequest;
import com.kotak.unified.db.model.UpdateResumeJourneyNotificationTrackNextCheckRecordResponse;
import com.kotak.unified.dbservice.service.ResumeJourneyNotificationTrackNextCheckService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import static com.kotak.unified.dbservice.TestUtils.LEAD_ID;

@ExtendWith(MockitoExtension.class)
class ResumeJourneyNotificationTrackNextCheckControllerTest {

    @Mock
    private ResumeJourneyNotificationTrackNextCheckService resumeJourneyNotificationTrackNextCheckService;
    private ResumeJourneyNotificationTrackNextCheckController resumeJourneyNotificationTrackNextCheckController;

    @BeforeEach
    void setUp() {
        resumeJourneyNotificationTrackNextCheckController = new ResumeJourneyNotificationTrackNextCheckController((resumeJourneyNotificationTrackNextCheckService));
    }

    @Test
    void test_createResumeJourneyNotificationNextCheck_success() {
        CreateResumeJourneyNotificationTrackNextCheckRecordRequest createResumeJourneyNotificationTrackNextCheckRecordRequest = new CreateResumeJourneyNotificationTrackNextCheckRecordRequest();
        CreateResumeJourneyNotificationTrackNextCheckRecordResponse createResumeJourneyNotificationTrackNextCheckRecordResponse = new CreateResumeJourneyNotificationTrackNextCheckRecordResponse();
        Mockito.when(resumeJourneyNotificationTrackNextCheckService.createResumeJourneyNotificationTrackNextCheckRecord(createResumeJourneyNotificationTrackNextCheckRecordRequest)).thenReturn(createResumeJourneyNotificationTrackNextCheckRecordResponse);
        ResponseEntity<CreateResumeJourneyNotificationTrackNextCheckRecordResponse> response = resumeJourneyNotificationTrackNextCheckController.createResumeJourneyNotificationNextCheck(createResumeJourneyNotificationTrackNextCheckRecordRequest);
        Assertions.assertEquals(HttpStatus.OK, response.getStatusCode());
        Assertions.assertEquals(createResumeJourneyNotificationTrackNextCheckRecordResponse, response.getBody());
    }

    @Test
    void test_getResumeJourneyNotificationTrackNextCheckRecord_success() {
        GetResumeJourneyNotificationTrackNextCheckRecordResponse getResumeJourneyNotificationTrackNextCheckRecordResponse = new GetResumeJourneyNotificationTrackNextCheckRecordResponse();
        Mockito.when(resumeJourneyNotificationTrackNextCheckService.getResumeJourneyNotificationTrackNextCheckRecord(LEAD_ID))
                .thenReturn(getResumeJourneyNotificationTrackNextCheckRecordResponse);
        ResponseEntity<GetResumeJourneyNotificationTrackNextCheckRecordResponse> response = resumeJourneyNotificationTrackNextCheckController
                .getResumeJourneyNotificationTrackNextCheckRecord(LEAD_ID);

        Assertions.assertEquals(HttpStatus.OK, response.getStatusCode());
        Assertions.assertEquals(getResumeJourneyNotificationTrackNextCheckRecordResponse, response.getBody());
    }

    @Test
    void test_updateResumeJourneyNotificationTrackNextCheckRecord_success() {
        UpdateResumeJourneyNotificationTrackNextCheckRecordRequest updateResumeJourneyNotificationTrackNextCheckRecordRequest = new UpdateResumeJourneyNotificationTrackNextCheckRecordRequest();
        UpdateResumeJourneyNotificationTrackNextCheckRecordResponse updateResumeJourneyNotificationTrackNextCheckRecordResponse = new UpdateResumeJourneyNotificationTrackNextCheckRecordResponse();
        Mockito.when(resumeJourneyNotificationTrackNextCheckService.updateResumeJourneyNotificationTrackNextCheckRecord(LEAD_ID, updateResumeJourneyNotificationTrackNextCheckRecordRequest))
                .thenReturn(updateResumeJourneyNotificationTrackNextCheckRecordResponse);
        ResponseEntity<UpdateResumeJourneyNotificationTrackNextCheckRecordResponse> response = resumeJourneyNotificationTrackNextCheckController
                .updateResumeJourneyNotificationTrackNextCheckRecord(LEAD_ID, updateResumeJourneyNotificationTrackNextCheckRecordRequest);

        Assertions.assertEquals(HttpStatus.OK, response.getStatusCode());
        Assertions.assertEquals(updateResumeJourneyNotificationTrackNextCheckRecordResponse, response.getBody());
    }
}