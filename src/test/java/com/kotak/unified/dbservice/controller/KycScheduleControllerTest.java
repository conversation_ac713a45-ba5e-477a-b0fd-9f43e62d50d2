package com.kotak.unified.dbservice.controller;

import builder.KycScheduleTestDataBuilder;
import com.kotak.unified.common.request.database.KycSchedulingRequest;
import com.kotak.unified.common.response.wrapper.ResponseWrapper;
import com.kotak.unified.db.KycSchedulingDetailsResponse;
import com.kotak.unified.db.KycSchedulingResponse;
import com.kotak.unified.dbservice.service.KycScheduleService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;

import java.util.Objects;

import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class KycScheduleControllerTest {
    private final KycScheduleController kycScheduleController;

    private final KycScheduleService mockKycScheduleService;

    KycScheduleControllerTest() {
        this.mockKycScheduleService = Mockito.mock(KycScheduleService.class);
        this.kycScheduleController = new KycScheduleController(mockKycScheduleService);
    }

    @Test
    void createKycSchedule() {
        KycSchedulingRequest kycSchedulingRequest = KycScheduleTestDataBuilder.createDummyKycSchedulingRequest();
        KycSchedulingResponse kycSchedulingResponse = KycScheduleTestDataBuilder.createDummyKycSchedulingResponse();

        when(mockKycScheduleService.createKycSchedule(kycSchedulingRequest)).thenReturn(kycSchedulingResponse);

        ResponseEntity<ResponseWrapper<KycSchedulingResponse>> kycSchedule = kycScheduleController.createKycSchedule(kycSchedulingRequest);

        verify(mockKycScheduleService, times(1)).createKycSchedule(kycSchedulingRequest);
        Assertions.assertEquals(HttpStatusCode.valueOf(HttpStatus.CREATED.value()), kycSchedule.getStatusCode());
        Assertions.assertEquals(kycSchedulingResponse, Objects.requireNonNull(kycSchedule.getBody()).getData());
        Assertions.assertNotNull(kycSchedule.getBody());
    }

    @Test
    void updateKycSchedule() {
        KycSchedulingRequest kycSchedulingRequest = KycScheduleTestDataBuilder.createDummyKycSchedulingRequest();
        KycSchedulingResponse kycSchedulingResponse = KycScheduleTestDataBuilder.createDummyKycSchedulingResponse();

        when(mockKycScheduleService.updateKycSchedule(kycSchedulingRequest)).thenReturn(kycSchedulingResponse);

        ResponseEntity<ResponseWrapper<KycSchedulingResponse>> kycSchedule = kycScheduleController.updateKycSchedule(kycSchedulingRequest);

        verify(mockKycScheduleService, times(1)).updateKycSchedule(kycSchedulingRequest);
        Assertions.assertEquals(HttpStatusCode.valueOf(HttpStatus.OK.value()), kycSchedule.getStatusCode());
        Assertions.assertEquals(kycSchedulingResponse, Objects.requireNonNull(kycSchedule.getBody()).getData());
        Assertions.assertNotNull(kycSchedule.getBody());
    }

    @Test
    void getKycSchedule() {
        KycSchedulingResponse kycSchedulingResponse = KycScheduleTestDataBuilder.createDummyKycSchedulingResponse();
        String leadTrackingId = KycScheduleTestDataBuilder.getLeadTrackingNumber();

        when(mockKycScheduleService.getKycScheduleForLeadTrackingId(leadTrackingId)).thenReturn(kycSchedulingResponse);

        ResponseEntity<ResponseWrapper<KycSchedulingResponse>> kycSchedule = kycScheduleController.getKycSchedule(leadTrackingId);

        verify(mockKycScheduleService, times(1)).getKycScheduleForLeadTrackingId(leadTrackingId);
        Assertions.assertEquals(HttpStatusCode.valueOf(HttpStatus.OK.value()), kycSchedule.getStatusCode());
        Assertions.assertEquals(kycSchedulingResponse, Objects.requireNonNull(kycSchedule.getBody()).getData());
        Assertions.assertNotNull(kycSchedule.getBody());
    }

    @Test
    void getKycScheduleUsingQueryParams() {
        KycSchedulingDetailsResponse kycSchedulingDetailsResponse = KycScheduleTestDataBuilder.createDummyKycSchedulingDetailsResponse();
        Long timestamp = KycScheduleTestDataBuilder.getKycScheduledAt();
        String comparisonOperator = KycScheduleTestDataBuilder.getGreaterThanOperator().toString();

        when(mockKycScheduleService.getKycSchedulesBasedOnTimestamp(timestamp, comparisonOperator)).thenReturn(kycSchedulingDetailsResponse);

        ResponseEntity<ResponseWrapper<KycSchedulingDetailsResponse>> kycSchedule = kycScheduleController.getKycSchedule(timestamp, comparisonOperator);

        verify(mockKycScheduleService, times(1)).getKycSchedulesBasedOnTimestamp(timestamp, comparisonOperator);
        Assertions.assertEquals(HttpStatusCode.valueOf(HttpStatus.OK.value()), kycSchedule.getStatusCode());
        Assertions.assertEquals(kycSchedulingDetailsResponse, Objects.requireNonNull(kycSchedule.getBody()).getData());
        Assertions.assertNotNull(kycSchedule.getBody());
    }
}