package com.kotak.unified.dbservice.controller;

import com.kotak.unified.common.response.wrapper.ResponseWrapper;
import com.kotak.unified.dbinterface.models.CpvDvuVerificationDetailsDto;
import com.kotak.unified.dbservice.TestUtils;
import com.kotak.unified.dbservice.service.impl.CpvDvuVerificationDetailsService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
public class CpvDvuVerificationDetailsControllerTest {
    @Mock
    private CpvDvuVerificationDetailsService cpvDvuVerificationDetailsService;

    @InjectMocks
    private CpvDvuVerificationDetailsController controller;

    @Test
    public void test_saveCpvDvuVerificationDetailsRecord() {
        CpvDvuVerificationDetailsDto cpvDvuVerificationDetailsDto = TestUtils.getMockedCpvDvuVerificationDetailsDto();
        Mockito.when(cpvDvuVerificationDetailsService.save(cpvDvuVerificationDetailsDto)).thenReturn(cpvDvuVerificationDetailsDto);
        ResponseEntity<ResponseWrapper<CpvDvuVerificationDetailsDto>> returnedResponse =
                this.controller.saveCpvDvuVerificationDetailsRecord(cpvDvuVerificationDetailsDto);
        Assertions.assertEquals(HttpStatusCode.valueOf(200), returnedResponse.getStatusCode());
        ResponseWrapper<CpvDvuVerificationDetailsDto> responseWrapper = returnedResponse.getBody();
        Assertions.assertNull(responseWrapper.getErrors());
        CpvDvuVerificationDetailsDto returnedDto = responseWrapper.getData();
        compareDto(cpvDvuVerificationDetailsDto, returnedDto);
    }

    @Test
    public void test_get_saveCpvDvuVerificationDetails() {
        CpvDvuVerificationDetailsDto cpvDvuVerificationDetailsDto = TestUtils.getMockedCpvDvuVerificationDetailsDto();
        Mockito.when(cpvDvuVerificationDetailsService.getCpvDvuVerificationDetails("a1")).thenReturn(cpvDvuVerificationDetailsDto);
        ResponseEntity<ResponseWrapper<CpvDvuVerificationDetailsDto>> returnedResponse =
                this.controller.getCpvDvuVerificationDetails("a1");
        Assertions.assertEquals(HttpStatusCode.valueOf(200), returnedResponse.getStatusCode());
        ResponseWrapper<CpvDvuVerificationDetailsDto> responseWrapper = returnedResponse.getBody();
        Assertions.assertNull(responseWrapper.getErrors());
        CpvDvuVerificationDetailsDto returnedDto = responseWrapper.getData();
        compareDto(cpvDvuVerificationDetailsDto, returnedDto);
    }

    private void compareDto(CpvDvuVerificationDetailsDto expectedDto, CpvDvuVerificationDetailsDto actualDto) {
        Assertions.assertEquals(expectedDto.getLeadTrackingNumber(), actualDto.getLeadTrackingNumber());
        Assertions.assertEquals(expectedDto.getActionTrackingId(), actualDto.getActionTrackingId());
        Assertions.assertEquals(expectedDto.getCpvCode(), actualDto.getCpvCode());
        Assertions.assertEquals(expectedDto.getDvuCode(), actualDto.getDvuCode());
        Assertions.assertEquals(expectedDto.getLatestStatus(), actualDto.getLatestStatus());
        Assertions.assertEquals(expectedDto.getLastStatusEventRecordedAt(), actualDto.getLastStatusEventRecordedAt());
        Assertions.assertEquals(expectedDto.getVersion(), actualDto.getVersion());
        Assertions.assertEquals(expectedDto.getCreatedAt(), actualDto.getCreatedAt());
        Assertions.assertEquals(expectedDto.getLastModifiedAt(), actualDto.getLastModifiedAt());
    }
}
