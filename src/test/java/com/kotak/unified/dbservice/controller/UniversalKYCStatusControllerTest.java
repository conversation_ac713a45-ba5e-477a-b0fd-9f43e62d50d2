package com.kotak.unified.dbservice.controller;

import com.kotak.unified.db.model.CreateUKYCRecordRequest;
import com.kotak.unified.db.model.CreateUKYCRecordResponse;
import com.kotak.unified.db.model.GetUKYCRecordResponse;
import com.kotak.unified.db.model.KYCChannelDTO;
import com.kotak.unified.db.model.KYCStatusDTO;
import com.kotak.unified.db.model.UKYCStatusDTO;
import com.kotak.unified.db.model.UpdateUKYCRecordRequest;
import com.kotak.unified.db.model.UpdateUKYCRecordResponse;
import com.kotak.unified.dbservice.service.UniversalKYCStatusService;
import com.kotak.unified.dbservice.validator.UniversalKYCStatusAPIRequestValidator;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;

import static com.kotak.unified.dbservice.TestUtils.TEST_AADHAAR_REF_KEY;
import static com.kotak.unified.dbservice.TestUtils.TEST_APPLICATION_ID;
import static org.mockito.Mockito.when;


// Tests for UniversalKYCStatusController class
@ExtendWith(MockitoExtension.class)
class UniversalKYCStatusControllerTest {
    @InjectMocks
    private UniversalKYCStatusAPIRequestValidator universalKYCStatusAPIRequestValidator;
    @Mock
    private UniversalKYCStatusService universalKYCStatusService;
    private UniversalKYCStatusController universalKYCStatusController;

    @BeforeEach
    public void setUp() {
        universalKYCStatusController = new UniversalKYCStatusController(
                universalKYCStatusService,
                universalKYCStatusAPIRequestValidator
                );
        ReflectionTestUtils.setField(universalKYCStatusAPIRequestValidator, "ukycAllowedJourneyTypes" , "SavingsAccount,SalaryAccount");

    }

    @Test
    void getUniversalKYCStatus_Success() {

    }

    // Controller successfully creates a UKYC record with valid input
    @Test
    public void test_createUKYCRecord_success() {
        CreateUKYCRecordRequest createUKYCRecordRequest = new CreateUKYCRecordRequest();
        createUKYCRecordRequest.journeyType("SavingsAccount");
        CreateUKYCRecordResponse createUKYCRecordResponse = new CreateUKYCRecordResponse();
        //environmentVariablesRule.set("UKYC_ALLOWED_JOURNEY_TYPES", "SavingsAccount#SalaryAccount");

        when(universalKYCStatusService.createUKYCRecord(createUKYCRecordRequest)).thenReturn(createUKYCRecordResponse);
        ResponseEntity<CreateUKYCRecordResponse> response = universalKYCStatusController.createUKYCRecord(createUKYCRecordRequest);
//        assertThat(System.getenv("system rules")).isEqualTo("works");
        Assertions.assertEquals(HttpStatus.OK, response.getStatusCode());
        Assertions.assertEquals(createUKYCRecordResponse, response.getBody());
    }

    @Test
    public void test_createUKYCRecord_throwsIllegalArgumentException() {
        CreateUKYCRecordRequest createUKYCRecordRequest = CreateUKYCRecordRequest.builder()
                .kycChannel(KYCChannelDTO.BIOMETRICS_KYC).journeyType("SavingsAccount").build();

        Assertions.assertThrows(IllegalArgumentException.class,
                () ->universalKYCStatusController.createUKYCRecord(createUKYCRecordRequest));

    }

    @Test
    public void test_getUKYCRecord_success() {
        GetUKYCRecordResponse getUKYCRecordResponse = new GetUKYCRecordResponse();
        when(universalKYCStatusService.getUKYCRecord(TEST_AADHAAR_REF_KEY, TEST_APPLICATION_ID))
                .thenReturn(getUKYCRecordResponse);
        ResponseEntity<GetUKYCRecordResponse> response = universalKYCStatusController
                .getUKYCRecord(TEST_AADHAAR_REF_KEY, TEST_APPLICATION_ID);

        Assertions.assertEquals(HttpStatus.OK, response.getStatusCode());
        Assertions.assertEquals(getUKYCRecordResponse, response.getBody());
    }

    @Test
    public void test_updateUKYCRecord_success() {
        UpdateUKYCRecordRequest updateUKYCRecordRequest = new UpdateUKYCRecordRequest();
        UpdateUKYCRecordResponse updateUKYCRecordResponse = new UpdateUKYCRecordResponse();

        updateUKYCRecordRequest.setActionTrackingId("ActionTrackingId");
        updateUKYCRecordRequest.setKycStatus(KYCStatusDTO.INITIATED);
        updateUKYCRecordRequest.setKycChannel(KYCChannelDTO.VIDEO_KYC);
        when(universalKYCStatusService.updateUKYCRecord(TEST_AADHAAR_REF_KEY, TEST_APPLICATION_ID, updateUKYCRecordRequest))
                .thenReturn(updateUKYCRecordResponse);
        ResponseEntity<UpdateUKYCRecordResponse> response = universalKYCStatusController
                .updateUKYCRecord(TEST_AADHAAR_REF_KEY, TEST_APPLICATION_ID, updateUKYCRecordRequest);

        Assertions.assertEquals(HttpStatus.OK, response.getStatusCode());
        Assertions.assertEquals(updateUKYCRecordResponse, response.getBody());
    }

    @Test
    public void test_updateUKYCRecord_ukycStatus_update_success() {
        UpdateUKYCRecordRequest updateUKYCRecordRequest = new UpdateUKYCRecordRequest();
        UpdateUKYCRecordResponse updateUKYCRecordResponse = new UpdateUKYCRecordResponse();

        updateUKYCRecordRequest.setUkycStatus(UKYCStatusDTO.ABORTED);
        when(universalKYCStatusService.updateUKYCRecord(TEST_AADHAAR_REF_KEY, TEST_APPLICATION_ID, updateUKYCRecordRequest))
                .thenReturn(updateUKYCRecordResponse);
        ResponseEntity<UpdateUKYCRecordResponse> response = universalKYCStatusController
                .updateUKYCRecord(TEST_AADHAAR_REF_KEY, TEST_APPLICATION_ID, updateUKYCRecordRequest);

        Assertions.assertEquals(HttpStatus.OK, response.getStatusCode());
        Assertions.assertEquals(updateUKYCRecordResponse, response.getBody());
    }


}