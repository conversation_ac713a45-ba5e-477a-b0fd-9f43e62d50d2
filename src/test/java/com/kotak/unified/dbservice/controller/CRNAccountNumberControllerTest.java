package com.kotak.unified.dbservice.controller;

import com.kotak.unified.common.response.async.AddCrnAndAccountNumberResponse;
import com.kotak.unified.db.request.AssignCACRNAndAccountNumberRequest;
import com.kotak.unified.db.request.InsertCACrnAndAccountNumberRequest;
import com.kotak.unified.db.response.AssignCACRNAndAccountNumberResponse;
import com.kotak.unified.dbservice.exceptions.EntityExistsException;
import com.kotak.unified.dbservice.exceptions.PregeneratedCRNAccountNumDepletedException;
import com.kotak.unified.dbservice.service.CAPreGeneratedCRNAccountNumService;
import com.kotak.unified.enums.CAJourneyType;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import software.amazon.awssdk.awscore.exception.AwsServiceException;

import javax.ws.rs.core.Response;

@ExtendWith(MockitoExtension.class)
public class CRNAccountNumberControllerTest {
    CRNAccountNumberController crnAccountNumberController;

    @Mock
    CAPreGeneratedCRNAccountNumService caPreGeneratedCRNAccountNumService;

    @BeforeEach
    public void setUp() {
        crnAccountNumberController = new CRNAccountNumberController(caPreGeneratedCRNAccountNumService);
    }

    @Test
    void testAddCACrnAndAccountNumber() {
        InsertCACrnAndAccountNumberRequest insertRequest = InsertCACrnAndAccountNumberRequest.builder()
                .crn("c1")
                .entityCrn("c2")
                .accountNumber("a1")
                .journeyType(CAJourneyType.BUSINESS_NON_PREFERRED_CURRENT_ACCOUNT)
                .build();
        ResponseEntity<?> responseEntity = crnAccountNumberController.addCACrnAndAccountNumber(insertRequest);
        AddCrnAndAccountNumberResponse addCrnAndAccountNumberResponse = AddCrnAndAccountNumberResponse.builder().isSuccess(true).build();
        Assertions.assertEquals(Response.Status.CREATED.getStatusCode(), responseEntity.getStatusCode().value());
        AddCrnAndAccountNumberResponse response = (AddCrnAndAccountNumberResponse) responseEntity.getBody();
        Assertions.assertEquals(addCrnAndAccountNumberResponse.getIsSuccess(), response.getIsSuccess());
    }

    @Test
    void testCAAddCrnAndAccountNumber_EntityAlreadyExistsFailure() throws  EntityExistsException {
        InsertCACrnAndAccountNumberRequest insertRequest = InsertCACrnAndAccountNumberRequest.builder()
                .crn("c1")
                .entityCrn("c2")
                .accountNumber("a1")
                .journeyType(CAJourneyType.BUSINESS_NON_PREFERRED_CURRENT_ACCOUNT)
                .build();
        Mockito.doThrow(new EntityExistsException("ENTITY_ALREADY_EXISTS", new Throwable("entity exists"))).when(caPreGeneratedCRNAccountNumService).insertCrnAndAccountNumber(insertRequest);
        ResponseEntity<?> responseEntity = crnAccountNumberController.addCACrnAndAccountNumber(insertRequest);
        Assertions.assertEquals(Response.Status.CONFLICT.getStatusCode(), responseEntity.getStatusCode().value());
        Mockito.verify(caPreGeneratedCRNAccountNumService, Mockito.times(1)).insertCrnAndAccountNumber(insertRequest);
    }

    @Test
    void testCAAddCrnAndAccountNumber_DynamoDBFailure() throws EntityExistsException {
        InsertCACrnAndAccountNumberRequest insertRequest = InsertCACrnAndAccountNumberRequest.builder()
                .crn("c1")
                .entityCrn("c2")
                .accountNumber("a1")
                .journeyType(CAJourneyType.BUSINESS_NON_PREFERRED_CURRENT_ACCOUNT)
                .build();
        Mockito.doThrow(AwsServiceException.builder().message("DB_ERROR").build()).when(caPreGeneratedCRNAccountNumService).insertCrnAndAccountNumber(insertRequest);
        ResponseEntity<?> responseEntity = crnAccountNumberController.addCACrnAndAccountNumber(insertRequest);
        Assertions.assertEquals(Response.Status.INTERNAL_SERVER_ERROR.getStatusCode(), responseEntity.getStatusCode().value());
        Mockito.verify(caPreGeneratedCRNAccountNumService, Mockito.times(1)).insertCrnAndAccountNumber(insertRequest);
    }

    @Test
    void testAddCACrnAndAccountNumber_Failure() throws  EntityExistsException {
        InsertCACrnAndAccountNumberRequest insertRequest = InsertCACrnAndAccountNumberRequest.builder()
                .crn("c1")
                .entityCrn("c2")
                .accountNumber("a1")
                .journeyType(CAJourneyType.BUSINESS_NON_PREFERRED_CURRENT_ACCOUNT)
                .build();

        Mockito.doThrow(new RuntimeException("UN_EXPECTED_ERROR")).when(caPreGeneratedCRNAccountNumService).insertCrnAndAccountNumber(insertRequest);
        ResponseEntity<?> responseEntity = crnAccountNumberController.addCACrnAndAccountNumber(insertRequest);
        Assertions.assertEquals(Response.Status.INTERNAL_SERVER_ERROR.getStatusCode(), responseEntity.getStatusCode().value());
        Mockito.verify(caPreGeneratedCRNAccountNumService, Mockito.times(1)).insertCrnAndAccountNumber(insertRequest);
    }

    @Test
    void test_assignCACRNAndAccountNumber_Valid_Input() throws EntityExistsException, PregeneratedCRNAccountNumDepletedException {
        AssignCACRNAndAccountNumberRequest request = AssignCACRNAndAccountNumberRequest.builder()
                .leadTrackingNumber("l1")
                .journeyType(CAJourneyType.BUSINESS_NON_PREFERRED_CURRENT_ACCOUNT)
                .build();
        AssignCACRNAndAccountNumberResponse response = AssignCACRNAndAccountNumberResponse.builder()
                .accountNumber("a1")
                .crn("c1")
                .entityCrn("c2")
                .journeyType(CAJourneyType.BUSINESS_NON_PREFERRED_CURRENT_ACCOUNT)
                .success(true)
                .build();

        Mockito.when(caPreGeneratedCRNAccountNumService.assignAccountNumber(request))
                .thenReturn(response);

        ResponseEntity<?> responseEntity = crnAccountNumberController.assignCACrnAndAccountNumber(request);
        ResponseEntity<?> expectedResponse = new ResponseEntity<>(response, HttpStatus.OK);
        Assertions.assertNotNull(responseEntity);
        Assertions.assertEquals(Response.Status.OK.getStatusCode(), responseEntity.getStatusCode().value());
        Assertions.assertEquals(expectedResponse.getBody().toString(), responseEntity.getBody().toString());
        Mockito.verify(caPreGeneratedCRNAccountNumService, Mockito.times(1)).assignAccountNumber(request);
    }

    @Test
    void test_assignCACRNAndAccountNumber_Existing_leadTrackingNumber() throws EntityExistsException, PregeneratedCRNAccountNumDepletedException {
        AssignCACRNAndAccountNumberRequest request = AssignCACRNAndAccountNumberRequest.builder()
                .leadTrackingNumber("l1")
                .journeyType(CAJourneyType.BUSINESS_NON_PREFERRED_CURRENT_ACCOUNT)
                .build();
        Mockito.when(caPreGeneratedCRNAccountNumService.assignAccountNumber(request))
                .thenThrow(new EntityExistsException("leadId already exists"));
        ResponseEntity<?> responseEntity = crnAccountNumberController.assignCACrnAndAccountNumber(request);
        Assertions.assertNotNull(responseEntity);
        Assertions.assertEquals(Response.Status.CONFLICT.getStatusCode(), responseEntity.getStatusCode().value());
        Mockito.verify(caPreGeneratedCRNAccountNumService, Mockito.times(1)).assignAccountNumber(request);
    }

    @Test
    void test_assignCACRNAndAccountNumber_PGN_Exhausted() throws EntityExistsException, PregeneratedCRNAccountNumDepletedException {
        AssignCACRNAndAccountNumberRequest request = AssignCACRNAndAccountNumberRequest.builder()
                .leadTrackingNumber("l1")
                .journeyType(CAJourneyType.BUSINESS_NON_PREFERRED_CURRENT_ACCOUNT)
                .build();
        Mockito.when(caPreGeneratedCRNAccountNumService.assignAccountNumber(request))
                .thenThrow(new PregeneratedCRNAccountNumDepletedException("No pre-generated CRN account num available to assign" +
                        " with journey type " + request.getJourneyType().toString()));
        ResponseEntity<?> responseEntity = crnAccountNumberController.assignCACrnAndAccountNumber(request);
        Assertions.assertNotNull(responseEntity);
        Assertions.assertEquals(Response.Status.NOT_FOUND.getStatusCode(), responseEntity.getStatusCode().value());
        Mockito.verify(caPreGeneratedCRNAccountNumService, Mockito.times(1)).assignAccountNumber(request);
    }

    @Test
    void test_assignCACRNAndAccountNumber_dynamoDB_exception() throws EntityExistsException, PregeneratedCRNAccountNumDepletedException {
        AssignCACRNAndAccountNumberRequest request = AssignCACRNAndAccountNumberRequest.builder()
                .leadTrackingNumber("l1")
                .journeyType(CAJourneyType.BUSINESS_NON_PREFERRED_CURRENT_ACCOUNT)
                .build();
        Mockito.doThrow(AwsServiceException.builder().message("DB_ERROR").build()).when(caPreGeneratedCRNAccountNumService).assignAccountNumber(request);
        ResponseEntity<?> responseEntity = crnAccountNumberController.assignCACrnAndAccountNumber(request);
        Assertions.assertEquals(Response.Status.INTERNAL_SERVER_ERROR.getStatusCode(), responseEntity.getStatusCode().value());
        Mockito.verify(caPreGeneratedCRNAccountNumService, Mockito.times(1)).assignAccountNumber(request);
    }

    @Test
    void test_assignCACRNAndAccountNumber_internal_exception() throws EntityExistsException, PregeneratedCRNAccountNumDepletedException {
        AssignCACRNAndAccountNumberRequest request = AssignCACRNAndAccountNumberRequest.builder()
                .leadTrackingNumber("l1")
                .journeyType(CAJourneyType.BUSINESS_NON_PREFERRED_CURRENT_ACCOUNT)
                .build();
        Mockito.when(caPreGeneratedCRNAccountNumService.assignAccountNumber(request))
                .thenThrow(new RuntimeException("Internal error"));
        ResponseEntity<?> responseEntity = crnAccountNumberController.assignCACrnAndAccountNumber(request);
        Assertions.assertNotNull(responseEntity);
        Assertions.assertEquals(Response.Status.INTERNAL_SERVER_ERROR.getStatusCode(), responseEntity.getStatusCode().value());
        Mockito.verify(caPreGeneratedCRNAccountNumService, Mockito.times(1)).assignAccountNumber(request);
    }


}
