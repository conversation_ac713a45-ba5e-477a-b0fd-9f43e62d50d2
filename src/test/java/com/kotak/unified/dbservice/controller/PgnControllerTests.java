package com.kotak.unified.dbservice.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.kotak.unified.common.response.ErrorResponse;
import com.kotak.unified.db.pgn.AccountNumSchemeCodePgnMetadata;
import com.kotak.unified.db.request.pgn.AssignPgnRequest;
import com.kotak.unified.db.request.pgn.InsertPgnRequest;
import com.kotak.unified.db.response.pgn.AssignPgnResponse;
import com.kotak.unified.db.response.pgn.InsertPgnResponse;
import com.kotak.unified.dbservice.exceptions.EntityExistsException;
import com.kotak.unified.dbservice.exceptions.PgnDepletedException;
import com.kotak.unified.dbservice.service.PgnService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class PgnControllerTests {

    private final PgnController pgnController;
    private final PgnService pgnService;

    public PgnControllerTests() {
        this.pgnService = mock(PgnService.class);
        this.pgnController = new PgnController(pgnService);
    }

    @Test
    public void test_assignPgn_success() throws JsonProcessingException {
        AssignPgnRequest assignPgnRequest = AssignPgnRequest.builder()
                .pgnType("SAVINGS")
                .leadTrackingNumber("**********")
                .build();

        AccountNumSchemeCodePgnMetadata accountNumSchemeCodePgnMetadata = AccountNumSchemeCodePgnMetadata.builder()
                .schemeCode("TEST_SCHEME_CODE")
                .accountNumber("1234567")
                .build();
        AssignPgnResponse expectedResponse = AssignPgnResponse.builder()
                .pgnAlreadyAssigned(false)
                .pgnMetadata(accountNumSchemeCodePgnMetadata)
                .crn("********")
                .pgnType("SAVINGS")
                .build();
        when(pgnService.assignPgn(assignPgnRequest)).thenReturn(expectedResponse);
        ResponseEntity<?> response = pgnController.assignPgn(assignPgnRequest);

        Assertions.assertEquals(HttpStatus.OK, response.getStatusCode());
        AssignPgnResponse actualResponse = (AssignPgnResponse) response.getBody();
        Assertions.assertNotNull(actualResponse);
    }

    @Test
    public void test_assignPgn_PgnDepleted_Exception() throws JsonProcessingException {
        AssignPgnRequest assignPgnRequest = AssignPgnRequest.builder()
                .pgnType("SAVINGS")
                .leadTrackingNumber("**********")
                .build();

        when(pgnService.assignPgn(any())).thenThrow(new PgnDepletedException("Test exception"));

        ResponseEntity<?> response = pgnController.assignPgn(assignPgnRequest);
        ErrorResponse actualResponse = (ErrorResponse) response.getBody();


        Assertions.assertNotNull(actualResponse);
        Assertions.assertEquals("Test exception", actualResponse.getErrorMessage());
        Assertions.assertEquals("PGN_DEPLETED", actualResponse.getErrorCode());
        Assertions.assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
    }

    @Test
    public void test_assignPgn_Runtime_Exception() {
        AssignPgnRequest assignPgnRequest = AssignPgnRequest.builder()
                .pgnType("SAVINGS")
                .leadTrackingNumber("**********")
                .build();

        when(pgnService.assignPgn(any())).thenThrow(new RuntimeException("Test exception"));

        ResponseEntity<?> response = pgnController.assignPgn(assignPgnRequest);
        ErrorResponse actualResponse = (ErrorResponse) response.getBody();
        Assertions.assertNotNull(actualResponse);
        Assertions.assertEquals("Test exception", actualResponse.getErrorMessage());
        Assertions.assertEquals("UNEXPECTED_ERROR", actualResponse.getErrorCode());
        Assertions.assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
    }

    @Test
    public void test_insertPgn_Success() throws EntityExistsException {
        AccountNumSchemeCodePgnMetadata accountNumSchemeCodePgnMetadata = AccountNumSchemeCodePgnMetadata.builder()
                .accountNumber("1234567")
                .schemeCode("CSSAC")
                .build();
        InsertPgnRequest insertPgnRequest = InsertPgnRequest.builder()
                .crn("********")
                .pgnType("SAVINGS")
                .pgnMetadata(accountNumSchemeCodePgnMetadata)
                .build();

        InsertPgnResponse expectedResponse = InsertPgnResponse.builder().success(true).build();
        when(pgnService.insertPgn(insertPgnRequest)).thenReturn(expectedResponse);

        ResponseEntity<?> responseEntity = pgnController.insertPgn(insertPgnRequest);
        InsertPgnResponse actualResponse = (InsertPgnResponse) responseEntity.getBody();
        Assertions.assertNotNull(actualResponse);
        Assertions.assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
        Assertions.assertTrue(actualResponse.getSuccess());
    }

    @Test
    public void test_insertPgn_entityExistsException() throws EntityExistsException {
        AccountNumSchemeCodePgnMetadata accountNumSchemeCodePgnMetadata = AccountNumSchemeCodePgnMetadata.builder()
                .accountNumber("1234567")
                .schemeCode("CSSAC")
                .build();
        InsertPgnRequest insertPgnRequest = InsertPgnRequest.builder()
                .crn("********")
                .pgnType("SAVINGS")
                .pgnMetadata(accountNumSchemeCodePgnMetadata)
                .build();

        when(pgnService.insertPgn(any())).thenThrow(new EntityExistsException("Test exception"));
        ResponseEntity<?> responseEntity = pgnController.insertPgn(insertPgnRequest);
        ErrorResponse errorResponse = (ErrorResponse) responseEntity.getBody();

        Assertions.assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, responseEntity.getStatusCode());
        Assertions.assertNotNull(errorResponse);
        Assertions.assertEquals("ENTITY_ALREADY_EXISTS", errorResponse.getErrorCode());
        Assertions.assertEquals("Test exception", errorResponse.getErrorMessage());
    }

    @Test
    public void test_insertPgn_RuntimeException() throws EntityExistsException {
        AccountNumSchemeCodePgnMetadata accountNumSchemeCodePgnMetadata = AccountNumSchemeCodePgnMetadata.builder()
                .accountNumber("1234567")
                .schemeCode("CSSAC")
                .build();
        InsertPgnRequest insertPgnRequest = InsertPgnRequest.builder()
                .crn("********")
                .pgnType("SAVINGS")
                .pgnMetadata(accountNumSchemeCodePgnMetadata)
                .build();

        when(pgnService.insertPgn(any())).thenThrow(new RuntimeException("Test exception"));
        ResponseEntity<?> responseEntity = pgnController.insertPgn(insertPgnRequest);
        ErrorResponse errorResponse = (ErrorResponse) responseEntity.getBody();

        Assertions.assertNotNull(errorResponse);
        Assertions.assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, responseEntity.getStatusCode());
        Assertions.assertEquals("UNEXPECTED_ERROR", errorResponse.getErrorCode());
        Assertions.assertEquals("Test exception", errorResponse.getErrorMessage());
    }

}
