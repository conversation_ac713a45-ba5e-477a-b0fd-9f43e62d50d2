package com.kotak.unified.dbservice.controller;

import com.kotak.unified.common.response.wrapper.ResponseWrapper;
import com.kotak.unified.db.mf.MutualFundsOnboardingDetailsRequest;
import com.kotak.unified.db.request.mf.GetMutualFundsOnboardingDetailsRequest;
import com.kotak.unified.dbservice.service.impl.MutualFundsOnboardingDetailsService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class MutualFundOnboardingDetailsControllerTest {
    @Mock
    private MutualFundsOnboardingDetailsService mutualFundsOnboardingDetailsService;

    @InjectMocks
    private MutualFundsOnboardingDetailsController controller;

    @Test
    public void test_saveMutualFundOnboardingDetails_success() {
        MutualFundsOnboardingDetailsRequest mockRequest = MutualFundsOnboardingDetailsRequest.builder().build();
        when(mutualFundsOnboardingDetailsService.save(mockRequest)).thenReturn(mockRequest);
        ResponseEntity<ResponseWrapper<MutualFundsOnboardingDetailsRequest>> returnedResponse =
                this.controller.save(mockRequest);
        Assertions.assertEquals(HttpStatusCode.valueOf(200), returnedResponse.getStatusCode());
        ResponseWrapper<MutualFundsOnboardingDetailsRequest> responseWrapper = returnedResponse.getBody();
        Assertions.assertNull(responseWrapper.getErrors());
        MutualFundsOnboardingDetailsRequest returnedRequest = responseWrapper.getData();
        compareRequest(mockRequest, returnedRequest);
        verify(mutualFundsOnboardingDetailsService, times(1)).save(any(MutualFundsOnboardingDetailsRequest.class));
    }

    @Test
    public void test_getMutualFundOnboardingDetails() {
        MutualFundsOnboardingDetailsRequest mockRequest = MutualFundsOnboardingDetailsRequest.builder().build();
        when(mutualFundsOnboardingDetailsService.getMutualFundOnboardingDetails("dummyEventTrackingId")).thenReturn(mockRequest);
        ResponseEntity<ResponseWrapper<MutualFundsOnboardingDetailsRequest>> returnedResponse =
                this.controller.getMutualFundOnboardingDetails("dummyEventTrackingId");
        Assertions.assertEquals(HttpStatusCode.valueOf(200), returnedResponse.getStatusCode());
        ResponseWrapper<MutualFundsOnboardingDetailsRequest> responseWrapper = returnedResponse.getBody();
        Assertions.assertNull(responseWrapper.getErrors());
        MutualFundsOnboardingDetailsRequest response = responseWrapper.getData();
        compareRequest(mockRequest, response);
        verify(mutualFundsOnboardingDetailsService, times(1)).getMutualFundOnboardingDetails(any(String.class));
    }

    @Test
    public void test_getByCrnAndLatestEventStatusInitiatedOrderByCreatedAtDesc() {
        MutualFundsOnboardingDetailsRequest mockRequest = MutualFundsOnboardingDetailsRequest.builder().build();
        GetMutualFundsOnboardingDetailsRequest getMutualFundsOnboardingDetailsRequest = GetMutualFundsOnboardingDetailsRequest.builder().crn("dummyCrn").eventStatus("dummyEventStatus").eventType("dummyEventType").build();
        when(mutualFundsOnboardingDetailsService.getCrnByEventTypeEventStatusAndCreatedAtDesc(getMutualFundsOnboardingDetailsRequest)).thenReturn(mockRequest);
        ResponseEntity<ResponseWrapper<MutualFundsOnboardingDetailsRequest>> returnedResponse =
                this.controller.getCrnByEventTypeEventStatusAndCreatedAtDesc(getMutualFundsOnboardingDetailsRequest);
        Assertions.assertEquals(HttpStatusCode.valueOf(200), returnedResponse.getStatusCode());
        ResponseWrapper<MutualFundsOnboardingDetailsRequest> responseWrapper = returnedResponse.getBody();
        Assertions.assertNull(responseWrapper.getErrors());
        MutualFundsOnboardingDetailsRequest response = responseWrapper.getData();
        compareRequest(mockRequest, response);
        verify(mutualFundsOnboardingDetailsService, times(1)).getCrnByEventTypeEventStatusAndCreatedAtDesc(any(GetMutualFundsOnboardingDetailsRequest.class));
    }

    private void compareRequest(MutualFundsOnboardingDetailsRequest expectedRequest, MutualFundsOnboardingDetailsRequest actualRequest) {
        Assertions.assertEquals(expectedRequest.getLeadTrackingNumber(), actualRequest.getLeadTrackingNumber());
        Assertions.assertEquals(expectedRequest.getEventTrackingId(), actualRequest.getEventTrackingId());
        Assertions.assertEquals(expectedRequest.getLatestEventStatus(), actualRequest.getLatestEventStatus());
        Assertions.assertEquals(expectedRequest.getCrn(), actualRequest.getCrn());
        Assertions.assertEquals(expectedRequest.getEventMetadataRequest(), actualRequest.getEventMetadataRequest());
        Assertions.assertEquals(expectedRequest.getEventType(), actualRequest.getEventType());
        Assertions.assertEquals(expectedRequest.getLatestEventStatus(), actualRequest.getLatestEventStatus());
        Assertions.assertEquals(expectedRequest.getVersion(), actualRequest.getVersion());
        Assertions.assertEquals(expectedRequest.getCreatedAt(), actualRequest.getCreatedAt());
        Assertions.assertEquals(expectedRequest.getLastModifiedAt(), actualRequest.getLastModifiedAt());
    }
}
