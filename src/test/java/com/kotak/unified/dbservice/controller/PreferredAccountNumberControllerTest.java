package com.kotak.unified.dbservice.controller;

import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;

import com.kotak.unified.common.response.wrapper.ResponseWrapper;
import com.kotak.unified.dbinterface.models.PreferredAccountNumberDto;
import com.kotak.unified.dbservice.service.PreferredAccountNumberService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
public class PreferredAccountNumberControllerTest {

    @Mock
    private PreferredAccountNumberService preferredAccountNumberService;

    @InjectMocks
    private PreferredAccountNumberController controller;

    private PreferredAccountNumberDto preferredAccountNumberDto;

    @BeforeEach
    public void setUp() {
        preferredAccountNumberDto = PreferredAccountNumberDto.builder()
                .preferredAccountNumber("**********")
                .leadTrackingNumber("123ab")
                .build();
    }

    @Test
    public void testSavePreferredAccountNumberRecord() {
        // Arrange
        when(preferredAccountNumberService.savePreferredAccountNumberRecord(preferredAccountNumberDto))
                .thenReturn(preferredAccountNumberDto);

        // Act
        ResponseEntity<ResponseWrapper<PreferredAccountNumberDto>> returnedResponse =
                controller.savePreferredAccountNumberRecord(preferredAccountNumberDto);

        // Assert
        assertEquals(HttpStatus.OK, returnedResponse.getStatusCode());
        ResponseWrapper<PreferredAccountNumberDto> responseWrapper = returnedResponse.getBody();
        assertNotNull(responseWrapper);
        assertNull(responseWrapper.getErrors());
        PreferredAccountNumberDto returnedDto = responseWrapper.getData();
        compareDto(preferredAccountNumberDto, returnedDto);
    }

    @Test
    public void testGetPreferredAccountNumberRecord() {
        // Arrange
        when(preferredAccountNumberService.getPreferredAccountNumberRecord("**********"))
                .thenReturn(preferredAccountNumberDto);

        // Act
        ResponseEntity<ResponseWrapper<PreferredAccountNumberDto>> returnedResponse =
                controller.getPreferredAccountNumberRecord("**********");

        // Assert
        assertEquals(HttpStatus.OK, returnedResponse.getStatusCode());
        ResponseWrapper<PreferredAccountNumberDto> responseWrapper = returnedResponse.getBody();
        assertNotNull(responseWrapper);
        assertNull(responseWrapper.getErrors());
        PreferredAccountNumberDto returnedDto = responseWrapper.getData();
        compareDto(preferredAccountNumberDto, returnedDto);
    }

    private void compareDto(PreferredAccountNumberDto expectedDto, PreferredAccountNumberDto actualDto) {
        assertEquals(expectedDto.getPreferredAccountNumber(), actualDto.getPreferredAccountNumber());
    }
}