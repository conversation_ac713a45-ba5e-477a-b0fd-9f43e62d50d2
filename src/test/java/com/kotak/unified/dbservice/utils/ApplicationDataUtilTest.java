package com.kotak.unified.dbservice.utils;

import com.kotak.unified.orchestrator.common.dbmodels.ApplicationData;
import com.kotak.unified.orchestrator.common.dbmodels.UserStatus;
import org.junit.jupiter.api.Test;

import static com.kotak.unified.dbservice.utils.Constants.NR_REKYC_JOURNEY_TYPE;
import static com.kotak.unified.dbservice.utils.Constants.NR_SAVINGS_ACCOUNT;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class ApplicationDataUtilTest {

    @Test
    void testGetProduct_NRRekycJourneyType() {
        UserStatus userStatus = mock(UserStatus.class);
        when(userStatus.getJourneyType()).thenReturn(NR_REKYC_JOURNEY_TYPE);

        String product = ApplicationDataUtil.getProduct(userStatus);

        assertEquals(NR_SAVINGS_ACCOUNT, product);
    }

    @Test
    void testGetProduct_PaHlJourneyType() {
        // Arrange
        ApplicationData applicationData = mock(ApplicationData.class);
        when(applicationData.getApplicationType()).thenReturn(Constants.HomeloanConstants.PA_HL_DIY_GRAPH_NAME);

        // Act
        String product = ApplicationDataUtil.getProduct(applicationData);

        // Assert
        assertEquals(Constants.HomeloanConstants.PA_DIY_PRODUCT, product);
    }

    @Test
    void testGetProduct_PaBtJourneyType() {
        // Arrange
        ApplicationData applicationData = mock(ApplicationData.class);
        when(applicationData.getApplicationType()).thenReturn(Constants.HomeloanConstants.PA_BT_DIY_GRAPH_NAME);

        // Act
        String product = ApplicationDataUtil.getProduct(applicationData);

        // Assert
        assertEquals(Constants.HomeloanConstants.PA_DIY_PRODUCT, product);
    }

    @Test
    void testGetProduct_PaLapJourneyType() {
        // Arrange
        ApplicationData applicationData = mock(ApplicationData.class);
        when(applicationData.getApplicationType()).thenReturn(Constants.HomeloanConstants.PA_LAP_DIY_GRAPH_NAME);

        // Act
        String product = ApplicationDataUtil.getProduct(applicationData);

        // Assert
        assertEquals(Constants.HomeloanConstants.PA_DIY_PRODUCT, product);
    }

    @Test
    void testGetProduct_EtbPqHlJourneyType() {
        // Arrange
        ApplicationData applicationData = mock(ApplicationData.class);
        when(applicationData.getApplicationType()).thenReturn(Constants.HomeloanConstants.PQ_HL_DIY_GRAPH_NAME);

        // Act
        String product = ApplicationDataUtil.getProduct(applicationData);

        // Assert
        assertEquals(Constants.HomeloanConstants.PQ_DIY_PRODUCT, product);
    }
}