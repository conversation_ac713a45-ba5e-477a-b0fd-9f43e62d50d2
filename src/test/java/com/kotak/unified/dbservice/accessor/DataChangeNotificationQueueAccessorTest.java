package com.kotak.unified.dbservice.accessor;

import com.kotak.unified.databridgeinterface.model.sqs.DataChangeNotificationMessage;
import com.kotak.unified.dbservice.TestUtils;
import com.kotak.unified.dbservice.config.DataChangeNotificationConfiguration;
import com.kotak.unified.dbservice.transformer.DataChangeNotificationMessageTransformer;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import software.amazon.awssdk.services.sqs.SqsClient;
import software.amazon.awssdk.services.sqs.model.SendMessageRequest;
import software.amazon.awssdk.services.sqs.model.SendMessageResponse;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class DataChangeNotificationQueueAccessorTest {
    private final DataChangeNotificationMessageTransformer dataChangeNotificationMessageTransformer;

    private final SqsClient sqsClient;

    private final DataChangeNotificationConfiguration dataChangeNotificationConfiguration;

    private final DataChangeNotificationQueueAccessor dataChangeNotificationQueueAccessor;

    DataChangeNotificationQueueAccessorTest() {
        this.dataChangeNotificationMessageTransformer = Mockito.mock(DataChangeNotificationMessageTransformer.class);
        this.sqsClient = Mockito.mock(SqsClient.class);
        this.dataChangeNotificationConfiguration = Mockito.mock(DataChangeNotificationConfiguration.class);
        this.dataChangeNotificationQueueAccessor = new DataChangeNotificationQueueAccessor(dataChangeNotificationMessageTransformer, sqsClient, dataChangeNotificationConfiguration);
    }

    @Test
    void publishMessageSuccess() {
        SendMessageRequest sendMessageRequest = SendMessageRequest.builder().build();
        String queueUrl = "test-queue-url";
        when(dataChangeNotificationConfiguration.getQueueUrl()).thenReturn(queueUrl);
        when(dataChangeNotificationMessageTransformer.convertDataBridgePublishMessageInputToMessageRequest(
                any(DataChangeNotificationMessage.class),
                anyString(),
                anyInt()
        )).thenReturn(sendMessageRequest);
        when(sqsClient.sendMessage(any(SendMessageRequest.class))).thenReturn(SendMessageResponse.builder().build());

        dataChangeNotificationQueueAccessor.publishMessage(TestUtils.DATA_CHANGE_NOTIFICATION_MESSAGE);

        verify(dataChangeNotificationConfiguration, times(1)).getQueueUrl();
        verify(dataChangeNotificationMessageTransformer, times(1)).convertDataBridgePublishMessageInputToMessageRequest(
                TestUtils.DATA_CHANGE_NOTIFICATION_MESSAGE,
                queueUrl, 0);
        verify(sqsClient, times(1)).sendMessage(sendMessageRequest);
    }

    @Test
    void publishMessageFailure() {
        SendMessageRequest sendMessageRequest = SendMessageRequest.builder().build();
        String queueUrl = "test-queue-url";
        when(dataChangeNotificationConfiguration.getQueueUrl()).thenReturn(queueUrl);
        when(dataChangeNotificationMessageTransformer.convertDataBridgePublishMessageInputToMessageRequest(
                any(DataChangeNotificationMessage.class),
                anyString(),
                anyInt()
        )).thenReturn(sendMessageRequest);
        when(sqsClient.sendMessage(any(SendMessageRequest.class))).thenThrow(RuntimeException.class);

        Assertions.assertThrows(RuntimeException.class, () -> dataChangeNotificationQueueAccessor.publishMessage(TestUtils.DATA_CHANGE_NOTIFICATION_MESSAGE));

        verify(dataChangeNotificationConfiguration, times(1)).getQueueUrl();
        verify(dataChangeNotificationMessageTransformer, times(1)).convertDataBridgePublishMessageInputToMessageRequest(
                TestUtils.DATA_CHANGE_NOTIFICATION_MESSAGE,
                queueUrl, 0 );
        verify(sqsClient, times(1)).sendMessage(sendMessageRequest);
    }
}