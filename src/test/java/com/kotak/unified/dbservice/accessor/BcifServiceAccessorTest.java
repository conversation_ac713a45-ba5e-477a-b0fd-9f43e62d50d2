package com.kotak.unified.dbservice.accessor;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kotak.unified.common.request.bcif.GetCrnDetailsRequest;
import com.kotak.unified.common.response.ErrorResponse;
import com.kotak.unified.common.response.PanDetailsResponse;
import com.kotak.unified.common.response.bcif.GetCrnDetailsResponse;
import com.kotak.unified.dbservice.model.ExternalServiceExecutionDetails;
import jakarta.inject.Named;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;

import javax.ws.rs.client.Invocation;
import javax.ws.rs.client.WebTarget;
import javax.ws.rs.core.Response;

import static org.mockito.ArgumentMatchers.any;

@ExtendWith(MockitoExtension.class)
@Slf4j
public class BcifServiceAccessorTest {

    @Mock
    BcifServiceAccessor bcifServiceAccessor;
    @Mock
    WebTarget bcifServiceWebTarget;

    @Mock
    ObjectMapper objectMapper;

    @Mock
    Invocation.Builder builder;

    @BeforeEach
    public void setUp()
    {
        bcifServiceAccessor = new BcifServiceAccessor(bcifServiceWebTarget);
    }
    @Test
    void test_getDynamicInfo_success() throws JsonProcessingException {
        String mockgetDynamicInfoEntity = "{\n" +
                "    \"isSuccess\" : true, \n" +
                "        \"isCrnActivated\": true\n" +
                "        }";
        Response mockResponse = Response.status(HttpStatus.OK.value())
                .entity(mockgetDynamicInfoEntity)
                .build();

        ExternalServiceExecutionDetails<GetCrnDetailsRequest, GetCrnDetailsResponse> expectedResponse =
                ExternalServiceExecutionDetails.<GetCrnDetailsRequest, GetCrnDetailsResponse>builder()
                        .request(GetCrnDetailsRequest
                                .builder()
                                .trackId("l1")
                                .encryptedCRN("c1")
                                .build())
                        .successResponse(GetCrnDetailsResponse.builder()
                                .isSuccess(true)
                                .isCrnActivated(true)
                                .build())
                        .build();

        Mockito.when(bcifServiceWebTarget.path(any())).thenReturn(bcifServiceWebTarget);
        Mockito.when(bcifServiceWebTarget.request()).thenReturn(builder);
        Mockito.when(builder.post(any())).thenReturn(mockResponse);
        ExternalServiceExecutionDetails<GetCrnDetailsRequest, GetCrnDetailsResponse> actualResponse = bcifServiceAccessor.getDynamicInfo("c1", "l1");
        Assertions.assertEquals(expectedResponse,actualResponse);
    }
    @Test
    void test_getDynamicInfo_Failure() throws JsonProcessingException {
        String mockgetDynamicInfoEntity = "{\n" +
                "    \"errorCode\" : \"error code\", \n" +
                "        \"errorMessage\": \"error message\" \n" +
                "        }";
        Response mockResponse = Response.status(HttpStatus.BAD_REQUEST.value())
                .entity(mockgetDynamicInfoEntity)
                .build();

        ExternalServiceExecutionDetails<GetCrnDetailsRequest, GetCrnDetailsResponse> expectedResponse =
                ExternalServiceExecutionDetails.<GetCrnDetailsRequest, GetCrnDetailsResponse>builder()
                        .request(GetCrnDetailsRequest
                                .builder()
                                .trackId("l1")
                                .encryptedCRN("c1")
                                .build())
                        .errorResponse(ErrorResponse.builder()
                                .errorCode("error code")
                                .errorMessage("error message")
                                .build())
                        .build();

        Mockito.when(bcifServiceWebTarget.path(any())).thenReturn(bcifServiceWebTarget);
        Mockito.when(bcifServiceWebTarget.request()).thenReturn(builder);
        Mockito.when(builder.post(any())).thenReturn(mockResponse);
        ExternalServiceExecutionDetails<GetCrnDetailsRequest, GetCrnDetailsResponse> actualResponse = bcifServiceAccessor.getDynamicInfo("c1", "l1");
        Assertions.assertEquals(expectedResponse,actualResponse);
    }

    @Test
    void test_getDynamicInfoNullResponse_Failure() throws JsonProcessingException {

        String mockgetDynamicInfoEntity = "{\n" +
                "    \"errorCode\" : null, \n" +
                "        \"errorMessage\": null \n" +
                "        }";

        Response mockResponse = Response.status(HttpStatus.BAD_REQUEST.value())
                .entity(mockgetDynamicInfoEntity)
                .build();

        ExternalServiceExecutionDetails<GetCrnDetailsRequest, GetCrnDetailsResponse> expectedResponse =
                ExternalServiceExecutionDetails.<GetCrnDetailsRequest, GetCrnDetailsResponse>builder()
                        .request(GetCrnDetailsRequest
                                .builder()
                                .trackId("l1")
                                .encryptedCRN("c1")
                                .build())
                        .build();

        Mockito.when(bcifServiceWebTarget.path(any())).thenReturn(bcifServiceWebTarget);
        Mockito.when(bcifServiceWebTarget.request()).thenReturn(builder);
        Mockito.when(builder.post(any())).thenReturn(mockResponse);
//        Assertions.assertThrows(JsonProcessingException.class,() -> objectMapper.writeValueAsString(GetCrnDetailsRequest.class));
        ExternalServiceExecutionDetails<GetCrnDetailsRequest, GetCrnDetailsResponse> actualResponse = bcifServiceAccessor.getDynamicInfo("c1", "l1");
        Assertions.assertEquals("{\n" +
                "    \"errorCode\" : null, \n" +
                "        \"errorMessage\": null \n" +
                "        }",actualResponse.getErrorResponse().getErrorMessage());
       // Assertions.assertThrows(JsonProcessingException.class,() -> bcifServiceAccessor.getDynamicInfo("c1", "l1"));
    }

}
