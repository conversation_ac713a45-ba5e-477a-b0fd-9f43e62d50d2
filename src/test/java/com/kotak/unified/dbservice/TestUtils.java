package com.kotak.unified.dbservice;

import com.kotak.unified.databridgeinterface.enums.DataSource;
import com.kotak.unified.databridgeinterface.model.sqs.DataChangeNotificationMessage;
import com.kotak.unified.db.AadhaarDetailsResponse;
import com.kotak.unified.db.AddressResponse;
import com.kotak.unified.db.CorpDetailsResponse;
import com.kotak.unified.db.DerivedAadhaarDetailsResponse;
import com.kotak.unified.db.DocumentsResponse;
import com.kotak.unified.db.ExecutionDataResponse;
import com.kotak.unified.db.GeoLocationResponse;
import com.kotak.unified.db.GuardianDetailsResponse;
import com.kotak.unified.db.JourneyStatusResponse;
import com.kotak.unified.db.NomineeDetailsResponse;
import com.kotak.unified.db.PanDetailsResponse;
import com.kotak.unified.db.PersonalDetailsResponse;
import com.kotak.unified.db.ProductDetailsResponse;
import com.kotak.unified.db.SavingsAccountJourneyMetadataResponse;
import com.kotak.unified.db.StepStatusResponse;
import com.kotak.unified.db.TagResponse;
import com.kotak.unified.db.UserStatusResponse;
import com.kotak.unified.db.UserStepStatusResponse;
import com.kotak.unified.db.VKYCDetailsResponse;
import com.kotak.unified.db.mf.EventMetadataRequest;
import com.kotak.unified.db.mf.MutualFundsOnboardingDetailsRequest;
import com.kotak.unified.db.mf.StepDetailsRequest;
import com.kotak.unified.db.model.CreateUKYCRecordRequest;
import com.kotak.unified.db.model.KYCChannelDTO;
import com.kotak.unified.db.model.KYCStatusDTO;
import com.kotak.unified.db.model.UpdateUKYCRecordRequest;
import com.kotak.unified.db.model.VideoKYCMetadataDTO;
import com.kotak.unified.dbinterface.models.CpvDvuVerificationDetailsDto;
import com.kotak.unified.dbinterface.models.NRCpvDvuVerificationMetadataDto;
import com.kotak.unified.dbservice.model.cpv.CpvDvuVerificationDetailsDDBModel;
import com.kotak.unified.dbservice.model.cpv.NRCpvDvuVerificationMetadata;
import com.kotak.unified.dbservice.model.mf.EventMetadata;
import com.kotak.unified.dbservice.model.mf.MutualFundsOnboardingDetailsDDBModel;
import com.kotak.unified.dbservice.model.mf.StepDetails;
import com.kotak.unified.orchestrator.common.dbmodels.AadhaarDetails;
import com.kotak.unified.orchestrator.common.dbmodels.Address;
import com.kotak.unified.orchestrator.common.dbmodels.CorpDetails;
import com.kotak.unified.orchestrator.common.dbmodels.DerivedAadhaarDetails;
import com.kotak.unified.orchestrator.common.dbmodels.DormantAccountActivationJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.DormantAccountActivationProductSpecifications;
import com.kotak.unified.orchestrator.common.dbmodels.EtbAccountDetail;
import com.kotak.unified.orchestrator.common.dbmodels.EtbDetails;
import com.kotak.unified.orchestrator.common.dbmodels.ExecutionData;
import com.kotak.unified.orchestrator.common.dbmodels.GeoLocation;
import com.kotak.unified.orchestrator.common.dbmodels.GuardianDetails;
import com.kotak.unified.orchestrator.common.dbmodels.JourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.JourneyStatus;
import com.kotak.unified.orchestrator.common.dbmodels.NomineeDetails;
import com.kotak.unified.orchestrator.common.dbmodels.PLApplicationMileStone;
import com.kotak.unified.orchestrator.common.dbmodels.PanDetails;
import com.kotak.unified.orchestrator.common.dbmodels.PaydayLoanJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.PersonalDetails;
import com.kotak.unified.orchestrator.common.dbmodels.PersonalLoanJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.ProductDetails;
import com.kotak.unified.orchestrator.common.dbmodels.SavingsAccountJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.Status;
import com.kotak.unified.orchestrator.common.dbmodels.StepStatus;
import com.kotak.unified.orchestrator.common.dbmodels.Tag;
import com.kotak.unified.orchestrator.common.dbmodels.UserStatus;
import com.kotak.unified.orchestrator.common.dbmodels.UserStepStatus;
import com.kotak.unified.orchestrator.common.dbmodels.VKYCDetails;
import com.kotak.unified.orchestrator.common.dbmodels.assets.AdditionalDetails;
import com.kotak.unified.orchestrator.common.dbmodels.assets.ApplicantDetails;
import com.kotak.unified.orchestrator.common.dbmodels.assets.CrnDetails;
import com.kotak.unified.orchestrator.common.dbmodels.assets.CrnNameMap;
import com.kotak.unified.orchestrator.common.dbmodels.assets.IncomeDetails;
import com.kotak.unified.orchestrator.common.dbmodels.assets.LoanIntent;
import com.kotak.unified.orchestrator.common.dbmodels.assets.PLAddressType;
import com.kotak.unified.orchestrator.common.dbmodels.assets.PLProductDetails;
import com.kotak.unified.orchestrator.common.dbmodels.ukycStatusModel.KYCChannel;
import com.kotak.unified.orchestrator.common.dbmodels.ukycStatusModel.KYCStatus;
import com.kotak.unified.orchestrator.common.dbmodels.ukycStatusModel.UKYCChannelStatus;
import com.kotak.unified.orchestrator.common.dbmodels.ukycStatusModel.UKYCStatus;
import com.kotak.unified.orchestrator.common.dbmodels.ukycStatusModel.UniversalKYCDetails;
import com.kotak.unified.orchestrator.common.dbmodels.ukycStatusModel.VideoKYCMetadata;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class TestUtils {
    public static final String LEAD_ID = "123456";
    public static final String BANK_LEAD_ID = "BL123456";
    public static final String PHONE_NUMBER = "**********";
    public static final String PAN_NUMBER = "**********";
    public static final String PRODUCT_ID = "abcd";
    public static final String JOURNEY_TYPE = "SavingsAccount";
    public static final String PERSONAL_LOAN_JOURNEY_TYPE = "PersonalLoan";
    public static final String SAVINGS_ACCOUNT_GRAPH_NAME = "SavingsAccount";
    public static final String PAYDAY_LOAN_GRAPH_NAME = "PaydayLoan";
    public static final String DORMANT_ACCOUNT_GRAPH_NAME="DormantAccountActivation";
    public static final String PA_HL_DIY_GRAPH_NAME="PreApprovedHomeLoanDiy";
    public static final String MUTUAL_FUNDS="MutualFunds";
    public static final Instant TEST_LAST_UPDATED_TIME = Instant.ofEpochSecond(3, 1);
    public static final String MOCK_EMAIL = "<EMAIL>";
    public static final String TEST_ACCOUNT_NUMBER = "testAccount";
    public static final String TEST_CRN = "testCrn";
    public static final String TEST_PAN_IMAGE = "testPan";
    public static final String TEST_SELFIE = "testSelfie";
    public static final String TEST_BRANCH_ADDRES = "branchAddress";
    public static final String TEST_SIGNATURE = "testSignature";
    public static final String TEST_ADDRESS_LINE1 = "testLine1";
    public static final String TEST_ADDRESS_LINE2 = "testLine2";
    public static final String TEST_ADDRESS_LINE3 = "testLine3";
    public static final String TEST_ADDRESS_PINCODE = "testPincode";
    public static final String TEST_ADDRESS_STATE = "testState";
    public static final String TEST_ADDRESS_CITY = "testCity";
    public static final String TEST_AADHAAR_FULL_NAME = "John Ron Doe";
    public static final String TEST_AADHAAR_DOB = "testDob";
    public static final String TEST_AADHAAR_GENDER = "testGender";
    public static final String TEST_AADHAAR_PHOTO = "testPhoto";
    public static final String TEST_AADHAAR_HOUSE = "testHouse";
    public static final String TEST_AADHAAR_LANDMARK = "testLandmark";
    public static final String TEST_AADHAAR_DISTRICT = "testDistrict";
    public static final String TEST_AADHAAR_STREET = "testStreet";
    public static final String TEST_AADHAAR_VTC = "testVtc";
    public static final String TEST_AADHAAR_CARE_OF = "testCareOf";
    public static final String AADHAAR_REFKEY = "R123456789";
    private static final String GET_PHONE_NUMBER_NODE_NAME = "GetPhoneNumber";
    public static final String TEST_AADHAAR_REF_KEY = "test_aadhaarRefKey";
    public static final String TEST_APPLICATION_ID = "test_applicationId";
    public static final String TEST_ACTION_TRACKING_ID = "test_actionTrackingId";
    public static final String TEST_JOURNEY_TYPE = "SA";
    public static final String TEST_CLIENT_ID = "test_clientId";
    public static final String TEST_AGENT_ID = "test_agentId";
    public static final String TEST_ACTOR = "test_actor";
    public static final String TEST_ACTOR_1 = TEST_ACTOR + "1";
    public static final String TEST_LATITUDE = "43242342.342";
    public static final String TEST_LONGITUDE = "21243.2123";

    private static final String TEST_ACTION_COMPLETE = "SETMPIN";
    private static final String NEW_LEAD_CREATED_JOURNEY_STATUS_REASON_CODE = "NewLead";
    public static final String USER_IP = "***********";
    public static final String TEST_FLAT_NO = "test_flat_no";
    public static final String TEST_ENTITY_NAME = "test_entity_name";
    public static final String TEST_UDYAM_REGISTRATION_NUMBER = "UDYAM-MH-19-055349";
    public static final String TEST_INCORPORATION_DATE = "2015-06-08";
    public static final String TEST_DOCUMENT_NUMBER = "123456789097";

    public static UserStatus getUserStatusWithPaydayLoanJourneyMetadata() {
        StepStatus stepStatus = StepStatus.builder()
                .name(GET_PHONE_NUMBER_NODE_NAME)
                .status(Status.INITIATED)
                .lastUpdatedTime(TestUtils.TEST_LAST_UPDATED_TIME)
                .actionComplete(TEST_ACTION_COMPLETE)
                .build();
        UserStepStatus userStepStatus = UserStepStatus.builder().status(Status.COMPLETED)
                .lastUpdatedTime(TestUtils.TEST_LAST_UPDATED_TIME)
                .isAsyncUIStep(true)
                .stepsStatus(Arrays.asList(stepStatus))
                .build();
        Map<String, UserStepStatus> statusMap = new HashMap<>();
        statusMap.put(PAYDAY_LOAN_GRAPH_NAME, userStepStatus);

        PaydayLoanJourneyMetadata journeyMetadata = PaydayLoanJourneyMetadata.builder().build();
        return UserStatus.builder()
                .leadTrackingNumber(LEAD_ID)
                .bankLeadTrackingNumber(BANK_LEAD_ID)
                .crn(TEST_CRN)
                .phoneNumber(PHONE_NUMBER)
                .panNumber(PAN_NUMBER)
                .journeyType("PDL")
                .journeyMetadata(journeyMetadata)
                .journeyStatus(JourneyStatus.ACCOUNT_OPENED_FKYC_DONE)
                .journeyStatusReasonCode(NEW_LEAD_CREATED_JOURNEY_STATUS_REASON_CODE)
                .userIp(USER_IP)
                .productId(TestUtils.PRODUCT_ID)
                .status(statusMap)
                .createdTime(Instant.now())
                .lastModifiedTime(Instant.now())

                .build();
    }
    public static UserStatus getUserStatusWithSavingsAccountJourneyMetadata() {
        GeoLocation geoLocation = GeoLocation.builder().latLongCity("TestCity")
                .latitudeAndLongitude("12.2;231.4")
                .latLongCountry("TestCountry")
                .latLongState("TestState")
                .build();
        VKYCDetails vkycDetails = VKYCDetails.builder()
                .geoLocation(geoLocation)
                .build();

        Address communicationAddress = Address.builder()
                .line1(TEST_ADDRESS_LINE1)
                .line2(TEST_ADDRESS_LINE2)
                .line3(TEST_ADDRESS_LINE3)
                .city(TEST_ADDRESS_CITY)
                .state(TEST_ADDRESS_STATE)
                .pincode(TEST_ADDRESS_PINCODE)
                .cityCode("cc1")
                .finacleCityCode("fcc1")
                .stateCode("sc1")
                .finacleStateCode("fsc1")
                .country("coun1")
                .build();

        AadhaarDetails aadhaarDetails = AadhaarDetails.builder()
                .name(TEST_AADHAAR_FULL_NAME)
                .dob(TEST_AADHAAR_DOB)
                .gender(TEST_AADHAAR_GENDER)
                .photo(TEST_AADHAAR_PHOTO)
                .house(TEST_AADHAAR_HOUSE)
                .careOf(TEST_AADHAAR_CARE_OF)
                .landmark(TEST_AADHAAR_LANDMARK)
                .district(TEST_AADHAAR_DISTRICT)
                .pincode(TEST_ADDRESS_PINCODE)
                .state(TEST_ADDRESS_STATE)
                .street(TEST_AADHAAR_STREET)
                .vtc(TEST_AADHAAR_VTC)
                .refKey(AADHAAR_REFKEY)
                .verifiedAt(Instant.now())
                .derivedAadhaarDetails(
                        DerivedAadhaarDetails
                                .builder()
                                .firstName("f1")
                                .middleName("m1")
                                .lastName("l1")
                                .fullName("f1m1l1")
                                .address(Address.builder().city("c1").line1("l1")
                                        .line2("l2")
                                        .line3("l3")
                                        .pincode("p1")
                                        .state("s1")
                                        .stateCode("sc1")
                                        .country("c1")
                                        .finacleCityCode("fc1")
                                        .finacleStateCode("fs1")
                                        .build())
                                .build()
                )
                .build();

        PanDetails panDetails = PanDetails.builder()
                .statusFlag("mockFlag")
                .validFlag("mockValid")
                .lastName("Doe")
                .firstName("John")
                .middleName("Ron")
                .title("Mr.")
                .updateDate("12.12.2012")
                .displayName("John Ron Doe")
                .aadhaarSeed("Y")
                .number(PAN_NUMBER)
                .build();

        ProductDetails productDetails = ProductDetails.builder()
                .fullKYCSchemeCode("K_ONE")
                .productType("Regular Savings Account")
                .minimumBalanceAmount(123)
                .limitedKYCMaximumBalanceAmount(1211)
                .promoCode("p1")
                .rmCode("r1")
                .lcCode("lc1")
                .lgCode("lg1")
                .rmCode("rm1")
                .productType("prd1")
                .activeMoneySweepOutAmount(1345)
                .campaignName("cam1")
                .channel("ch1")
                .corpCode("corp1")
                .source("s1")
                .lineOfBusiness("lob1")
                .isActiveMoneyEnabled(true)
                .build();

        NomineeDetails nm = NomineeDetails.builder()
                .DOB("dob1")
                .name("nam1")
                .relationshipType("rel1")
                .guardianDetails(GuardianDetails.builder().name("nam2")
                        .DOB("dob2")
                        .guardianAddress(Address.builder()
                                .line1("l11")
                                .line2("l22")
                                .line3("l223")
                                .city("ci1")
                                .country("coun1")
                                .finacleCityCode("ficc1")
                                .finacleStateCode("fics1")
                                .cityCode("cicc1")
                                .stateCode("socc1")
                                .build())
                        .build())
                .nomineeAddress(Address.builder()
                        .line1("nl11")
                        .line2("nl22")
                        .line3("nl223")
                        .city("nci1")
                        .country("ncoun1")
                        .finacleCityCode("nficc1")
                        .finacleStateCode("nfics1")
                        .cityCode("ncicc1")
                        .stateCode("nsocc1")
                        .build())
                .build();

        PersonalDetails pd = PersonalDetails.builder()
                .annualIncome(Tag.builder().code("a1").value("av1").build())
                .maritalStatus(Tag.builder().code("m1").value("mv1").build())
                .annualSalary(1132132)
                .corpDetails(CorpDetails.builder()
                        .corpCode("corpc1").finacleCorpCode("fcorpc1").fullName("fnc").build())
                .motherName("m1")
                .occupation(Tag.builder().code("a1").value("av1").build())
                .sourceOfIncome(Tag.builder().code("a1").value("av1").build())
                .build();

        SavingsAccountJourneyMetadata journeyMetadata = SavingsAccountJourneyMetadata.builder()
                .crn(TEST_CRN)
                .accountNumber(TEST_ACCOUNT_NUMBER)
                .accountOpeningDate(Instant.now().minus(5, ChronoUnit.DAYS))
                .vkycDetails(vkycDetails)
                .communicationAddress(communicationAddress)
                .isCommunicationAddressSameAsAadhaarAddress(true)
                .activMoneyOptIn(true)
                .emailAddress(TestUtils.MOCK_EMAIL)
                .aadhaarDetails(aadhaarDetails)
                .panDetails(panDetails)
                .productDetails(productDetails)
                .personalDetails(pd)
                .nomineeDetails(nm)
                .initialPanNumber(PAN_NUMBER)
                .build();
        Instant ncifVerificationTime = Instant.now().minus(71, ChronoUnit.HOURS);
        ExecutionData executionData = ExecutionData.builder()
                .addFundsAttempts(3)
                .emailOTPRetryLastAttemptedTime(Instant.now())
                .aadhaarRetryLastAttemptedTime(Instant.now())
                .emailOTPResendAttempts(4)
                .ncifLastVerifiedTime(ncifVerificationTime)
                .emailOTPWrongAttempts(5)
                .addFundsLastAttemptedTime(Instant.now())
                .isFirstTimeAadhaarOTPSent(true)
                .isInCompleteDOB(true)
                .isMobileOtpAlreadySent(false)
                .isNomineeSkipped(true)
                .isPanAadhaarLinked(true)
                .isFirstTimeEmailOTPSent(true)
                .emailValidationRetryAttemptsLeft(5)
                .verificationUnblockTime(Instant.now().plus(4,ChronoUnit.HOURS))
                .mobileOTPResendAttempts(3)
                .mobileOTPWrongAttempts(6)
                .otpId("e292")
                .otpResendAttemptsLeft(5)
                .otpRetryAttemptsLeft(2)
                .isEmailAddressVerified(true)
                .build();

        StepStatus stepStatus = StepStatus.builder()
                .name(GET_PHONE_NUMBER_NODE_NAME)
                .status(Status.INITIATED)
                .lastUpdatedTime(TestUtils.TEST_LAST_UPDATED_TIME)
                .actionComplete(TEST_ACTION_COMPLETE)
                .build();
        UserStepStatus userStepStatus = UserStepStatus.builder().status(Status.COMPLETED)
                .lastUpdatedTime(TestUtils.TEST_LAST_UPDATED_TIME)
                .isAsyncUIStep(true)
                .stepsStatus(Arrays.asList(stepStatus))
                .build();
        Map<String, UserStepStatus> statusMap = new HashMap<>();
        statusMap.put(SAVINGS_ACCOUNT_GRAPH_NAME, userStepStatus);


        return UserStatus.builder()
                .leadTrackingNumber(LEAD_ID)
                .bankLeadTrackingNumber(BANK_LEAD_ID)
                .crn(TEST_CRN)
                .phoneNumber(PHONE_NUMBER)
                .panNumber(PAN_NUMBER)
                .journeyType(JOURNEY_TYPE)
                .journeyMetadata(journeyMetadata)
                .journeyStatus(JourneyStatus.ACCOUNT_OPENED_FKYC_DONE)
                .journeyStatusReasonCode(NEW_LEAD_CREATED_JOURNEY_STATUS_REASON_CODE)
                .userIp(USER_IP)
                .productId(TestUtils.PRODUCT_ID)
                .status(statusMap)
                .createdTime(Instant.now())
                .lastModifiedTime(Instant.now())
                .executionData(executionData)
                .build();
    }

    public static UserStatus getUserStatusWithPersonalLoanJourneyMetadata() {
        GeoLocation geoLocation = GeoLocation.builder().latLongCity("TestCity")
                .latitudeAndLongitude("12.2;231.4")
                .latLongCountry("TestCountry")
                .latLongState("TestState")
                .build();
        VKYCDetails vkycDetails = VKYCDetails.builder()
                .geoLocation(geoLocation)
                .build();

        Address communicationAddress = Address.builder()
                .line1(TEST_ADDRESS_LINE1)
                .line2(TEST_ADDRESS_LINE2)
                .line3(TEST_ADDRESS_LINE3)
                .city(TEST_ADDRESS_CITY)
                .state(TEST_ADDRESS_STATE)
                .pincode(TEST_ADDRESS_PINCODE)
                .cityCode("cc1")
                .finacleCityCode("fcc1")
                .stateCode("sc1")
                .finacleStateCode("fsc1")
                .country("coun1")
                .build();

        AadhaarDetails aadhaarDetails = AadhaarDetails.builder()
                .responseCode("001")
                .transactionId("1234")
                .locality("locality")
                .tkn("tkn")
                .code("code")
                .name(TEST_AADHAAR_FULL_NAME)
                .dob(TEST_AADHAAR_DOB)
                .gender(TEST_AADHAAR_GENDER)
                .photo(TEST_AADHAAR_PHOTO)
                .house(TEST_AADHAAR_HOUSE)
                .careOf(TEST_AADHAAR_CARE_OF)
                .landmark(TEST_AADHAAR_LANDMARK)
                .district(TEST_AADHAAR_DISTRICT)
                .pincode(TEST_ADDRESS_PINCODE)
                .state(TEST_ADDRESS_STATE)
                .street(TEST_AADHAAR_STREET)
                .vtc(TEST_AADHAAR_VTC)
                .refKey(AADHAAR_REFKEY)
                .verifiedAt(Instant.now())
                .derivedAadhaarDetails(
                        DerivedAadhaarDetails
                                .builder()
                                .firstName("f1")
                                .middleName("m1")
                                .lastName("l1")
                                .fullName("f1m1l1")
                                .address(Address.builder().city("c1").line1("l1")
                                        .line2("l2")
                                        .line3("l3")
                                        .pincode("p1")
                                        .state("s1")
                                        .stateCode("sc1")
                                        .country("c1")
                                        .cityCode("c1")
                                        .finacleCityCode("fc1")
                                        .finacleStateCode("fs1")
                                        .build())
                                .build()
                )
                .build();

        PanDetails panDetails = PanDetails.builder()
                .statusFlag("mockFlag")
                .validFlag("mockValid")
                .lastName("Doe")
                .firstName("John")
                .middleName("Ron")
                .title("Mr.")
                .updateDate("12.12.2012")
                .displayName("John Ron Doe")
                .aadhaarSeed("Y")
                .number(PAN_NUMBER)
                .build();

        AdditionalDetails additionalDetails = AdditionalDetails.builder()
                .motherName("motherName")
                .maritalStatus("maritalStatus")
                .build();

        CrnNameMap crnNameMap = CrnNameMap.builder()
                .crn("crn")
                .crnName("crnName")
                .crnId("crnId")
                .build();

        CrnDetails crnDetails = CrnDetails.builder()
                .availableCrn(Collections.singletonList(crnNameMap))
                .selectedCrn(crnNameMap)
                .build();

        IncomeDetails incomeDetails = IncomeDetails.builder()
                .monthlyIncome("100000000")
                .modeOfSalary("mode")
                .build();

        ApplicantDetails applicantDetails = ApplicantDetails.builder()
                .aadhaarDetails(aadhaarDetails)
                .panDetails(panDetails)
                .vkycDetails(vkycDetails)
                .additionalDetails(additionalDetails)
                .crnDetails(crnDetails)
                .incomeDetails(incomeDetails)
                .apac("apac")
                .communicationAddressSameAsAddress(PLAddressType.AADHAAR_ADDRESS)
                .address(communicationAddress)
                .emailId("email")
                .transactionId("txnId")
                .build();

        LoanIntent loanIntent = LoanIntent.builder()
                .desiredLoanAmount("10000")
                .desiredLoanTenure("12")
                .build();

        PLProductDetails plProductDetails = PLProductDetails.builder()
                .productType("productType")
                .productJourney("productJourney")
                .lineOfBusiness("lineOfBusiness")
                .source("source")
                .schemeCode("schemeCode")
                .subSource("subSource")
                .productName("productName")
                .build();

        PersonalLoanJourneyMetadata journeyMetadata = PersonalLoanJourneyMetadata.builder()
                .applicantDetails(applicantDetails)
                .loanIntent(loanIntent)
                .productDetails(plProductDetails)
                .plApplicationMileStone(PLApplicationMileStone.APPLICATION_CREATED)
                .blockedReasons(Arrays.asList("1000", "2000"))
                .build();

        Instant ncifVerificationTime = Instant.now().minus(71, ChronoUnit.HOURS);
        ExecutionData executionData = ExecutionData.builder()
                .addFundsAttempts(3)
                .emailOTPRetryLastAttemptedTime(Instant.now())
                .aadhaarRetryLastAttemptedTime(Instant.now())
                .emailOTPResendAttempts(4)
                .ncifLastVerifiedTime(ncifVerificationTime)
                .emailOTPWrongAttempts(5)
                .addFundsLastAttemptedTime(Instant.now())
                .isFirstTimeAadhaarOTPSent(true)
                .isInCompleteDOB(true)
                .isMobileOtpAlreadySent(false)
                .isNomineeSkipped(true)
                .isPanAadhaarLinked(true)
                .isFirstTimeEmailOTPSent(true)
                .emailValidationRetryAttemptsLeft(5)
                .verificationUnblockTime(Instant.now().plus(4,ChronoUnit.HOURS))
                .mobileOTPResendAttempts(3)
                .mobileOTPWrongAttempts(6)
                .otpId("e292")
                .otpResendAttemptsLeft(5)
                .otpRetryAttemptsLeft(2)
                .isEmailAddressVerified(true)
                .build();

        StepStatus stepStatus = StepStatus.builder()
                .name(GET_PHONE_NUMBER_NODE_NAME)
                .status(Status.INITIATED)
                .lastUpdatedTime(TestUtils.TEST_LAST_UPDATED_TIME)
                .actionComplete(TEST_ACTION_COMPLETE)
                .build();
        UserStepStatus userStepStatus = UserStepStatus.builder().status(Status.COMPLETED)
                .lastUpdatedTime(TestUtils.TEST_LAST_UPDATED_TIME)
                .isAsyncUIStep(true)
                .stepsStatus(Collections.singletonList(stepStatus))
                .build();
        Map<String, UserStepStatus> statusMap = new HashMap<>();
        statusMap.put(PERSONAL_LOAN_JOURNEY_TYPE, userStepStatus);


        return UserStatus.builder()
                .leadTrackingNumber(LEAD_ID)
                .bankLeadTrackingNumber(BANK_LEAD_ID)
                .phoneNumber(PHONE_NUMBER)
                .panNumber(PAN_NUMBER)
                .journeyType(PERSONAL_LOAN_JOURNEY_TYPE)
                .journeyMetadata(journeyMetadata)
                .journeyStatus(JourneyStatus.ACCOUNT_OPENED_FKYC_DONE)
                .journeyStatusReasonCode(NEW_LEAD_CREATED_JOURNEY_STATUS_REASON_CODE)
                .userIp(USER_IP)
                .productId(TestUtils.PRODUCT_ID)
                .status(statusMap)
                .createdTime(Instant.now())
                .lastModifiedTime(Instant.now())
                .executionData(executionData)
                .build();
    }

    public static UserStatusResponse getUserStatusResponseWithSavingsAccountJourneyMetadata() {
        DocumentsResponse vkycDocuments = DocumentsResponse.builder()
                .panImage(TEST_PAN_IMAGE)
                .selfie(TEST_SELFIE)
                .signatureImage(TEST_SIGNATURE)
                .build();
        GeoLocationResponse geoLocation = GeoLocationResponse.builder().latLongCity("TestCity")
                .latitudeAndLongitude("12.2;231.4")
                .latLongCountry("TestCountry")
                .latLongState("TestState")
                .build();
        VKYCDetailsResponse vkycDetails = VKYCDetailsResponse.builder()
                .documents(vkycDocuments)
                .geoLocation(geoLocation)
                .build();

        AddressResponse communicationAddress = AddressResponse.builder()
                .line1(TEST_ADDRESS_LINE1)
                .line2(TEST_ADDRESS_LINE2)
                .line3(TEST_ADDRESS_LINE3)
                .city(TEST_ADDRESS_CITY)
                .state(TEST_ADDRESS_STATE)
                .pincode(TEST_ADDRESS_PINCODE)
                .cityCode("cc1")
                .finacleCityCode("fcc1")
                .stateCode("sc1")
                .finacleStateCode("fsc1")
                .country("coun1")
                .build();

        AadhaarDetailsResponse aadhaarDetails = AadhaarDetailsResponse.builder()
                .name(TEST_AADHAAR_FULL_NAME)
                .DOB(TEST_AADHAAR_DOB)
                .gender(TEST_AADHAAR_GENDER)
                .photo(TEST_AADHAAR_PHOTO)
                .house(TEST_AADHAAR_HOUSE)
                .careOf(TEST_AADHAAR_CARE_OF)
                .landmark(TEST_AADHAAR_LANDMARK)
                .district(TEST_AADHAAR_DISTRICT)
                .pincode(TEST_ADDRESS_PINCODE)
                .state(TEST_ADDRESS_STATE)
                .street(TEST_AADHAAR_STREET)
                .vtc(TEST_AADHAAR_VTC)
                .refKey(AADHAAR_REFKEY)
                .verifiedAt(Instant.now())
                .derivedAadhaarDetails(
                        DerivedAadhaarDetailsResponse
                                .builder()
                                .firstName("f1")
                                .middleName("m1")
                                .lastName("l1")
                                .fullName("f1m1l1")
                                .address(AddressResponse.builder().city("c1").line1("l1")
                                        .line2("l2")
                                        .line3("l3")
                                        .pincode("p1")
                                        .state("s1")
                                        .stateCode("sc1")
                                        .country("c1")
                                        .finacleCityCode("fc1")
                                        .finacleStateCode("fs1")
                                        .build())
                                .build()
                )
                .build();

        PanDetailsResponse panDetails = PanDetailsResponse.builder()
                .statusFlag("mockFlag")
                .validFlag("mockValid")
                .lastName("Doe")
                .firstName("John")
                .middleName("Ron")
                .title("Mr.")
                .updateDate("12.12.2012")
                .displayName("John Ron Doe")
                .aadhaarSeed("Y")
                .number(PAN_NUMBER)
                .build();

        ProductDetailsResponse productDetails = ProductDetailsResponse.builder()
                .fullKYCSchemeCode("K_ONE")
                .productType("Regular Savings Account")
                .minimumBalanceAmount(123)
                .limitedKYCMaximumBalanceAmount(1211)
                .promoCode("p1")
                .rmCode("r1")
                .lcCode("lc1")
                .lgCode("lg1")
                .rmCode("rm1")
                .productType("prd1")
                .activeMoneySweepOutAmount(1345)
                .campaignName("cam1")
                .channel("ch1")
                .corpCode("corp1")
                .source("s1")
                .lineOfBusiness("lob1")
                .build();

        NomineeDetailsResponse nm = NomineeDetailsResponse.builder()
                .DOB("dob1")
                .name("nam1")
                .relationshipType("rel1")
                .guardianDetails(GuardianDetailsResponse.builder().name("nam2")
                        .DOB("dob2")
                        .guardianAddress(AddressResponse.builder()
                                .line1("l11")
                                .line2("l22")
                                .line3("l223")
                                .city("ci1")
                                .country("coun1")
                                .finacleCityCode("ficc1")
                                .finacleStateCode("fics1")
                                .cityCode("cicc1")
                                .stateCode("socc1")
                                .build())
                        .build())
                .nomineeAddress(AddressResponse.builder()
                        .line1("nl11")
                        .line2("nl22")
                        .line3("nl223")
                        .city("nci1")
                        .country("ncoun1")
                        .finacleCityCode("nficc1")
                        .finacleStateCode("nfics1")
                        .cityCode("ncicc1")
                        .stateCode("nsocc1")
                        .build())
                .build();

        PersonalDetailsResponse pd = PersonalDetailsResponse.builder()
                .annualIncome(TagResponse.builder().code("a1").value("av1").build())
                .maritalStatus(TagResponse.builder().code("m1").value("mv1").build())
                .annualSalary(1132132)
                .corpDetails(CorpDetailsResponse.builder()
                        .corpCode("corpc1").finacleCorpCode("fcorpc1").fullName("fnc").build())
                .motherName("m1")
                .occupation(TagResponse.builder().code("a1").value("av1").build())
                .sourceOfIncome(TagResponse.builder().code("a1").value("av1").build())
                .build();

        SavingsAccountJourneyMetadataResponse journeyMetadata = SavingsAccountJourneyMetadataResponse.builder()
                .crn(TEST_CRN)
                .accountNumber(TEST_ACCOUNT_NUMBER)
                .accountOpeningDate(Instant.now().minus(5, ChronoUnit.DAYS))
                .vkycDetails(vkycDetails)
                .communicationAddress(communicationAddress)
                .isCommunicationAddressSameAsAadhaarAddress(true)
                .activMoneyOptIn(true)
                .emailAddress(TestUtils.MOCK_EMAIL)
                .aadhaarDetails(aadhaarDetails)
                .panDetails(panDetails)
                .productDetails(productDetails)
                .personalDetails(pd)
                .nomineeDetails(nm)
                .build();
        Instant ncifVerificationTime = Instant.now().minus(71, ChronoUnit.HOURS);
        ExecutionDataResponse executionData = ExecutionDataResponse.builder()
                .addFundsAttempts(3)
                .emailOTPRetryLastAttemptedTime(Instant.now())
                .aadhaarRetryLastAttemptedTime(Instant.now())
                .emailOTPResendAttempts(4)
                .ncifLastVerifiedTime(ncifVerificationTime)
                .emailOTPWrongAttempts(5)
                .addFundsLastAttemptedTime(Instant.now())
                .isFirstTimeAadhaarOTPSent(true)
                .isInCompleteDOB(true)
                .isMobileOtpAlreadySent(false)
                .isNomineeSkipped(true)
                .isPanAadhaarLinked(true)
                .isFirstTimeEmailOTPSent(true)
                .emailValidationRetryAttemptsLeft(5)
                .verificationUnblockTime(Instant.now().plus(4,ChronoUnit.HOURS))
                .mobileOTPResendAttempts(3)
                .mobileOTPWrongAttempts(6)
                .otpId("e292")
                .otpResendAttemptsLeft(5)
                .otpRetryAttemptsLeft(2)
                .build();

        StepStatusResponse stepStatus = StepStatusResponse.builder()
                .name(GET_PHONE_NUMBER_NODE_NAME)
                .status(Status.INITIATED.toString())
                .lastUpdatedTime(TestUtils.TEST_LAST_UPDATED_TIME)
                .actionComplete(TEST_ACTION_COMPLETE)
                .build();
        UserStepStatusResponse userStepStatus = UserStepStatusResponse.builder()
                .lastUpdatedTime(TestUtils.TEST_LAST_UPDATED_TIME)
                .stepsStatus(Arrays.asList(stepStatus))
                .build();
        Map<String, UserStepStatusResponse> statusMap = new HashMap<>();
        statusMap.put(SAVINGS_ACCOUNT_GRAPH_NAME, userStepStatus);


        return UserStatusResponse.builder()
                .leadTrackingNumber(LEAD_ID)
                .bankLeadTrackingNumber(BANK_LEAD_ID)
                .crn(TEST_CRN)
                .phoneNumber(PHONE_NUMBER)
                .panNumber(PAN_NUMBER)
                .journeyType(JOURNEY_TYPE)
                .journeyMetadata(journeyMetadata)
                .journeyStatus(JourneyStatusResponse.ACCOUNT_OPENED_FKYC_DONE)
                .journeyStatusReasonCode(NEW_LEAD_CREATED_JOURNEY_STATUS_REASON_CODE)
                .userIp(USER_IP)
                .productId(TestUtils.PRODUCT_ID)
                .status(statusMap)
                .createdTime(Instant.now())
                .lastModifiedTime(Instant.now())
                .executionData(executionData)
                .build();
    }

    public static UserStatus getUserStatusWithDormancyJourneyMetadata() {
        EtbDetails etbDetails = EtbDetails.builder().crnList(Arrays.asList("crn123","crn456")).build();
        EtbAccountDetail etbAccountDetail1 = EtbAccountDetail.builder().accountNumber("ac1").branchCode("brc1").schemeCode("sch1")
                .build();
        EtbAccountDetail etbAccountDetail2 = EtbAccountDetail.builder().accountNumber("ac2").branchCode("brc2").schemeCode("sch2")
                .build();
        EtbAccountDetail etbAccountDetail3 = EtbAccountDetail.builder().accountNumber("ac3").branchCode("brc3").schemeCode("sch3")
                .build();
        etbDetails.setEtbAccountDetailList(Arrays.asList(etbAccountDetail1, etbAccountDetail2, etbAccountDetail3));
        DormantAccountActivationJourneyMetadata journeyMetadata = DormantAccountActivationJourneyMetadata.builder()
                .etbDetails(etbDetails)
                .validDormantAccounts(Arrays.asList(etbAccountDetail1, etbAccountDetail2))
                .productSpecifications(DormantAccountActivationProductSpecifications.builder()
                        .rmCode("dummyRMCode")
                        .lcCode("dummyRMCode")
                        .lcName("dummyLcName")
                        .campaignName("campaignName")
                        .channel("dummyChannel")
                        .source("dummySource")
                        .build()
                )
                .build();
        return getUserStatus(DORMANT_ACCOUNT_GRAPH_NAME, journeyMetadata);
    }

    private static UserStatus getUserStatus(String journeyType, JourneyMetadata journeyMetadata)
    {
        StepStatus stepStatus = StepStatus.builder()
                .name(GET_PHONE_NUMBER_NODE_NAME)
                .status(Status.INITIATED)
                .lastUpdatedTime(TestUtils.TEST_LAST_UPDATED_TIME)
                .actionComplete(TEST_ACTION_COMPLETE)
                .build();
        UserStepStatus userStepStatus = UserStepStatus.builder().status(Status.COMPLETED)
                .lastUpdatedTime(TestUtils.TEST_LAST_UPDATED_TIME)
                .isAsyncUIStep(true)
                .stepsStatus(Collections.singletonList(stepStatus))
                .build();
        Map<String, UserStepStatus> statusMap = new HashMap<>();
        statusMap.put(DORMANT_ACCOUNT_GRAPH_NAME, userStepStatus);

        ExecutionData executionData = ExecutionData.builder()
                .addFundsAttempts(3)
                .emailOTPRetryLastAttemptedTime(Instant.now())
                .aadhaarRetryLastAttemptedTime(Instant.now())
                .emailOTPResendAttempts(4)
                .ncifLastVerifiedTime(Instant.now())
                .emailOTPWrongAttempts(5)
                .addFundsLastAttemptedTime(Instant.now())
                .isFirstTimeAadhaarOTPSent(true)
                .isInCompleteDOB(true)
                .isMobileOtpAlreadySent(false)
                .isNomineeSkipped(true)
                .isPanAadhaarLinked(true)
                .isFirstTimeEmailOTPSent(true)
                .emailValidationRetryAttemptsLeft(5)
                .verificationUnblockTime(Instant.now().plus(4,ChronoUnit.HOURS))
                .mobileOTPResendAttempts(3)
                .mobileOTPWrongAttempts(6)
                .otpId("e292")
                .otpResendAttemptsLeft(5)
                .otpRetryAttemptsLeft(2)
                .isEmailAddressVerified(true)
                .build();

        return UserStatus.builder()
                .leadTrackingNumber(LEAD_ID)
                .bankLeadTrackingNumber(BANK_LEAD_ID)
                .phoneNumber(PHONE_NUMBER)
                .panNumber(PAN_NUMBER)
                .journeyType(PERSONAL_LOAN_JOURNEY_TYPE)
                .journeyMetadata(journeyMetadata)
                .journeyStatus(JourneyStatus.ACCOUNT_OPENED_FKYC_DONE)
                .journeyStatusReasonCode(NEW_LEAD_CREATED_JOURNEY_STATUS_REASON_CODE)
                .userIp(USER_IP)
                .productId(TestUtils.PRODUCT_ID)
                .status(statusMap)
                .createdTime(Instant.now())
                .lastModifiedTime(Instant.now())
                .executionData(executionData)
                .build();
    }

    public static CreateUKYCRecordRequest getCreateUKYCRecordRequestWithVideoKYC() {
        return CreateUKYCRecordRequest.builder()
                .applicationId(TEST_APPLICATION_ID)
                .aadhaarRefKey(TEST_AADHAAR_REF_KEY)
                .journeyType(TEST_JOURNEY_TYPE)
                .kycChannel(KYCChannelDTO.VIDEO_KYC)
                .kycStatus(KYCStatusDTO.INITIATED)
                .actionTrackingId(TEST_ACTION_TRACKING_ID)
                .clientId(TEST_CLIENT_ID)
                .actor(TEST_ACTOR)
                .build();
    }

    public static UpdateUKYCRecordRequest getUpdateUKYCRecordRequestWithVideoKYC() {
        VideoKYCMetadataDTO videoKYCMetadata = VideoKYCMetadataDTO.builder()
                .agentId(TEST_AGENT_ID)
                .build();
        return UpdateUKYCRecordRequest.builder()
                .crn(TEST_CRN)
                .kycMetadata(videoKYCMetadata)
                .kycChannel(KYCChannelDTO.VIDEO_KYC)
                .kycStatus(KYCStatusDTO.COMPLETED)
                .actionTrackingId(TEST_ACTION_TRACKING_ID)
                .actor(TEST_ACTOR_1)
                .build();

    }

    public static UniversalKYCDetails getUniversalKYCDetailsWithVideoKYC() {
        VideoKYCMetadata videoKYCMetadata = VideoKYCMetadata.builder()
                .agentId(TEST_AGENT_ID)
                .build();
        UKYCChannelStatus ukycChannelStatus = UKYCChannelStatus.builder()
                .kycStatus(KYCStatus.INITIATED)
                .kycChannel(KYCChannel.VIDEO_KYC)
                .kycMetadata(videoKYCMetadata)
                .actionTrackingId(TEST_ACTION_TRACKING_ID)
                .build();

        return UniversalKYCDetails.builder()
                .aadhaarRefKey(TEST_AADHAAR_REF_KEY)
                .applicationId(TEST_APPLICATION_ID)
                .journeyType(TEST_JOURNEY_TYPE)
                .ukycStatus(UKYCStatus.INITIATED)
                .clientId(TEST_CLIENT_ID)
                .createdBy(TEST_ACTOR)
                .ukycChannelStatusList(List.of(ukycChannelStatus))
                .build();
    }

    public static UniversalKYCDetails getUniversalKYCDetailsWithNoKYCChannel() {

        return UniversalKYCDetails.builder()
                .aadhaarRefKey(TEST_AADHAAR_REF_KEY)
                .applicationId(TEST_APPLICATION_ID)
                .journeyType(TEST_JOURNEY_TYPE)
                .clientId(TEST_CLIENT_ID)
                .createdBy(TEST_ACTOR)
                .ukycChannelStatusList(new ArrayList<>())
                .build();
    }

    public static final DataChangeNotificationMessage DATA_CHANGE_NOTIFICATION_MESSAGE = DataChangeNotificationMessage
            .builder()
            .dataSourceTrackingId(LEAD_ID)
            .dataSource(DataSource.USER_JOURNEY_STATUS)
            .build();

    public static CpvDvuVerificationDetailsDto getMockedCpvDvuVerificationDetailsDto() {
        return CpvDvuVerificationDetailsDto.builder()
                .leadTrackingNumber("l1")
                .actionTrackingId("a1")
                .version(1)
                .cpvCode("c1")
                .dvuCode("d1")
                .createdAt(Instant.now())
                .lastModifiedAt(Instant.now())
                .latestStatus("DVU Rejected")
                .lastStatusEventRecordedAt(Instant.now())
                .cpvDvuVerificationMetadataDto(NRCpvDvuVerificationMetadataDto.builder()
                        .rejectionReason("Rejection res1")
                        .sectionsRejected(Arrays.asList("Communication", "Personal"))
                        .build())
                .build();
    }

    public static CpvDvuVerificationDetailsDDBModel getMockedCpvDvuVerificationDetailsDDB() {
        return CpvDvuVerificationDetailsDDBModel.builder()
                .leadTrackingNumber("l1")
                .actionTrackingId("a1")
                .version(1)
                .cpvCode("c1")
                .dvuCode("d1")
                .createdAt(Instant.now())
                .lastModifiedAt(Instant.now())
                .latestStatus("DVU Rejected")
                .lastStatusEventRecordedAt(Instant.now())
                .cpvDvuVerificationMetadata(NRCpvDvuVerificationMetadata.builder()
                        .rejectionReason("Rejection res1")
                        .sectionsRejected(Arrays.asList("Communication", "Personal"))
                        .build())
                .build();
    }

    public static MutualFundsOnboardingDetailsRequest getMockedMutualFundOnboardingDetailsRequest() {
        List<StepDetailsRequest> stepDetailsDtoList = new ArrayList<>();
        stepDetailsDtoList.add(StepDetailsRequest.builder().stepName("dummyStep1").stepStatus("COMPLETED").build());
        stepDetailsDtoList.add(StepDetailsRequest.builder().stepName("dummyStep2").stepStatus("FAILED").stepReasonDetails("DUMMY_REASON").build());
        return MutualFundsOnboardingDetailsRequest.builder()
                .crn("dummyCrn")
                .eventTrackingId("dummyEventTrackingId")
                .leadTrackingNumber("dummyLeadTrackingNumber")
                .eventType("TRANSACTION_READY")
                .eventMetadataRequest(EventMetadataRequest.builder().stepDetails(stepDetailsDtoList).build())
                .latestEventStatus("INITIATED")
                .createdAt(Instant.now())
                .lastModifiedAt(Instant.now())
                .build();
    }

    public static MutualFundsOnboardingDetailsDDBModel getMockedMutualFundOnboardingDetailsDDBModel() {
        List<StepDetails> stepDetailsList = new ArrayList<>();
        stepDetailsList.add(StepDetails.builder().stepName("dummyStep1").stepStatus("COMPLETED").build());
        stepDetailsList.add(StepDetails.builder().stepName("dummyStep2").stepStatus("FAILED").stepReasonDetails("DUMMY_REASON").build());
        return MutualFundsOnboardingDetailsDDBModel.builder()
                .crn("dummyCrn")
                .eventTrackingId("dummyEventTrackingId")
                .leadTrackingNumber("dummyLeadTrackingNumber")
                .eventType("TRANSACTION_READY")
                .eventMetadata(EventMetadata.builder().stepDetails(stepDetailsList).build())
                .latestEventStatus("INITIATED").createdAt(Instant.now())
                .lastModifiedAt(Instant.now())
                .build();
    }
}
