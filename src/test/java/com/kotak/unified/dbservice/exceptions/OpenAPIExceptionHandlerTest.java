package com.kotak.unified.dbservice.exceptions;

import com.kotak.unified.db.model.ErrorResponse;
import com.kotak.unified.dbservice.enums.ErrorCause;
import com.kotak.unified.dbservice.exceptions.custom.RestException;
import com.kotak.unified.dbservice.model.exception.DatabaseErrorResponse;
import jakarta.validation.ConstraintViolationException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;

import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class OpenAPIExceptionHandlerTest {
    private final OpenAPIExceptionHandler openAPIExceptionHandler = new OpenAPIExceptionHandler();

    @Test
    void handleRestException_ReturnsErrorResponse() {
        RestException restException = new RestException(HttpStatus.INTERNAL_SERVER_ERROR, DatabaseErrorResponse.fromErrorCode(ErrorCause.INTERNAL_SERVER_ERROR));
        ResponseEntity<ErrorResponse> errorResponseResponseEntity = openAPIExceptionHandler.handleRestException(restException);
        Assertions.assertNotNull(errorResponseResponseEntity.getBody());
        Assertions.assertEquals(HttpStatusCode.valueOf(HttpStatus.INTERNAL_SERVER_ERROR.value()), errorResponseResponseEntity.getStatusCode());
        Assertions.assertEquals(ErrorCause.INTERNAL_SERVER_ERROR.getCode(), errorResponseResponseEntity.getBody().getErrorCode());
    }

    @Test
    void handleConstraintViolationException() {
        ConstraintViolationException exception = mock(ConstraintViolationException.class);
        when(exception.getConstraintViolations()).thenReturn(Set.of());

        ResponseEntity<ErrorResponse> response = openAPIExceptionHandler.handleConstraintViolationException(exception);

        assertEquals(ErrorCause.INPUT_VALIDATION_FAILED.getCode(), response.getBody().getErrorCode());
    }

    @Test
    void handleMethodArgumentNotValidException() {
        MethodArgumentNotValidException exception = mock(MethodArgumentNotValidException.class);
        BindingResult bindingResult = mock(BindingResult.class);
        when(exception.getBindingResult()).thenReturn(bindingResult);
        when(bindingResult.getFieldErrors()).thenReturn(List.of());
        var response = openAPIExceptionHandler.handleMethodArgumentNotValidException(exception);
        assertEquals(ErrorCause.INPUT_VALIDATION_FAILED.getCode(), response.getBody().getErrorCode());
    }

    @Test
    void handleMissingParameterException() {
        MissingServletRequestParameterException exception = mock(MissingServletRequestParameterException.class);

        var response = openAPIExceptionHandler.handleMissingParameterException(exception);

        assertEquals(ErrorCause.QUERY_PARAM_NOT_FOUND.getCode(), response.getBody().getErrorCode());
    }

    @Test
    void handleRuntimeException() {
        RuntimeException exception = mock(RuntimeException.class);

        var response = openAPIExceptionHandler.handleRuntimeException(exception);

        assertEquals(ErrorCause.INTERNAL_SERVER_ERROR.getCode(), response.getBody().getErrorCode());
    }

    @Test
    void handleIllegalArgumentException() {
        IllegalArgumentException exception = new IllegalArgumentException("Test exception");

        ResponseEntity<ErrorResponse> response = openAPIExceptionHandler.handleIllegalArgumentException(exception);

        assertNotNull(response.getBody());
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertEquals(ErrorCause.INPUT_VALIDATION_FAILED.getCode(), response.getBody().getErrorCode());
    }


}