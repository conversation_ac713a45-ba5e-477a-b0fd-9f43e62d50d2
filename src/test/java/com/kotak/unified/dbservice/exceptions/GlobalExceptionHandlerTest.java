package com.kotak.unified.dbservice.exceptions;

import com.kotak.unified.common.response.wrapper.ResponseWrapper;
import com.kotak.unified.dbservice.enums.ErrorCause;
import com.kotak.unified.dbservice.exceptions.custom.RestException;
import com.kotak.unified.dbservice.model.exception.DatabaseErrorResponse;
import jakarta.validation.ConstraintViolationException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.bind.MissingServletRequestParameterException;

import java.util.Set;

class GlobalExceptionHandlerTest {
    private final GlobalExceptionHandler globalExceptionHandler = new GlobalExceptionHandler();

    @Test
    void handleRestException() {
        RestException restException = new RestException(HttpStatus.NOT_FOUND, DatabaseErrorResponse.fromErrorCode(ErrorCause.INTERNAL_SERVER_ERROR));
        ResponseEntity<ResponseWrapper<Void>> responseWrapperResponseEntity = globalExceptionHandler.handleRestException(restException);
        Assertions.assertNotNull(responseWrapperResponseEntity.getBody());
        Assertions.assertNull(responseWrapperResponseEntity.getBody().getData());
        Assertions.assertEquals(HttpStatusCode.valueOf(HttpStatus.NOT_FOUND.value()), responseWrapperResponseEntity.getStatusCode());
        Assertions.assertEquals(1, responseWrapperResponseEntity.getBody().getErrors().size());
        Assertions.assertEquals(1, responseWrapperResponseEntity.getBody().getErrors().size());
        Assertions.assertEquals(ErrorCause.INTERNAL_SERVER_ERROR.getCode(), responseWrapperResponseEntity.getBody().getErrors().get(0).getErrorCode());
    }

    @Test
    void handleRestException_InternalServiceException() {
        RestException restException = new RestException(HttpStatus.INTERNAL_SERVER_ERROR, DatabaseErrorResponse.fromErrorCode(ErrorCause.INTERNAL_SERVER_ERROR));
        ResponseEntity<ResponseWrapper<Void>> responseWrapperResponseEntity = globalExceptionHandler.handleRestException(restException);
        Assertions.assertNotNull(responseWrapperResponseEntity.getBody());
        Assertions.assertNull(responseWrapperResponseEntity.getBody().getData());
        Assertions.assertEquals(HttpStatusCode.valueOf(HttpStatus.INTERNAL_SERVER_ERROR.value()), responseWrapperResponseEntity.getStatusCode());
        Assertions.assertEquals(1, responseWrapperResponseEntity.getBody().getErrors().size());
        Assertions.assertEquals(1, responseWrapperResponseEntity.getBody().getErrors().size());
        Assertions.assertEquals(ErrorCause.INTERNAL_SERVER_ERROR.getCode(), responseWrapperResponseEntity.getBody().getErrors().get(0).getErrorCode());
    }

    @Test
    void handleGenericThrowable() {
        Throwable throwable = new Throwable();
        ResponseEntity<ResponseWrapper<Void>> responseWrapperResponseEntity = globalExceptionHandler.handleGenericThrowable(throwable);
        Assertions.assertNotNull(responseWrapperResponseEntity.getBody());
        Assertions.assertNull(responseWrapperResponseEntity.getBody().getData());
        Assertions.assertEquals(HttpStatusCode.valueOf(HttpStatus.INTERNAL_SERVER_ERROR.value()), responseWrapperResponseEntity.getStatusCode());
        Assertions.assertEquals(1, responseWrapperResponseEntity.getBody().getErrors().size());
        Assertions.assertEquals(ErrorCause.INTERNAL_SERVER_ERROR.getCode(), responseWrapperResponseEntity.getBody().getErrors().get(0).getErrorCode());
    }

    @Test
    void handleHttpMediaTypeNotSupportedException() {
        HttpMediaTypeNotSupportedException httpMediaTypeNotSupportedException = new HttpMediaTypeNotSupportedException("");
        ResponseEntity<ResponseWrapper<Void>> responseWrapperResponseEntity = globalExceptionHandler.handleHttpMediaTypeNotSupportedException(httpMediaTypeNotSupportedException);
        Assertions.assertNotNull(responseWrapperResponseEntity.getBody());
        Assertions.assertNull(responseWrapperResponseEntity.getBody().getData());
        Assertions.assertEquals(HttpStatusCode.valueOf(HttpStatus.BAD_REQUEST.value()), responseWrapperResponseEntity.getStatusCode());
        Assertions.assertEquals(1, responseWrapperResponseEntity.getBody().getErrors().size());
        Assertions.assertEquals(ErrorCause.INPUT_VALIDATION_FAILED.getCode(), responseWrapperResponseEntity.getBody().getErrors().get(0).getErrorCode());
    }

    @Test
    void handleConstraintViolationException() {
        ConstraintViolationException constraintViolationException = new ConstraintViolationException(Set.of());
        ResponseEntity<ResponseWrapper<Void>> responseWrapperResponseEntity = globalExceptionHandler.handleConstraintViolationException(constraintViolationException);
        Assertions.assertNotNull(responseWrapperResponseEntity.getBody());
        Assertions.assertNull(responseWrapperResponseEntity.getBody().getData());
        Assertions.assertEquals(HttpStatusCode.valueOf(HttpStatus.BAD_REQUEST.value()), responseWrapperResponseEntity.getStatusCode());
        Assertions.assertEquals(0, responseWrapperResponseEntity.getBody().getErrors().size());
    }

    @Test
    void handleRuntimeException() {
        RuntimeException constraintViolationException = new RuntimeException("No given Enum constant");
        ResponseEntity<ResponseWrapper<Void>> responseWrapperResponseEntity = globalExceptionHandler.handleRuntimeException(constraintViolationException);
        Assertions.assertNotNull(responseWrapperResponseEntity.getBody());
        Assertions.assertNull(responseWrapperResponseEntity.getBody().getData());
        Assertions.assertEquals(HttpStatusCode.valueOf(HttpStatus.INTERNAL_SERVER_ERROR.value()), responseWrapperResponseEntity.getStatusCode());
        Assertions.assertEquals(2, responseWrapperResponseEntity.getBody().getErrors().size());
        Assertions.assertEquals(ErrorCause.INTERNAL_SERVER_ERROR.getCode(), responseWrapperResponseEntity.getBody().getErrors().get(0).getErrorCode());
        Assertions.assertEquals(HttpStatus.INTERNAL_SERVER_ERROR.toString(), responseWrapperResponseEntity.getBody().getErrors().get(1).getErrorCode());
        Assertions.assertEquals("No given Enum constant", responseWrapperResponseEntity.getBody().getErrors().get(1).getErrorMessage());
    }

    @Test
    void test() {
        System.out.println(HttpStatus.FORBIDDEN.toString());
    }

    @Test
    void handleMissingServletRequestParameterException() {
        MissingServletRequestParameterException missingServletRequestParameterException = new MissingServletRequestParameterException("", "");
        ResponseEntity<ResponseWrapper<Void>> responseWrapperResponseEntity = globalExceptionHandler.handleMissingParameterException(missingServletRequestParameterException);
        Assertions.assertNotNull(responseWrapperResponseEntity.getBody());
        Assertions.assertNull(responseWrapperResponseEntity.getBody().getData());
        Assertions.assertEquals(HttpStatusCode.valueOf(HttpStatus.BAD_REQUEST.value()), responseWrapperResponseEntity.getStatusCode());
        Assertions.assertEquals(1, responseWrapperResponseEntity.getBody().getErrors().size());
        Assertions.assertEquals(ErrorCause.QUERY_PARAM_NOT_FOUND.getCode(), responseWrapperResponseEntity.getBody().getErrors().get(0).getErrorCode());
    }

}