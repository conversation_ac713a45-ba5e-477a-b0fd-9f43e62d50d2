package com.kotak.unified.dbservice.model.converter;

import com.kotak.unified.dbservice.model.converter.mf.EventMetadataAttributeConverter;
import com.kotak.unified.dbservice.model.mf.EventMetadata;
import com.kotak.unified.dbservice.model.mf.StepDetails;
import org.junit.Test;
import software.amazon.awssdk.services.dynamodb.model.AttributeValue;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThrows;

public class EventMetadataAttributeConverterTest {
    private static final String INVALID_METADATA_JSON_STRING = "{\"stepDetails\":[\"stepName\":\"Step1\",\"stepStatus\":\"COMPLETED\",\"stepReasonDetails\":null}]})";

    @Test
    public void test_transformFromAndTransformTo() {
        EventMetadataAttributeConverter converter = new EventMetadataAttributeConverter();
        List<StepDetails> stepDetailsList = new ArrayList<>();
        stepDetailsList.add(StepDetails.builder().stepName("Step1").stepStatus("COMPLETED").build());
        stepDetailsList.add(StepDetails.builder().stepName("Step2").stepStatus("FAILED").stepReasonDetails("dummyErrorReason").build());

        EventMetadata metadata = EventMetadata.builder().stepDetails(stepDetailsList).build();
        AttributeValue attributeValue = converter.transformFrom(metadata);
        EventMetadata transformedMetadata = converter.transformTo(attributeValue);
        assertEquals(metadata, transformedMetadata);
    }

    // Throws RuntimeException when transforming to AttributeValue with invalid EventMetadata object
    @Test
    public void test_transformFromWithInvalidMetadata() {
        EventMetadataAttributeConverter converter = new EventMetadataAttributeConverter();
        AttributeValue attributeValue = AttributeValue.builder().s(INVALID_METADATA_JSON_STRING).build();
        assertThrows(RuntimeException.class, () -> converter.transformTo(attributeValue));
    }
}
