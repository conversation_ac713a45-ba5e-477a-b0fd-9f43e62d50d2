package com.kotak.unified.dbservice.validator;

import com.kotak.unified.db.model.BiometricsKYCMetadataDTO;
import com.kotak.unified.db.model.CreateUKYCRecordRequest;
import com.kotak.unified.db.model.KYCChannelDTO;
import com.kotak.unified.db.model.KYCMetadataDTO;
import com.kotak.unified.db.model.KYCStatusDTO;
import com.kotak.unified.db.model.UKYCStatusDTO;
import com.kotak.unified.db.model.UpdateUKYCRecordRequest;
import com.kotak.unified.orchestrator.common.dbmodels.ukycStatusModel.KYCStatus;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import static com.kotak.unified.dbservice.TestUtils.JOURNEY_TYPE;
import static com.kotak.unified.dbservice.TestUtils.TEST_ACTION_TRACKING_ID;
import static org.junit.jupiter.api.Assertions.*;

class UniversalKYCStatusAPIRequestValidatorTest {

    private UniversalKYCStatusAPIRequestValidator validator;

    @BeforeEach
    void setUp() {
        validator = new UniversalKYCStatusAPIRequestValidator();
        ReflectionTestUtils.setField(validator, "ukycAllowedJourneyTypes" , "SavingsAccount,SalaryAccount");

    }

    @Test
    void validate_CreateUKYCRecordRequest_withNullKycChannel() {
        CreateUKYCRecordRequest request = new CreateUKYCRecordRequest();
        request.setKycChannel(null);
        request.journeyType(JOURNEY_TYPE);
        assertDoesNotThrow(() -> validator.validate(request));
    }

    @Test
    void validate_CreateUKYCRecordRequest_withNullKycStatusOrActionTrackingId() {
        CreateUKYCRecordRequest request = new CreateUKYCRecordRequest();
        request.setKycChannel(KYCChannelDTO.VIDEO_KYC);
        request.setKycStatus(null);
        request.setActionTrackingId(null);
        request.journeyType(JOURNEY_TYPE);
        assertThrows(IllegalArgumentException.class, () -> validator.validate(request));
    }

    @Test
    void validate_CreateUKYCRecordRequest_withMismatchedKycMetadataType() {
        KYCMetadataDTO kycMetadataDTO = new BiometricsKYCMetadataDTO();
        kycMetadataDTO.setType("BiometricsKYCMetadataDTO");
        CreateUKYCRecordRequest request = new CreateUKYCRecordRequest();
        request.setKycChannel(KYCChannelDTO.VIDEO_KYC);
        request.setActionTrackingId(TEST_ACTION_TRACKING_ID);
        request.setKycStatus(KYCStatusDTO.INITIATED);
        request.setKycMetadata(kycMetadataDTO);
        request.journeyType(JOURNEY_TYPE);
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> validator.validate(request));
        Assertions.assertEquals("KycMetadata type: BiometricsKYCMetadataDTO does not match kycChannel VIDEO_KYC.", exception.getMessage());

    }

    @Test
    void validate_CreateUKYCRecordRequest_withNonNullKycChannelAndKYCStatus() {
        CreateUKYCRecordRequest request = new CreateUKYCRecordRequest();
        request.setKycChannel(KYCChannelDTO.VIDEO_KYC);
        request.setKycStatus(KYCStatusDTO.INITIATED);
        request.setActionTrackingId(TEST_ACTION_TRACKING_ID);
        request.journeyType(JOURNEY_TYPE);
        assertDoesNotThrow(() -> validator.validate(request));
    }

    @Test
    void validate_CreateUKYCRecordRequest_withNullKycChannelAndNonNullKycStatus_throwsException() {
        CreateUKYCRecordRequest request = new CreateUKYCRecordRequest();
        request.setKycStatus(KYCStatusDTO.INITIATED);
        request.setActionTrackingId(TEST_ACTION_TRACKING_ID);
        request.journeyType(JOURNEY_TYPE);
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> validator.validate(request));
        Assertions.assertEquals("actionTrackingId or kycMetadata cannot be non null when kycChannel is null.", exception.getMessage());
    }

    @Test
    void validate_kycChannel_kycMetadata_compatibility() {
        KYCMetadataDTO kycMetadata = BiometricsKYCMetadataDTO.builder().agentId("agentId").build();
        kycMetadata.setType("BiometricsKYCMetadataDTO");
        UpdateUKYCRecordRequest request = UpdateUKYCRecordRequest.builder()
                .kycChannel(KYCChannelDTO.VIDEO_KYC)
                .actionTrackingId(TEST_ACTION_TRACKING_ID)
                .kycStatus(KYCStatusDTO.INITIATED)
                .kycMetadata(kycMetadata)
                .build();
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> validator.validate(request));
        Assertions.assertEquals("KycMetadata type: BiometricsKYCMetadataDTO does not match kycChannel VIDEO_KYC.", exception.getMessage());
    }

    @Test
    void validateUpdateUKYCRecordRequest_getUkycStatus_notNull_ActionTrackingId_present() {
        UpdateUKYCRecordRequest request = UpdateUKYCRecordRequest.builder()
                .ukycStatus(UKYCStatusDTO.ABORTED)
                .actionTrackingId(TEST_ACTION_TRACKING_ID)
                .build();
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> validator.validate(request));
        Assertions.assertEquals("ActionTrackingId, KycChannel and KycStatus must be null, when ukycStatus is present.", exception.getMessage());
    }

    @Test
    void validateUpdateUKYCRecordRequest_getUkycStatus_notNull_KycChannel_present() {
        UpdateUKYCRecordRequest request = UpdateUKYCRecordRequest.builder()
                .ukycStatus(UKYCStatusDTO.ABORTED)
                .kycChannel(KYCChannelDTO.BIOMETRICS_KYC)
                .build();
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> validator.validate(request));
        Assertions.assertEquals("ActionTrackingId, KycChannel and KycStatus must be null, when ukycStatus is present.", exception.getMessage());
    }

    @Test
    void validateUpdateUKYCRecordRequest_UkycStatusMarkedAborted_notNull_KycStatus_present() {
        UpdateUKYCRecordRequest request = UpdateUKYCRecordRequest.builder()
                .ukycStatus(UKYCStatusDTO.ABORTED)
                .kycStatus(KYCStatusDTO.ABORTED)
                .build();
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> validator.validate(request));
        Assertions.assertEquals("ActionTrackingId, KycChannel and KycStatus must be null, when ukycStatus is present.", exception.getMessage());
    }

    @Test
    void validateUpdateUKYCRecordRequest_UkycStatusMarkedPending_notNull_KycStatus_present() {
        UpdateUKYCRecordRequest request = UpdateUKYCRecordRequest.builder()
                .ukycStatus(UKYCStatusDTO.PENDING)
                .kycStatus(KYCStatusDTO.ABORTED)
                .build();
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> validator.validate(request));
        Assertions.assertEquals("ActionTrackingId, KycChannel and KycStatus must be null, when ukycStatus is present.", exception.getMessage());
    }

    @Test
    void validateUpdateUKYCRecordRequest_InvalidUkycStatus() {
        UpdateUKYCRecordRequest request = UpdateUKYCRecordRequest.builder()
                .ukycStatus(UKYCStatusDTO.COMPLETED)
                .build();
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> validator.validate(request));
        Assertions.assertEquals("Invalid ukycstatus value for update request.", exception.getMessage());
    }

    @Test
    void validateUpdateUKYCRecordRequest_getUkycStatus_Null_ActionTrackingId_null() {
        UpdateUKYCRecordRequest request = UpdateUKYCRecordRequest.builder()
                .kycChannel(KYCChannelDTO.BIOMETRICS_KYC)
                .kycStatus(KYCStatusDTO.ABORTED)
                .build();
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> validator.validate(request));
        Assertions.assertEquals("ActionTrackingId must be not null.", exception.getMessage());
    }

    @Test
    void validateUpdateUKYCRecordRequest_getUkycStatus_Null_kycChannel_null() {
        UpdateUKYCRecordRequest request = UpdateUKYCRecordRequest.builder()
                .actionTrackingId(TEST_ACTION_TRACKING_ID)
                .kycStatus(KYCStatusDTO.ABORTED)
                .build();
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> validator.validate(request));
        Assertions.assertEquals("KycChannel must be not null.", exception.getMessage());
    }
}