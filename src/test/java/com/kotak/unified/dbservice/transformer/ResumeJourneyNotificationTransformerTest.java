package com.kotak.unified.dbservice.transformer;

import org.junit.jupiter.api.Test;

import java.util.ArrayList;

import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class ResumeJourneyNotificationTransformerTest {

    private final ResumeJourneyNotificationTransformer resumeJourneyNotificationTransformer = new ResumeJourneyNotificationTransformer();

    @Test
    void convertDTOToActiveScheduledNotificationNudgeDetails_NullCheck() {
        assertNull(resumeJourneyNotificationTransformer.convertDTOToActiveScheduledNotificationNudgeDetails(null));
    }

    @Test
    void convertModelToGetResumeJourneyNotificationStatusRecordResponse_NullCheck() {
        assertNull(resumeJourneyNotificationTransformer.convertModelToGetResumeJourneyNotificationStatusRecordResponse(null));
    }

    @Test
    void convertModelToActiveScheduledNotificationNudgeDetailsDTO_NullCheck() {
        assertNull(resumeJourneyNotificationTransformer.convertModelToActiveScheduledNotificationNudgeDetailsDTO(null));
    }

    @Test
    void convertDTOToScheduledNotificationNudgeDetails_NullCheck() {
        assertNull(resumeJourneyNotificationTransformer.convertDTOToScheduledNotificationNudgeDetails(null));
    }

    @Test
    void convertModelToScheduledNotificationNudgeDetailsDTO_NullCheck() {
        assertNull(resumeJourneyNotificationTransformer.convertModelToScheduledNotificationNudgeDetailsDTO(null));
    }

    @Test
    void convertModelToScheduledNotificationNudgeDetailsDTOList_NullCheck() {
        assertTrue(resumeJourneyNotificationTransformer.convertModelToScheduledNotificationNudgeDetailsDTOList(null).isEmpty());
    }

    @Test
    void convertModelToScheduledNotificationNudgeDetailsDTOList_Empty_NullCheck() {
        assertTrue(resumeJourneyNotificationTransformer.convertModelToScheduledNotificationNudgeDetailsDTOList(new ArrayList<>()).isEmpty());
    }

    @Test
    void convertDTOToScheduledNotificationNudgeDetailsList_NullCheck() {
        assertTrue(resumeJourneyNotificationTransformer.convertDTOToScheduledNotificationNudgeDetailsList(null).isEmpty());
    }

    @Test
    void convertDTOToScheduledNotificationNudgeDetailsList_Empty_NullCheck() {
        assertTrue(resumeJourneyNotificationTransformer.convertDTOToScheduledNotificationNudgeDetailsList(new ArrayList<>()).isEmpty());
    }

    @Test
    void convertModelToActiveScheduledNotificationNudgeDetailsDTOList_NullCheck() {
        assertTrue(resumeJourneyNotificationTransformer.convertDTOToScheduledNotificationNudgeDetailsList(null).isEmpty());
    }

    @Test
    void convertModelToActiveScheduledNotificationNudgeDetailsDTOList_Empty_NullCheck() {
        assertTrue(resumeJourneyNotificationTransformer.convertDTOToScheduledNotificationNudgeDetailsList(new ArrayList<>()).isEmpty());
    }
}
