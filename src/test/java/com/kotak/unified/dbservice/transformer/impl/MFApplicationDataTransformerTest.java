package com.kotak.unified.dbservice.transformer.impl;

import com.kotak.unified.common.exception.InvalidRequestException;
import com.kotak.unified.db.ApplicationData;
import com.kotak.unified.db.ApplicationDataFilter;
import com.kotak.unified.db.GetApplicationDataRequest;
import com.kotak.unified.db.mf.MFApplicationData;
import com.kotak.unified.dbservice.exceptions.EntityNotFoundException;
import com.kotak.unified.orchestrator.common.dbmodels.MutualFundsJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.SavingsAccountJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.UserStatus;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Assertions;

import java.util.List;

import static com.kotak.unified.dbservice.utils.Constants.MUTUAL_FUNDS;
import static org.junit.jupiter.api.Assertions.assertThrows;

public class MFApplicationDataTransformerTest {
    private final MFApplicationDataTransformer mfApplicationDataTransformer;

    MFApplicationDataTransformerTest(){
        mfApplicationDataTransformer = new MFApplicationDataTransformer();
    }

    @Test
    public void test_getProduct(){
        Assertions.assertEquals(MUTUAL_FUNDS, mfApplicationDataTransformer.getProduct());
    }

    @Test
    public void test_populateApplicationData_success() throws InvalidRequestException, EntityNotFoundException {
        UserStatus mockUserStatus = UserStatus.builder()
                .journeyMetadata(MutualFundsJourneyMetadata.builder()
                        .isOnboarded(true)
                        .build())
                .leadTrackingNumber("l1")
                .build();
        GetApplicationDataRequest request = GetApplicationDataRequest.builder()
                .dataFilters(List.of(ApplicationDataFilter.IS_ONBOARDED)
                )
                .build();
        ApplicationData applicationData = this.mfApplicationDataTransformer.populateApplicationData(request, mockUserStatus);
        MFApplicationData mFApplicationData = (MFApplicationData) applicationData;
        Assertions.assertNotNull(mFApplicationData);
        Assertions.assertInstanceOf(MFApplicationData.class, applicationData);
        Assertions.assertTrue(mFApplicationData.getIsOnboarded());

    }

    @Test
    public void test_populateApplicationData_failure_null_journeyMetadata() throws InvalidRequestException, EntityNotFoundException {
        UserStatus mockUserStatus = UserStatus.builder()
                .journeyMetadata(null)
                .leadTrackingNumber("l1")
                .build();
        GetApplicationDataRequest request = GetApplicationDataRequest.builder()
                .dataFilters(List.of(ApplicationDataFilter.IS_ONBOARDED)
                )
                .build();

        EntityNotFoundException re = assertThrows(EntityNotFoundException.class,
                () -> this.mfApplicationDataTransformer.populateApplicationData(request, mockUserStatus));
        Assertions.assertNotNull(re);
        Assertions.assertEquals("No application data found for lead id : l1", re.getMessage());
    }

    @Test
    public void test_populateApplicationData_failure_incorrect_type_journeyMetadata(){
        UserStatus mockUserStatus = UserStatus.builder()
                .journeyMetadata(SavingsAccountJourneyMetadata.builder().build())
                .leadTrackingNumber("l1")
                .build();
        GetApplicationDataRequest request = GetApplicationDataRequest.builder()
                .dataFilters(List.of(ApplicationDataFilter.IS_ONBOARDED)
                )
                .build();

        InvalidRequestException re = assertThrows(InvalidRequestException.class,
                () -> this.mfApplicationDataTransformer.populateApplicationData(request, mockUserStatus));
        Assertions.assertNotNull(re);
        Assertions.assertEquals("Application Id does not belong to MutualFunds Journey for lead : l1", re.getMessage());
    }
}
