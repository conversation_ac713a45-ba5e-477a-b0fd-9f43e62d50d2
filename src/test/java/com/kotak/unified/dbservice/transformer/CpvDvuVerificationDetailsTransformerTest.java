package com.kotak.unified.dbservice.transformer;

import com.kotak.unified.dbinterface.models.CpvDvuVerificationDetailsDto;
import com.kotak.unified.dbinterface.models.NRCpvDvuVerificationMetadataDto;
import com.kotak.unified.dbservice.TestUtils;
import com.kotak.unified.dbservice.model.cpv.CpvDvuVerificationDetailsDDBModel;
import com.kotak.unified.dbservice.model.cpv.NRCpvDvuVerificationMetadata;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class CpvDvuVerificationDetailsTransformerTest {
    @InjectMocks
    private CpvDvuVerificationDetailsTransformer cpvDvuVerificationDetailsTransformer;

    @Test
    public void test_convert_dto_to_ddb_model_success(){
        CpvDvuVerificationDetailsDto cpvDvuVerificationDetailsDto = TestUtils.getMockedCpvDvuVerificationDetailsDto();
        CpvDvuVerificationDetailsDDBModel cpvDvuVerificationDetailsDDBModel = this.cpvDvuVerificationDetailsTransformer
                .convertDtoToDDBModel(cpvDvuVerificationDetailsDto);

        Assertions.assertEquals(cpvDvuVerificationDetailsDto.getActionTrackingId(), cpvDvuVerificationDetailsDDBModel.getActionTrackingId());
        Assertions.assertEquals(cpvDvuVerificationDetailsDto.getLeadTrackingNumber(), cpvDvuVerificationDetailsDDBModel.getLeadTrackingNumber());
        Assertions.assertEquals(cpvDvuVerificationDetailsDto.getCpvCode(), cpvDvuVerificationDetailsDDBModel.getCpvCode());
        Assertions.assertEquals(cpvDvuVerificationDetailsDto.getDvuCode(), cpvDvuVerificationDetailsDDBModel.getDvuCode());
        Assertions.assertEquals(cpvDvuVerificationDetailsDto.getLatestStatus(), cpvDvuVerificationDetailsDDBModel.getLatestStatus());
        Assertions.assertEquals(cpvDvuVerificationDetailsDto.getLastStatusEventRecordedAt(), cpvDvuVerificationDetailsDDBModel.getLastStatusEventRecordedAt());

        Assertions.assertNull(cpvDvuVerificationDetailsDDBModel.getCreatedAt());
        Assertions.assertNull(cpvDvuVerificationDetailsDDBModel.getLastModifiedAt());

        Assertions.assertEquals(((NRCpvDvuVerificationMetadataDto) cpvDvuVerificationDetailsDto
                        .getCpvDvuVerificationMetadataDto()).getRejectionReason(),
                ((NRCpvDvuVerificationMetadataDto) cpvDvuVerificationDetailsDto.getCpvDvuVerificationMetadataDto()).getRejectionReason());
        Assertions.assertEquals(((NRCpvDvuVerificationMetadataDto) cpvDvuVerificationDetailsDto
                        .getCpvDvuVerificationMetadataDto()).getSectionsRejected(),
                ((NRCpvDvuVerificationMetadataDto) cpvDvuVerificationDetailsDto.getCpvDvuVerificationMetadataDto()).getSectionsRejected());
    }

    @Test
    public void test_convert_ddbmodel_to_dto_success() {
        CpvDvuVerificationDetailsDDBModel cpvDvuVerificationDetailsDDBModel = TestUtils.getMockedCpvDvuVerificationDetailsDDB();
        CpvDvuVerificationDetailsDto cpvDvuVerificationDetailsDto = this.cpvDvuVerificationDetailsTransformer
                .convertDDBModelToDto(cpvDvuVerificationDetailsDDBModel);

        Assertions.assertEquals(cpvDvuVerificationDetailsDDBModel.getActionTrackingId(), cpvDvuVerificationDetailsDto.getActionTrackingId());
        Assertions.assertEquals(cpvDvuVerificationDetailsDDBModel.getLeadTrackingNumber(),cpvDvuVerificationDetailsDto.getLeadTrackingNumber());
        Assertions.assertEquals(cpvDvuVerificationDetailsDDBModel.getCpvCode(),cpvDvuVerificationDetailsDto.getCpvCode());
        Assertions.assertEquals(cpvDvuVerificationDetailsDDBModel.getDvuCode(),cpvDvuVerificationDetailsDto.getDvuCode());
        Assertions.assertEquals(cpvDvuVerificationDetailsDDBModel.getLatestStatus(),cpvDvuVerificationDetailsDto.getLatestStatus());
        Assertions.assertEquals(cpvDvuVerificationDetailsDDBModel.getLastStatusEventRecordedAt(),cpvDvuVerificationDetailsDto.getLastStatusEventRecordedAt());

        Assertions.assertEquals(cpvDvuVerificationDetailsDDBModel.getCreatedAt(), cpvDvuVerificationDetailsDto.getCreatedAt());
        Assertions.assertEquals(cpvDvuVerificationDetailsDDBModel.getLastModifiedAt(), cpvDvuVerificationDetailsDto.getLastModifiedAt());

        Assertions.assertEquals(((NRCpvDvuVerificationMetadata) cpvDvuVerificationDetailsDDBModel
                        .getCpvDvuVerificationMetadata()).getRejectionReason(),
                ((NRCpvDvuVerificationMetadataDto) cpvDvuVerificationDetailsDto.getCpvDvuVerificationMetadataDto()).getRejectionReason());
        Assertions.assertEquals(((NRCpvDvuVerificationMetadata) cpvDvuVerificationDetailsDDBModel
                        .getCpvDvuVerificationMetadata()).getSectionsRejected(),
                ((NRCpvDvuVerificationMetadataDto) cpvDvuVerificationDetailsDto.getCpvDvuVerificationMetadataDto()).getSectionsRejected());

    }
}
