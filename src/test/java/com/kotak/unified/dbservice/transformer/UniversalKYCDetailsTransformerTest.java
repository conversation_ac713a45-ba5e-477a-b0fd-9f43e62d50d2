package com.kotak.unified.dbservice.transformer;

import com.kotak.unified.db.model.BiometricsKYCMetadataDTO;
import com.kotak.unified.db.model.CreateUKYCRecordRequest;
import com.kotak.unified.db.model.KYCChannelDTO;
import com.kotak.unified.db.model.KYCMetadataDTO;
import com.kotak.unified.db.model.KYCStatusDTO;
import com.kotak.unified.db.model.LocationDTO;
import com.kotak.unified.db.model.UKYCStatusDTO;
import com.kotak.unified.db.model.UpdateUKYCRecordRequest;
import com.kotak.unified.orchestrator.common.dbmodels.ukycStatusModel.BiometricsKYCMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.ukycStatusModel.KYCChannel;
import com.kotak.unified.orchestrator.common.dbmodels.ukycStatusModel.KYCMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.ukycStatusModel.KYCStatus;
import com.kotak.unified.orchestrator.common.dbmodels.ukycStatusModel.Location;
import com.kotak.unified.orchestrator.common.dbmodels.ukycStatusModel.UKYCChannelStatus;
import com.kotak.unified.orchestrator.common.dbmodels.ukycStatusModel.UKYCStatus;
import com.kotak.unified.orchestrator.common.dbmodels.ukycStatusModel.UniversalKYCDetails;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

import static com.kotak.unified.dbservice.TestUtils.TEST_AGENT_ID;
import static com.kotak.unified.dbservice.TestUtils.TEST_LATITUDE;
import static com.kotak.unified.dbservice.TestUtils.TEST_LONGITUDE;
import static org.junit.jupiter.api.Assertions.*;

class UniversalKYCDetailsTransformerTest {

    private final UniversalKYCDetailsTransformer universalKYCDetailsTransformer = new UniversalKYCDetailsTransformer();

    @Test
    void convertModelToKYCMetadata_BiometricKYCMetadataDTO() {
        BiometricsKYCMetadata biometricsKYCMetadata = BiometricsKYCMetadata.builder()
                .agentId(TEST_AGENT_ID)
                .location(Location.builder().longitude(TEST_LONGITUDE).latitude(TEST_LATITUDE).build())
                .build();
        KYCMetadataDTO kycMetadataDTO = universalKYCDetailsTransformer.convertModelToKYCMetadataDTO(biometricsKYCMetadata, KYCChannel.BIOMETRICS_KYC);
        BiometricsKYCMetadataDTO biometricsKYCMetadataDTO = (BiometricsKYCMetadataDTO) kycMetadataDTO;
        assertEquals(TEST_AGENT_ID, biometricsKYCMetadataDTO.getAgentId());
        assertEquals(TEST_LATITUDE, biometricsKYCMetadataDTO.getLocation().getLatitude());
        assertEquals(TEST_LONGITUDE, biometricsKYCMetadataDTO.getLocation().getLongitude());
    }

    @Test
    void convertDTOToKYCMetadata_BiometricKYCMetadata() {
        BiometricsKYCMetadataDTO biometricsKYCMetadataDTO = BiometricsKYCMetadataDTO.builder()
                .agentId(TEST_AGENT_ID)
                .location(LocationDTO.builder().longitude(TEST_LONGITUDE).latitude(TEST_LATITUDE).build())
                .build();
        KYCMetadata kycMetadata = universalKYCDetailsTransformer.convertDTOToKYCMetadata(biometricsKYCMetadataDTO, KYCChannelDTO.BIOMETRICS_KYC);
        BiometricsKYCMetadata biometricsKYCMetadata = (BiometricsKYCMetadata) kycMetadata;
        assertEquals(TEST_AGENT_ID, biometricsKYCMetadata.getAgentId());
        assertEquals(TEST_LATITUDE, biometricsKYCMetadata.getLocation().getLatitude());
        assertEquals(TEST_LONGITUDE, biometricsKYCMetadata.getLocation().getLongitude());
    }

    @Test
    void getAppropriateUKYCStatus_returnsRejected() {
        List<UKYCChannelStatus> ukycChannelStatusList = new ArrayList<>();
        ukycChannelStatusList.add(UKYCChannelStatus.builder().kycStatus(KYCStatus.REJECTED).build());
        ukycChannelStatusList.add(UKYCChannelStatus.builder().kycStatus(KYCStatus.REJECTED).build());
        Assertions.assertEquals(UKYCStatus.REJECTED, universalKYCDetailsTransformer.getAppropriateUKYCStatus(ukycChannelStatusList));
    }

    @Test
    void getAppropriateUKYCStatus_returnsPending() {
        Assertions.assertEquals(UKYCStatus.PENDING, universalKYCDetailsTransformer.getAppropriateUKYCStatus(null));
        Assertions.assertEquals(UKYCStatus.PENDING, universalKYCDetailsTransformer.getAppropriateUKYCStatus(new ArrayList<>()));
        List<UKYCChannelStatus> ukycChannelStatusList = new ArrayList<>();
        ukycChannelStatusList.add(UKYCChannelStatus.builder().kycStatus(KYCStatus.REJECTED).build());
        ukycChannelStatusList.add(UKYCChannelStatus.builder().kycStatus(KYCStatus.PENDING).build());
        Assertions.assertEquals(UKYCStatus.PENDING, universalKYCDetailsTransformer.getAppropriateUKYCStatus(ukycChannelStatusList));
    }

    @Test
    void getAppropriateUKYCStatus_returnsCompleted() {
        List<UKYCChannelStatus> ukycChannelStatusList = new ArrayList<>();
        ukycChannelStatusList.add(UKYCChannelStatus.builder().kycStatus(KYCStatus.REJECTED).build());
        ukycChannelStatusList.add(UKYCChannelStatus.builder().kycStatus(KYCStatus.PENDING).build());
        ukycChannelStatusList.add(UKYCChannelStatus.builder().kycStatus(KYCStatus.COMPLETED).build());
        Assertions.assertEquals(UKYCStatus.COMPLETED, universalKYCDetailsTransformer.getAppropriateUKYCStatus(ukycChannelStatusList));
    }

    @Test
    void getAppropriateUKYCStatus_returnsInitiated() {
        List<UKYCChannelStatus> ukycChannelStatusList = new ArrayList<>();
        ukycChannelStatusList.add(UKYCChannelStatus.builder().kycStatus(KYCStatus.INITIATED).build());
        ukycChannelStatusList.add(UKYCChannelStatus.builder().kycStatus(KYCStatus.INITIATED).build());
        Assertions.assertEquals(UKYCStatus.INITIATED, universalKYCDetailsTransformer.getAppropriateUKYCStatus(ukycChannelStatusList));
    }


    @Test
    void updateUniversalKYCDetails_throwsIllegalArgumentException() {
        UniversalKYCDetails universalKYCDetails = new UniversalKYCDetails();
        universalKYCDetails.setUkycChannelStatusList(new ArrayList<>());
        UKYCChannelStatus ukycChannelStatus = new UKYCChannelStatus();
        ukycChannelStatus.setKycStatus(KYCStatus.COMPLETED);
        ukycChannelStatus.setActionTrackingId("testActionTrackingId");
        universalKYCDetails.getUkycChannelStatusList().add(ukycChannelStatus);

        UpdateUKYCRecordRequest updateUKYCRecordRequest = new UpdateUKYCRecordRequest();
        updateUKYCRecordRequest.setKycStatus(KYCStatusDTO.PENDING);
        updateUKYCRecordRequest.setActionTrackingId("testActionTrackingId");

        assertThrows(IllegalArgumentException.class, () -> {
            universalKYCDetailsTransformer.updateUniversalKYCDetails(universalKYCDetails, updateUKYCRecordRequest);
        });
    }

    @Test
    void test_convertRequestToUniversalKYCDetails_withKYCChannelNull() {
        CreateUKYCRecordRequest createUKYCRecordRequest = CreateUKYCRecordRequest.builder()
                .kycStatus(KYCStatusDTO.REJECTED)
                .build();
        UniversalKYCDetails universalKYCDetails = universalKYCDetailsTransformer.convertRequestToUniversalKYCDetails(createUKYCRecordRequest);
        Assertions.assertEquals(UKYCStatus.REJECTED, universalKYCDetails.getUkycStatus());
        createUKYCRecordRequest = new CreateUKYCRecordRequest();
        universalKYCDetails = universalKYCDetailsTransformer.convertRequestToUniversalKYCDetails(createUKYCRecordRequest);
        Assertions.assertEquals(UKYCStatus.PENDING, universalKYCDetails.getUkycStatus());
    }

    @Test
    void updateUniversalKYCDetails_UpdateToAbortedStatus() {
        UniversalKYCDetails universalKYCDetails = new UniversalKYCDetails();
        universalKYCDetails.setUkycChannelStatusList(new ArrayList<>());
        UKYCChannelStatus ukycChannelStatus = new UKYCChannelStatus();
        ukycChannelStatus.setKycStatus(KYCStatus.INITIATED);
        ukycChannelStatus.setActionTrackingId("testActionTrackingId");
        universalKYCDetails.getUkycChannelStatusList().add(ukycChannelStatus);

        UpdateUKYCRecordRequest updateUKYCRecordRequest = new UpdateUKYCRecordRequest();
        updateUKYCRecordRequest.setUkycStatus(UKYCStatusDTO.ABORTED);

        universalKYCDetailsTransformer.updateUniversalKYCDetails(universalKYCDetails, updateUKYCRecordRequest);

        Assertions.assertEquals(UKYCStatus.ABORTED, universalKYCDetails.getUkycStatus());
    }

    @Test
    void updateUniversalKYCDetails_UpdateToInitiatedStatus() {
        UniversalKYCDetails universalKYCDetails = new UniversalKYCDetails();
        universalKYCDetails.setUkycChannelStatusList(new ArrayList<>());
        UKYCChannelStatus ukycChannelStatus = new UKYCChannelStatus();
        ukycChannelStatus.setKycStatus(KYCStatus.ABORTED);
        ukycChannelStatus.setActionTrackingId("testActionTrackingId");
        universalKYCDetails.getUkycChannelStatusList().add(ukycChannelStatus);

        UpdateUKYCRecordRequest updateUKYCRecordRequest = new UpdateUKYCRecordRequest();
        updateUKYCRecordRequest.setUkycStatus(UKYCStatusDTO.INITIATED);

        universalKYCDetailsTransformer.updateUniversalKYCDetails(universalKYCDetails, updateUKYCRecordRequest);

        Assertions.assertEquals(UKYCStatus.INITIATED, universalKYCDetails.getUkycStatus());
    }

    @Test
    void convertModelToUKYCChannelStatusDTO_NullCheck(){
        Assertions.assertNull(universalKYCDetailsTransformer.convertModelToUKYCChannelStatusDTO(null));
    }

    @Test
    void convertModelToUKYCChannelStatusDTOList_NullCheck(){
        Assertions.assertTrue(universalKYCDetailsTransformer.convertModelToUKYCChannelStatusDTOList(null).isEmpty());
    }

    @Test
    void convertModelToGetUKYCRecordResponse_NullCheck(){
        Assertions.assertNull(universalKYCDetailsTransformer.convertModelToGetUKYCRecordResponse(null));
    }

    @Test
    void convertModelToKYCChannelDTO_NullCheck(){
        Assertions.assertNull(universalKYCDetailsTransformer.convertModelToKYCChannelDTO(null));
    }

    @Test
    void convertModelToKYCStatusDTO_NullCheck(){
        Assertions.assertNull(universalKYCDetailsTransformer.convertModelToKYCStatusDTO(null));
    }

    @Test
    void convertDTOToKYCStatus_NullCheck(){
        Assertions.assertNull(universalKYCDetailsTransformer.convertDTOToKYCStatus(null));
    }

    @Test
    void convertModelToUKYCStatusDTO_NullCheck(){
        Assertions.assertNull(universalKYCDetailsTransformer.convertModelToUKYCStatusDTO(null));
    }

    @Test
    void convertDTOToUKYCStatus_NullCheck(){
        Assertions.assertNull(universalKYCDetailsTransformer.convertDTOToUKYCStatus(null));
    }

    @Test
    void convertModelToKYCMetadataDTO_NullCheck(){
        Assertions.assertNull(universalKYCDetailsTransformer.convertModelToKYCMetadataDTO(null, null));
    }
    @Test
    void convertModelToKYCMetadataDTO_2_NullCheck(){
        Assertions.assertNull(universalKYCDetailsTransformer.convertModelToKYCMetadataDTO(null, KYCChannel.VIDEO_KYC));
    }
    @Test
    void convertModelToKYCMetadataDTO_3_NullCheck(){
        BiometricsKYCMetadata biometricsKYCMetadata = BiometricsKYCMetadata.builder()
                .agentId(TEST_AGENT_ID)
                .location(Location.builder().longitude(TEST_LONGITUDE).latitude(TEST_LATITUDE).build())
                .build();

        Assertions.assertNull(universalKYCDetailsTransformer.convertModelToKYCMetadataDTO(biometricsKYCMetadata, null));
    }

    @Test
    void convertDTOToKYCMetadata_NullCheck(){
        Assertions.assertNull(universalKYCDetailsTransformer.convertDTOToKYCMetadata(null, null));
    }

    @Test
    void convertDTOToKYCMetadata_2_NullCheck(){
        Assertions.assertNull(universalKYCDetailsTransformer.convertDTOToKYCMetadata(null, KYCChannelDTO.VIDEO_KYC));
    }
    @Test
    void convertDTOToKYCMetadata_3_NullCheck(){
        BiometricsKYCMetadataDTO biometricsKYCMetadataDTO = BiometricsKYCMetadataDTO.builder()
                .agentId(TEST_AGENT_ID)
                .location(LocationDTO.builder().longitude(TEST_LONGITUDE).latitude(TEST_LATITUDE).build())
                .build();
        Assertions.assertNull(universalKYCDetailsTransformer.convertDTOToKYCMetadata(biometricsKYCMetadataDTO, null));
    }

    @Test
    void convertDTOToVideoKYCMetadata_NullCheck(){
        Assertions.assertNull(universalKYCDetailsTransformer.convertDTOToVideoKYCMetadata(null));
    }

    @Test
    void convertDTOToBiometricsKYCMetadata_NullCheck(){
        Assertions.assertNull(universalKYCDetailsTransformer.convertDTOToBiometricsKYCMetadata(null));
    }

    @Test
    void convertModelToLocationDTO_NullCheck(){
        Assertions.assertNull(universalKYCDetailsTransformer.convertModelToLocationDTO(null));
    }

    @Test
    void convertDTOToLocation_NullCheck(){
        Assertions.assertNull(universalKYCDetailsTransformer.convertDTOToLocation(null));
    }
}