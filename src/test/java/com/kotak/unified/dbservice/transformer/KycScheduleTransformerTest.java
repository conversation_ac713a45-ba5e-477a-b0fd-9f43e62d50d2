package com.kotak.unified.dbservice.transformer;

import builder.KycScheduleTestDataBuilder;
import com.kotak.unified.common.enums.ScheduleType;
import com.kotak.unified.common.request.Address;
import com.kotak.unified.common.request.BranchDetails;
import com.kotak.unified.common.request.database.KycSchedulingRequest;
import com.kotak.unified.db.AddressResponse;
import com.kotak.unified.db.BranchDetailsResponse;
import com.kotak.unified.db.KycSchedulingDetailsResponse;
import com.kotak.unified.db.KycSchedulingResponse;
import com.kotak.unified.dbservice.model.KycSchedule;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.List;

import static com.kotak.unified.dbservice.TestUtils.TEST_BRANCH_ADDRES;
import static org.junit.Assert.assertThrows;

class KycScheduleTransformerTest {
    private final KycScheduleTransformer kycScheduleTransformer;

    KycScheduleTransformerTest() {
        this.kycScheduleTransformer = new KycScheduleTransformer();
    }


    @Test
    void convertRequestToModelOfSchedule() {
        KycSchedulingRequest kycSchedulingRequest = KycScheduleTestDataBuilder.createDummyKycSchedulingRequest();

        KycSchedule kycSchedule = kycScheduleTransformer.convertRequestToModel(kycSchedulingRequest);

        Assertions.assertEquals(kycSchedulingRequest.getScheduledAt(), kycSchedule.getScheduledAt().toEpochMilli());
        Assertions.assertEquals(kycSchedulingRequest.getLeadTrackingNumber(), kycSchedule.getLeadTrackingId());
        Assertions.assertEquals(kycSchedulingRequest.getAddress().getLine1(), kycSchedule.getAddress().getLine1());
        Assertions.assertEquals(kycSchedulingRequest.getAddress().getLine2(), kycSchedule.getAddress().getLine2());
        Assertions.assertEquals(kycSchedulingRequest.getAddress().getLine3(), kycSchedule.getAddress().getLine3());
        Assertions.assertEquals(kycSchedulingRequest.getAddress().getPincode(), kycSchedule.getAddress().getPincode());
        Assertions.assertEquals(kycSchedulingRequest.getAddress().getCity(), kycSchedule.getAddress().getCity());
        Assertions.assertEquals(kycSchedulingRequest.getAddress().getState(), kycSchedule.getAddress().getState());
    }

    @Test
    void convertModelToResponseOfSchedule() {
        KycSchedule dummyKycSchedule = KycScheduleTestDataBuilder.createDummyKycSchedule();

        KycSchedulingResponse kycSchedulingResponse = kycScheduleTransformer.convertModelToResponse(dummyKycSchedule);

        Assertions.assertEquals(dummyKycSchedule.getScheduledAt().toEpochMilli(), kycSchedulingResponse.getScheduledAt());
        Assertions.assertEquals(dummyKycSchedule.getLeadTrackingId(), kycSchedulingResponse.getLeadTrackingNumber());
        Assertions.assertEquals(dummyKycSchedule.getAddress().getLine1(), kycSchedulingResponse.getAddress().getLine1());
        Assertions.assertEquals(dummyKycSchedule.getAddress().getLine2(), kycSchedulingResponse.getAddress().getLine2());
        Assertions.assertEquals(dummyKycSchedule.getAddress().getLine3(), kycSchedulingResponse.getAddress().getLine3());
        Assertions.assertEquals(dummyKycSchedule.getAddress().getPincode(), kycSchedulingResponse.getAddress().getPincode());
        Assertions.assertEquals(dummyKycSchedule.getAddress().getCity(), kycSchedulingResponse.getAddress().getCity());
        Assertions.assertEquals(dummyKycSchedule.getAddress().getState(), kycSchedulingResponse.getAddress().getState());
    }

    @Test
    void convertModelToBranchDetailsResponse() {
        BranchDetails branchDetails = BranchDetails.builder().address(TEST_BRANCH_ADDRES).build();
        BranchDetailsResponse branchDetailsResponse = kycScheduleTransformer.convertModelToResponseBranchDetails(branchDetails);
        Assertions.assertEquals(branchDetailsResponse.getAddress(),branchDetails.getAddress());
    }
    @Test
    void updateKycSchedule() {
        KycSchedulingRequest kycSchedulingRequest = KycScheduleTestDataBuilder.createDummyKycSchedulingRequest();
        KycSchedule kycSchedule = KycScheduleTestDataBuilder.createDummyKycSchedule();
        String leadTrackingId = kycSchedule.getLeadTrackingId();
        kycScheduleTransformer.updateKycSchedule(kycSchedule, kycSchedulingRequest);
        Assertions.assertEquals(kycSchedulingRequest.getScheduledAt(), kycSchedule.getScheduledAt().toEpochMilli());
        Assertions.assertEquals(leadTrackingId, kycSchedule.getLeadTrackingId());
        Assertions.assertEquals(kycSchedulingRequest.getAddress().getLine1(), kycSchedule.getAddress().getLine1());
        Assertions.assertEquals(kycSchedulingRequest.getAddress().getLine2(), kycSchedule.getAddress().getLine2());
        Assertions.assertEquals(kycSchedulingRequest.getAddress().getLine3(), kycSchedule.getAddress().getLine3());
        Assertions.assertEquals(kycSchedulingRequest.getAddress().getPincode(), kycSchedule.getAddress().getPincode());
        Assertions.assertEquals(kycSchedulingRequest.getAddress().getCity(), kycSchedule.getAddress().getCity());
        Assertions.assertEquals(kycSchedulingRequest.getAddress().getState(), kycSchedule.getAddress().getState());
    }

    @Test
    void updateKycSchedule_Exception() {
        KycSchedulingRequest kycSchedulingRequest = KycScheduleTestDataBuilder.createDummyKycSchedulingRequest();
        kycSchedulingRequest.setScheduleType(null);
        KycSchedule kycSchedule = KycScheduleTestDataBuilder.createDummyKycSchedule();
        assertThrows( RuntimeException.class, ()-> kycScheduleTransformer.updateKycSchedule(kycSchedule, kycSchedulingRequest));
    }

    @Test
    void updateBranchKycSchedule() {
        KycSchedulingRequest kycSchedulingRequest = KycScheduleTestDataBuilder.createDummyKycSchedulingRequest();
        kycSchedulingRequest.setScheduleType(ScheduleType.KYC_AT_BRANCH);
        kycSchedulingRequest.setBranchDetails(BranchDetails.builder().address(TEST_BRANCH_ADDRES).build());
        KycSchedule kycSchedule = KycScheduleTestDataBuilder.createDummyKycSchedule();
        kycSchedule.setScheduleType(ScheduleType.KYC_AT_BRANCH);
        kycSchedule.setBranchDetails(BranchDetails.builder().address(TEST_BRANCH_ADDRES).build());
        String leadTrackingId = kycSchedule.getLeadTrackingId();

        kycScheduleTransformer.updateKycSchedule(kycSchedule, kycSchedulingRequest);

        Assertions.assertEquals(kycSchedulingRequest.getScheduledAt(), kycSchedule.getScheduledAt().toEpochMilli());
        Assertions.assertEquals(leadTrackingId, kycSchedule.getLeadTrackingId());
        Assertions.assertEquals(kycSchedulingRequest.getScheduleType(), kycSchedule.getScheduleType());
        Assertions.assertEquals(kycSchedulingRequest.getBranchDetails().getAddress(), kycSchedule.getBranchDetails().getAddress());
    }

    @Test
    void convertRequestToModelOfAddress() {
        Address addressRequest = KycScheduleTestDataBuilder.createDummyAddressRequest();

        com.kotak.unified.dbservice.model.common.Address address = kycScheduleTransformer.convertRequestToModel(addressRequest);

        Assertions.assertEquals(addressRequest.getLine1(), address.getLine1());
        Assertions.assertEquals(addressRequest.getLine2(), address.getLine2());
        Assertions.assertEquals(addressRequest.getLine3(), address.getLine3());
        Assertions.assertEquals(addressRequest.getPincode(), address.getPincode());
        Assertions.assertEquals(addressRequest.getCity(), address.getCity());
        Assertions.assertEquals(addressRequest.getState(), address.getState());
    }

    @Test
    void convertModelToResponseOfAddress() {
        com.kotak.unified.dbservice.model.common.Address dummyAddress = KycScheduleTestDataBuilder.createDummyAddress();

        AddressResponse addressResponse = kycScheduleTransformer.convertModelToResponse(dummyAddress);

        Assertions.assertEquals(dummyAddress.getLine1(), addressResponse.getLine1());
        Assertions.assertEquals(dummyAddress.getLine2(), addressResponse.getLine2());
        Assertions.assertEquals(dummyAddress.getLine3(), addressResponse.getLine3());
        Assertions.assertEquals(dummyAddress.getPincode(), addressResponse.getPincode());
        Assertions.assertEquals(dummyAddress.getCity(), addressResponse.getCity());
        Assertions.assertEquals(dummyAddress.getState(), addressResponse.getState());
    }

    @Test
    void convertModelToResponseOfSchedules() {
        List<KycSchedule> dummyKycSchedule = KycScheduleTestDataBuilder.createDummyKycScheduleList();

        KycSchedulingDetailsResponse kycSchedulingDetailsResponse = kycScheduleTransformer.convertModelToResponse(dummyKycSchedule);

        Assertions.assertEquals(dummyKycSchedule.get(0).getScheduledAt().toEpochMilli(), kycSchedulingDetailsResponse.getKycSchedulingResponseList().get(0).getScheduledAt());
        Assertions.assertEquals(dummyKycSchedule.get(0).getLeadTrackingId(), kycSchedulingDetailsResponse.getKycSchedulingResponseList().get(0).getLeadTrackingNumber());
        Assertions.assertEquals(dummyKycSchedule.get(0).getAddress().getLine1(), kycSchedulingDetailsResponse.getKycSchedulingResponseList().get(0).getAddress().getLine1());
        Assertions.assertEquals(dummyKycSchedule.get(0).getAddress().getLine2(), kycSchedulingDetailsResponse.getKycSchedulingResponseList().get(0).getAddress().getLine2());
        Assertions.assertEquals(dummyKycSchedule.get(0).getAddress().getLine3(), kycSchedulingDetailsResponse.getKycSchedulingResponseList().get(0).getAddress().getLine3());
        Assertions.assertEquals(dummyKycSchedule.get(0).getAddress().getPincode(), kycSchedulingDetailsResponse.getKycSchedulingResponseList().get(0).getAddress().getPincode());
        Assertions.assertEquals(dummyKycSchedule.get(0).getAddress().getCity(), kycSchedulingDetailsResponse.getKycSchedulingResponseList().get(0).getAddress().getCity());
        Assertions.assertEquals(dummyKycSchedule.get(0).getAddress().getState(), kycSchedulingDetailsResponse.getKycSchedulingResponseList().get(0).getAddress().getState());
    }


}