package com.kotak.unified.dbservice.transformer.impl;

import com.kotak.unified.common.exception.InvalidRequestException;
import com.kotak.unified.db.*;
import com.kotak.unified.db.DisbursementDetails;
import com.kotak.unified.db.KotakLifeInsuranceDetails;
import com.kotak.unified.db.assets.AdditionalDetailsResponse;
import com.kotak.unified.db.assets.IncomeDetailsResponse;
import com.kotak.unified.db.assets.LoanIntentResponse;
import com.kotak.unified.db.assets.PLBasicDetails;
import com.kotak.unified.db.assets.PLProductDetailsResponse;
import com.kotak.unified.orchestrator.common.dbmodels.*;
import com.kotak.unified.orchestrator.common.dbmodels.AssociatedEmployeeDetails;
import com.kotak.unified.orchestrator.common.dbmodels.NomineeDetails;
import com.kotak.unified.orchestrator.common.dbmodels.assets.AdditionalDetails;
import com.kotak.unified.orchestrator.common.dbmodels.assets.ApplicantDetails;
import com.kotak.unified.orchestrator.common.dbmodels.assets.CrnDetails;
import com.kotak.unified.orchestrator.common.dbmodels.assets.CrnNameMap;
import com.kotak.unified.orchestrator.common.dbmodels.assets.IncomeDetails;
import com.kotak.unified.orchestrator.common.dbmodels.assets.LoanIntent;
import com.kotak.unified.orchestrator.common.dbmodels.assets.PLAddressType;
import com.kotak.unified.orchestrator.common.dbmodels.assets.PLProductDetails;
import java.util.Map;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.modelmapper.ModelMapper;
import org.modelmapper.TypeToken;
import org.springframework.data.util.Pair;

import java.time.Instant;
import java.util.List;

import static com.kotak.unified.db.ApplicationDataFilter.*;
import static com.kotak.unified.dbservice.utils.Constants.PERSONAL_LOAN_GRAPH_NAME;
import static com.kotak.unified.orchestrator.common.dbmodels.PLApplicationMileStone.APPLICANT_DETAILS_SUBMITTED;
import static com.kotak.unified.orchestrator.common.dbmodels.PLApplicationMileStone.APPLICATION_CREATED;
import static com.kotak.unified.db.ApplicationDataFilter.APPLICATION_CREATED_TIME;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThrows;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.kotak.unified.orchestrator.common.dbmodels.assets.EmploymentDetails;

public class PLApplicationDataTransformerTest {

  private static final String UTM_SOURCE = "utm_source";
  private static final String WEB = "web";
  private static final String TEST_FIP = "testFip";
  private static final String TEST_PHONE = "testPhone";
  private final ModelMapper modelMapper;
  private final PLApplicationDataTransformer transformer;

  private final UserStatus userStatus;
  private final PersonalLoanJourneyMetadata journeyMetadata;

  private final GetApplicationDataRequest request;

  private final ApplicantDetails applicantDetails;

  private final Instant lastModifiedTime;
  private final Instant applicationCreatedTime;


  private static final String PERSONAL_LOAN = "PL";
  private static final String LEAD_TRACKING_NUMBER = "l1";

  public PLApplicationDataTransformerTest() {
    this.modelMapper = mock(ModelMapper.class);

    this.transformer = new PLApplicationDataTransformer(modelMapper);
    this.request = mock(
        GetApplicationDataRequest.class);
    this.userStatus = mock(
        UserStatus.class);
    this.journeyMetadata = mock(
        PersonalLoanJourneyMetadata.class);
    this.applicantDetails = mock(
        ApplicantDetails.class);
    this.lastModifiedTime = mock(
        Instant.class);
    this.applicationCreatedTime = mock(
        Instant.class);
  }

  @Test
  void testGetProduct() {
    Assertions.assertEquals(PERSONAL_LOAN,
        transformer.getProduct());
  }

  @Test
  void testUpdateApplicationDataWithTmxDetails() {
    final TmxDetails tmxDetails = mock(TmxDetails.class);
    final TMXDetails tmxDetailsResponse = mock(
        TMXDetails.class);
    setUserStatusExpectation();
    when(request.getDataFilters()).thenReturn(List.of(TMX_DETAILS));
    when(journeyMetadata.getTmxSession()).thenReturn(tmxDetails);
    when(modelMapper.map(tmxDetails,
        TMXDetails.class)).thenReturn(
        tmxDetailsResponse);
    final PLApplicationData response = transformer();
    Assertions.assertEquals(tmxDetailsResponse,
        response.getTmxSession());
    Assertions.assertEquals(lastModifiedTime,
        response.getLastModifiedTime());
  }

  @Test
  void testUpdateApplicationDataWithMilestoneHistory() {
    final Instant instant = Instant.now();
    final List<Pair<PLApplicationMileStone, Instant>> mileStoneHistory = List.of(
        Pair.of(APPLICATION_CREATED, instant),
        Pair.of(APPLICANT_DETAILS_SUBMITTED, instant));
    final List<Pair<PLApplicationMileStoneResponse, Instant>> mileStoneHistoryResponse = List.of(
        Pair.of(PLApplicationMileStoneResponse.APPLICATION_CREATED, Instant.now()),
        Pair.of(PLApplicationMileStoneResponse.APPLICANT_DETAILS_SUBMITTED, Instant.now()));
    setUserStatusExpectation();
    when(request.getDataFilters()).thenReturn(List.of(MILESTONE_HISTORY_LIST));
    when(journeyMetadata.getPlApplicationMileStoneHistory()).thenReturn(mileStoneHistory);
    when(modelMapper.map(mileStoneHistory,
        new TypeToken<List<Pair<PLApplicationMileStone, Instant>>>() {
        }.getType())).thenReturn(
        mileStoneHistoryResponse);
    final PLApplicationData response = transformer();
    Assertions.assertEquals(mileStoneHistoryResponse,
        response.getPlApplicationMileStoneHistory());
    Assertions.assertEquals(lastModifiedTime,
        response.getLastModifiedTime());
  }
  @Test
  void testUpdateApplicationDataWithBlockedReasons() {
    final List<String> blockedReasons = List.of("2001");
    final List<String> blockedReasonsResponse = List.of("2001");
    setUserStatusExpectation();
    when(request.getDataFilters()).thenReturn(List.of(BLOCKED_REASONS));
    when(journeyMetadata.getBlockedReasons()).thenReturn(blockedReasons);
    when(modelMapper.map(blockedReasons,
        new TypeToken<List<String>>() {
        }.getType())).thenReturn(
        blockedReasonsResponse);
    final PLApplicationData response = transformer();
    Assertions.assertEquals(blockedReasonsResponse,
        response.getBlockedReasons());
    Assertions.assertEquals(lastModifiedTime,
        response.getLastModifiedTime());
  }

  @Test
  void testPopulateApplicationDataWithAadhaarDetails() {
    final AadhaarDetails aadhaarDetails = mock(AadhaarDetails.class);
    final AadhaarDetailsResponse aadhaarDetailsResponse = mock(
        AadhaarDetailsResponse.class);
    setUserStatusExpectation();
    when(request.getDataFilters()).thenReturn(List.of(AADHAAR));
    when(applicantDetails.getAadhaarDetails()).thenReturn(aadhaarDetails);
    when(modelMapper.map(aadhaarDetails,
        AadhaarDetailsResponse.class)).thenReturn(
        aadhaarDetailsResponse);
    final PLApplicationData response = transformer();
    Assertions.assertEquals(aadhaarDetailsResponse,
        response.getAadhaarDetails());
    Assertions.assertEquals(lastModifiedTime,
        response.getLastModifiedTime());
  }

  @Test
  void testPopulateApplicationDataWithAadhaarDetailsNull() {
    setUserStatusExpectation();
    final PersonalLoanJourneyMetadata personalLoanJourneyMetadata =
            (PersonalLoanJourneyMetadata) userStatus.getJourneyMetadata();
    personalLoanJourneyMetadata.getApplicantDetails()
            .setAadhaarDetails(null);
    when(request.getDataFilters()).thenReturn(List.of(AADHAAR));
    final PLApplicationDataTransformer plApplicationDataTransformer =
            new PLApplicationDataTransformer(new ModelMapper());
    final PLApplicationData response =
            (PLApplicationData) plApplicationDataTransformer.populateApplicationData(
                    request, userStatus);
    Assertions.assertNull(response.getAadhaarDetails());
    Assertions.assertEquals(lastModifiedTime,
            response.getLastModifiedTime());
  }

  @Test
  void testPopulateApplicationDataWithPanDetails() {
    final PanDetails details = mock(PanDetails.class);
    final PanDetailsResponse detailsResponse = mock(
        PanDetailsResponse.class);
    setUserStatusExpectation();
    when(request.getDataFilters()).thenReturn(List.of(PAN));
    when(applicantDetails.getPanDetails()).thenReturn(details);
    when(modelMapper.map(details,
        PanDetailsResponse.class)).thenReturn(
        detailsResponse);
    final PLApplicationData response = transformer();
    Assertions.assertEquals(detailsResponse,
        response.getPanDetails());
    Assertions.assertEquals(lastModifiedTime,
        response.getLastModifiedTime());
  }

  @Test
  void testPopulateApplicationDataWithPanDetailsNull() {
    setUserStatusExpectation();
    final PersonalLoanJourneyMetadata personalLoanJourneyMetadata =
            (PersonalLoanJourneyMetadata) userStatus.getJourneyMetadata();
    personalLoanJourneyMetadata.getApplicantDetails()
            .setPanDetails(null);
    when(request.getDataFilters()).thenReturn(List.of(PAN));
    final PLApplicationDataTransformer plApplicationDataTransformer =
            new PLApplicationDataTransformer(new ModelMapper());
    final PLApplicationData response =
            (PLApplicationData) plApplicationDataTransformer.populateApplicationData(
                    request, userStatus);
    Assertions.assertNull(response.getPanDetails());
    Assertions.assertEquals(lastModifiedTime,
            response.getLastModifiedTime());
  }

  @Test
  void testPopulateApplicationDataWithDisbursementDetails() {
    final com.kotak.unified.orchestrator.common.dbmodels.DisbursementDetails details
        = mock(com.kotak.unified.orchestrator.common.dbmodels.DisbursementDetails.class);
    final DisbursementDetails detailsResponse = mock(
        DisbursementDetails.class);
    setUserStatusExpectation();
    when(request.getDataFilters()).thenReturn(List.of(DISBURSEMENT_DETAILS));
    when(journeyMetadata
        .getDisbursementDetails()).thenReturn(details);
    when(modelMapper.map(details,
        DisbursementDetails.class)).thenReturn(
        detailsResponse);
    final PLApplicationData response = transformer();
    Assertions.assertEquals(detailsResponse,
        response.getDisbursementDetails());
    Assertions.assertEquals(lastModifiedTime,
        response.getLastModifiedTime());
  }

  @Test
  void testPopulateApplicationDataWithKLIDetails() {
    final com.kotak.unified.orchestrator.common.dbmodels.KotakLifeInsuranceDetails details
            = mock(com.kotak.unified.orchestrator.common.dbmodels.KotakLifeInsuranceDetails.class);
    final KotakLifeInsuranceDetails detailsResponse = mock(
            KotakLifeInsuranceDetails.class);
    setUserStatusExpectation();
    when(request.getDataFilters()).thenReturn(List.of(KLI_DETAILS));
    when(journeyMetadata
            .getKli()).thenReturn(details);
    when(modelMapper.map(details,
            KotakLifeInsuranceDetails.class)).thenReturn(
            detailsResponse);
    final PLApplicationData response = transformer();
    Assertions.assertEquals(detailsResponse.getInsuranceAmount(),
            response.getKotakLifeInsurance().getInsuranceAmount());
    Assertions.assertEquals(detailsResponse.isEnabled(),
            response.getKotakLifeInsurance().isEnabled());
    Assertions.assertEquals(lastModifiedTime,
            response.getLastModifiedTime());
  }

  @Test
  void testPopulateApplicationDataWithNomineeDetails() {
    final ApplicantDetails details
            = mock(ApplicantDetails.class);

    final NomineeDetails detailsResponse = NomineeDetails.builder().
            DOB("dd-mm-yyyy").
            gender("male").
            name("Test-user").
            relationshipType("SPOUSE").
            build();
    NomineeDetails kliNomineeDetails = NomineeDetails.builder().
            DOB("dd-mm-yyyy").
            gender("male").
            name("Test-user").
            relationshipType("SPOUSE").
            build();
    ApplicantDetails applicantDetails = ApplicantDetails.builder().nomineeDetails(kliNomineeDetails).build();
    setUserStatusExpectation();
    when(request.getDataFilters()).thenReturn(List.of(NOMINEE_DETAILS));
    when(journeyMetadata.getApplicantDetails()).thenReturn(applicantDetails);
    when(modelMapper.map(details,
            NomineeDetails.class)).thenReturn(
            detailsResponse);
    final PLApplicationData response = transformer();
    Assertions.assertEquals(detailsResponse.getRelationshipType(),
            response.getNomineeDetails().getRelationshipType());
    Assertions.assertEquals(detailsResponse.getDOB(),
            response.getNomineeDetails().getDOB());
    Assertions.assertEquals(detailsResponse.getName(),
            response.getNomineeDetails().getName());
    Assertions.assertEquals(detailsResponse.getGender(),
            response.getNomineeDetails().getGender());
    Assertions.assertEquals(lastModifiedTime,
            response.getLastModifiedTime());
  }

  @Test
  void testPopulateApplicationDataWithAadhaarAndPanDetails() {
    final AadhaarDetails aadhaarDetails = mock(AadhaarDetails.class);
    final AadhaarDetailsResponse aadhaarDetailsResponse = mock(
        AadhaarDetailsResponse.class);
    final PanDetails panDetails = mock(PanDetails.class);
    final PanDetailsResponse panDetailsResponse = mock(
        PanDetailsResponse.class);
    setUserStatusExpectation();
    when(request.getDataFilters()).thenReturn(List.of(AADHAAR, PAN));
    when(applicantDetails.getAadhaarDetails()).thenReturn(aadhaarDetails);
    when(applicantDetails.getPanDetails()).thenReturn(panDetails);
    when(modelMapper.map(aadhaarDetails,
        AadhaarDetailsResponse.class)).thenReturn(
        aadhaarDetailsResponse);
    when(modelMapper.map(panDetails,
        PanDetailsResponse.class)).thenReturn(
        panDetailsResponse);
    final PLApplicationData response = transformer();
    Assertions.assertEquals(aadhaarDetailsResponse,
        response.getAadhaarDetails());
    Assertions.assertEquals(panDetailsResponse,
        response.getPanDetails());
    Assertions.assertEquals(lastModifiedTime,
        response.getLastModifiedTime());
  }

  @Test
  void testPopulateApplicationDataWithIncomeDetails() {
    final IncomeDetails details = mock(IncomeDetails.class);
    final IncomeDetailsResponse detailsResponse = mock(
        IncomeDetailsResponse.class);
    setUserStatusExpectation();
    when(request.getDataFilters()).thenReturn(List.of(INCOME));
    when(applicantDetails.getIncomeDetails()).thenReturn(details);
    when(modelMapper.map(details,
        IncomeDetailsResponse.class)).thenReturn(
        detailsResponse);
    final PLApplicationData response = transformer();
    Assertions.assertEquals(detailsResponse,
        response.getIncomeDetails());
    Assertions.assertEquals(lastModifiedTime,
        response.getLastModifiedTime());
  }

  @Test
  void testPopulateApplicationDataWithAddressDetails() {
    final Address details = mock(Address.class);
    final AddressResponse detailsResponse = mock(
        AddressResponse.class);
    final PLAddressType communicationAddressSameAsAddress = mock(
        PLAddressType.class);
    final PLAddressTypeResponse addressType = mock(
        PLAddressTypeResponse.class);
    setUserStatusExpectation();
    when(request.getDataFilters()).thenReturn(List.of(ADDRESS));
    when(applicantDetails.getAddress()).thenReturn(details);
    when(modelMapper.map(details,
        AddressResponse.class)).thenReturn(
        detailsResponse);
    when(modelMapper.map(communicationAddressSameAsAddress,
        PLAddressTypeResponse.class)).thenReturn(
        addressType);
    final PLApplicationData response = transformer();
    Assertions.assertEquals(detailsResponse,
        response.getAddress());
    Assertions.assertEquals(lastModifiedTime,
        response.getLastModifiedTime());
  }

  @Test
  void testPopulateApplicationDataWithAddressDetailsNull() {
    setUserStatusExpectation();
    final PersonalLoanJourneyMetadata personalLoanJourneyMetadata =
            (PersonalLoanJourneyMetadata) userStatus.getJourneyMetadata();
    personalLoanJourneyMetadata.getApplicantDetails()
            .setAddress(null);
    personalLoanJourneyMetadata.getApplicantDetails()
            .setCommunicationAddressSameAsAddress(null);
    when(request.getDataFilters()).thenReturn(List.of(ADDRESS));
    final PLApplicationDataTransformer plApplicationDataTransformer =
            new PLApplicationDataTransformer(new ModelMapper());
    final PLApplicationData response =
            (PLApplicationData) plApplicationDataTransformer.populateApplicationData(
                    request, userStatus);
    Assertions.assertNull(response.getAddress());
    Assertions.assertNull(response.getCommunicationAddressSameAsAddress());
    Assertions.assertEquals(lastModifiedTime,
            response.getLastModifiedTime());
  }

  @Test
  void testPopulateApplicationDataWithAdditionalDetails() {
    final AdditionalDetails details = mock(AdditionalDetails.class);
    final AdditionalDetailsResponse detailsResponse = mock(
        AdditionalDetailsResponse.class);
    setUserStatusExpectation();
    when(request.getDataFilters()).thenReturn(List.of(ADDITIONAL_DETAILS));
    when(applicantDetails.getAdditionalDetails()).thenReturn(details);
    when(modelMapper.map(details,
        AdditionalDetailsResponse.class)).thenReturn(
        detailsResponse);
    final PLApplicationData response = transformer();
    Assertions.assertEquals(detailsResponse,
        response.getAdditionalDetails());
    Assertions.assertEquals(lastModifiedTime,
        response.getLastModifiedTime());
  }

  @Test
  void testPopulateApplicationDataWithAdditionalDetailsNull() {
    setUserStatusExpectation();
    final PersonalLoanJourneyMetadata personalLoanJourneyMetadata =
            (PersonalLoanJourneyMetadata) userStatus.getJourneyMetadata();
    personalLoanJourneyMetadata.getApplicantDetails()
            .setAdditionalDetails(null);
    when(request.getDataFilters()).thenReturn(List.of(ADDITIONAL_DETAILS));
    final PLApplicationDataTransformer plApplicationDataTransformer =
            new PLApplicationDataTransformer(new ModelMapper());
    final PLApplicationData response =
            (PLApplicationData) plApplicationDataTransformer.populateApplicationData(
                    request, userStatus);
    Assertions.assertNull(response.getAdditionalDetails());
    Assertions.assertEquals(lastModifiedTime,
            response.getLastModifiedTime());
  }

  @Test
  void testPopulateApplicationDataWithUTMParams() {
    setUserStatusExpectation();
    final Map<String, String> resumeUTMTags = mock(Map.class);
    final Map<String, String> utmTags = mock(Map.class);
    resumeUTMTags.put("source", "web");
    final AdditionalDetails details = mock(AdditionalDetails.class);
    final AdditionalDetailsResponse detailsResponse = mock(
            AdditionalDetailsResponse.class);
    setUserStatusExpectation();
    when(applicantDetails.getAdditionalDetails()).thenReturn(details);
    when(modelMapper.map(details,
            AdditionalDetailsResponse.class)).thenReturn(
            detailsResponse);
    when(request.getDataFilters()).thenReturn(List.of(RESUME_UTM_TAGS
    , LEAD_CREATION_CHANNEL));
    when(journeyMetadata.getResumeUtmTags()).thenReturn(resumeUTMTags);
    when(journeyMetadata.getUtmTags()).thenReturn(utmTags);
    final PLApplicationDataTransformer plApplicationDataTransformer =
            new PLApplicationDataTransformer(new ModelMapper());
    final PLApplicationData response =
            (PLApplicationData) plApplicationDataTransformer.populateApplicationData(
                    request, userStatus);
    Assertions.assertEquals(response.getResumeUtmTags(), resumeUTMTags);
    Assertions.assertEquals(lastModifiedTime,
            response.getLastModifiedTime());
  }
  @Test
  void testPopulateApplicationDataWithLoanIntent() {
    final LoanIntent details = mock(LoanIntent.class);
    final LoanIntentResponse detailsResponse = mock(
        LoanIntentResponse.class);
    setUserStatusExpectation();
    when(request.getDataFilters()).thenReturn(List.of(INTENT));
    when(journeyMetadata.getLoanIntent()).thenReturn(details);
    when(modelMapper.map(details,
        LoanIntentResponse.class)).thenReturn(
        detailsResponse);
    final PLApplicationData response = transformer();
    Assertions.assertEquals(detailsResponse,
        response.getIntent());
    Assertions.assertEquals(lastModifiedTime,
        response.getLastModifiedTime());
  }

  @Test
  void testPopulateApplicationDataWithPhoneNumber() {
    final String phone = "phone";
    setUserStatusExpectation();
    when(request.getDataFilters()).thenReturn(List.of(PHONE_NUMBER));
    when(userStatus.getPhoneNumber()).thenReturn(phone);
    final PLApplicationData response = transformer();
    Assertions.assertEquals(phone,
        response.getPhoneNumber());
    Assertions.assertEquals(lastModifiedTime,
        response.getLastModifiedTime());
  }

  @Test
  void testPopulateApplicationDataWithEmailAndNullEmailVerified() {
    final String email = "email";
    setUserStatusExpectation();
    when(request.getDataFilters()).thenReturn(List.of(EMAIL_ID));
    when(applicantDetails.getEmailId()).thenReturn(email);
    final PLApplicationData response = transformer();
    Assertions.assertEquals(email,
        response.getEmailId());
    Assertions.assertEquals(lastModifiedTime,
        response.getLastModifiedTime());
    Assertions.assertNull(response.getIsEmailVerified());
  }
  @Test
  void testPopulateApplicationDataWithEmailAndTrueEmailVerified() {
    final String email = "email";
    setUserStatusExpectation();
    when(request.getDataFilters()).thenReturn(List.of(EMAIL_ID));
    when(applicantDetails.getEmailId()).thenReturn(email);
    userStatus.setExecutionData(ExecutionData.builder()
        .isEmailAddressVerified(Boolean.TRUE).build());
    final PLApplicationData response = transformer();
    Assertions.assertEquals(email,
        response.getEmailId());
    Assertions.assertEquals(lastModifiedTime,
        response.getLastModifiedTime());
    Assertions.assertNull(response.getIsEmailVerified());
  }

  @Test
  void testPopulateApplicationDataWithVkycDetails() {
    setUserStatusExpectation();
    when(request.getDataFilters()).thenReturn(List.of(VKYC));
    final PLApplicationData response = transformer();
    Assertions.assertEquals(lastModifiedTime,
        response.getLastModifiedTime());
  }

  @Test
  void testPopulateApplicationDataWithProductDetails() {
    final PLProductDetails details = mock(PLProductDetails.class);
    final PLProductDetailsResponse detailsResponse = mock(
        PLProductDetailsResponse.class);
    setUserStatusExpectation();
    when(request.getDataFilters()).thenReturn(List.of(PRODUCT));
    when(journeyMetadata.getProductDetails()).thenReturn(details);
    when(modelMapper.map(details,
        PLProductDetailsResponse.class)).thenReturn(
        detailsResponse);
    final PLApplicationData response = transformer();
    Assertions.assertEquals(detailsResponse,
        response.getProductDetails());
    Assertions.assertEquals(lastModifiedTime,
        response.getLastModifiedTime());
  }

  @Test
  void testPopulateApplicationDataWithSelectedCrn() {
    final CrnDetails crnDetails = mock(CrnDetails.class);
    final CrnNameMap crnNameMap = mock(
        CrnNameMap.class);
    final String crn = "crn";
    setUserStatusExpectation();
    when(request.getDataFilters()).thenReturn(List.of(CRN));
    when(applicantDetails.getCrnDetails()).thenReturn(crnDetails);
    when(crnDetails.getSelectedCrn()).thenReturn(crnNameMap);
    when(crnNameMap.getCrn()).thenReturn(crn);
    final PLApplicationData response = transformer();
    Assertions.assertEquals(crn,
        response.getSelectedCrn());
    Assertions.assertEquals(lastModifiedTime,
        response.getLastModifiedTime());
  }

  @Test
  void testPopulateApplicationDataWithSelectedCrnWhenCrnIsNull() {
    final CrnDetails crnDetails = new CrnDetails();
    crnDetails.setSelectedCrn(null);
    setUserStatusExpectation();
    final PersonalLoanJourneyMetadata personalLoanJourneyMetadata =
            (PersonalLoanJourneyMetadata) userStatus.getJourneyMetadata();
    personalLoanJourneyMetadata.getApplicantDetails().setCrnDetails(crnDetails);
    when(request.getDataFilters()).thenReturn(List.of(CRN));
    final PLApplicationData response = transformer();
    Assertions.assertNull(response.getSelectedCrn());
    Assertions.assertEquals(lastModifiedTime,
            response.getLastModifiedTime());
  }

  @Test
  void testPopulateApplicationDataWithBasicDetailFromCrn() {
    final CrnDetails crnDetails = mock(CrnDetails.class);
    final CrnNameMap crnNameMap = mock(CrnNameMap.class);
    final String crnName = "crnName";
    final String dob = "21-02-2000";
    final PLBasicDetails plBasicDetails =
        PLBasicDetails.builder()
            .customerName(crnName)
            .dob(dob)
            .gender("M")
            .build();
    setUserStatusExpectation();
    when(request.getDataFilters()).thenReturn(List.of(BASIC_DETAIL));
    when(applicantDetails.getCrnDetails()).thenReturn(crnDetails);
    when(applicantDetails.getDob()).thenReturn(dob);
    when(applicantDetails.getAdditionalDetails()).thenReturn(
        AdditionalDetails.builder().gender("M").build());
    when(crnDetails.getSelectedCrn()).thenReturn(crnNameMap);
    when(crnNameMap.getCrnName()).thenReturn(crnName);
    final PLApplicationData response = transformer();
    Assertions.assertEquals(plBasicDetails, response.getBasicDetails());
    Assertions.assertEquals(lastModifiedTime, response.getLastModifiedTime());
  }

  @Test
  void testPopulateApplicationDataWithBasicDetailFromPan() {
    final CrnDetails crnDetails = mock(CrnDetails.class);
    final PanDetails panDetails = mock(PanDetails.class);
    final String panName = "panName";
    final String panDOB = "01-01-1990";
    final PLBasicDetails plBasicDetails =
        PLBasicDetails.builder()
            .customerName(panName)
            .dob(panDOB)
            .gender("M")
            .build();
    AdditionalDetails additionalDetails = AdditionalDetails.builder()
        .gender("M").build();
    setUserStatusExpectation();
    when(request.getDataFilters()).thenReturn(List.of(BASIC_DETAIL));
    when(applicantDetails.getPanDetails()).thenReturn(panDetails);
    when(applicantDetails.getCrnDetails()).thenReturn(crnDetails);
    when(applicantDetails.getAdditionalDetails()).thenReturn(additionalDetails);
    when(panDetails.getDisplayName()).thenReturn(panName);
    when(panDetails.getDateOfBirth()).thenReturn(panDOB);
    final PLApplicationData response = transformer();
    Assertions.assertEquals(plBasicDetails, response.getBasicDetails());
    Assertions.assertEquals(lastModifiedTime, response.getLastModifiedTime());
  }


  @Test
  void testPopulateApplicationDataUserStatusWithoutJourneyMetadata() {
    when(userStatus.getJourneyMetadata()).thenReturn(null);
    when(userStatus.getLeadTrackingNumber()).thenReturn(LEAD_TRACKING_NUMBER);
    Exception exception = assertThrows(InvalidRequestException.class,
        this::transformer);
    Assertions.assertEquals(
        "Application Id does not belong to PL Journey l1",
        exception.getMessage());
  }

  @Test
  void updateApplicationDataWithOccupationType() {
    setUserStatusExpectation();
    when(request.getDataFilters()).thenReturn(List.of(OCCUPATION_TYPE));
    when(journeyMetadata.getApplicantDetails()
        .getOccupation()).thenReturn("SELF");
    final PLApplicationData response = transformer();
    Assertions.assertEquals("SELF",
        response.getOccupation());
    Assertions.assertEquals(lastModifiedTime,
        response.getLastModifiedTime());
  }

  @Test
  void updateApplicationDataWithEmploymentDetails() {
    final EmploymentDetails employmentDetails = mock(
        EmploymentDetails.class);
    final EmploymentDetailsResponse employmentDetailsResponse = mock(
        EmploymentDetailsResponse.class);
    setUserStatusExpectation();
    when(request.getDataFilters()).thenReturn(List.of(EMPLOYMENT_DETAILS));
    when(journeyMetadata.getApplicantDetails()
        .getEmploymentDetails()).thenReturn(employmentDetails);
    when(modelMapper.map(employmentDetails,
        EmploymentDetailsResponse.class)).thenReturn(
        employmentDetailsResponse);
    final PLApplicationData response = transformer();
    Assertions.assertEquals(employmentDetailsResponse,
        response.getEmploymentDetailsResponse());
    Assertions.assertEquals(lastModifiedTime,
        response.getLastModifiedTime());
  }

  @Test
  void updateApplicationDataWithFiConsents() {
    setUserStatusExpectation();
    Map<String, String> fiConsents = mock(Map.class);
    when(request.getDataFilters()).thenReturn(List.of(FI_CONSENTS));
    when(journeyMetadata.getFiConsents()).thenReturn(Map.of());
    when(modelMapper.map(fiConsents, Map.class)).thenReturn(Map.of());
    final PLApplicationData response = transformer();
    Assertions.assertEquals(Map.of(), response.getFiConsentDetails());
    Assertions.assertEquals(lastModifiedTime, response.getLastModifiedTime());
  }
  @Test
  void updateApplicationDataWithRmCode() {
    setUserStatusExpectation();
    when(request.getDataFilters()).thenReturn(List.of(RM_CODE));
    when(journeyMetadata.getRmCode()).thenReturn("KMBLXXXX");
    final PLApplicationData response = transformer();
    Assertions.assertEquals("KMBLXXXX",
        response.getRmCode());
  }

  @Test
  void updateApplicationDataWithEmployeeRelationShipNameAndCode() {
    AssociatedEmployeeDetails employeeDetails = mock(
        AssociatedEmployeeDetails.class);
    final com.kotak.unified.db.AssociatedEmployeeDetails employeeDetailsDb = mock(
        com.kotak.unified.db.AssociatedEmployeeDetails.class);
    setUserStatusExpectation();
    when(request.getDataFilters()).thenReturn(List.of(EMPLOYEE_RELATION));
    when(journeyMetadata.getApplicantDetails().getAssociatedEmployeeDetails()).thenReturn(employeeDetails);
    when(modelMapper.map(employeeDetails,
        com.kotak.unified.db.AssociatedEmployeeDetails.class)).thenReturn(
        employeeDetailsDb);
    final PLApplicationData response = transformer();
    Assertions.assertEquals(employeeDetailsDb.getName(),
        response.getAssociatedEmployeeDetails().getName());
    Assertions.assertEquals(employeeDetailsDb.getCode(),
        response.getAssociatedEmployeeDetails().getCode());
  }

  @Test
  void testupdateApplicationDataWithLoanType() {
    final String loanType = "PL_PA";
    final ExecutionData executionData = new ExecutionData();
    setUserStatusExpectation();
    executionData.setLatestCreditOffer(loanType);
    when(request.getDataFilters()).thenReturn(List.of(LOAN_TYPE));
    when(userStatus.getExecutionData()).thenReturn(executionData);
    final PLApplicationData response = transformer();
    Assertions.assertEquals(loanType,
            response.getLoanType());
    Assertions.assertEquals(lastModifiedTime,
            response.getLastModifiedTime());
  }

  private PLApplicationData transformer() {
    return (PLApplicationData) transformer.populateApplicationData(
        request, userStatus);
  }

  private void setUserStatusExpectation() {
    when(userStatus.getJourneyType()).thenReturn(PERSONAL_LOAN_GRAPH_NAME);
    when(userStatus.getJourneyMetadata()).thenReturn(journeyMetadata);
    when(userStatus.getLastModifiedTime()).thenReturn(lastModifiedTime);
    when(applicantDetails.getNomineeDetails()).thenReturn(NomineeDetails.builder()
            .DOB("01/01/1990").name("test")
            .relationshipType("Mother").gender("Female").build());
    when(journeyMetadata.getApplicantDetails()).thenReturn(applicantDetails);
    when(journeyMetadata.getKli()).thenReturn(com.kotak.unified.orchestrator.common.dbmodels.KotakLifeInsuranceDetails.builder()
        .isEnabled(true).insuranceAmount(1234.0).build());
    when(userStatus.getCreatedTime()).thenReturn(applicationCreatedTime);
  }

  @Test
  void testUpdateApplicationDataWithJourneyMileStone() {
    setUserStatusExpectation();
    when(request.getDataFilters()).thenReturn(List.of(JOURNEY_MILESTONE));
    when(journeyMetadata.getPlApplicationMileStone()).thenReturn(
        APPLICATION_CREATED);
    final PLApplicationData response = transformer();
    Assertions.assertEquals(PLApplicationMileStoneResponse.APPLICATION_CREATED,
        response.getJourneyMileStone());
  }

  @Test
  void testUpdateApplicationDataWithJourneyMileStoneNull() {
    setUserStatusExpectation();
    when(request.getDataFilters()).thenReturn(List.of(JOURNEY_MILESTONE));
    when(journeyMetadata.getPlApplicationMileStone()).thenReturn(null);
    final PLApplicationData response = transformer();
    Assertions.assertNull(response.getJourneyMileStone());
  }

  @Test
  void testUpdateApplicationDataWithUtmTags() {
    setUserStatusExpectation();
    Map<String, String> utmSource = Map.of(UTM_SOURCE, WEB);
    when(request.getDataFilters()).thenReturn(List.of(UTM_TAGS));
    when(journeyMetadata.getUtmTags()).thenReturn(
        utmSource);
    final PLApplicationData response = transformer();
    Assertions.assertEquals(utmSource,
        response.getUtmTags());
  }

  @Test
  void testUpdateApplicationDataWithOtpLogs() {
    setUserStatusExpectation();
    Instant now = Instant.now();
    when(request.getDataFilters()).thenReturn(List.of(OTP_LOG));
    when(journeyMetadata.getOtpLog()).thenReturn(now);
    final PLApplicationData response = transformer();
    Assertions.assertEquals(now,
        response.getOtpLog());
  }


  @Test
  void testUpdateApplicationCreatedTime() {
    setUserStatusExpectation();
    Instant now = Instant.now();
    when(request.getDataFilters()).thenReturn(List.of(APPLICATION_CREATED_TIME));
    when(userStatus.getCreatedTime()).thenReturn(now);
    final PLApplicationData response = transformer();
    Assertions.assertEquals(now,
        response.getApplicationCreatedTime());
  }

  @Test
  void testUpdateAccountAggregatorDetails() {
    setUserStatusExpectation();
    when(request.getDataFilters()).thenReturn(List.of(ACCOUNT_AGGREGATOR_DETAILS));
    when(applicantDetails.getLinkedBankAccounts()).thenReturn(List.of(TEST_FIP));
    when(applicantDetails.getBankAccountLinkedPhoneNumber()).thenReturn(TEST_PHONE);

    final PLApplicationData response = transformer();
    Assertions.assertEquals(TEST_FIP, response.getSelectedFip());
    Assertions.assertEquals(TEST_PHONE, response.getBankAccountLinkedPhoneNumber());
  }

  @Test
  void testPopulateApplicationDataWithNullIncomeDetails() {
    setUserStatusExpectation();
    when(request.getDataFilters()).thenReturn(List.of(INCOME));
    when(applicantDetails.getIncomeDetails()).thenReturn(null);
    final PLApplicationData response = transformer();
    assertEquals(response.getIncomeDetails(), null);
    Assertions.assertEquals(lastModifiedTime,
        response.getLastModifiedTime());
  }
  @Test
  void testPopulateApplicationDataWithNullDob() {
    setUserStatusExpectation();
    when(request.getDataFilters()).thenReturn(List.of(BASIC_DETAIL));
    when(applicantDetails.getDob()).thenReturn(null);
    final PLApplicationData response = transformer();
    assertEquals(response.getBasicDetails().getDob(), null);
    Assertions.assertEquals(lastModifiedTime,
        response.getLastModifiedTime());
  }
  @Test
  void testPopulateApplicationDataWithNullName() {
    setUserStatusExpectation();
    when(request.getDataFilters()).thenReturn(List.of(BASIC_DETAIL));
    when(applicantDetails.getCustomerName()).thenReturn(null);
    final PLApplicationData response = transformer();
    assertEquals(response.getBasicDetails().getCustomerName(), null);
    Assertions.assertEquals(lastModifiedTime,
        response.getLastModifiedTime());
  }

  @Test
  void testPopulateApplicationDataWithNullGender() {
    setUserStatusExpectation();
    when(request.getDataFilters()).thenReturn(List.of(BASIC_DETAIL));
    when(applicantDetails.getAdditionalDetails()).thenReturn(AdditionalDetails.builder()
        .build());
    when(applicantDetails.getAdditionalDetails().getGender()).thenReturn(null);
    final PLApplicationData response = transformer();
    assertEquals(response.getBasicDetails().getGender(), null);
    Assertions.assertEquals(lastModifiedTime,
        response.getLastModifiedTime());
  }

  @Test
  void testPopulateApplicationDataWithNullLoanIntent() {
    setUserStatusExpectation();
    when(request.getDataFilters()).thenReturn(List.of(BASIC_DETAIL));
    when(journeyMetadata.getLoanIntent()).thenReturn(null);
    final PLApplicationData response = transformer();
    assertEquals(response.getIntent(), null);
    Assertions.assertEquals(lastModifiedTime,
        response.getLastModifiedTime());
  }
}
