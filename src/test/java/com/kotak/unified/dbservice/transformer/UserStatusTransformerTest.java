package com.kotak.unified.dbservice.transformer;

import com.kotak.unified.enums.UserJourneyStatusField;
import com.kotak.unified.orchestrator.common.dbmodels.BankDetails;
import com.kotak.unified.orchestrator.common.dbmodels.BusinessLoanJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.CurrentAccountJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.JourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.PersonalDetails;
import com.kotak.unified.orchestrator.common.dbmodels.ProductDetails;
import com.kotak.unified.orchestrator.common.dbmodels.SavingsAccountJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.UserStatus;
import com.kotak.unified.orchestrator.common.dbmodels.ca.CAProductSpecifications;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertThrows;

@ExtendWith(MockitoExtension.class)
public class UserStatusTransformerTest {

    private final UserStatusTransformer userStatusTransformer;
    UserStatusTransformerTest() {
        this.userStatusTransformer = new UserStatusTransformer();
    }

    @ParameterizedTest
    @ValueSource(strings = {"SavingsAccount", "SalaryAccount","EtbSavingsAccount","EtbSalaryAccount"})
    public void test_applyPatch_with_SavingsAccountJourneyMetadatJourneys_valid_updates_success(String journeyType)
    {
        JourneyMetadata sm = SavingsAccountJourneyMetadata.builder()
                .personalDetails(PersonalDetails
                        .builder()
                        .build())
                .bankDetails(BankDetails.builder().build())
                .productDetails(ProductDetails.builder().build())
                .build();
        UserStatus us = UserStatus.builder()
                        .journeyType(journeyType)
                        .journeyMetadata(sm)
                        .build();
        Map<UserJourneyStatusField, Object> updates = new HashMap<>();
        updates.put(UserJourneyStatusField.BRANCH_CODE,"B112");
        updates.put(UserJourneyStatusField.COCO_CODE,"C112");
        updates.put(UserJourneyStatusField.LC_CODE,"LC112");
        updates.put(UserJourneyStatusField.RM_CODE,"RM112");
        updates.put(UserJourneyStatusField.LOB_CODE,"LOB112");
        this.userStatusTransformer.applyPatch(us, updates);

        SavingsAccountJourneyMetadata updatedData = (SavingsAccountJourneyMetadata)us.getJourneyMetadata();
        Assertions.assertEquals("B112",updatedData.getBankDetails().getBranchCode());
        Assertions.assertEquals("C112",updatedData.getProductDetails().getCorpCode());
        Assertions.assertEquals("LC112",updatedData.getProductDetails().getLcCode());
        Assertions.assertEquals("RM112",updatedData.getProductDetails().getRmCode());
        Assertions.assertEquals("LOB112",updatedData.getProductDetails().getLineOfBusiness());
    }

    @ParameterizedTest
    @ValueSource(strings = {"AssistedBusinessCurrentAccount", "AssistedIndividualCurrentAccount"})
    public void test_applyPatch_with_CurrentAccountJourneyMetadata_valid_updates_success(String journeyType) {
        CurrentAccountJourneyMetadata cam = CurrentAccountJourneyMetadata.builder()
                .productSpecifications(CAProductSpecifications.builder().build())
                .build();
        UserStatus us = UserStatus.builder()
                .journeyType(journeyType)
                .journeyMetadata(cam)
                .build();
        Map<UserJourneyStatusField, Object> updates = new HashMap<>();
        updates.put(UserJourneyStatusField.BRANCH_CODE, "B112");
        updates.put(UserJourneyStatusField.COCO_CODE, "C112");
        updates.put(UserJourneyStatusField.LC_CODE, "LC112");
        updates.put(UserJourneyStatusField.RM_CODE, "RM112");
        updates.put(UserJourneyStatusField.LOB_CODE, "LOB112");
        updates.put(UserJourneyStatusField.INITIATOR_CODE, "999");
        updates.put(UserJourneyStatusField.SEGMENT_CODE, "SEG1");

        this.userStatusTransformer.applyPatch(us, updates);

        CAProductSpecifications updatedData = (CAProductSpecifications) ((CurrentAccountJourneyMetadata) us.getJourneyMetadata()).getProductSpecifications();
        Assertions.assertEquals("B112", updatedData.getBranchCode());
        Assertions.assertEquals("C112", updatedData.getCocoCode());
        Assertions.assertEquals("LC112", updatedData.getLcCode());
        Assertions.assertEquals("RM112", updatedData.getRmCode());
        Assertions.assertEquals("LOB112", updatedData.getLineOfBusiness());
        Assertions.assertEquals(999, updatedData.getInitiatorCode());
        Assertions.assertEquals("SEG1",updatedData.getSegment());
    }

    @Test
    public void test_applyPatch_with_missing_CAProductSpecifications_Failure() {
        CurrentAccountJourneyMetadata cam = CurrentAccountJourneyMetadata.builder().build();
        UserStatus us = UserStatus.builder()
                .journeyType("AssistedBusinessCurrentAccount")
                .journeyMetadata(cam)
                .build();
        Map<UserJourneyStatusField, Object> updates = new HashMap<>();
        updates.put(UserJourneyStatusField.BRANCH_CODE, "B112");

        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            this.userStatusTransformer.applyPatch(us, updates);
        });
        Assertions.assertEquals("Product specifications is empty", exception.getMessage());
    }


    @Test
    public void test_applyPatch_with_valid_few_updates_success()
    {
        JourneyMetadata sm = SavingsAccountJourneyMetadata.builder()
                .personalDetails(PersonalDetails
                        .builder()
                        .build())
                .bankDetails(BankDetails.builder().build())
                .productDetails(ProductDetails.builder().build())
                .build();
        UserStatus us = UserStatus.builder()
                .journeyType("SavingsAccount")
                .journeyMetadata(sm)
                .build();
        Map<UserJourneyStatusField, Object> updates = new HashMap<>();
        updates.put(UserJourneyStatusField.BRANCH_CODE,"B112");
        updates.put(UserJourneyStatusField.COCO_CODE,"C112");
        updates.put(UserJourneyStatusField.PROMO_CODE,"PROMO");
        updates.put(UserJourneyStatusField.LG_CODE,"1234");
        this.userStatusTransformer.applyPatch(us, updates);

        SavingsAccountJourneyMetadata updatedData = (SavingsAccountJourneyMetadata)us.getJourneyMetadata();
        Assertions.assertEquals("B112",updatedData.getBankDetails().getBranchCode());
        Assertions.assertEquals("C112",updatedData.getProductDetails().getCorpCode());
        Assertions.assertEquals("PROMO",updatedData.getProductDetails().getPromoCode());
        Assertions.assertEquals("1234",updatedData.getProductDetails().getLgCode());
        Assertions.assertNull(updatedData.getProductDetails().getLcCode());
        Assertions.assertNull(updatedData.getProductDetails().getRmCode());
        Assertions.assertNull(updatedData.getProductDetails().getLineOfBusiness());
    }

    @Test
    public void test_applyPatch_with_single_updates_success()
    {
        JourneyMetadata sm = SavingsAccountJourneyMetadata.builder()
                .personalDetails(PersonalDetails
                        .builder()
                        .build())
                .build();
        UserStatus us = UserStatus.builder()
                .journeyType("SavingsAccount")
                .journeyMetadata(sm)
                .build();
        Map<UserJourneyStatusField, Object> updates = new HashMap<>();
        updates.put(UserJourneyStatusField.BRANCH_CODE,"B112");
        this.userStatusTransformer.applyPatch(us, updates);

        SavingsAccountJourneyMetadata updatedData = (SavingsAccountJourneyMetadata)us.getJourneyMetadata();
        Assertions.assertEquals("B112",updatedData.getBankDetails().getBranchCode());
        Assertions.assertNull(updatedData.getProductDetails());
    }

    @Test
    public void test_applyPatch_with_unknownJourneyType_Failure() {
        JourneyMetadata sm = new BusinessLoanJourneyMetadata();
        UserStatus us = UserStatus.builder()
                .journeyType("BusinessLoan")
                .journeyMetadata(sm)
                .build();
        Map<UserJourneyStatusField, Object> updates = new HashMap<>();
        updates.put(UserJourneyStatusField.BRANCH_CODE, "B112");
        updates.put(UserJourneyStatusField.LG_CODE, "LG112");
        UnsupportedOperationException uoe = assertThrows(UnsupportedOperationException.class,
                () -> this.userStatusTransformer.applyPatch(us, updates));
        Assertions.assertEquals("Unsupported journeyType lead marked for update : "
                + us.getJourneyType(), uoe.getMessage());
    }
}
