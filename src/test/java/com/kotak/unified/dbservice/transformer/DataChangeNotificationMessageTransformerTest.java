package com.kotak.unified.dbservice.transformer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kotak.unified.databridgeinterface.model.sqs.DataChangeNotificationMessage;
import com.kotak.unified.dbservice.TestUtils;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import software.amazon.awssdk.services.sqs.model.SendMessageRequest;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class DataChangeNotificationMessageTransformerTest {
    private final ObjectMapper objectMapper;

    private final DataChangeNotificationMessageTransformer dataChangeNotificationMessageTransformer;

    DataChangeNotificationMessageTransformerTest() {
        this.objectMapper = Mockito.mock(ObjectMapper.class);
        this.dataChangeNotificationMessageTransformer = new DataChangeNotificationMessageTransformer(objectMapper);
    }

    @Test
    @SneakyThrows
    void convertDataBridgePublishMessageInputToMessageRequest() {
        String queueUrl = "test-queue-url";
        Integer delay = 0;
        DataChangeNotificationMessage dataChangeNotificationMessage = TestUtils.DATA_CHANGE_NOTIFICATION_MESSAGE;
        String messageBody = "dummy-message-body";

        when(objectMapper.writeValueAsString(any(DataChangeNotificationMessage.class))).thenReturn(messageBody);

        SendMessageRequest sendMessageRequest = dataChangeNotificationMessageTransformer.convertDataBridgePublishMessageInputToMessageRequest(dataChangeNotificationMessage, queueUrl, delay);

        Assertions.assertEquals(queueUrl, sendMessageRequest.queueUrl());
        Assertions.assertEquals(delay, sendMessageRequest.delaySeconds());
        Assertions.assertEquals(messageBody, sendMessageRequest.messageBody());

        verify(objectMapper, times(1)).writeValueAsString(dataChangeNotificationMessage);
    }

    @Test
    @SneakyThrows
    void convertDataBridgePublishMessageInputToMessageRequestFailure() {
        String queueUrl = "test-queue-url";
        Integer delay = 0;
        DataChangeNotificationMessage dataChangeNotificationMessage = TestUtils.DATA_CHANGE_NOTIFICATION_MESSAGE;

        when(objectMapper.writeValueAsString(any(DataChangeNotificationMessage.class))).thenThrow(RuntimeException.class);

        Assertions.assertThrows(
                RuntimeException.class,
                () -> dataChangeNotificationMessageTransformer.convertDataBridgePublishMessageInputToMessageRequest(dataChangeNotificationMessage, queueUrl, delay)
        );

        verify(objectMapper, times(1)).writeValueAsString(dataChangeNotificationMessage);
    }
}