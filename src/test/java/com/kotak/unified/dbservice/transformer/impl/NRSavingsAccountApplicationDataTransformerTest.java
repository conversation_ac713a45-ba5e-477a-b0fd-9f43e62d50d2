package com.kotak.unified.dbservice.transformer.impl;

import com.kotak.unified.common.exception.InvalidRequestException;
import com.kotak.unified.db.ApplicationData;
import com.kotak.unified.db.ApplicationDataFilter;
import com.kotak.unified.db.GetApplicationDataRequest;
import com.kotak.unified.db.PersonalDetailsResponse;
import com.kotak.unified.db.TagResponse;
import com.kotak.unified.db.nr.NRAccountTypeResponse;
import com.kotak.unified.db.nr.NRSavingsAccountApplicationData;
import com.kotak.unified.dbservice.exceptions.EntityNotFoundException;
import com.kotak.unified.orchestrator.common.dbmodels.Address;
import com.kotak.unified.orchestrator.common.dbmodels.AddressProof;
import com.kotak.unified.orchestrator.common.dbmodels.BranchCodeDetails;
import com.kotak.unified.orchestrator.common.dbmodels.CPVAttemptDetails;
import com.kotak.unified.orchestrator.common.dbmodels.ExecutionData;
import com.kotak.unified.orchestrator.common.dbmodels.NRSavingsAccountJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.PanDetails;
import com.kotak.unified.orchestrator.common.dbmodels.PersonalDetails;
import com.kotak.unified.orchestrator.common.dbmodels.SavingsAccountJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.Tag;
import com.kotak.unified.orchestrator.common.dbmodels.UserStatus;
import com.kotak.unified.orchestrator.common.dbmodels.nr.DocumentAvailableType;
import com.kotak.unified.orchestrator.common.dbmodels.nr.NRAccountType;
import com.kotak.unified.orchestrator.common.dbmodels.nr.NRProductSpecifications;
import com.kotak.unified.orchestrator.common.dbmodels.nr.NRTaxDetails;
import com.kotak.unified.orchestrator.common.dbmodels.nr.NRUploadedDocumentLinks;
import com.kotak.unified.orchestrator.common.dbmodels.nr.OCIDetails;
import com.kotak.unified.orchestrator.common.dbmodels.nr.PassportDetails;
import com.kotak.unified.orchestrator.common.dbmodels.nr.VisaDetails;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.modelmapper.ModelMapper;
import org.modelmapper.convention.MatchingStrategies;

import java.util.Arrays;
import java.util.List;

public class NRSavingsAccountApplicationDataTransformerTest {
    public static final String OVERSEAS_ADDRESS = "overseasAddress";
    private final NRSavingsAccountApplicationDataTransformer nrSavingsAccountApplicationDataTransformer;

    NRSavingsAccountApplicationDataTransformerTest() {
        ModelMapper modelMapper = new ModelMapper();
        modelMapper.getConfiguration().setMatchingStrategy(MatchingStrategies.STRICT);
        nrSavingsAccountApplicationDataTransformer = new NRSavingsAccountApplicationDataTransformer(modelMapper);
    }

    @Test
    public void test_transformation_valid_NR_DocumentType_Visa_lead_OVD() throws EntityNotFoundException, InvalidRequestException {
        GetApplicationDataRequest request = GetApplicationDataRequest.builder()
                .dataFilters(getApplicationDataFilterList())
                .build();
        NRTaxDetails nrTaxDetails = NRTaxDetails.builder()
                .hasPanNumber(true)
                .panDetails(PanDetails.builder()
                                    .number("pan")
                                    .displayName("displayName")
                                    .build())
                .build();
        PassportDetails passportDetails = PassportDetails.builder()
                .dateOfBirth("01/01/1990")
                .gender("gender")
                .issueDate("01/01/1990")
                .expiryDate("01/01/1990")
                .gender("gender")
                .nationality(Tag.builder().code("c1").value("nationality").build())
                .givenName("givenName")
                .surname("surname")
                .build();
        VisaDetails visaDetails = VisaDetails.builder()
                .expiryDate("01/01/1990")
                .issueDate("01/01/1990")
                .visaIssuanceCountry(Tag.builder().code("v1").value("visaIssuanceCountry").build())
                .visaType("visaType")
                .build();
        Address overseasAddress = Address.builder().line1("oa1").line2("oa2").line3("oa3").city("oc1").state("os1")
                .country("oco1").pincode("op1").build();
        Address secondAddress = Address.builder().line1("ca1").line2("ca2").line3("ca3").city("cc1").state("cs1")
                .country("cco1").pincode("cp1").build();
        AddressProof secondAddressProof = AddressProof.builder()
                .documentCode("docCode")
                .documentName("docName")
                .documentNumber("1234567")
                .documentType("docType")
                .build();
        AddressProof overseasAddressProof = AddressProof.builder()
                .documentCode("docCode")
                .documentName("docName")
                .documentNumber("1234567")
                .documentType("OVD")
                .build();
        NRProductSpecifications nrProductSpecifications = NRProductSpecifications.builder()
                .nreSchemeCode("nreSchemeCode")
                .nroSchemeCode("nroSchemeCode")
                .productName("productName")
                .productName("productVariant")
                .rmCode("rmCode")
                .build();
        PersonalDetails personalDetails = PersonalDetails.builder()
                .motherName("motherName")
                .fatherName("fatherName")
                .corpDetails(null)
                .annualSalary(1000000)
                .annualIncome(Tag.builder().build())
                .occupation(Tag.builder().build())
                .maritalStatus(Tag.builder().build())
                .sourceOfIncome(Tag.builder().build())
                .countryOfBirth(Tag.builder().code("IN").value("INDIA").build())
                .build();

        PersonalDetailsResponse personalDetailsResponse = PersonalDetailsResponse.builder()
                .motherName("motherName")
                .fatherName("fatherName")
                .corpDetails(null)
                .annualSalary(1000000)
                .annualIncome(TagResponse.builder().build())
                .occupation(TagResponse.builder().build())
                .maritalStatus(TagResponse.builder().build())
                .sourceOfIncome(TagResponse.builder().build())
                .countryOfBirth(TagResponse.builder().code("IN").value("INDIA").build())
                .build();
        NRSavingsAccountJourneyMetadata journeyMetadata = NRSavingsAccountJourneyMetadata.builder()
                .passportDetails(passportDetails)
                .visaDetails(visaDetails)
                .secondAddress(secondAddress)
                .overseasAddress(overseasAddress)
                .addressTypeForCommunication(OVERSEAS_ADDRESS)
                .isOverseasAddressProvided(true)
                .isSecondAddressProvided(true)
                .productSpecifications(nrProductSpecifications)
                .isdCode("isdCode")
                .emailAddress("<EMAIL>")
                .documentAvailableType(DocumentAvailableType.VISA)
                .preferredBranchDetails(BranchCodeDetails.builder().branchCode("1234").build())
                .personalDetails(personalDetails)
                .nrTaxDetails(nrTaxDetails)
                .secondAddressProof(secondAddressProof)
                .overseasAddressProof(overseasAddressProof)
                .isNomineeSkipped(true)
                .latestCpvAttemptActionTrackingId("NR_123456789")
                .accountTypes(List.of(NRAccountType.NRO, NRAccountType.NRE))
                .build();

        UserStatus userStatus = UserStatus.builder()
                .journeyType("NRSavingsAccount")
                .leadTrackingNumber("leadId")
                .phoneNumber("phoneNumber")
                .panNumber("panNumber")
                .executionData(ExecutionData.builder().isEmailAddressVerified(true).build())
                .journeyMetadata(journeyMetadata)
                .build();

        ApplicationData applicationData = this.nrSavingsAccountApplicationDataTransformer.populateApplicationData(request, userStatus);
        NRSavingsAccountApplicationData nrAccountActivationApplicationData = (NRSavingsAccountApplicationData) applicationData;
        Assertions.assertNotNull(applicationData);
        Assertions.assertInstanceOf(NRSavingsAccountApplicationData.class, applicationData);
        Assertions.assertEquals(nrAccountActivationApplicationData.getPhoneNumber(), "phoneNumber");
        Assertions.assertEquals(nrAccountActivationApplicationData.getIsdCode(), "isdCode");
        Assertions.assertEquals("<EMAIL>", nrAccountActivationApplicationData.getEmailAddress());
        Assertions.assertEquals(nrAccountActivationApplicationData.getNrTaxDetails().getPanDetailsResponse().getNumber(), nrTaxDetails.getPanDetails().getNumber());
        Assertions.assertEquals(nrAccountActivationApplicationData.getNrTaxDetails().getPanDetailsResponse().getDisplayName(), nrTaxDetails.getPanDetails().getDisplayName());
        Assertions.assertEquals(nrAccountActivationApplicationData.getIsPanAvailable(), nrTaxDetails.getHasPanNumber());
        Assertions.assertEquals(nrAccountActivationApplicationData.getPassportDetails().getPassportNumber(), passportDetails.getPassportNumber());
        Assertions.assertEquals(nrAccountActivationApplicationData.getPassportDetails().getPassportName(), passportDetails.getGivenName());
        Assertions.assertEquals(nrAccountActivationApplicationData.getPassportDetails().getPassportSurname(), passportDetails.getSurname());
        Assertions.assertEquals(nrAccountActivationApplicationData.getPassportDetails().getGender(), passportDetails.getGender());

        Assertions.assertEquals(nrAccountActivationApplicationData.getPassportDetails().getNationality().getCode(), passportDetails.getNationality().getCode());
        Assertions.assertEquals(nrAccountActivationApplicationData.getPassportDetails().getNationality().getValue(), passportDetails.getNationality().getValue());

        Assertions.assertEquals(nrAccountActivationApplicationData.getPersonalDetailsResponse(), personalDetailsResponse);
        Assertions.assertEquals(nrAccountActivationApplicationData.getNrProductSpecifications().getClassification(), nrProductSpecifications.getClassification());

        Assertions.assertEquals(nrAccountActivationApplicationData.getNrProductSpecifications().getProductVariant(), nrProductSpecifications.getProductVariant());

        Assertions.assertEquals(nrAccountActivationApplicationData.getVisaDetails().getVisaType(), visaDetails.getVisaType());
        Assertions.assertEquals(nrAccountActivationApplicationData.getVisaDetails().getVisaExpiryDate(), visaDetails.getExpiryDate());
        Assertions.assertEquals(nrAccountActivationApplicationData.getVisaDetails().getVisaIssueDate(), visaDetails.getIssueDate());
        Assertions.assertEquals(nrAccountActivationApplicationData.getVisaDetails().getVisaIssuanceCountry().getCode(), visaDetails.getVisaIssuanceCountry().getCode());
        Assertions.assertEquals(nrAccountActivationApplicationData.getVisaDetails().getVisaIssuanceCountry().getValue(), visaDetails.getVisaIssuanceCountry().getValue());

        Assertions.assertEquals(nrAccountActivationApplicationData.getSecondAddressProof().getDocumentName(), secondAddressProof.getDocumentName());
        Assertions.assertEquals(nrAccountActivationApplicationData.getSecondAddressProof().getDocumentCode(), secondAddressProof.getDocumentCode());
        Assertions.assertEquals(nrAccountActivationApplicationData.getSecondAddressProof().getDocumentType(), secondAddressProof.getDocumentType());

        Assertions.assertEquals(nrAccountActivationApplicationData.getOverseasAddressProof().getDocumentName(), overseasAddressProof.getDocumentName());
        Assertions.assertEquals(nrAccountActivationApplicationData.getOverseasAddressProof().getDocumentCode(), overseasAddressProof.getDocumentCode());
        Assertions.assertEquals(nrAccountActivationApplicationData.getOverseasAddressProof().getDocumentType(), overseasAddressProof.getDocumentType());

        Assertions.assertEquals(nrAccountActivationApplicationData.getSecondAddress().getCity(), secondAddress.getCity());
        Assertions.assertEquals(nrAccountActivationApplicationData.getSecondAddress().getCountry(), secondAddress.getCountry());
        Assertions.assertEquals(nrAccountActivationApplicationData.getSecondAddress().getLine1(), secondAddress.getLine1());
        Assertions.assertEquals(nrAccountActivationApplicationData.getSecondAddress().getLine3(), secondAddress.getLine3());
        Assertions.assertEquals(nrAccountActivationApplicationData.getSecondAddress().getLine2(), secondAddress.getLine2());
        Assertions.assertEquals(nrAccountActivationApplicationData.getSecondAddress().getState(), secondAddress.getState());
        Assertions.assertEquals(nrAccountActivationApplicationData.getSecondAddress().getPincode(), secondAddress.getPincode());
        Assertions.assertEquals(nrAccountActivationApplicationData.getOverseasAddress().getCity(), overseasAddress.getCity());
        Assertions.assertEquals(nrAccountActivationApplicationData.getOverseasAddress().getCountry(), overseasAddress.getCountry());
        Assertions.assertEquals(nrAccountActivationApplicationData.getOverseasAddress().getLine1(), overseasAddress.getLine1());
        Assertions.assertEquals(nrAccountActivationApplicationData.getOverseasAddress().getLine3(), overseasAddress.getLine3());
        Assertions.assertEquals(nrAccountActivationApplicationData.getOverseasAddress().getLine2(), overseasAddress.getLine2());
        Assertions.assertEquals(nrAccountActivationApplicationData.getOverseasAddress().getState(), overseasAddress.getState());
        Assertions.assertEquals(nrAccountActivationApplicationData.getOverseasAddress().getPincode(), overseasAddress.getPincode());
        Assertions.assertEquals(nrAccountActivationApplicationData.getAddressTypeForCommunication(), OVERSEAS_ADDRESS);
        Assertions.assertEquals(nrAccountActivationApplicationData.getIsOverseasAddressProvided(), true);
        Assertions.assertEquals(nrAccountActivationApplicationData.getIsSecondAddressProvided(), true);
        Assertions.assertEquals(nrAccountActivationApplicationData.getIsNomineeSkipped(), true);
        Assertions.assertEquals(nrAccountActivationApplicationData.getActionTrackingId(), "NR_123456789");
        Assertions.assertEquals(nrAccountActivationApplicationData.getAccountTypes(), List.of(NRAccountTypeResponse.NRO, NRAccountTypeResponse.NRE));
        Assertions.assertEquals("VISA", nrAccountActivationApplicationData.getDocumentAvailableType());
        Assertions.assertEquals(nrAccountActivationApplicationData.getDocumentAvailableType(), "VISA");
        Assertions.assertEquals("1234", nrAccountActivationApplicationData.getBranchCodeDetailsResponse().getBranchCode());
    }

    @Test
    public void test_transformation_valid_NR_DocumentType_Visa_lead_DeemedOVD() throws EntityNotFoundException, InvalidRequestException {
        GetApplicationDataRequest request = GetApplicationDataRequest.builder()
                .dataFilters(getApplicationDataFilterList())
                .build();
        NRTaxDetails nrTaxDetails = NRTaxDetails.builder()
                .hasPanNumber(true)
                .panDetails(PanDetails.builder()
                                    .number("pan")
                                    .displayName("displayName")
                                    .build())
                .build();
        Address overseasAddress = Address.builder().line1("oa1").line2("oa2").line3("oa3").city("oc1").state("os1")
                .country("oco1").pincode("op1").build();
        Address secondAddress = Address.builder().line1("ca1").line2("ca2").line3("ca3").city("cc1").state("cs1")
                .country("cco1").pincode("cp1").build();
        AddressProof secondAddressProof = AddressProof.builder()
                .documentCode("docCode")
                .documentName("docName")
                .documentNumber("1234567")
                .documentType("docType")
                .build();
        AddressProof overseasAddressProof = AddressProof.builder()
                .documentCode("docCode")
                .documentName("docName")
                .documentNumber("1234567")
                .documentType("DeemedOVD")
                .build();
        NRProductSpecifications nrProductSpecifications = NRProductSpecifications.builder()
                .nreSchemeCode("nreSchemeCode")
                .nroSchemeCode("nroSchemeCode")
                .productName("productName")
                .rmCode("rmCode")
                .build();
        PersonalDetails personalDetails = PersonalDetails.builder()
                .motherName("motherName")
                .fatherName("fatherName")
                .corpDetails(null)
                .annualSalary(1000000)
                .annualIncome(Tag.builder().build())
                .occupation(Tag.builder().build())
                .maritalStatus(Tag.builder().build())
                .sourceOfIncome(Tag.builder().build())
                .countryOfBirth(Tag.builder().code("IN").value("INDIA").build())
                .build();

        NRSavingsAccountJourneyMetadata journeyMetadata = NRSavingsAccountJourneyMetadata.builder()
                .passportDetails(null)
                .visaDetails(null)
                .secondAddress(secondAddress)
                .overseasAddress(overseasAddress)
                .addressTypeForCommunication(OVERSEAS_ADDRESS)
                .isOverseasAddressProvided(true)
                .isSecondAddressProvided(true)
                .productSpecifications(nrProductSpecifications)
                .isdCode("isdCode")
                .emailAddress("<EMAIL>")
                .countryOfResidence(Tag.builder().code("US").value("United States of America").build())
                .documentAvailableType(DocumentAvailableType.VISA)
                .personalDetails(personalDetails)
                .nrTaxDetails(nrTaxDetails)
                .secondAddressProof(secondAddressProof)
                .overseasAddressProof(overseasAddressProof)
                .isNomineeSkipped(true)
                .latestCpvAttemptActionTrackingId("NR_123456789")
                .preferredBranchDetails(BranchCodeDetails.builder().branchCode("1234").build())
                .accountTypes(List.of(NRAccountType.NRO, NRAccountType.NRE))
                .build();

        UserStatus userStatus = UserStatus.builder()
                .journeyType("NRSavingsAccount")
                .leadTrackingNumber("leadId")
                .phoneNumber("phoneNumber")
                .panNumber("panNumber")
                .executionData(ExecutionData.builder().isEmailAddressVerified(true).build())
                .journeyMetadata(journeyMetadata)
                .build();

        ApplicationData applicationData = this.nrSavingsAccountApplicationDataTransformer.populateApplicationData(request, userStatus);
        NRSavingsAccountApplicationData nrAccountActivationApplicationData = (NRSavingsAccountApplicationData) applicationData;
        Assertions.assertNotNull(applicationData);
        Assertions.assertInstanceOf(NRSavingsAccountApplicationData.class, applicationData);
        Assertions.assertEquals(nrAccountActivationApplicationData.getPhoneNumber(), "phoneNumber");
        Assertions.assertEquals(nrAccountActivationApplicationData.getIsdCode(), "isdCode");
        Assertions.assertEquals("<EMAIL>", nrAccountActivationApplicationData.getEmailAddress());
        Assertions.assertEquals(TagResponse.builder().code("US").value("United States of America").build(),
                                nrAccountActivationApplicationData.getCountryOfResidence());
        Assertions.assertEquals(nrAccountActivationApplicationData.getNrTaxDetails().getPanDetailsResponse().getNumber(), nrTaxDetails.getPanDetails().getNumber());
        Assertions.assertEquals(nrAccountActivationApplicationData.getNrTaxDetails().getPanDetailsResponse().getDisplayName(), nrTaxDetails.getPanDetails().getDisplayName());
        Assertions.assertEquals(nrAccountActivationApplicationData.getIsPanAvailable(), nrTaxDetails.getHasPanNumber());
        Assertions.assertEquals(nrAccountActivationApplicationData.getNrProductSpecifications().getClassification(), nrProductSpecifications.getClassification());


        Assertions.assertEquals(nrAccountActivationApplicationData.getSecondAddressProof().getDocumentName(), secondAddressProof.getDocumentName());
        Assertions.assertEquals(nrAccountActivationApplicationData.getSecondAddressProof().getDocumentCode(), secondAddressProof.getDocumentCode());
        Assertions.assertEquals(nrAccountActivationApplicationData.getSecondAddressProof().getDocumentType(), secondAddressProof.getDocumentType());

        Assertions.assertEquals(nrAccountActivationApplicationData.getOverseasAddressProof().getDocumentName(), overseasAddressProof.getDocumentName());
        Assertions.assertEquals(nrAccountActivationApplicationData.getOverseasAddressProof().getDocumentCode(), overseasAddressProof.getDocumentCode());
        Assertions.assertEquals(nrAccountActivationApplicationData.getOverseasAddressProof().getDocumentType(), overseasAddressProof.getDocumentType());

        Assertions.assertEquals(nrAccountActivationApplicationData.getSecondAddress().getCity(), secondAddress.getCity());
        Assertions.assertEquals(nrAccountActivationApplicationData.getSecondAddress().getCountry(), secondAddress.getCountry());
        Assertions.assertEquals(nrAccountActivationApplicationData.getSecondAddress().getLine1(), secondAddress.getLine1());
        Assertions.assertEquals(nrAccountActivationApplicationData.getSecondAddress().getLine3(), secondAddress.getLine3());
        Assertions.assertEquals(nrAccountActivationApplicationData.getSecondAddress().getLine2(), secondAddress.getLine2());
        Assertions.assertEquals(nrAccountActivationApplicationData.getSecondAddress().getState(), secondAddress.getState());
        Assertions.assertEquals(nrAccountActivationApplicationData.getSecondAddress().getPincode(), secondAddress.getPincode());
        Assertions.assertEquals(nrAccountActivationApplicationData.getOverseasAddress().getCity(), overseasAddress.getCity());
        Assertions.assertEquals(nrAccountActivationApplicationData.getOverseasAddress().getCountry(), overseasAddress.getCountry());
        Assertions.assertEquals(nrAccountActivationApplicationData.getOverseasAddress().getLine1(), overseasAddress.getLine1());
        Assertions.assertEquals(nrAccountActivationApplicationData.getOverseasAddress().getLine3(), overseasAddress.getLine3());
        Assertions.assertEquals(nrAccountActivationApplicationData.getOverseasAddress().getLine2(), overseasAddress.getLine2());
        Assertions.assertEquals(nrAccountActivationApplicationData.getOverseasAddress().getState(), overseasAddress.getState());
        Assertions.assertEquals(nrAccountActivationApplicationData.getOverseasAddress().getPincode(), overseasAddress.getPincode());
        Assertions.assertEquals(nrAccountActivationApplicationData.getAddressTypeForCommunication(), OVERSEAS_ADDRESS);
        Assertions.assertEquals(nrAccountActivationApplicationData.getIsOverseasAddressProvided(), true);
        Assertions.assertEquals(nrAccountActivationApplicationData.getIsSecondAddressProvided(), true);
        Assertions.assertEquals(nrAccountActivationApplicationData.getIsNomineeSkipped(), true);
        Assertions.assertEquals(nrAccountActivationApplicationData.getActionTrackingId(), "NR_123456789");
        Assertions.assertEquals(nrAccountActivationApplicationData.getAccountTypes(), List.of(NRAccountTypeResponse.NRO, NRAccountTypeResponse.NRE));
        Assertions.assertEquals("VISA", nrAccountActivationApplicationData.getDocumentAvailableType());
        Assertions.assertEquals("1234", nrAccountActivationApplicationData.getBranchCodeDetailsResponse().getBranchCode());
    }

    // test transformation PERSONAL_DETAILS with null fields such as maritalStatus and others
    @Test
    public void test_transformation_personalDetails_nullFields() throws InvalidRequestException, EntityNotFoundException {
        PersonalDetails personalDetails = PersonalDetails.builder()
                .build();

        NRSavingsAccountJourneyMetadata journeyMetadata = NRSavingsAccountJourneyMetadata.builder()
                .personalDetails(personalDetails)
                .build();
        UserStatus userStatus = UserStatus.builder()
                .journeyType("NRSavingsAccount")
                .leadTrackingNumber("leadId")
                .phoneNumber("phoneNumber")
                .panNumber("panNumber")
                .journeyMetadata(journeyMetadata)
                .build();

        GetApplicationDataRequest request = GetApplicationDataRequest.builder()
                .dataFilters(List.of(
                        ApplicationDataFilter.PERSONAL_DETAILS))
                .build();
        ApplicationData applicationData = this.nrSavingsAccountApplicationDataTransformer.populateApplicationData(request, userStatus);
        NRSavingsAccountApplicationData nrAccountActivationApplicationData = (NRSavingsAccountApplicationData) applicationData;
        PersonalDetailsResponse personalDetailsResponse = nrAccountActivationApplicationData.getPersonalDetailsResponse();

        Assertions.assertEquals(personalDetails.getMotherName(), personalDetailsResponse.getMotherName());
        Assertions.assertEquals(personalDetails.getFatherName(), personalDetailsResponse.getFatherName());
        Assertions.assertEquals(personalDetails.getAnnualSalary(), personalDetailsResponse.getAnnualSalary());
        Assertions.assertNull(personalDetailsResponse.getCountryOfBirth());
    }

    @Test
    public void test_transformation_valid_NR_DocumentType_Oci_lead() throws EntityNotFoundException, InvalidRequestException {
        GetApplicationDataRequest request = GetApplicationDataRequest.builder()
                .dataFilters(getApplicationDataFilterList())
                .build();
        NRTaxDetails nrTaxDetails = NRTaxDetails.builder()
                .hasPanNumber(true)
                .panDetails(PanDetails.builder()
                                    .number("pan")
                                    .displayName("displayName")
                                    .build())
                .build();
        PassportDetails passportDetails = PassportDetails.builder()
                .dateOfBirth("01/01/1990")
                .gender("gender")
                .issueDate("01/01/1990")
                .expiryDate("01/01/1990")
                .gender("gender")
                .nationality(Tag.builder().code("c1").value("nationality").build())
                .givenName("givenName")
                .surname("surname")
                .build();
        OCIDetails ociDetails = OCIDetails.builder()
                .ociNumber("ociNumber")
                .dateOfIssue("dateOfIssue")
                .placeOfIssue("placeOfIssue")
                .build();
        Address overseasAddress = Address.builder().line1("oa1").line2("oa2").line3("oa3").city("oc1").state("os1")
                .country("oco1").pincode("op1").build();
        Address secondAddress = Address.builder().line1("ca1").line2("ca2").line3("ca3").city("cc1").state("cs1")
                .country("cco1").pincode("cp1").build();
        AddressProof secondAddressProof = AddressProof.builder()
                .documentCode("docCode")
                .documentName("docName")
                .documentNumber("1234567")
                .documentType("docType")
                .build();
        AddressProof overseasAddressProof = AddressProof.builder()
                .documentCode("docCode")
                .documentName("docName")
                .documentNumber("1234567")
                .documentType("OVD")
                .build();
        NRProductSpecifications nrProductSpecifications = NRProductSpecifications.builder()
                .nreSchemeCode("nreSchemeCode")
                .nroSchemeCode("nroSchemeCode")
                .productName("productName")
                .rmCode("rmCode")
                .build();
        PersonalDetails personalDetails = PersonalDetails.builder()
                .motherName("motherName")
                .fatherName("fatherName")
                .corpDetails(null)
                .annualSalary(1000000)
                .annualIncome(Tag.builder().build())
                .occupation(Tag.builder().build())
                .maritalStatus(Tag.builder().build())
                .sourceOfIncome(Tag.builder().build())
                .countryOfBirth(Tag.builder().code("IN").value("INDIA").build())
                .build();

        PersonalDetailsResponse personalDetailsResponse = PersonalDetailsResponse.builder()
                .motherName("motherName")
                .fatherName("fatherName")
                .corpDetails(null)
                .annualSalary(1000000)
                .annualIncome(TagResponse.builder().build())
                .occupation(TagResponse.builder().build())
                .maritalStatus(TagResponse.builder().build())
                .sourceOfIncome(TagResponse.builder().build())
                .countryOfBirth(TagResponse.builder().code("IN").value("INDIA").build())
                .build();
        NRSavingsAccountJourneyMetadata journeyMetadata = NRSavingsAccountJourneyMetadata.builder()
                .passportDetails(passportDetails)
                .ociDetails(ociDetails)
                .secondAddress(secondAddress)
                .overseasAddress(overseasAddress)
                .addressTypeForCommunication(OVERSEAS_ADDRESS)
                .isOverseasAddressProvided(true)
                .isSecondAddressProvided(true)
                .productSpecifications(nrProductSpecifications)
                .isdCode("isdCode")
                .emailAddress(null)
                .countryOfResidence(null)
                .documentAvailableType(DocumentAvailableType.OCI)
                .preferredBranchDetails(BranchCodeDetails.builder().branchCode("1234").build())
                .personalDetails(personalDetails)
                .nrTaxDetails(nrTaxDetails)
                .secondAddressProof(secondAddressProof)
                .overseasAddressProof(overseasAddressProof)
                .build();

        UserStatus userStatus = UserStatus.builder()
                .journeyType("NRSavingsAccount")
                .leadTrackingNumber("leadId")
                .phoneNumber("phoneNumber")
                .panNumber("panNumber")
                .executionData(ExecutionData.builder().isEmailAddressVerified(true).build())
                .journeyMetadata(journeyMetadata)
                .build();

        ApplicationData applicationData = this.nrSavingsAccountApplicationDataTransformer.populateApplicationData(request, userStatus);
        NRSavingsAccountApplicationData nrAccountActivationApplicationData = (NRSavingsAccountApplicationData) applicationData;
        Assertions.assertNotNull(applicationData);
        Assertions.assertInstanceOf(NRSavingsAccountApplicationData.class, applicationData);
        Assertions.assertEquals(nrAccountActivationApplicationData.getPhoneNumber(), "phoneNumber");
        Assertions.assertEquals(nrAccountActivationApplicationData.getIsdCode(), "isdCode");
        Assertions.assertNull(nrAccountActivationApplicationData.getEmailAddress());
        Assertions.assertNull(nrAccountActivationApplicationData.getCountryOfResidence());
        Assertions.assertEquals(nrAccountActivationApplicationData.getNrTaxDetails().getPanDetailsResponse().getNumber(), nrTaxDetails.getPanDetails().getNumber());
        Assertions.assertEquals(nrAccountActivationApplicationData.getNrTaxDetails().getPanDetailsResponse().getDisplayName(), nrTaxDetails.getPanDetails().getDisplayName());
        Assertions.assertEquals(nrAccountActivationApplicationData.getIsPanAvailable(), nrTaxDetails.getHasPanNumber());
        Assertions.assertEquals(nrAccountActivationApplicationData.getPassportDetails().getPassportNumber(), passportDetails.getPassportNumber());
        Assertions.assertEquals(nrAccountActivationApplicationData.getPassportDetails().getPassportName(), passportDetails.getGivenName());
        Assertions.assertEquals(nrAccountActivationApplicationData.getPassportDetails().getPassportSurname(), passportDetails.getSurname());
        Assertions.assertEquals(nrAccountActivationApplicationData.getPassportDetails().getGender(), passportDetails.getGender());

        Assertions.assertEquals(nrAccountActivationApplicationData.getPassportDetails().getNationality().getCode(), passportDetails.getNationality().getCode());
        Assertions.assertEquals(nrAccountActivationApplicationData.getPassportDetails().getNationality().getValue(), passportDetails.getNationality().getValue());

        Assertions.assertEquals(nrAccountActivationApplicationData.getPersonalDetailsResponse(), personalDetailsResponse);
        Assertions.assertEquals(nrAccountActivationApplicationData.getNrProductSpecifications().getClassification(), nrProductSpecifications.getClassification());
        Assertions.assertEquals(nrAccountActivationApplicationData.getNrOciDetailsResponse().getOciNumber(), ociDetails.getOciNumber());
        Assertions.assertEquals(nrAccountActivationApplicationData.getNrOciDetailsResponse().getDateOfIssue(), ociDetails.getDateOfIssue());
        Assertions.assertEquals(nrAccountActivationApplicationData.getNrOciDetailsResponse().getPlaceOfIssue(), ociDetails.getPlaceOfIssue());

        Assertions.assertEquals(nrAccountActivationApplicationData.getSecondAddressProof().getDocumentName(), secondAddressProof.getDocumentName());
        Assertions.assertEquals(nrAccountActivationApplicationData.getSecondAddressProof().getDocumentCode(), secondAddressProof.getDocumentCode());
        Assertions.assertEquals(nrAccountActivationApplicationData.getSecondAddressProof().getDocumentType(), secondAddressProof.getDocumentType());

        Assertions.assertEquals(nrAccountActivationApplicationData.getOverseasAddressProof().getDocumentName(), overseasAddressProof.getDocumentName());
        Assertions.assertEquals(nrAccountActivationApplicationData.getOverseasAddressProof().getDocumentCode(), overseasAddressProof.getDocumentCode());
        Assertions.assertEquals(nrAccountActivationApplicationData.getOverseasAddressProof().getDocumentType(), overseasAddressProof.getDocumentType());

        Assertions.assertEquals(nrAccountActivationApplicationData.getSecondAddress().getCity(), secondAddress.getCity());
        Assertions.assertEquals(nrAccountActivationApplicationData.getSecondAddress().getCountry(), secondAddress.getCountry());
        Assertions.assertEquals(nrAccountActivationApplicationData.getSecondAddress().getLine1(), secondAddress.getLine1());
        Assertions.assertEquals(nrAccountActivationApplicationData.getSecondAddress().getLine3(), secondAddress.getLine3());
        Assertions.assertEquals(nrAccountActivationApplicationData.getSecondAddress().getLine2(), secondAddress.getLine2());
        Assertions.assertEquals(nrAccountActivationApplicationData.getSecondAddress().getState(), secondAddress.getState());
        Assertions.assertEquals(nrAccountActivationApplicationData.getSecondAddress().getPincode(), secondAddress.getPincode());
        Assertions.assertEquals(nrAccountActivationApplicationData.getOverseasAddress().getCity(), overseasAddress.getCity());
        Assertions.assertEquals(nrAccountActivationApplicationData.getOverseasAddress().getCountry(), overseasAddress.getCountry());
        Assertions.assertEquals(nrAccountActivationApplicationData.getOverseasAddress().getLine1(), overseasAddress.getLine1());
        Assertions.assertEquals(nrAccountActivationApplicationData.getOverseasAddress().getLine3(), overseasAddress.getLine3());
        Assertions.assertEquals(nrAccountActivationApplicationData.getOverseasAddress().getLine2(), overseasAddress.getLine2());
        Assertions.assertEquals(nrAccountActivationApplicationData.getOverseasAddress().getState(), overseasAddress.getState());
        Assertions.assertEquals(nrAccountActivationApplicationData.getOverseasAddress().getPincode(), overseasAddress.getPincode());
        Assertions.assertEquals(nrAccountActivationApplicationData.getAddressTypeForCommunication(), OVERSEAS_ADDRESS);
        Assertions.assertEquals(nrAccountActivationApplicationData.getIsOverseasAddressProvided(), true);
        Assertions.assertEquals(nrAccountActivationApplicationData.getIsSecondAddressProvided(), true);
        Assertions.assertEquals(nrAccountActivationApplicationData.getDocumentAvailableType(), "OCI");
        Assertions.assertEquals("1234", nrAccountActivationApplicationData.getBranchCodeDetailsResponse().getBranchCode());
    }

    @Test
    public void test_transformation_latest_cpv_attempt_details() throws EntityNotFoundException, InvalidRequestException {
        GetApplicationDataRequest request = GetApplicationDataRequest.builder()
                .dataFilters(List.of(ApplicationDataFilter.LATEST_CPV_ATTEMPT_DETAILS))
                .build();

        CPVAttemptDetails cpvAttemptDetails = CPVAttemptDetails.builder()
                .cpvDVUStatus("status")
                .actionTrackingId("trackingId")
                .rejectionReason("reason")
                .sectionsRejected(Arrays.asList("section1", "section2"))
                .build();

        NRSavingsAccountJourneyMetadata journeyMetadata = NRSavingsAccountJourneyMetadata.builder()
                .latestCpvAttemptDetails(cpvAttemptDetails)
                .build();

        UserStatus userStatus = UserStatus.builder()
                .journeyType("NRSavingsAccount")
                .leadTrackingNumber("leadId")
                .journeyMetadata(journeyMetadata)
                .build();

        ApplicationData applicationData = this.nrSavingsAccountApplicationDataTransformer.populateApplicationData(request, userStatus);
        NRSavingsAccountApplicationData nrAccountActivationApplicationData = (NRSavingsAccountApplicationData) applicationData;

        Assertions.assertNotNull(applicationData);
        Assertions.assertInstanceOf(NRSavingsAccountApplicationData.class, applicationData);
        Assertions.assertNotNull(nrAccountActivationApplicationData.getLatestCpvAttemptDetails());
        Assertions.assertEquals("status", nrAccountActivationApplicationData.getLatestCpvAttemptDetails().getCpvDVUStatus());
        Assertions.assertEquals("trackingId", nrAccountActivationApplicationData.getLatestCpvAttemptDetails().getActionTrackingId());
        Assertions.assertEquals("reason", nrAccountActivationApplicationData.getLatestCpvAttemptDetails().getRejectionReason());
        Assertions.assertEquals(Arrays.asList("section1", "section2"), nrAccountActivationApplicationData.getLatestCpvAttemptDetails().getSectionsRejected());
    }

    @Test
    public void test_transformation_uploaded_document_links() throws EntityNotFoundException, InvalidRequestException {
        GetApplicationDataRequest request = GetApplicationDataRequest.builder()
                .dataFilters(List.of(ApplicationDataFilter.UPLOADED_DOCUMENT_LINKS))
                .build();

        NRUploadedDocumentLinks uploadedDocumentLinks = NRUploadedDocumentLinks.builder()
                .passportImages(Arrays.asList("passportImage1", "passportImage2"))
                .visaImages(Arrays.asList("visaImage1", "visaImage2"))
                .ociImages(Arrays.asList("ociImage1", "ociImage2"))
                .overseasAddressImages(Arrays.asList("overseasAddressImage1", "overseasAddressImage2"))
                .secondAddressImages(Arrays.asList("secondAddressImage1", "secondAddressImage2"))
                .signatureImage("signatureImage")
                .customerImage("customerImage")
                .build();

        NRSavingsAccountJourneyMetadata journeyMetadata = NRSavingsAccountJourneyMetadata.builder()
                .uploadedDocumentLinks(uploadedDocumentLinks)
                .build();

        UserStatus userStatus = UserStatus.builder()
                .journeyType("NRSavingsAccount")
                .leadTrackingNumber("leadId")
                .journeyMetadata(journeyMetadata)
                .build();

        ApplicationData applicationData = this.nrSavingsAccountApplicationDataTransformer.populateApplicationData(request, userStatus);
        NRSavingsAccountApplicationData nrAccountActivationApplicationData = (NRSavingsAccountApplicationData) applicationData;

        Assertions.assertNotNull(applicationData);
        Assertions.assertInstanceOf(NRSavingsAccountApplicationData.class, applicationData);
        Assertions.assertNotNull(nrAccountActivationApplicationData.getUploadedDocumentLinks());
        Assertions.assertEquals(Arrays.asList("passportImage1", "passportImage2"), nrAccountActivationApplicationData.getUploadedDocumentLinks().getPassportImages());
        Assertions.assertEquals(Arrays.asList("visaImage1", "visaImage2"), nrAccountActivationApplicationData.getUploadedDocumentLinks().getVisaImages());
        Assertions.assertEquals(Arrays.asList("ociImage1", "ociImage2"), nrAccountActivationApplicationData.getUploadedDocumentLinks().getOciImages());
        Assertions.assertEquals(Arrays.asList("overseasAddressImage1", "overseasAddressImage2"), nrAccountActivationApplicationData.getUploadedDocumentLinks().getOverseasAddressImages());
        Assertions.assertEquals(Arrays.asList("secondAddressImage1", "secondAddressImage2"), nrAccountActivationApplicationData.getUploadedDocumentLinks().getSecondAddressImages());
        Assertions.assertEquals("signatureImage", nrAccountActivationApplicationData.getUploadedDocumentLinks().getSignatureImage());
        Assertions.assertEquals("customerImage", nrAccountActivationApplicationData.getUploadedDocumentLinks().getCustomerImage());
    }

    @Test
    public void test_transformation_null_document_available_type() throws EntityNotFoundException, InvalidRequestException {
        GetApplicationDataRequest request = GetApplicationDataRequest.builder()
                .dataFilters(List.of(ApplicationDataFilter.DOCUMENT_AVAILABLE_TYPE))
                .build();
        NRSavingsAccountJourneyMetadata journeyMetadata = NRSavingsAccountJourneyMetadata.builder().build();

        UserStatus userStatus = UserStatus.builder()
                .journeyType("NRSavingsAccount")
                .leadTrackingNumber("leadId")
                .phoneNumber("phoneNumber")
                .panNumber("panNumber")
                .executionData(ExecutionData.builder().isEmailAddressVerified(true).build())
                .journeyMetadata(journeyMetadata)
                .build();
        ApplicationData applicationData = this.nrSavingsAccountApplicationDataTransformer.populateApplicationData(request, userStatus);
        NRSavingsAccountApplicationData nrAccountActivationApplicationData = (NRSavingsAccountApplicationData) applicationData;
        Assertions.assertNotNull(applicationData);
        Assertions.assertNull(nrAccountActivationApplicationData.getDocumentAvailableType());

    }

    @Test
    public void test_transformation_branchCodeDetails_null() throws EntityNotFoundException, InvalidRequestException {
        GetApplicationDataRequest request = GetApplicationDataRequest.builder()
                .dataFilters(List.of(ApplicationDataFilter.BRANCH_CODE_DETAILS))
                .build();
        NRSavingsAccountJourneyMetadata journeyMetadata = NRSavingsAccountJourneyMetadata.builder().build();

        UserStatus userStatus = UserStatus.builder()
                .journeyType("NRSavingsAccount")
                .leadTrackingNumber("leadId")
                .phoneNumber("phoneNumber")
                .panNumber("panNumber")
                .executionData(ExecutionData.builder().isEmailAddressVerified(true).build())
                .journeyMetadata(journeyMetadata)
                .build();
        ApplicationData applicationData = this.nrSavingsAccountApplicationDataTransformer.populateApplicationData(request, userStatus);
        NRSavingsAccountApplicationData nrAccountActivationApplicationData = (NRSavingsAccountApplicationData) applicationData;
        Assertions.assertNotNull(applicationData);
        Assertions.assertNull(nrAccountActivationApplicationData.getBranchCodeDetailsResponse());

    }

    @Test
    public void test_transformation_invalid_lead_null_metadata() {
        UserStatus userStatus = UserStatus.builder().leadTrackingNumber("l1").build();

        EntityNotFoundException ir = Assertions.assertThrows(EntityNotFoundException.class, () -> this.nrSavingsAccountApplicationDataTransformer
                .populateApplicationData(null, userStatus));
        Assertions.assertEquals("No application data found for lead id : l1", ir.getMessage());
    }

    @Test
    public void test_transformation_invalid_lead() {
        UserStatus us = UserStatus.builder()
                .leadTrackingNumber("l1")
                .journeyMetadata(SavingsAccountJourneyMetadata.builder().build()).build();

        InvalidRequestException ir = Assertions.assertThrows(InvalidRequestException.class, () -> this.nrSavingsAccountApplicationDataTransformer
                .populateApplicationData(null, us));
        Assertions.assertEquals("Application Id does not belong to NR Savings account Journey for lead : l1", ir.getMessage());
    }


    private static List<ApplicationDataFilter> getApplicationDataFilterList() {
        return Arrays.asList(ApplicationDataFilter.ISD_CODE,
                             ApplicationDataFilter.EMAIL_ID,
                             ApplicationDataFilter.COUNTRY_OF_RESIDENCE,
                             ApplicationDataFilter.PASSPORT_DETAILS,
                             ApplicationDataFilter.PRODUCT_SPECIFICATIONS,
                             ApplicationDataFilter.PERSONAL_DETAILS,
                             ApplicationDataFilter.SECOND_ADDRESS,
                             ApplicationDataFilter.OVERSEAS_ADDRESS,
                             ApplicationDataFilter.ADDRESS_TYPE_FOR_COMMUNICATION,
                             ApplicationDataFilter.PHONE_NUMBER,
                             ApplicationDataFilter.TAX_DETAILS,
                             ApplicationDataFilter.DOCUMENT_AVAILABLE_TYPE,
                             ApplicationDataFilter.BRANCH_CODE_DETAILS,
                             ApplicationDataFilter.SECOND_ADDRESS_PROOF,
                             ApplicationDataFilter.OVERSEAS_ADDRESS_PROOF,
                             ApplicationDataFilter.IS_PAN_AVAILABLE,
                             ApplicationDataFilter.IS_NOMINEE_SKIPPED,
                             ApplicationDataFilter.NR_ACCOUNT_TYPES,
                             ApplicationDataFilter.CPV_ATTEMPT_ACTION_TRACKING_ID,
                             ApplicationDataFilter.OCI_DETAILS,
                             ApplicationDataFilter.VISA_DETAILS,
                             ApplicationDataFilter.IS_OVERSEAS_ADDRESS_PROVIDED,
                             ApplicationDataFilter.IS_SECOND_ADDRESS_PROVIDED
        );
    }

}
