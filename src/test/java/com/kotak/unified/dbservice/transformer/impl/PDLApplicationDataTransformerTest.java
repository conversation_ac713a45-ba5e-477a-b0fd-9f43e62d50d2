package com.kotak.unified.dbservice.transformer.impl;

import com.kotak.unified.common.exception.InvalidRequestException;
import com.kotak.unified.db.AadhaarDetailsResponse;
import com.kotak.unified.db.DisbursementDetails;
import com.kotak.unified.db.GetApplicationDataRequest;
import com.kotak.unified.db.PDLApplicationData;
import com.kotak.unified.db.PanDetailsResponse;
import com.kotak.unified.db.assets.PDLProductDetailsResponse;
import com.kotak.unified.orchestrator.common.dbmodels.AadhaarDetails;
import com.kotak.unified.orchestrator.common.dbmodels.Address;
import com.kotak.unified.orchestrator.common.dbmodels.ExecutionData;
import com.kotak.unified.orchestrator.common.dbmodels.NomineeDetails;
import com.kotak.unified.orchestrator.common.dbmodels.PanDetails;
import com.kotak.unified.orchestrator.common.dbmodels.PaydayLoanJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.UserStatus;
import com.kotak.unified.orchestrator.common.dbmodels.assets.AdditionalDetails;
import com.kotak.unified.orchestrator.common.dbmodels.assets.ApplicantDetails;
import com.kotak.unified.orchestrator.common.dbmodels.assets.CrnDetails;
import com.kotak.unified.orchestrator.common.dbmodels.assets.CrnNameMap;
import com.kotak.unified.orchestrator.common.dbmodels.assets.CustomerName;
import com.kotak.unified.orchestrator.common.dbmodels.assets.PDLProductDetails;
import java.time.Instant;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.modelmapper.ModelMapper;
import org.modelmapper.TypeToken;

import static com.kotak.unified.db.ApplicationDataFilter.AADHAAR;
import static com.kotak.unified.db.ApplicationDataFilter.ADDRESS;
import static com.kotak.unified.db.ApplicationDataFilter.BASIC_DETAIL;
import static com.kotak.unified.db.ApplicationDataFilter.BLOCKED_REASONS;
import static com.kotak.unified.db.ApplicationDataFilter.CRN;
import static com.kotak.unified.db.ApplicationDataFilter.DISBURSEMENT_DETAILS;
import static com.kotak.unified.db.ApplicationDataFilter.EMAIL_ID;
import static com.kotak.unified.db.ApplicationDataFilter.LOAN_TYPE;
import static com.kotak.unified.db.ApplicationDataFilter.MILESTONE_HISTORY_LIST;
import static com.kotak.unified.db.ApplicationDataFilter.PAN;
import static com.kotak.unified.db.ApplicationDataFilter.PHONE_NUMBER;
import static com.kotak.unified.db.ApplicationDataFilter.PRODUCT;
import static com.kotak.unified.db.ApplicationDataFilter.RM_CODE;
import static com.kotak.unified.db.ApplicationDataFilter.USER_IP;
import static com.kotak.unified.dbservice.utils.Constants.PAYDAY_LOAN_GRAPH_NAME;
import static org.junit.Assert.assertThrows;
import static org.mockito.Mockito.when;

class PDLApplicationDataTransformerTest {
    private final ModelMapper modelMapper;
    private final PDLApplicationDataTransformer transformer;
    private final UserStatus userStatus;
    private final PaydayLoanJourneyMetadata journeyMetadata;
    private final GetApplicationDataRequest request;
    private final ApplicantDetails applicantDetails;
    private final Instant lastModifiedTime;
    private static final String PAYDAY_LOAN = "PDL";
    private static final String LEAD_TRACKING_NUMBER = "l1";

    public PDLApplicationDataTransformerTest() {
        this.modelMapper = Mockito.mock(ModelMapper.class);
        this.transformer = new PDLApplicationDataTransformer(modelMapper);
        this.request = Mockito.mock(GetApplicationDataRequest.class);
        this.userStatus = Mockito.mock(UserStatus.class);
        this.journeyMetadata = Mockito.mock(PaydayLoanJourneyMetadata.class);
        this.applicantDetails = Mockito.mock(ApplicantDetails.class);
        this.lastModifiedTime = Mockito.mock(Instant.class);
    }

    @Test
    void testGetProduct() {
        Assertions.assertEquals(PAYDAY_LOAN, transformer.getProduct());
    }

    @Test
    void testUpdateApplicationDataWithBlockedReasons() {
        final List<String> blockedReasons = List.of("2001");
        final List<String> blockedReasonsResponse = List.of("2001");
        setUserStatusExpectation();
        when(request.getDataFilters()).thenReturn(List.of(BLOCKED_REASONS));
        when(journeyMetadata.getBlockedReasons()).thenReturn(blockedReasons);
        when(modelMapper.map(blockedReasons, new TypeToken<List<String>>() {
        }.getType())).thenReturn(blockedReasonsResponse);
        final PDLApplicationData response = transformer();
        Assertions.assertEquals(blockedReasonsResponse, response.getBlockedReasons());
        Assertions.assertEquals(lastModifiedTime, response.getLastModifiedTime());
    }

    @Test
    void testPopulateApplicationDataWithAadhaarDetails() {
        final AadhaarDetails aadhaarDetails = Mockito.mock(AadhaarDetails.class);
        final AadhaarDetailsResponse aadhaarDetailsResponse = Mockito.mock(AadhaarDetailsResponse.class);
        setUserStatusExpectation();
        when(request.getDataFilters()).thenReturn(List.of(AADHAAR));
        when(applicantDetails.getAadhaarDetails()).thenReturn(aadhaarDetails);
        when(modelMapper.map(aadhaarDetails, AadhaarDetailsResponse.class)).thenReturn(aadhaarDetailsResponse);
        final PDLApplicationData response = transformer();
        Assertions.assertEquals(aadhaarDetailsResponse, response.getAadhaarDetails());
        Assertions.assertEquals(lastModifiedTime, response.getLastModifiedTime());
    }

    @Test
    void testPopulateApplicationDataWithPanDetails() {
        final PanDetails details = Mockito.mock(PanDetails.class);
        final PanDetailsResponse detailsResponse = Mockito.mock(PanDetailsResponse.class);
        setUserStatusExpectation();
        when(request.getDataFilters()).thenReturn(List.of(PAN));
        when(applicantDetails.getPanDetails()).thenReturn(details);
        when(modelMapper.map(details, PanDetailsResponse.class)).thenReturn(detailsResponse);
        final PDLApplicationData response = transformer();
        Assertions.assertEquals(detailsResponse, response.getPanDetails());
        Assertions.assertEquals(lastModifiedTime, response.getLastModifiedTime());
    }

    @Test
    void testPopulateApplicationDataWithDisbursementDetails() {
        final com.kotak.unified.orchestrator.common.dbmodels.DisbursementDetails details = Mockito.mock(com.kotak.unified.orchestrator.common.dbmodels.DisbursementDetails.class);
        final DisbursementDetails detailsResponse = Mockito.mock(DisbursementDetails.class);
        setUserStatusExpectation();
        when(request.getDataFilters()).thenReturn(List.of(DISBURSEMENT_DETAILS));
        when(journeyMetadata.getDisbursementDetails()).thenReturn(details);
        when(modelMapper.map(details, DisbursementDetails.class)).thenReturn(detailsResponse);
        final PDLApplicationData response = transformer();
        Assertions.assertEquals(detailsResponse, response.getDisbursementDetails());
        Assertions.assertEquals(lastModifiedTime, response.getLastModifiedTime());
    }

    @Test
    void testPopulateApplicationDataWithAadhaarAndPanDetails() {
        final AadhaarDetails aadhaarDetails = Mockito.mock(AadhaarDetails.class);
        final AadhaarDetailsResponse aadhaarDetailsResponse = Mockito.mock(AadhaarDetailsResponse.class);
        final PanDetails panDetails = Mockito.mock(PanDetails.class);
        final PanDetailsResponse panDetailsResponse = Mockito.mock(PanDetailsResponse.class);
        setUserStatusExpectation();
        when(request.getDataFilters()).thenReturn(List.of(AADHAAR, PAN));
        when(applicantDetails.getAadhaarDetails()).thenReturn(aadhaarDetails);
        when(applicantDetails.getPanDetails()).thenReturn(panDetails);
        when(modelMapper.map(aadhaarDetails, AadhaarDetailsResponse.class)).thenReturn(aadhaarDetailsResponse);
        when(modelMapper.map(panDetails, PanDetailsResponse.class)).thenReturn(panDetailsResponse);
        final PDLApplicationData response = transformer();
        Assertions.assertEquals(aadhaarDetailsResponse, response.getAadhaarDetails());
        Assertions.assertEquals(panDetailsResponse, response.getPanDetails());
        Assertions.assertEquals(lastModifiedTime, response.getLastModifiedTime());
    }

    @Test
    void testPopulateApplicationDataWithPhoneNumber() {
        final String phone = "phone";
        setUserStatusExpectation();
        when(request.getDataFilters()).thenReturn(List.of(PHONE_NUMBER));
        when(userStatus.getPhoneNumber()).thenReturn(phone);
        final PDLApplicationData response = transformer();
        Assertions.assertEquals(phone, response.getPhoneNumber());
        Assertions.assertEquals(lastModifiedTime, response.getLastModifiedTime());
    }

    @Test
    void testPopulateApplicationDataWithEmail() {
        final String email = "email";
        setUserStatusExpectation();
        when(request.getDataFilters()).thenReturn(List.of(EMAIL_ID));
        when(applicantDetails.getEmailId()).thenReturn(email);
        final PDLApplicationData response = transformer();
        Assertions.assertEquals(email, response.getEmailId());
        Assertions.assertFalse(response.isEmailVerified());
        Assertions.assertEquals(lastModifiedTime, response.getLastModifiedTime());
    }

    @Test
    void testPopulateApplicationDataWithEmailVerified() {
        final String email = "email";
        setUserStatusExpectation();
        when(request.getDataFilters()).thenReturn(List.of(EMAIL_ID));
        when(applicantDetails.getEmailId()).thenReturn(email);
        when(userStatus.getExecutionData()).thenReturn(ExecutionData.builder().isEmailAddressVerified(true).build());
        final PDLApplicationData response = transformerWithEmailVerifiedTrue();
        Assertions.assertEquals(email, response.getEmailId());
        Assertions.assertTrue(response.isEmailVerified());
        Assertions.assertEquals(lastModifiedTime, response.getLastModifiedTime());
    }

    @Test
    void testPopulateApplicationDataWithNullEmailVerified() {
        final String email = "email";
        setUserStatusExpectation();
        when(request.getDataFilters()).thenReturn(List.of(EMAIL_ID));
        when(applicantDetails.getEmailId()).thenReturn(email);
        when(userStatus.getExecutionData()).thenReturn(ExecutionData.builder().isEmailAddressVerified(null).build());
        final PDLApplicationData response = transformerWithEmailVerifiedTrue();
        Assertions.assertEquals(email, response.getEmailId());
        Assertions.assertFalse(response.isEmailVerified());
        Assertions.assertEquals(lastModifiedTime, response.getLastModifiedTime());
    }

    @Test
    void testPopulateApplicationDataWithAddress() {
        setUserStatusExpectation();
        when(request.getDataFilters()).thenReturn(List.of(ADDRESS));
        when(applicantDetails.getAddress()).thenReturn(Address.builder().build());
        final PDLApplicationData response = transformer();
        Assertions.assertEquals(lastModifiedTime, response.getLastModifiedTime());
    }

    @Test
    void testPopulateApplicationDataWithMilestoneHistory() {
        setUserStatusExpectation();
        when(request.getDataFilters()).thenReturn(List.of(MILESTONE_HISTORY_LIST));
        final PDLApplicationData response = transformer();
        Assertions.assertEquals(lastModifiedTime, response.getLastModifiedTime());
    }

    @Test
    void testPopulateApplicationDataWithNoFilter() {
        setUserStatusExpectation();
        when(request.getDataFilters()).thenReturn(List.of());
        final PDLApplicationData response = transformer();
        Assertions.assertEquals(lastModifiedTime, response.getLastModifiedTime());
    }

    @Test
    void testPopulateApplicationDataWithProductDetails() {
        final PDLProductDetails details = Mockito.mock(PDLProductDetails.class);
        final PDLProductDetailsResponse detailsResponse = Mockito.mock(PDLProductDetailsResponse.class);
        setUserStatusExpectation();
        when(request.getDataFilters()).thenReturn(List.of(PRODUCT));
        when(journeyMetadata.getProductDetails()).thenReturn(details);
        when(modelMapper.map(details, PDLProductDetailsResponse.class)).thenReturn(detailsResponse);
        final PDLApplicationData response = transformer();
        Assertions.assertEquals(detailsResponse, response.getProductDetails());
        Assertions.assertEquals(lastModifiedTime, response.getLastModifiedTime());
    }

    @Test
    void testPopulateApplicationDataWithSelectedCrn() {
        final CrnDetails crnDetails = Mockito.mock(CrnDetails.class);
        final CrnNameMap crnNameMap = Mockito.mock(CrnNameMap.class);
        final String crn = "crn";
        setUserStatusExpectation();
        when(request.getDataFilters()).thenReturn(List.of(CRN));
        when(applicantDetails.getCrnDetails()).thenReturn(crnDetails);
        when(crnDetails.getSelectedCrn()).thenReturn(crnNameMap);
        when(crnNameMap.getCrn()).thenReturn(crn);
        final PDLApplicationData response = transformer();
        Assertions.assertEquals(crn, response.getCrn());
        Assertions.assertEquals(lastModifiedTime, response.getLastModifiedTime());
    }

    @Test
    void testPopulateApplicationDataWithBasicDetailFromCrn() {
        final CrnDetails crnDetails = Mockito.mock(CrnDetails.class);
        final String crnName = "crnName";
        final String dob = "21-02-2000";
        setUserStatusExpectation();
        when(request.getDataFilters()).thenReturn(List.of(BASIC_DETAIL));
        CustomerName customerName = new CustomerName();
        customerName.setFullName("Kotak Mahindra");
        when(applicantDetails.getCustomerName()).thenReturn(customerName);
        when(applicantDetails.getCrnDetails()).thenReturn(crnDetails);
        when(applicantDetails.getDob()).thenReturn(dob);
        when(applicantDetails.getAdditionalDetails()).thenReturn(AdditionalDetails.builder().gender("M").build());

        final PDLApplicationData response = transformer();
        Assertions.assertEquals("Kotak Mahindra", response.getBasicDetails().getCustomerName());
        Assertions.assertEquals(lastModifiedTime, response.getLastModifiedTime());
    }

    @Test
    void testPopulateApplicationDataWithBasicDetailFromAadhaar() {
        final CrnDetails crnDetails = Mockito.mock(CrnDetails.class);
        final AadhaarDetails aadhaarDetails = Mockito.mock(AadhaarDetails.class);
        CustomerName customerName = new CustomerName();
        customerName.setFullName("Kotak Mahindra");
        final String aadhaarName = "aadhaarName";
        setUserStatusExpectation();
        when(request.getDataFilters()).thenReturn(List.of(BASIC_DETAIL));
        when(applicantDetails.getCustomerName()).thenReturn(customerName);
        when(applicantDetails.getCrnDetails()).thenReturn(crnDetails);
        when(aadhaarDetails.getName()).thenReturn(aadhaarName);
        when(aadhaarDetails.getGender()).thenReturn("M");
        final PDLApplicationData response = transformer();
        Assertions.assertEquals("Kotak Mahindra", response.getBasicDetails().getCustomerName());
        Assertions.assertEquals(lastModifiedTime, response.getLastModifiedTime());
    }

    @Test
    void testPopulateApplicationDataUserStatusWithoutJourneyMetadata() {
        when(userStatus.getJourneyMetadata()).thenReturn(null);
        when(userStatus.getLeadTrackingNumber()).thenReturn(LEAD_TRACKING_NUMBER);
        Exception exception = assertThrows(InvalidRequestException.class, this::transformer);
        Assertions.assertEquals("Application Id does not belong to PDL Journey l1", exception.getMessage());
    }

    @Test
    void testupdateApplicationDataWithLoanType() {
        final String loanType = "PL_PA";
        final ExecutionData executionData = new ExecutionData();
        setUserStatusExpectation();
        executionData.setLatestCreditOffer(loanType);
        when(request.getDataFilters()).thenReturn(List.of(LOAN_TYPE));
        when(userStatus.getExecutionData()).thenReturn(executionData);
        final PDLApplicationData response = transformer();
        Assertions.assertEquals(loanType, response.getLoanType());
        Assertions.assertEquals(lastModifiedTime, response.getLastModifiedTime());
    }

    private PDLApplicationData transformerWithEmailVerifiedTrue() {
        PDLApplicationData pdlApplicationData =  (PDLApplicationData) transformer.populateApplicationData(request, userStatus);
        return pdlApplicationData;
    }
    private PDLApplicationData transformer() {
        return (PDLApplicationData) transformer.populateApplicationData(request, userStatus);
    }

    private void setUserStatusExpectation() {
        when(userStatus.getJourneyType()).thenReturn(PAYDAY_LOAN_GRAPH_NAME);
        when(userStatus.getJourneyMetadata()).thenReturn(journeyMetadata);
        when(userStatus.getLastModifiedTime()).thenReturn(lastModifiedTime);
        when(applicantDetails.getNomineeDetails()).thenReturn(NomineeDetails.builder().DOB("01/01/1990").name("test").relationshipType("Mother").gender("Female").build());
        when(journeyMetadata.getApplicantDetails()).thenReturn(applicantDetails);
        when(journeyMetadata.getKli()).thenReturn(com.kotak.unified.orchestrator.common.dbmodels.KotakLifeInsuranceDetails.builder().isEnabled(true).insuranceAmount(1234.0).build());
        when(journeyMetadata.getRmCode()).thenReturn("KMBL");
    }

    @Test
    void testPopulateApplicationDataWithBasicDetailCustomerNameNull() {
        setUserStatusExpectation();
        when(request.getDataFilters()).thenReturn(List.of(BASIC_DETAIL));
        when(applicantDetails.getCustomerName()).thenReturn(null);
        when(applicantDetails.getCrnDetails()).thenReturn(null);
        final PDLApplicationData response = transformer();
        Assertions.assertNull(response.getBasicDetails().getCustomerName());
        Assertions.assertEquals(lastModifiedTime, response.getLastModifiedTime());
    }

    @Test
    void testPopulateApplicationDataWithFilterUserIP() {
        setUserStatusExpectation();
        when(request.getDataFilters()).thenReturn(List.of(USER_IP));
        when(applicantDetails.getCustomerName()).thenReturn(null);
        when(applicantDetails.getCrnDetails()).thenReturn(null);
        Assertions.assertThrows(InvalidRequestException.class, ()-> transformer());
    }
    @Test
    void testPopulateApplicationDataWithFilterRmCode() {
        String rmCode = "KMBL";
        setUserStatusExpectation();
        when(request.getDataFilters()).thenReturn(List.of(RM_CODE));
        final PDLApplicationData response = transformer();
        Assertions.assertEquals(rmCode, response.getRmCode());
        Assertions.assertEquals(lastModifiedTime, response.getLastModifiedTime());
    }
}
