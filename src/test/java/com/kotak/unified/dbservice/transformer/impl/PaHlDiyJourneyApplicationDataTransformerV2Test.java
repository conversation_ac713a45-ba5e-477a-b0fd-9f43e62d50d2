package com.kotak.unified.dbservice.transformer.impl;

import com.kotak.unified.common.exception.InvalidRequestException;
import com.kotak.unified.db.ApplicationData;
import com.kotak.unified.db.ApplicationDataFilter;
import com.kotak.unified.db.GetApplicationDataRequest;
import com.kotak.unified.db.PaHlDiyJourneyApplicationData;
import com.kotak.unified.dbservice.exceptions.EntityNotFoundException;
import com.kotak.unified.dbservice.utils.Constants;
import com.kotak.unified.orchestrator.common.dbmodels.CreditCardJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.Declaration;
import com.kotak.unified.orchestrator.common.dbmodels.JourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.PaHlDiyJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.hl.PaDiyJourneyType;
import com.kotak.unified.orchestrator.common.dbmodels.hl.SanctionLetterDetails;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.modelmapper.ModelMapper;

import java.util.List;

@ExtendWith(MockitoExtension.class)
class PaHlDiyJourneyApplicationDataTransformerV2Test {

    private final ModelMapper modelMapper = new ModelMapper();
    private final PaHlDiyJourneyApplicationDataTransformerV2 transformer =
            new PaHlDiyJourneyApplicationDataTransformerV2(modelMapper);

    @Test
    void populateApplicationData_ValidRequest_PaHlDiy_ReturnsPaHlDiyJourneyApplicationData()
            throws EntityNotFoundException, InvalidRequestException {
        // Arrange
        GetApplicationDataRequest getApplicationDataRequest = GetApplicationDataRequest.builder()
                .dataFilters(List.of(ApplicationDataFilter.ALL))
                .build();
        com.kotak.unified.orchestrator.common.dbmodels.ApplicationData applicationDataDDBModel =
                getApplicationDataDDBModel(
                        Constants.HomeloanConstants.PA_HL_DIY_GRAPH_NAME, getPaHlDiyJourneyMetadata(PaDiyJourneyType.PA_HL_DIY));

        // Act
        ApplicationData applicationData = transformer.populateApplicationData(getApplicationDataRequest,
                applicationDataDDBModel);

        // Assert
        Assertions.assertNotNull(applicationData);
        Assertions.assertInstanceOf(PaHlDiyJourneyApplicationData.class, applicationData);
        PaHlDiyJourneyApplicationData data = (PaHlDiyJourneyApplicationData) applicationData;
        Assertions.assertEquals(((PaHlDiyJourneyApplicationData) applicationData).getPaHlDiyJourneyMilestone(),
                data.getPaHlDiyJourneyMilestone());
        Assertions.assertEquals(((PaHlDiyJourneyApplicationData) applicationData).getUserDetails(),
                data.getUserDetails());
        Assertions.assertEquals(((PaHlDiyJourneyApplicationData) applicationData).getHomeLoanOfferDetails(),
                data.getHomeLoanOfferDetails());
        Assertions.assertEquals(((PaHlDiyJourneyApplicationData) applicationData).getSanctionLetterDetails(),
                data.getSanctionLetterDetails());
        Assertions.assertEquals(((PaHlDiyJourneyApplicationData) applicationData).getTermsAndConditionsDeclaration(),
                data.getTermsAndConditionsDeclaration());
    }

    @Test
    void populateApplicationData_ValidRequest_PaBtDiy_ReturnsPaHlDiyJourneyApplicationData()
            throws EntityNotFoundException, InvalidRequestException {
        // Arrange
        GetApplicationDataRequest getApplicationDataRequest = GetApplicationDataRequest.builder()
                .dataFilters(List.of(ApplicationDataFilter.ALL))
                .build();
        com.kotak.unified.orchestrator.common.dbmodels.ApplicationData applicationDataDDBModel =
                getApplicationDataDDBModel(
                        Constants.HomeloanConstants.PA_BT_DIY_GRAPH_NAME, getPaHlDiyJourneyMetadata(PaDiyJourneyType.PA_BT_DIY));

        // Act
        ApplicationData applicationData = transformer.populateApplicationData(getApplicationDataRequest,
                applicationDataDDBModel);

        // Assert
        Assertions.assertNotNull(applicationData);
        Assertions.assertInstanceOf(PaHlDiyJourneyApplicationData.class, applicationData);
        PaHlDiyJourneyApplicationData data = (PaHlDiyJourneyApplicationData) applicationData;
        Assertions.assertEquals(((PaHlDiyJourneyApplicationData) applicationData).getPaHlDiyJourneyMilestone(),
                data.getPaHlDiyJourneyMilestone());
        Assertions.assertEquals(((PaHlDiyJourneyApplicationData) applicationData).getUserDetails(),
                data.getUserDetails());
        Assertions.assertEquals(((PaHlDiyJourneyApplicationData) applicationData).getHomeLoanOfferDetails(),
                data.getHomeLoanOfferDetails());
        Assertions.assertEquals(((PaHlDiyJourneyApplicationData) applicationData).getSanctionLetterDetails(),
                data.getSanctionLetterDetails());
        Assertions.assertEquals(((PaHlDiyJourneyApplicationData) applicationData).getTermsAndConditionsDeclaration(),
                data.getTermsAndConditionsDeclaration());
    }

    @Test
    void populateApplicationData_NullJourneyMetadata_ThrowsEntityNotFoundException() {
        // Arrange
        GetApplicationDataRequest getApplicationDataRequest = new GetApplicationDataRequest();
        com.kotak.unified.orchestrator.common.dbmodels.ApplicationData applicationDataDDBModel =
                getApplicationDataDDBModel(Constants.HomeloanConstants.PA_HL_DIY_GRAPH_NAME, null);

        // Act & Assert
        Assertions.assertThrows(InvalidRequestException.class,
                () -> transformer.populateApplicationData(getApplicationDataRequest, applicationDataDDBModel));
    }

    @Test
    void populateApplicationData_InvalidJourneyMetadata_ThrowsInvalidRequestException() {
        // Arrange
        GetApplicationDataRequest getApplicationDataRequest = GetApplicationDataRequest.builder()
                .dataFilters(List.of())
                .build();
        com.kotak.unified.orchestrator.common.dbmodels.ApplicationData applicationDataDDBModel =
                getApplicationDataDDBModel(Constants.CC, new CreditCardJourneyMetadata());

        // Act & Assert
        Assertions.assertThrows(InvalidRequestException.class,
                () -> transformer.populateApplicationData(getApplicationDataRequest, applicationDataDDBModel));
    }

    @Test
    void populateApplicationData_InvalidDataFilter_ThrowsInvalidRequestException() {
        // Arrange
        GetApplicationDataRequest request = GetApplicationDataRequest.builder()
                .dataFilters(List.of(ApplicationDataFilter.ADDRESS))
                .build();
        com.kotak.unified.orchestrator.common.dbmodels.ApplicationData applicationDataDDBModel =
                getApplicationDataDDBModel(Constants.HomeloanConstants.PA_HL_DIY_GRAPH_NAME,
                        getPaHlDiyJourneyMetadata(PaDiyJourneyType.PA_HL_DIY));

        // Act & Assert
        Assertions.assertThrows(InvalidRequestException.class,
                () -> transformer.populateApplicationData(request, applicationDataDDBModel));
    }

    @Test
    void getProduct_ReturnsCorrectProduct() {
        // Act
        String product = transformer.getProduct();

        // Assert
        Assertions.assertEquals("PA_DIY", product);
    }

    private com.kotak.unified.orchestrator.common.dbmodels.ApplicationData getApplicationDataDDBModel(
            String applicationType, JourneyMetadata journeyMetadata) {
        com.kotak.unified.orchestrator.common.dbmodels.ApplicationData applicationDataDDBModel =
                new com.kotak.unified.orchestrator.common.dbmodels.ApplicationData();
        applicationDataDDBModel.setCrn("CRN123456");
        applicationDataDDBModel.setApplicationType(applicationType);
        applicationDataDDBModel.setJourneyMetadata(journeyMetadata);
        return applicationDataDDBModel;
    }

    private PaHlDiyJourneyMetadata getPaHlDiyJourneyMetadata(PaDiyJourneyType paDiyJourneyType) {
        PaHlDiyJourneyMetadata paDiyJourneyMetadata = new PaHlDiyJourneyMetadata();
        paDiyJourneyMetadata.setPaHlDiyJourneyMilestone(
                com.kotak.unified.orchestrator.common.dbmodels.hl.PaHlDiyJourneyMilestone.SANCTION_LETTER_GENERATED);
        paDiyJourneyMetadata.setPaDiyJourneyType(paDiyJourneyType);
        paDiyJourneyMetadata.setUserDetails(com.kotak.unified.orchestrator.common.dbmodels.hl.UserDetails.builder().build());
        paDiyJourneyMetadata.setHomeLoanOfferDetails(
                com.kotak.unified.orchestrator.common.dbmodels.hl.HomeLoanOfferDetails.builder().build());
        paDiyJourneyMetadata.setSanctionLetterDetails(SanctionLetterDetails.builder().build());
        paDiyJourneyMetadata.setTermsAndConditionsDeclaration(Declaration.builder().build());

        return paDiyJourneyMetadata;
    }
}