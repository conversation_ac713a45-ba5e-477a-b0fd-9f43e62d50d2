package com.kotak.unified.dbservice.transformer.impl;

import com.kotak.unified.common.exception.InvalidRequestException;
import com.kotak.unified.db.ApplicationData;
import com.kotak.unified.db.ApplicationDataFilter;
import com.kotak.unified.db.GetApplicationDataRequest;
import com.kotak.unified.db.PersonalDetailsResponse;
import com.kotak.unified.db.TagResponse;
import com.kotak.unified.db.nr.NRAccountTypeResponse;
import com.kotak.unified.db.nr.NRSavingsAccountApplicationData;
import com.kotak.unified.db.sa.SavingsAccountApplicationData;
import com.kotak.unified.dbservice.TestUtils;
import com.kotak.unified.dbservice.exceptions.EntityNotFoundException;
import com.kotak.unified.orchestrator.common.dbmodels.AadhaarDetails;
import com.kotak.unified.orchestrator.common.dbmodels.Address;
import com.kotak.unified.orchestrator.common.dbmodels.AddressProof;
import com.kotak.unified.orchestrator.common.dbmodels.BankDetails;
import com.kotak.unified.orchestrator.common.dbmodels.CPVAttemptDetails;
import com.kotak.unified.orchestrator.common.dbmodels.Declaration;
import com.kotak.unified.orchestrator.common.dbmodels.EtbDetails;
import com.kotak.unified.orchestrator.common.dbmodels.ExecutionData;
import com.kotak.unified.orchestrator.common.dbmodels.NRSavingsAccountJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.NomineeDetails;
import com.kotak.unified.orchestrator.common.dbmodels.PanDetails;
import com.kotak.unified.orchestrator.common.dbmodels.PersonalDetails;
import com.kotak.unified.orchestrator.common.dbmodels.SavingsAccountJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.Tag;
import com.kotak.unified.orchestrator.common.dbmodels.UserStatus;
import com.kotak.unified.orchestrator.common.dbmodels.VKYCDetails;
import com.kotak.unified.orchestrator.common.dbmodels.nr.DocumentAvailableType;
import com.kotak.unified.orchestrator.common.dbmodels.nr.NRAccountType;
import com.kotak.unified.orchestrator.common.dbmodels.nr.NRProductSpecifications;
import com.kotak.unified.orchestrator.common.dbmodels.nr.NRTaxDetails;
import com.kotak.unified.orchestrator.common.dbmodels.nr.NRUploadedDocumentLinks;
import com.kotak.unified.orchestrator.common.dbmodels.nr.OCIDetails;
import com.kotak.unified.orchestrator.common.dbmodels.nr.PassportDetails;
import com.kotak.unified.orchestrator.common.dbmodels.nr.VisaDetails;
import com.kotak.unified.orchestrator.common.dbmodels.vkycStatusModel.VideoKycDetails;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.modelmapper.ModelMapper;
import org.modelmapper.convention.MatchingStrategies;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

public class SavingsAccountApplicationDataTransformerTest {
    private final SavingsAccountApplicationDataTransformer savingsAccountApplicationDataTransformer;

    SavingsAccountApplicationDataTransformerTest() {
        ModelMapper modelMapper = new ModelMapper();
        savingsAccountApplicationDataTransformer = new SavingsAccountApplicationDataTransformer(modelMapper);
    }

    @Test
    public void test_transformation_valid_customer_SavingsAccount() throws EntityNotFoundException, InvalidRequestException {
        GetApplicationDataRequest request = GetApplicationDataRequest.builder()
                .dataFilters(Arrays.asList(ApplicationDataFilter.PAN,
                        ApplicationDataFilter.PHONE_NUMBER))
                .build();

        SavingsAccountJourneyMetadata journeyMetadata = SavingsAccountJourneyMetadata.builder()
                .panDetails(PanDetails.builder().number("panNumber").build())
                .build();

        UserStatus userStatus = UserStatus.builder()
                .journeyType("SavingsAccount")
                .leadTrackingNumber("leadId")
                .phoneNumber("phoneNumber")
                .panNumber("panNumber")
                .executionData(ExecutionData.builder().isEmailAddressVerified(true).build())
                .journeyMetadata(journeyMetadata)
                .build();

        ApplicationData applicationData = this.savingsAccountApplicationDataTransformer.populateApplicationData(request, userStatus);
        SavingsAccountApplicationData savingsAccountApplicationData = (SavingsAccountApplicationData) applicationData;
        Assertions.assertNotNull(applicationData);
        Assertions.assertInstanceOf(SavingsAccountApplicationData.class, applicationData);
        Assertions.assertEquals(savingsAccountApplicationData.getPhoneNumber(), "phoneNumber");
        Assertions.assertEquals(savingsAccountApplicationData.getPan(), "panNumber");
    }

    @Test
    public void test_transformation_valid_customer_SavingsAccount_ApplicationDataFilter_ALL() throws EntityNotFoundException, InvalidRequestException {
        GetApplicationDataRequest request = GetApplicationDataRequest.builder()
                .dataFilters(Arrays.asList(ApplicationDataFilter.ALL))
                .build();

        SavingsAccountJourneyMetadata journeyMetadata = SavingsAccountJourneyMetadata.builder()
                .panDetails(PanDetails.builder().number("panNumber").build())
                .isCommunicationAddressSameAsAadhaarAddress(true)
                .activMoneyOptIn(true)
                .etbDetails(EtbDetails.builder()
                        .build())
                .etbDob(TestUtils.TEST_AADHAAR_DOB)
                .crn(TestUtils.TEST_CRN)
                .etbDetails(EtbDetails.builder().build())
                .isAddressPincodeServiceable(true)
                .nomineeDetails(NomineeDetails.builder().build())
                .communicationAddress(Address.builder().build())
                .vkycDetails(VKYCDetails.builder().build())
                .personalDetails(PersonalDetails.builder().build())
                .declarations(Map.of("declarations", Declaration.builder().build()))
                .aadhaarDetails(AadhaarDetails.builder().build())
                .bankDetails(BankDetails.builder().build())
                .build();

        UserStatus userStatus = UserStatus.builder()
                .journeyType("SavingsAccount")
                .leadTrackingNumber("leadId")
                .phoneNumber("phoneNumber")
                .panNumber("panNumber")
                .executionData(ExecutionData.builder().isEmailAddressVerified(true).build())
                .journeyMetadata(journeyMetadata)
                .build();

        ApplicationData applicationData = this.savingsAccountApplicationDataTransformer.populateApplicationData(request, userStatus);
        SavingsAccountApplicationData savingsAccountApplicationData = (SavingsAccountApplicationData) applicationData;
        Assertions.assertNotNull(applicationData);
        Assertions.assertInstanceOf(SavingsAccountApplicationData.class, applicationData);
        Assertions.assertEquals(savingsAccountApplicationData.getPhoneNumber(), "phoneNumber");
        Assertions.assertEquals(savingsAccountApplicationData.getPan(), "panNumber");
        Assertions.assertNotNull(savingsAccountApplicationData.getAadhaarDetails());
        Assertions.assertNotNull(savingsAccountApplicationData.getBankDetails());
        Assertions.assertNotNull(savingsAccountApplicationData.getNomineeDetails());
        Assertions.assertNotNull(savingsAccountApplicationData.getPersonalDetails());
        Assertions.assertNotNull(savingsAccountApplicationData.getCommunicationAddress());
        Assertions.assertNotNull(savingsAccountApplicationData.getVkycDetails());
        Assertions.assertNotNull(savingsAccountApplicationData.getDeclarations());
    }

    @Test
    public void test_transformation_valid_customer_SavingsAccount_ApplicationDataFilter_ALL_with_null_fields() throws EntityNotFoundException, InvalidRequestException {
        GetApplicationDataRequest request = GetApplicationDataRequest.builder()
                .dataFilters(Arrays.asList(ApplicationDataFilter.ALL))
                .build();

        SavingsAccountJourneyMetadata journeyMetadata = SavingsAccountJourneyMetadata.builder()
                .build();

        UserStatus userStatus = UserStatus.builder()
                .journeyType("SavingsAccount")
                .leadTrackingNumber("leadId")
                .phoneNumber("phoneNumber")
                .panNumber("panNumber")
                .executionData(ExecutionData.builder().isEmailAddressVerified(true).build())
                .journeyMetadata(journeyMetadata)
                .build();

        ApplicationData applicationData = this.savingsAccountApplicationDataTransformer.populateApplicationData(request, userStatus);
        SavingsAccountApplicationData savingsAccountApplicationData = (SavingsAccountApplicationData) applicationData;
        Assertions.assertNotNull(applicationData);
        Assertions.assertInstanceOf(SavingsAccountApplicationData.class, applicationData);
        Assertions.assertEquals(savingsAccountApplicationData.getPhoneNumber(), "phoneNumber");
    }

    @Test
    public void test_transformation_valid_customer_EtbSavingsAccount() throws EntityNotFoundException, InvalidRequestException {
        GetApplicationDataRequest request = GetApplicationDataRequest.builder()
                .dataFilters(Arrays.asList(ApplicationDataFilter.PAN,
                        ApplicationDataFilter.PHONE_NUMBER))
                .build();

        SavingsAccountJourneyMetadata journeyMetadata = SavingsAccountJourneyMetadata.builder()
                .panDetails(PanDetails.builder().number("panNumber").build())
                .build();

        UserStatus userStatus = UserStatus.builder()
                .journeyType("EtbSavingsAccount")
                .leadTrackingNumber("leadId")
                .phoneNumber("phoneNumber")
                .panNumber("panNumber")
                .executionData(ExecutionData.builder().isEmailAddressVerified(true).build())
                .journeyMetadata(journeyMetadata)
                .build();

        ApplicationData applicationData = this.savingsAccountApplicationDataTransformer.populateApplicationData(request, userStatus);
        SavingsAccountApplicationData savingsAccountApplicationData = (SavingsAccountApplicationData) applicationData;
        Assertions.assertNotNull(applicationData);
        Assertions.assertInstanceOf(SavingsAccountApplicationData.class, applicationData);
        Assertions.assertEquals(savingsAccountApplicationData.getPhoneNumber(), "phoneNumber");
        Assertions.assertEquals(savingsAccountApplicationData.getPan(), "panNumber");
    }

    @Test
    public void test_transformation_valid_customer_SalaryAccount() throws EntityNotFoundException, InvalidRequestException {
        GetApplicationDataRequest request = GetApplicationDataRequest.builder()
                .dataFilters(Arrays.asList(ApplicationDataFilter.PAN,
                        ApplicationDataFilter.PHONE_NUMBER))
                .build();

        SavingsAccountJourneyMetadata journeyMetadata = SavingsAccountJourneyMetadata.builder()
                .panDetails(PanDetails.builder().number("panNumber").build())
                .build();

        UserStatus userStatus = UserStatus.builder()
                .journeyType("SalaryAccount")
                .leadTrackingNumber("leadId")
                .phoneNumber("phoneNumber")
                .panNumber("panNumber")
                .executionData(ExecutionData.builder().isEmailAddressVerified(true).build())
                .journeyMetadata(journeyMetadata)
                .build();

        ApplicationData applicationData = this.savingsAccountApplicationDataTransformer.populateApplicationData(request, userStatus);
        SavingsAccountApplicationData savingsAccountApplicationData = (SavingsAccountApplicationData) applicationData;
        Assertions.assertNotNull(applicationData);
        Assertions.assertInstanceOf(SavingsAccountApplicationData.class, applicationData);
        Assertions.assertEquals(savingsAccountApplicationData.getPhoneNumber(), "phoneNumber");
        Assertions.assertEquals(savingsAccountApplicationData.getPan(), "panNumber");
    }

    @Test
    public void test_transformation_valid_customer_EtbSalaryAccount() throws EntityNotFoundException, InvalidRequestException {
        GetApplicationDataRequest request = GetApplicationDataRequest.builder()
                .dataFilters(Arrays.asList(ApplicationDataFilter.PAN,
                        ApplicationDataFilter.PHONE_NUMBER))
                .build();

        SavingsAccountJourneyMetadata journeyMetadata = SavingsAccountJourneyMetadata.builder()
                .panDetails(PanDetails.builder().number("panNumber").build())
                .build();

        UserStatus userStatus = UserStatus.builder()
                .journeyType("EtbSalaryAccount")
                .leadTrackingNumber("leadId")
                .phoneNumber("phoneNumber")
                .panNumber("panNumber")
                .executionData(ExecutionData.builder().isEmailAddressVerified(true).build())
                .journeyMetadata(journeyMetadata)
                .build();

        ApplicationData applicationData = this.savingsAccountApplicationDataTransformer.populateApplicationData(request, userStatus);
        SavingsAccountApplicationData savingsAccountApplicationData = (SavingsAccountApplicationData) applicationData;
        Assertions.assertNotNull(applicationData);
        Assertions.assertInstanceOf(SavingsAccountApplicationData.class, applicationData);
        Assertions.assertEquals(savingsAccountApplicationData.getPhoneNumber(), "phoneNumber");
        Assertions.assertEquals(savingsAccountApplicationData.getPan(), "panNumber");
    }

    @Test
    public void test_transformation_valid_customer_invalidFilter() throws EntityNotFoundException, InvalidRequestException {
        GetApplicationDataRequest request = GetApplicationDataRequest.builder()
                .dataFilters(Arrays.asList(ApplicationDataFilter.PAN,
                        ApplicationDataFilter.PERSONAL_DETAILS))
                .build();

        SavingsAccountJourneyMetadata journeyMetadata = SavingsAccountJourneyMetadata.builder()
                .panDetails(PanDetails.builder().number("panNumber").build())
                .build();

        UserStatus userStatus = UserStatus.builder()
                .journeyType("SavingsAccount")
                .leadTrackingNumber("leadId")
                .phoneNumber("phoneNumber")
                .panNumber("panNumber")
                .executionData(ExecutionData.builder().isEmailAddressVerified(true).build())
                .journeyMetadata(journeyMetadata)
                .build();

        InvalidRequestException exception = assertThrows(InvalidRequestException.class, ()-> this.savingsAccountApplicationDataTransformer.populateApplicationData(request, userStatus));
        assertEquals("Data filter passed is not applicable : PERSONAL_DETAILS", exception.getMessage());
    }

    @Test
    public void test_transformation_invalid_lead_null_metadata() {
        UserStatus userStatus = UserStatus.builder().leadTrackingNumber("l1").build();

        EntityNotFoundException ir = assertThrows(EntityNotFoundException.class, () -> this.savingsAccountApplicationDataTransformer
                .populateApplicationData(null, userStatus));
        Assertions.assertEquals("No application data found for lead id : l1", ir.getMessage());
    }

    @Test
    public void test_transformation_invalid_lead() {
        UserStatus us = UserStatus.builder()
                .leadTrackingNumber("l1")
                .journeyMetadata(NRSavingsAccountJourneyMetadata.builder().build()).build();

        InvalidRequestException ir = assertThrows(InvalidRequestException.class, () -> this.savingsAccountApplicationDataTransformer
                .populateApplicationData(null, us));
        Assertions.assertEquals("Application Id does not belong to Savings account Journey for lead : l1",ir.getMessage());
    }
}
