package com.kotak.unified.dbservice.transformer;

import com.kotak.unified.dbinterface.models.PreferredAccountNumberDto;
import com.kotak.unified.dbservice.model.PreferredAccountNumberDDBModel;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.Instant;

import static com.kotak.unified.dbservice.utils.Constants.SOFT_BLOCK;
import static org.junit.jupiter.api.Assertions.*;

class PreferredAccountNumberTransformerTest {

    private PreferredAccountNumberTransformer preferredAccountNumberTransformer;

    private PreferredAccountNumberDto preferredAccountNumberDto;
    private PreferredAccountNumberDDBModel preferredAccountNumberDDBModel;

    @BeforeEach
    public void setUp() {
        Instant now = Instant.now();
        preferredAccountNumberDto = PreferredAccountNumberDto.builder()
                .preferredAccountNumber("**********")
                .leadTrackingNumber("lead123")
                .blockStatus(SOFT_BLOCK)
                .version(1)
                .lastModifiedAt(now)
                .createdAt(now)
                .build();

        preferredAccountNumberDDBModel = PreferredAccountNumberDDBModel.builder()
                .preferredAccountNumber("**********")
                .leadTrackingNumber("lead123")
                .blockStatus(SOFT_BLOCK)
                .version(1)
                .lastModifiedAt(now)
                .createdAt(now)
                .build();

        preferredAccountNumberTransformer = new PreferredAccountNumberTransformer();
    }

    @Test
    void testConvertDDBModelToDTO() {
        PreferredAccountNumberDto dto =
                preferredAccountNumberTransformer.convertDDBModelToDTO(preferredAccountNumberDDBModel);

        assertEquals(preferredAccountNumberDto.getPreferredAccountNumber(), dto.getPreferredAccountNumber());
        assertEquals(preferredAccountNumberDto.getLeadTrackingNumber(), dto.getLeadTrackingNumber());
        assertEquals(preferredAccountNumberDto.getBlockStatus(), dto.getBlockStatus());
        assertEquals(preferredAccountNumberDto.getVersion(), dto.getVersion());
        assertEquals(preferredAccountNumberDto.getCreatedAt(), dto.getCreatedAt());
        assertEquals(preferredAccountNumberDto.getLastModifiedAt(), dto.getLastModifiedAt());
    }

    @Test
    void testConvertDtoToDDBModel() {
        PreferredAccountNumberDDBModel ddbModel =
                preferredAccountNumberTransformer.convertDtoToDDBModel(preferredAccountNumberDto);

        assertEquals(preferredAccountNumberDDBModel.getBlockStatus(), ddbModel.getBlockStatus());
        assertEquals(preferredAccountNumberDDBModel.getPreferredAccountNumber(), ddbModel.getPreferredAccountNumber());
        assertEquals(preferredAccountNumberDDBModel.getLeadTrackingNumber(), ddbModel.getLeadTrackingNumber());
        assertEquals(preferredAccountNumberDDBModel.getVersion(), 1);
    }

}