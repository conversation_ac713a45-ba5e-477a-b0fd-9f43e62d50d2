package com.kotak.unified.dbservice.transformer;

import com.kotak.unified.db.pgn.AccountNumSchemeCodePgnMetadata;
import com.kotak.unified.db.response.pgn.AssignPgnResponse;
import com.kotak.unified.dbservice.enums.PgnType;
import com.kotak.unified.dbservice.model.PGNStatus;
import com.kotak.unified.dbservice.model.pgn.PgnDDBModel;
import com.kotak.unified.dbservice.model.pgn.PgnMetadata;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.modelmapper.ModelMapper;

@ExtendWith(MockitoExtension.class)
public class PgnTransformerTests {

    private final PgnTransformer pgnTransformer;

    public PgnTransformerTests() {
        this.pgnTransformer = new PgnTransformer(new ModelMapper());
    }

    @Test
    public void test_convertPgnDdbModelToAssignPgnResponse() {
        AccountNumSchemeCodePgnMetadata accountNumSchemeCodePgnMetadata = AccountNumSchemeCodePgnMetadata.builder()
                .schemeCode("CSSAC").accountNumber("**********").build();
        AssignPgnResponse expectedResponse = AssignPgnResponse.builder()
                .crn("********")
                .pgnAlreadyAssigned(true)
                .pgnType("SAVINGS")
                .leadTrackingNumber("leadId")
                .pgnMetadata(accountNumSchemeCodePgnMetadata)
                .build();

        com.kotak.unified.dbservice.model.pgn.AccountNumSchemeCodePgnMetadata pgnMetadata =
                com.kotak.unified.dbservice.model.pgn.AccountNumSchemeCodePgnMetadata.builder()
                        .accountNumber("**********")
                        .schemeCode("CSSAC")
                        .build();

        PgnDDBModel pgnDDBModel = PgnDDBModel.builder()
                .crn("********")
                .leadTrackingNumber("leadId")
                .pgnType(PgnType.SAVINGS)
                .status(PGNStatus.ASSIGNED)
                .pgnMetadata(pgnMetadata)
                .build();

        AssignPgnResponse actualResponse = pgnTransformer.convertPgnDdbModelToAssignPgnResponse(pgnDDBModel, true);

        Assertions.assertNotNull(actualResponse);
        Assertions.assertEquals(expectedResponse, actualResponse);
    }

    @Test
    public void test_convertRequestPgnMetadataToPgnMetadataModel() {
        com.kotak.unified.db.pgn.PgnMetadata pgnMetadata = AccountNumSchemeCodePgnMetadata.builder()
                .schemeCode("CSSAC")
                .accountNumber("**********")
                .build();

        PgnMetadata expectedResponse = com.kotak.unified.dbservice.model.pgn.AccountNumSchemeCodePgnMetadata.builder()
                .schemeCode("CSSAC")
                .accountNumber("**********")
                .build();
        PgnMetadata actualResponse = pgnTransformer.convertRequestPgnMetadataToPgnMetadataModel("SAVINGS", pgnMetadata);

        Assertions.assertEquals(expectedResponse,actualResponse);
    }
}
