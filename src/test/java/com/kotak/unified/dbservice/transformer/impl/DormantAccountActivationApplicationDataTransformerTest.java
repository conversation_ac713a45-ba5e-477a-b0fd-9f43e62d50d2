package com.kotak.unified.dbservice.transformer.impl;

import com.kotak.unified.common.exception.InvalidRequestException;
import com.kotak.unified.db.ApplicationData;
import com.kotak.unified.db.ApplicationDataFilter;
import com.kotak.unified.db.GetApplicationDataRequest;
import com.kotak.unified.db.response.DormantAccountActivationApplicationData;
import com.kotak.unified.dbservice.exceptions.EntityNotFoundException;
import com.kotak.unified.orchestrator.common.dbmodels.Declaration;
import com.kotak.unified.orchestrator.common.dbmodels.DormantAccountActivationJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.DormantAccountActivationProductSpecifications;
import com.kotak.unified.orchestrator.common.dbmodels.EtbAccountDetail;
import com.kotak.unified.orchestrator.common.dbmodels.EtbDetails;
import com.kotak.unified.orchestrator.common.dbmodels.ExecutionData;
import com.kotak.unified.orchestrator.common.dbmodels.IneligibleDormantAccountDetails;
import com.kotak.unified.orchestrator.common.dbmodels.PersonalDetails;
import com.kotak.unified.orchestrator.common.dbmodels.SavingsAccountJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.Tag;
import com.kotak.unified.orchestrator.common.dbmodels.UserStatus;
import com.kotak.unified.orchestrator.common.enums.ReasonForDormancyIneligibility;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.modelmapper.ModelMapper;
import org.modelmapper.convention.MatchingStrategies;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class DormantAccountActivationApplicationDataTransformerTest {
    private final DormantAccountActivationApplicationDataTransformer dormantAccountActivationApplicationDataTransformer;
    DormantAccountActivationApplicationDataTransformerTest()
    {
        ModelMapper modelMapper = new ModelMapper();
        modelMapper.getConfiguration().setMatchingStrategy(MatchingStrategies.STRICT);
        dormantAccountActivationApplicationDataTransformer = new DormantAccountActivationApplicationDataTransformer(modelMapper);
    }

    @Test
    public void test_transformation_valid_dormancy_lead() throws InvalidRequestException, EntityNotFoundException {
        EtbAccountDetail etbAccountDetail = EtbAccountDetail.builder().accountNumber("acc1")
                .branchCode("br1")
                .schemeCode("sc1")
                .build();
        EtbAccountDetail etbAccountDetail2 = EtbAccountDetail.builder().accountNumber("acc2")
                .branchCode("br2")
                .schemeCode("sc2")
                .build();
        List<EtbAccountDetail> validDormantAccounts = Arrays.asList(etbAccountDetail, etbAccountDetail2);
        EtbDetails etbDetails = EtbDetails.builder().crnList(Arrays.asList("c1","c2")).fullName("irobo").build();
        PersonalDetails personalDetails = PersonalDetails.builder()
                .annualIncome(Tag.builder().code("code1").value("value1").build())
                .annualSalary(1134)
                .build();
        Map<String, Declaration> declarationMap = new HashMap<>();
        declarationMap.put("declaration1",Declaration.builder()
                .name("i'm a human")
                .declarationAccepted(true)
                .dateTime(Instant.now())
                .build());
        declarationMap.put("declaration2",Declaration.builder()
                .name("i'm not robot")
                .declarationAccepted(true)
                .dateTime(Instant.now())
                .build());

        var ineligibleDormantAccountDetailsList = List.of(
                IneligibleDormantAccountDetails.builder()
                        .accountNumber("1234")
                        .reasonForDormancyIneligibility(ReasonForDormancyIneligibility.ACCOUNT_CLOSED)
                        .build(),
                IneligibleDormantAccountDetails.builder()
                        .accountNumber("4321")
                        .reasonForDormancyIneligibility(ReasonForDormancyIneligibility.ACCOUNT_FROZEN)
                        .build()
        );

        DormantAccountActivationJourneyMetadata dm = DormantAccountActivationJourneyMetadata
                .builder()
                .validDormantAccounts(validDormantAccounts)
                .ineligibleDormantAccountDetailsList(ineligibleDormantAccountDetailsList)
                .etbDetails(etbDetails)
                .dob("23/12/1996")
                .dormancyReason("Out of curiosity")
                .isCommunicationAddressSameAsExistingBcifAddress(true)
                .personalDetails(personalDetails)
                .declarations(declarationMap)
                .productSpecifications(DormantAccountActivationProductSpecifications.builder()
                        .rmCode("dummyRmCode")
                        .lcCode("dummyRmCode")
                        .lcName("dummyName")
                        .channel("NA")
                        .source("NA")
                        .campaignName("NA")
                        .build())
                .build();

        UserStatus us = UserStatus.builder().journeyType("DormantAccountActivation").phoneNumber("**********")
                .executionData(ExecutionData.builder().isEmailAddressVerified(true).build())
                .journeyMetadata(dm)
                .build();
        GetApplicationDataRequest getApplicationDataRequest = GetApplicationDataRequest.builder()
                .dataFilters(Arrays.asList(
                        ApplicationDataFilter.PHONE_NUMBER, ApplicationDataFilter.DATE_OF_BIRTH,
                        ApplicationDataFilter.PERSONAL_DETAILS, ApplicationDataFilter.EMAIL_VERIFIED,
                        ApplicationDataFilter.ETB_DETAILS, ApplicationDataFilter.VALID_DORMANT_ACCOUNTS,
                        ApplicationDataFilter.DORMANCY_REMOVAL_REASON, ApplicationDataFilter.COMMUNICATION_ADDRESS_ENTERED_IN_JOURNEY,
                        ApplicationDataFilter.DECLARATIONS,ApplicationDataFilter.PRODUCT_SPECIFICATIONS,
                        ApplicationDataFilter.INELIGIBLE_DORMANT_ACCOUNT_DETAILS_LIST))
                .build();
        ApplicationData da = this.dormantAccountActivationApplicationDataTransformer.populateApplicationData(getApplicationDataRequest, us);
        Assertions.assertNotNull(da);
        Assertions.assertInstanceOf(DormantAccountActivationApplicationData.class, da);
        DormantAccountActivationApplicationData dormantData = (DormantAccountActivationApplicationData)da;
        Assertions.assertEquals("**********",dormantData.getPhoneNumber());
        Assertions.assertEquals("23/12/1996",dormantData.getCustomerDob());
        Assertions.assertEquals("Out of curiosity",dormantData.getDormancyRemovalReason());
        Assertions.assertTrue(dormantData.getIsCommunicationAddressSameAsExistingBcifAddress());
        Assertions.assertEquals("code1",dormantData.getPersonalDetailsResponse().getAnnualIncome().getCode());
        Assertions.assertEquals("value1",dormantData.getPersonalDetailsResponse().getAnnualIncome().getValue());
        Assertions.assertEquals(1134,dormantData.getPersonalDetailsResponse().getAnnualSalary());
        Assertions.assertTrue(dormantData.getIsEmailAddressVerified());
        Assertions.assertEquals(2,dormantData.getEtbDetailsDto().getCrnList().size());
        Assertions.assertEquals("c1",dormantData.getEtbDetailsDto().getCrnList().get(0));
        Assertions.assertEquals("c2",dormantData.getEtbDetailsDto().getCrnList().get(1));
        Assertions.assertEquals("irobo",dormantData.getEtbDetailsDto().getFullName());
        Assertions.assertEquals(2,dormantData.getValidDormantAccounts().size());
        Assertions.assertEquals("br1",dormantData.getValidDormantAccounts().get(0).getBranchCode());
        Assertions.assertEquals("sc1",dormantData.getValidDormantAccounts().get(0).getSchemeCode());
        Assertions.assertEquals("acc1",dormantData.getValidDormantAccounts().get(0).getAccountNumber());
        Assertions.assertEquals("br2",dormantData.getValidDormantAccounts().get(1).getBranchCode());
        Assertions.assertEquals("sc2",dormantData.getValidDormantAccounts().get(1).getSchemeCode());
        Assertions.assertEquals("acc2",dormantData.getValidDormantAccounts().get(1).getAccountNumber());
        Assertions.assertEquals(2,dormantData.getDeclarationResponses().size());
        Assertions.assertEquals("dummyRmCode",dormantData.getDormantAccountActivationProductSpecificationResponse().getRmCode());
        Assertions.assertEquals("dummyRmCode",dormantData.getDormantAccountActivationProductSpecificationResponse().getLcCode());
        Assertions.assertEquals("dummyName",dormantData.getDormantAccountActivationProductSpecificationResponse().getLcName());
        Assertions.assertEquals("NA",dormantData.getDormantAccountActivationProductSpecificationResponse().getChannel());
        Assertions.assertEquals("NA",dormantData.getDormantAccountActivationProductSpecificationResponse().getSource());
        Assertions.assertEquals("NA",dormantData.getDormantAccountActivationProductSpecificationResponse().getCampaignName());
        Assertions.assertEquals(ineligibleDormantAccountDetailsList.get(0).getAccountNumber(),
                dormantData.getIneligibleDormantAccountDetailsDtoList().get(0).getAccountNumber());
        Assertions.assertEquals(ineligibleDormantAccountDetailsList.get(0).getReasonForDormancyIneligibility().name(),
                dormantData.getIneligibleDormantAccountDetailsDtoList().get(0).getReasonForDormancyIneligibility().name());
        Assertions.assertEquals(ineligibleDormantAccountDetailsList.get(1).getAccountNumber(),
                dormantData.getIneligibleDormantAccountDetailsDtoList().get(1).getAccountNumber());
        Assertions.assertEquals(ineligibleDormantAccountDetailsList.get(1).getReasonForDormancyIneligibility().name(),
                dormantData.getIneligibleDormantAccountDetailsDtoList().get(1).getReasonForDormancyIneligibility().name());
    }

    @Test
    public void test_transformation_invalid_lead_notdormancy() {
        UserStatus us = UserStatus.builder()
                .leadTrackingNumber("l1")
                .journeyMetadata(SavingsAccountJourneyMetadata.builder().build()).build();

        InvalidRequestException ir = Assertions.assertThrows(InvalidRequestException.class, () -> this.dormantAccountActivationApplicationDataTransformer
                .populateApplicationData(null, us));
        Assertions.assertEquals("Application Id does not belong to Dormant account activation Journey for lead : l1",ir.getMessage());
    }

    @Test
    public void test_transformation_invalid_lead_null_metadata() {
        UserStatus us = UserStatus.builder()
                .leadTrackingNumber("l1").build();

        EntityNotFoundException ir = Assertions.assertThrows(EntityNotFoundException.class, () -> this.dormantAccountActivationApplicationDataTransformer
                .populateApplicationData(null, us));
        Assertions.assertEquals("No application data found for lead id : l1",ir.getMessage());
    }

    @Test
    public void test_transformation_valid_dormancy_lead_optional_fields_null() throws InvalidRequestException, EntityNotFoundException {

        List<EtbAccountDetail> validDormantAccounts = null;
        EtbDetails etbDetails = null;
        PersonalDetails personalDetails = null;
        Map<String, Declaration> declarationMap = null;

        DormantAccountActivationJourneyMetadata dm = DormantAccountActivationJourneyMetadata
                .builder()
                .validDormantAccounts(validDormantAccounts)
                .ineligibleDormantAccountDetailsList(null)
                .etbDetails(etbDetails)
                .dob("23/12/1996")
                .dormancyReason("Out of curiosity")
                .isCommunicationAddressSameAsExistingBcifAddress(null)
                .personalDetails(personalDetails)
                .declarations(declarationMap)
                .build();

        UserStatus us = UserStatus.builder().journeyType("DormantAccountActivation").phoneNumber("**********")
                .executionData(null)
                .journeyMetadata(dm)
                .build();
        GetApplicationDataRequest getApplicationDataRequest = GetApplicationDataRequest.builder()
                .dataFilters(Arrays.asList(
                        ApplicationDataFilter.PHONE_NUMBER, ApplicationDataFilter.DATE_OF_BIRTH,
                        ApplicationDataFilter.PERSONAL_DETAILS, ApplicationDataFilter.EMAIL_VERIFIED,
                        ApplicationDataFilter.ETB_DETAILS, ApplicationDataFilter.VALID_DORMANT_ACCOUNTS,
                        ApplicationDataFilter.DORMANCY_REMOVAL_REASON, ApplicationDataFilter.COMMUNICATION_ADDRESS_ENTERED_IN_JOURNEY,
                        ApplicationDataFilter.DECLARATIONS, ApplicationDataFilter.INELIGIBLE_DORMANT_ACCOUNT_DETAILS_LIST))
                .build();
        ApplicationData da = this.dormantAccountActivationApplicationDataTransformer.populateApplicationData(getApplicationDataRequest, us);
        Assertions.assertNotNull(da);
        Assertions.assertInstanceOf(DormantAccountActivationApplicationData.class, da);
        DormantAccountActivationApplicationData dormantData = (DormantAccountActivationApplicationData)da;
        Assertions.assertEquals("**********",dormantData.getPhoneNumber());
        Assertions.assertEquals("23/12/1996",dormantData.getCustomerDob());
        Assertions.assertEquals("Out of curiosity",dormantData.getDormancyRemovalReason());
        Assertions.assertNull(dormantData.getIsCommunicationAddressSameAsExistingBcifAddress());
        Assertions.assertNull(dormantData.getIsEmailAddressVerified());
        Assertions.assertNull(dormantData.getEtbDetailsDto());
        Assertions.assertNull(dormantData.getValidDormantAccounts());
        Assertions.assertNull(dormantData.getPersonalDetailsResponse());
        Assertions.assertNull(dormantData.getDeclarationResponses());
        Assertions.assertNull(dormantData.getIneligibleDormantAccountDetailsDtoList());
    }

    @Test
    public void test_transformation_valid_dormancy_lead_with_optional_fields_empty() throws InvalidRequestException, EntityNotFoundException {
        List<EtbAccountDetail> validDormantAccounts = new ArrayList<>();
        EtbDetails etbDetails = EtbDetails.builder().crnList(Arrays.asList("c1","c2")).fullName("irobo").build();
        PersonalDetails personalDetails = PersonalDetails.builder()
                .annualIncome(Tag.builder().code("code1").value("value1").build())
                .annualSalary(1134)
                .build();
        Map<String, Declaration> declarationMap = new HashMap<>();

        DormantAccountActivationJourneyMetadata dm = DormantAccountActivationJourneyMetadata
                .builder()
                .validDormantAccounts(validDormantAccounts)
                .ineligibleDormantAccountDetailsList(Collections.emptyList())
                .etbDetails(etbDetails)
                .dob("23/12/1996")
                .dormancyReason("Out of curiosity")
                .isCommunicationAddressSameAsExistingBcifAddress(false)
                .personalDetails(personalDetails)
                .declarations(declarationMap)
                .build();

        UserStatus us = UserStatus.builder().journeyType("DormantAccountActivation").phoneNumber("**********")
                .executionData(ExecutionData.builder().isEmailAddressVerified(false).build())
                .journeyMetadata(dm)
                .build();
        GetApplicationDataRequest getApplicationDataRequest = GetApplicationDataRequest.builder()
                .dataFilters(Arrays.asList(
                        ApplicationDataFilter.PHONE_NUMBER, ApplicationDataFilter.DATE_OF_BIRTH,
                        ApplicationDataFilter.PERSONAL_DETAILS, ApplicationDataFilter.EMAIL_VERIFIED,
                        ApplicationDataFilter.ETB_DETAILS, ApplicationDataFilter.VALID_DORMANT_ACCOUNTS,
                        ApplicationDataFilter.DORMANCY_REMOVAL_REASON, ApplicationDataFilter.COMMUNICATION_ADDRESS_ENTERED_IN_JOURNEY,
                        ApplicationDataFilter.DECLARATIONS, ApplicationDataFilter.INELIGIBLE_DORMANT_ACCOUNT_DETAILS_LIST))
                .build();
        ApplicationData da = this.dormantAccountActivationApplicationDataTransformer.populateApplicationData(getApplicationDataRequest, us);
        Assertions.assertNotNull(da);
        Assertions.assertInstanceOf(DormantAccountActivationApplicationData.class, da);
        DormantAccountActivationApplicationData dormantData = (DormantAccountActivationApplicationData)da;
        Assertions.assertEquals("**********",dormantData.getPhoneNumber());
        Assertions.assertEquals("23/12/1996",dormantData.getCustomerDob());
        Assertions.assertEquals("Out of curiosity",dormantData.getDormancyRemovalReason());
        Assertions.assertFalse(dormantData.getIsCommunicationAddressSameAsExistingBcifAddress());
        Assertions.assertEquals("code1",dormantData.getPersonalDetailsResponse().getAnnualIncome().getCode());
        Assertions.assertEquals("value1",dormantData.getPersonalDetailsResponse().getAnnualIncome().getValue());
        Assertions.assertEquals(1134,dormantData.getPersonalDetailsResponse().getAnnualSalary());
        Assertions.assertFalse(dormantData.getIsEmailAddressVerified());
        Assertions.assertEquals(2,dormantData.getEtbDetailsDto().getCrnList().size());
        Assertions.assertEquals("c1",dormantData.getEtbDetailsDto().getCrnList().get(0));
        Assertions.assertEquals("c2",dormantData.getEtbDetailsDto().getCrnList().get(1));
        Assertions.assertEquals("irobo",dormantData.getEtbDetailsDto().getFullName());
        Assertions.assertNull(dormantData.getValidDormantAccounts());
        Assertions.assertNull(dormantData.getDeclarationResponses());
        Assertions.assertNull(dormantData.getIneligibleDormantAccountDetailsDtoList());
    }

    @Test
    public void test_transformation_invalid_filter() {
        UserStatus us = UserStatus.builder()
                .leadTrackingNumber("l1")
                .journeyMetadata(DormantAccountActivationJourneyMetadata.builder().build()).build();
        GetApplicationDataRequest dataRequest = GetApplicationDataRequest.builder()
                .dataFilters(Arrays.asList(ApplicationDataFilter.BASIC_DETAIL)).build();

        InvalidRequestException ir = Assertions.assertThrows(InvalidRequestException.class, () -> this.dormantAccountActivationApplicationDataTransformer
                .populateApplicationData(dataRequest, us));
        Assertions.assertEquals("Data filter passed is not applicable : BASIC_DETAIL", ir.getMessage());
    }

    @Test
    public void test_getProduct()
    {
        Assertions.assertEquals("DormantAccountActivation",this.dormantAccountActivationApplicationDataTransformer.getProduct());
    }
}
