package com.kotak.unified.dbservice.transformer.impl;

import com.kotak.unified.common.exception.InvalidRequestException;
import com.kotak.unified.db.ApplicationDataFilter;
import com.kotak.unified.db.BLApplicationData;
import com.kotak.unified.db.GetApplicationDataRequest;
import com.kotak.unified.dbservice.utils.Constants;
import com.kotak.unified.orchestrator.common.dbmodels.AadhaarDetails;
import com.kotak.unified.orchestrator.common.dbmodels.Address;
import com.kotak.unified.orchestrator.common.dbmodels.BusinessLoanJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.JourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.PanDetails;
import com.kotak.unified.orchestrator.common.dbmodels.UserStatus;
import com.kotak.unified.orchestrator.common.dbmodels.assets.LoanIntent;
import com.kotak.unified.orchestrator.common.dbmodels.bl.AddressDetails;
import com.kotak.unified.orchestrator.common.dbmodels.bl.BLProductSpecifications;
import com.kotak.unified.orchestrator.common.dbmodels.bl.BankStatementDetails;
import com.kotak.unified.orchestrator.common.dbmodels.bl.CrnDetails;
import com.kotak.unified.orchestrator.common.dbmodels.bl.InsuranceDetails;
import com.kotak.unified.orchestrator.common.dbmodels.bl.NomineeDetails;
import com.kotak.unified.orchestrator.common.dbmodels.bl.OfferDetails;
import java.time.Instant;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.modelmapper.ModelMapper;

public class BLApplicationDataTransformerTest {

  private final ModelMapper modelMapper = new ModelMapper();
  private final BLApplicationDataTransformer transformer = new BLApplicationDataTransformer(modelMapper);

  private static final String SAMPLE_EMAIL = "sample email";
  private static final String SAMPLE_CRN = "1234";

  @Test
  public void populateApplicationDataTest() throws InvalidRequestException {
    GetApplicationDataRequest request = this.getSampleAppDataRequest();
    UserStatus userStatus = this.getSampleUserStatus();
    var response = transformer.populateApplicationData(request, userStatus);
    Assertions.assertNotNull(response);
    Assertions.assertInstanceOf(BLApplicationData.class, response);
    Assertions.assertEquals(SAMPLE_EMAIL, ((BLApplicationData) response).getEmail());
    Assertions.assertEquals(SAMPLE_CRN, ((BLApplicationData) response).getSelectedCrnDetails().getCrn());
  }

  @Test
  public void invalidRequestThrowsExceptionTest() {
    UserStatus userStatus = this.getSampleUserStatus();
    userStatus.setJourneyMetadata(null);
    Assertions.assertThrows(InvalidRequestException.class,
        () -> transformer.populateApplicationData(new GetApplicationDataRequest(), userStatus));
  }

  @Test
  public void invalidRequestThrowsExceptionOnExtraFilterTest() {
    UserStatus userStatus = this.getSampleUserStatus();
    GetApplicationDataRequest request = GetApplicationDataRequest.builder()
        .dataFilters(List.of(ApplicationDataFilter.INCOME)).build();
    Assertions.assertThrows(InvalidRequestException.class,
        () -> transformer.populateApplicationData(request, userStatus));
  }

  @Test
  public void getProductTest() {
    Assertions.assertEquals(Constants.BUSINESS_LOAN, transformer.getProduct());
  }

  @Test
  public void applicationDataNotSetWhenAbsentInMetadata() throws InvalidRequestException {
    BusinessLoanJourneyMetadata metadata = new BusinessLoanJourneyMetadata();
    UserStatus userStatus = UserStatus.builder()
        .journeyType(Constants.BUSINESS_LOAN_GRAPH_NAME)
        .journeyMetadata(metadata)
        .createdTime(Instant.now())
        .build();
    GetApplicationDataRequest request = GetApplicationDataRequest.builder()
        .dataFilters(List.of(ApplicationDataFilter.INSURANCE_DETAILS, ApplicationDataFilter.PAN,
            ApplicationDataFilter.BANK_STATEMENT_DETAILS,
            ApplicationDataFilter.PRODUCT_SPECIFICATIONS, ApplicationDataFilter.CRN)).build();
    var response = transformer.populateApplicationData(request, userStatus);
    Assertions.assertNotNull(response);
    Assertions.assertInstanceOf(BLApplicationData.class, response);
    Assertions.assertNull(((BLApplicationData) response).getInsuranceDetails());

  }

  private UserStatus getSampleUserStatus() {
    return UserStatus.builder()
        .journeyType(Constants.BUSINESS_LOAN_GRAPH_NAME)
        .journeyMetadata(this.blJourneyMetadata())
        .createdTime(Instant.now())
        .build();
  }

  private JourneyMetadata blJourneyMetadata() {
    BusinessLoanJourneyMetadata metadata = new BusinessLoanJourneyMetadata();
    metadata.setAddressDetailsList(List.of(new AddressDetails()));
    metadata.setOfferDetailsList(List.of(new OfferDetails()));
    metadata.setBankStatementDetails(new BankStatementDetails());
    metadata.setSelectedCrnDetails(new CrnDetails());
    metadata.getSelectedCrnDetails().setCrn(SAMPLE_CRN);
    metadata.getSelectedCrnDetails().setAddress(new Address());
    metadata.setApplicantNegative(false);
    metadata.setPanDetails(new PanDetails());
    metadata.setAadhaarDetails(new AadhaarDetails());
    metadata.setLoanIntent(new LoanIntent());
    metadata.setNomineeDetails(List.of(new NomineeDetails()));
    metadata.setInsuranceDetails(new InsuranceDetails());
    metadata.setLoanCommencementDate(Instant.now().toString());
    metadata.setProductSpecifications(new BLProductSpecifications());
    metadata.setEmail(SAMPLE_EMAIL);
    return metadata;
  }

  private GetApplicationDataRequest getSampleAppDataRequest() {
    return GetApplicationDataRequest.builder()
        .dataFilters(List.of(
            ApplicationDataFilter.ADDRESS,
            ApplicationDataFilter.OFFER_DETAILS,
            ApplicationDataFilter.BANK_STATEMENT_DETAILS,
            ApplicationDataFilter.CRN,
            ApplicationDataFilter.NEGATIVE_APPLICANT_DETAILS,
            ApplicationDataFilter.APPLICATION_CREATED_TIME,
            ApplicationDataFilter.PAN,
            ApplicationDataFilter.PHONE_NUMBER,
            ApplicationDataFilter.EMAIL_ID,
            ApplicationDataFilter.AADHAAR,
            ApplicationDataFilter.INTENT,
            ApplicationDataFilter.PRODUCT,
            ApplicationDataFilter.JOURNEY_MILESTONE,
            ApplicationDataFilter.USER_IP,
            ApplicationDataFilter.LC_CODE,
            ApplicationDataFilter.AGREEMENT_DATA,
            ApplicationDataFilter.PRODUCT_SPECIFICATIONS,
            ApplicationDataFilter.NOMINEE_DETAILS,
            ApplicationDataFilter.INSURANCE_DETAILS,
            ApplicationDataFilter.LOAN_COMMENCEMENT_DATE,
            ApplicationDataFilter.DECLARATIONS_ACCEPTED
        ))
        .build();
  }

}
