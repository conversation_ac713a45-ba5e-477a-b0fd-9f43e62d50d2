package com.kotak.unified.dbservice.transformer.impl;

import com.kotak.unified.common.exception.InvalidRequestException;
import com.kotak.unified.db.AddressResponse;
import com.kotak.unified.db.ApplicationData;
import com.kotak.unified.db.ApplicationDataFilter;
import com.kotak.unified.db.GetApplicationDataRequest;
import com.kotak.unified.db.ca.CAApplicationData;
import com.kotak.unified.dbservice.utils.Constants;
import com.kotak.unified.orchestrator.common.dbmodels.AadhaarDetails;
import com.kotak.unified.orchestrator.common.dbmodels.Address;
import com.kotak.unified.orchestrator.common.dbmodels.BankDetails;
import com.kotak.unified.orchestrator.common.dbmodels.CurrentAccountJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.EtbDetails;
import com.kotak.unified.orchestrator.common.dbmodels.JourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.NomineeDetails;
import com.kotak.unified.orchestrator.common.dbmodels.PanDetails;
import com.kotak.unified.orchestrator.common.dbmodels.ProductSpecifications;
import com.kotak.unified.orchestrator.common.dbmodels.AddOns;
import com.kotak.unified.orchestrator.common.dbmodels.AddOnType;
import com.kotak.unified.orchestrator.common.dbmodels.UserStatus;
import com.kotak.unified.orchestrator.common.dbmodels.Declaration;
import com.kotak.unified.orchestrator.common.dbmodels.ca.BasicDetails;
import com.kotak.unified.orchestrator.common.dbmodels.ca.GstDetails;
import com.kotak.unified.orchestrator.common.dbmodels.ca.OccupationDetails;
import com.kotak.unified.orchestrator.common.dbmodels.ca.OperatingAddress;
import com.kotak.unified.orchestrator.common.dbmodels.ca.IECDetails;
import com.kotak.unified.orchestrator.common.dbmodels.ca.FSSAIDetails;
import com.kotak.unified.orchestrator.common.dbmodels.PrivyDetails;
import com.kotak.unified.orchestrator.common.dbmodels.ca.UdyamDetails;
import com.kotak.unified.orchestrator.common.dbmodels.ca.UdyamAddress;
import com.kotak.unified.orchestrator.common.dbmodels.ca.DerivedEntityProof2Details;
import com.kotak.unified.orchestrator.common.dbmodels.ca.EntityProofDetails;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.modelmapper.ModelMapper;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.List;
import java.util.Collections;

import static com.kotak.unified.dbservice.TestUtils.TEST_AADHAAR_LANDMARK;
import static com.kotak.unified.dbservice.TestUtils.TEST_AADHAAR_REF_KEY;
import static com.kotak.unified.dbservice.TestUtils.TEST_ADDRESS_CITY;
import static com.kotak.unified.dbservice.TestUtils.TEST_ADDRESS_PINCODE;
import static com.kotak.unified.dbservice.TestUtils.TEST_ADDRESS_STATE;
import static com.kotak.unified.dbservice.TestUtils.TEST_FLAT_NO;
import static com.kotak.unified.dbservice.TestUtils.TEST_ENTITY_NAME;
import static com.kotak.unified.dbservice.TestUtils.TEST_UDYAM_REGISTRATION_NUMBER;
import static com.kotak.unified.dbservice.TestUtils.TEST_INCORPORATION_DATE;
import static com.kotak.unified.dbservice.TestUtils.TEST_ADDRESS_LINE1;
import static com.kotak.unified.dbservice.TestUtils.TEST_ADDRESS_LINE2;
import static com.kotak.unified.dbservice.TestUtils.TEST_ADDRESS_LINE3;
import static com.kotak.unified.dbservice.TestUtils.TEST_DOCUMENT_NUMBER;
import static com.kotak.unified.dbservice.utils.Constants.FSSAI_LFDCA_CODE;

@ExtendWith(MockitoExtension.class)
public class CAApplicationDataTransformerTests {

    ModelMapper modelMapper = new ModelMapper();

    @InjectMocks
    private CAApplicationDataTransformer caApplicationDataTransformer;

    @BeforeEach
    public void setup(){
        caApplicationDataTransformer = new CAApplicationDataTransformer(modelMapper);
    }

    @Test
    public void populateApplicationDataTest() throws InvalidRequestException {
        GetApplicationDataRequest request = getSampleAppDataRequest();
        UserStatus userStatus = getSampleUserStatus();
        ApplicationData response =  caApplicationDataTransformer.populateApplicationData(request, userStatus);
        Assertions.assertNotNull(response);
        Assertions.assertInstanceOf(CAApplicationData.class, response);
        Assertions.assertEquals("*********", ((CAApplicationData) response).getEntityCrn());
        Assertions.assertEquals("*********", ((CAApplicationData) response).getCrn());
        Assertions.assertEquals(Instant.EPOCH, ((CAApplicationData) response).getApplicationCreatedTime());
    }

    @Test
    public void populateApplicationDataTestNullUserIp() throws InvalidRequestException {
        GetApplicationDataRequest request = getSampleAppDataRequest();
        UserStatus userStatus = getSampleUserStatus();
        userStatus.setUserIp(null);
        ApplicationData response =  caApplicationDataTransformer.populateApplicationData(request, userStatus);
        Assertions.assertNotNull(response);
        Assertions.assertInstanceOf(CAApplicationData.class, response);
        Assertions.assertEquals("*********", ((CAApplicationData) response).getEntityCrn());
        Assertions.assertEquals("*********", ((CAApplicationData) response).getCrn());
        Assertions.assertNull(((CAApplicationData) response).getUserIp());
    }

    @Test
    public void populateApplicationDataTestUserIp() throws InvalidRequestException {
        GetApplicationDataRequest request = getSampleAppDataRequest();
        UserStatus userStatus = getSampleUserStatus();
        ApplicationData response =  caApplicationDataTransformer.populateApplicationData(request, userStatus);
        Assertions.assertNotNull(response);
        Assertions.assertInstanceOf(CAApplicationData.class, response);
        Assertions.assertEquals("*********", ((CAApplicationData) response).getEntityCrn());
        Assertions.assertEquals("*********", ((CAApplicationData) response).getCrn());
        Assertions.assertNotNull(((CAApplicationData) response).getUserIp());
    }

    @Test
    public void populateApplicationDataTestNullAadhaarDetails() throws InvalidRequestException {
        GetApplicationDataRequest request = getSampleAppDataRequest();
        UserStatus userStatus = getSampleUserStatus();
        CurrentAccountJourneyMetadata journeyMetadata = (CurrentAccountJourneyMetadata) userStatus.getJourneyMetadata();
        journeyMetadata.setAadhaarDetails(null);
        ApplicationData response =  caApplicationDataTransformer.populateApplicationData(request, userStatus);
        Assertions.assertNotNull(response);
        Assertions.assertInstanceOf(CAApplicationData.class, response);
        Assertions.assertEquals("*********", ((CAApplicationData) response).getEntityCrn());
        Assertions.assertEquals("*********", ((CAApplicationData) response).getCrn());
        Assertions.assertNull(((CAApplicationData) response).getAadhaarDetails());
    }

    @Test
    public void populateApplicationDataTestNullPanDetails() throws InvalidRequestException {
        GetApplicationDataRequest request = getSampleAppDataRequest();
        UserStatus userStatus = getSampleUserStatus();
        CurrentAccountJourneyMetadata journeyMetadata = (CurrentAccountJourneyMetadata) userStatus.getJourneyMetadata();
        journeyMetadata.setPanDetails(null);
        ApplicationData response =  caApplicationDataTransformer.populateApplicationData(request, userStatus);
        Assertions.assertNotNull(response);
        Assertions.assertInstanceOf(CAApplicationData.class, response);
        Assertions.assertEquals("*********", ((CAApplicationData) response).getEntityCrn());
        Assertions.assertEquals("*********", ((CAApplicationData) response).getCrn());
        Assertions.assertNull(((CAApplicationData) response).getPanDetails());
    }

    @Test
    public void populateApplicationDataTestNullGstDetails() throws InvalidRequestException {
        GetApplicationDataRequest request = getSampleAppDataRequest();
        UserStatus userStatus = getSampleUserStatus();
        CurrentAccountJourneyMetadata journeyMetadata = (CurrentAccountJourneyMetadata) userStatus.getJourneyMetadata();
        journeyMetadata.setGstDetails(null);
        ApplicationData response =  caApplicationDataTransformer.populateApplicationData(request, userStatus);
        Assertions.assertNotNull(response);
        Assertions.assertInstanceOf(CAApplicationData.class, response);
        Assertions.assertEquals("*********", ((CAApplicationData) response).getEntityCrn());
        Assertions.assertEquals("*********", ((CAApplicationData) response).getCrn());
        Assertions.assertNull(((CAApplicationData) response).getGstDetails());
    }

    @Test
    public void populateApplicationDataTestNullBankDetails() throws InvalidRequestException {
        GetApplicationDataRequest request = getSampleAppDataRequest();
        UserStatus userStatus = getSampleUserStatus();
        CurrentAccountJourneyMetadata journeyMetadata = (CurrentAccountJourneyMetadata) userStatus.getJourneyMetadata();
        journeyMetadata.setBankDetails(null);
        ApplicationData response =  caApplicationDataTransformer.populateApplicationData(request, userStatus);
        Assertions.assertNotNull(response);
        Assertions.assertInstanceOf(CAApplicationData.class, response);
        Assertions.assertEquals("*********", ((CAApplicationData) response).getEntityCrn());
        Assertions.assertEquals("*********", ((CAApplicationData) response).getCrn());
        Assertions.assertNull(((CAApplicationData) response).getBankDetails());
    }

    @Test
    public void populateApplicationDataTestNullProductSpecifications() throws InvalidRequestException {
        GetApplicationDataRequest request = getSampleAppDataRequest();
        UserStatus userStatus = getSampleUserStatus();
        CurrentAccountJourneyMetadata journeyMetadata = (CurrentAccountJourneyMetadata) userStatus.getJourneyMetadata();
        journeyMetadata.setProductSpecifications(null);
        ApplicationData response =  caApplicationDataTransformer.populateApplicationData(request, userStatus);
        Assertions.assertNotNull(response);
        Assertions.assertInstanceOf(CAApplicationData.class, response);
        Assertions.assertEquals("*********", ((CAApplicationData) response).getEntityCrn());
        Assertions.assertEquals("*********", ((CAApplicationData) response).getCrn());
        Assertions.assertNull(((CAApplicationData) response).getCaProductSpecificationsResponse());
    }

    @Test
    public void populateApplicationDataTestNullBasicDetails() throws InvalidRequestException {
        GetApplicationDataRequest request = getSampleAppDataRequest();
        UserStatus userStatus = getSampleUserStatus();
        CurrentAccountJourneyMetadata journeyMetadata = (CurrentAccountJourneyMetadata) userStatus.getJourneyMetadata();
        journeyMetadata.setBasicDetails(null);
        ApplicationData response =  caApplicationDataTransformer.populateApplicationData(request, userStatus);
        Assertions.assertNotNull(response);
        Assertions.assertInstanceOf(CAApplicationData.class, response);
        Assertions.assertEquals("*********", ((CAApplicationData) response).getEntityCrn());
        Assertions.assertEquals("*********", ((CAApplicationData) response).getCrn());
        Assertions.assertNull(((CAApplicationData) response).getBasicDetails());
    }

    @Test
    public void populateApplicationDataTestNullNomineeDetails() throws InvalidRequestException {
        GetApplicationDataRequest request = getSampleAppDataRequest();
        UserStatus userStatus = getSampleUserStatus();
        CurrentAccountJourneyMetadata journeyMetadata = (CurrentAccountJourneyMetadata) userStatus.getJourneyMetadata();
        journeyMetadata.setNomineeDetails(null);
        ApplicationData response =  caApplicationDataTransformer.populateApplicationData(request, userStatus);
        Assertions.assertNotNull(response);
        Assertions.assertInstanceOf(CAApplicationData.class, response);
        Assertions.assertEquals("*********", ((CAApplicationData) response).getEntityCrn());
        Assertions.assertEquals("*********", ((CAApplicationData) response).getCrn());
        Assertions.assertNull(((CAApplicationData) response).getNomineeDetails());
    }

    @Test
    public void populateApplicationDataTestNullCommunicationAddress() throws InvalidRequestException {
        GetApplicationDataRequest request = getSampleAppDataRequest();
        UserStatus userStatus = getSampleUserStatus();
        CurrentAccountJourneyMetadata journeyMetadata = (CurrentAccountJourneyMetadata) userStatus.getJourneyMetadata();
        journeyMetadata.setCommunicationAddress(null);
        ApplicationData response =  caApplicationDataTransformer.populateApplicationData(request, userStatus);
        Assertions.assertNotNull(response);
        Assertions.assertInstanceOf(CAApplicationData.class, response);
        Assertions.assertEquals("*********", ((CAApplicationData) response).getEntityCrn());
        Assertions.assertEquals("*********", ((CAApplicationData) response).getCrn());
        Assertions.assertNull(((CAApplicationData) response).getCommunicationAddress());
    }

    @Test
    public void populateApplicationDataTestNullOperatingAddress() throws InvalidRequestException {
        GetApplicationDataRequest request = getSampleAppDataRequest();
        UserStatus userStatus = getSampleUserStatus();
        CurrentAccountJourneyMetadata journeyMetadata = (CurrentAccountJourneyMetadata) userStatus.getJourneyMetadata();
        journeyMetadata.setOperatingAddress(null);
        ApplicationData response =  caApplicationDataTransformer.populateApplicationData(request, userStatus);
        Assertions.assertNotNull(response);
        Assertions.assertInstanceOf(CAApplicationData.class, response);
        Assertions.assertEquals("*********", ((CAApplicationData) response).getEntityCrn());
        Assertions.assertEquals("*********", ((CAApplicationData) response).getCrn());
        Assertions.assertNull(((CAApplicationData) response).getOperatingAddress());
    }

    @Test
    public void populateApplicationDataTestNullOccupationDetails() throws InvalidRequestException {
        GetApplicationDataRequest request = getSampleAppDataRequest();
        UserStatus userStatus = getSampleUserStatus();
        CurrentAccountJourneyMetadata journeyMetadata = (CurrentAccountJourneyMetadata) userStatus.getJourneyMetadata();
        journeyMetadata.setOccupationDetails(null);
        ApplicationData response =  caApplicationDataTransformer.populateApplicationData(request, userStatus);
        Assertions.assertNotNull(response);
        Assertions.assertInstanceOf(CAApplicationData.class, response);
        Assertions.assertEquals("*********", ((CAApplicationData) response).getEntityCrn());
        Assertions.assertEquals("*********", ((CAApplicationData) response).getCrn());
        Assertions.assertNull(((CAApplicationData) response).getOccupationDetails());
        Assertions.assertEquals(12345, ((CAApplicationData) response).getFundingAmount());
    }

    @Test
    public void populateApplicationDataTestNullIECDetails() throws InvalidRequestException {
        GetApplicationDataRequest request = getSampleAppDataRequest();
        UserStatus userStatus = getSampleUserStatus();
        CurrentAccountJourneyMetadata journeyMetadata = (CurrentAccountJourneyMetadata) userStatus.getJourneyMetadata();
        journeyMetadata.setOperatingAddressType("IEC");
        journeyMetadata.setIecDetails(null);
        ApplicationData response =  caApplicationDataTransformer.populateApplicationData(request, userStatus);
        Assertions.assertNotNull(response);
        Assertions.assertInstanceOf(CAApplicationData.class, response);
        Assertions.assertEquals("*********", ((CAApplicationData) response).getEntityCrn());
        Assertions.assertEquals("*********", ((CAApplicationData) response).getCrn());
        Assertions.assertNull(((CAApplicationData) response).getIecDetails());
        Assertions.assertEquals(12345, ((CAApplicationData) response).getFundingAmount());
    }

    @Test
    public void populateApplicationDataTestNullFSSAIDetails() throws InvalidRequestException {
        GetApplicationDataRequest request = getSampleAppDataRequest();
        UserStatus userStatus = getSampleUserStatus();
        CurrentAccountJourneyMetadata journeyMetadata = (CurrentAccountJourneyMetadata) userStatus.getJourneyMetadata();
        journeyMetadata.setOperatingAddressType(FSSAI_LFDCA_CODE);
        journeyMetadata.setFssaiDetails(null);
        ApplicationData response =  caApplicationDataTransformer.populateApplicationData(request, userStatus);
        Assertions.assertNotNull(response);
        Assertions.assertInstanceOf(CAApplicationData.class, response);
        Assertions.assertEquals("*********", ((CAApplicationData) response).getEntityCrn());
        Assertions.assertEquals("*********", ((CAApplicationData) response).getCrn());
        Assertions.assertNull(((CAApplicationData) response).getFssaiDetails());
        Assertions.assertEquals(12345, ((CAApplicationData) response).getFundingAmount());
    }

    @Test
    public void populateApplicationDataTestNullUdyamDetails() throws InvalidRequestException {
        GetApplicationDataRequest request = getSampleAppDataRequest();
        UserStatus userStatus = getSampleUserStatus();
        CurrentAccountJourneyMetadata journeyMetadata = (CurrentAccountJourneyMetadata) userStatus.getJourneyMetadata();
        journeyMetadata.setOperatingAddressType("UDYAM");
        journeyMetadata.setUdyamDetails(null);
        ApplicationData response =  caApplicationDataTransformer.populateApplicationData(request, userStatus);
        Assertions.assertNotNull(response);
        Assertions.assertInstanceOf(CAApplicationData.class, response);
        Assertions.assertEquals("*********", ((CAApplicationData) response).getEntityCrn());
        Assertions.assertEquals("*********", ((CAApplicationData) response).getCrn());
        Assertions.assertNull(((CAApplicationData) response).getUdyamDetails());
        Assertions.assertEquals(12345, ((CAApplicationData) response).getFundingAmount());
    }

    @Test
    public void populateApplicationDataTestIECDetails() throws InvalidRequestException {
        GetApplicationDataRequest request = getSampleAppDataRequest();
        UserStatus userStatus = getSampleUserStatus();
        CurrentAccountJourneyMetadata journeyMetadata = (CurrentAccountJourneyMetadata) userStatus.getJourneyMetadata();
        journeyMetadata.setOperatingAddressType("IEC");
        ApplicationData response =  caApplicationDataTransformer.populateApplicationData(request, userStatus);

        Assertions.assertNotNull(response);
        Assertions.assertInstanceOf(CAApplicationData.class, response);
        Assertions.assertEquals("*********", ((CAApplicationData) response).getEntityCrn());
        Assertions.assertEquals("*********", ((CAApplicationData) response).getCrn());
        Assertions.assertNotNull(((CAApplicationData) response).getIecDetails());
        Assertions.assertEquals("iec address", ((CAApplicationData) response).getIecDetails().getAddress());
        Assertions.assertEquals(12345, ((CAApplicationData) response).getFundingAmount());
    }

    @Test
    public void populateApplicationDataTestNullDerivedEP2Details() throws InvalidRequestException {
        GetApplicationDataRequest request = getSampleAppDataRequest();
        UserStatus userStatus = getSampleUserStatus();
        CurrentAccountJourneyMetadata journeyMetadata = (CurrentAccountJourneyMetadata) userStatus.getJourneyMetadata();
        journeyMetadata.setDerivedEntityProof2Details(null);
        ApplicationData response =  caApplicationDataTransformer.populateApplicationData(request, userStatus);
        Assertions.assertNotNull(response);
        Assertions.assertInstanceOf(CAApplicationData.class, response);
        Assertions.assertEquals("*********", ((CAApplicationData) response).getEntityCrn());
        Assertions.assertEquals("*********", ((CAApplicationData) response).getCrn());
        Assertions.assertNull(((CAApplicationData) response).getDerivedEntityProof2Details());
        Assertions.assertEquals(12345, ((CAApplicationData) response).getFundingAmount());
    }

    @Test
    public void populateApplicationDataTestFSSAIDetails() throws InvalidRequestException {
        GetApplicationDataRequest request = getSampleAppDataRequest();
        UserStatus userStatus = getSampleUserStatus();
        CurrentAccountJourneyMetadata journeyMetadata = (CurrentAccountJourneyMetadata) userStatus.getJourneyMetadata();
        journeyMetadata.setOperatingAddressType(FSSAI_LFDCA_CODE);
        ApplicationData response =  caApplicationDataTransformer.populateApplicationData(request, userStatus);

        Assertions.assertNotNull(response);
        Assertions.assertInstanceOf(CAApplicationData.class, response);
        Assertions.assertEquals("*********", ((CAApplicationData) response).getEntityCrn());
        Assertions.assertEquals("*********", ((CAApplicationData) response).getCrn());
        Assertions.assertNotNull(((CAApplicationData) response).getFssaiDetails());
        Assertions.assertEquals("fssai address", ((CAApplicationData) response).getFssaiDetails().getAddress());
        Assertions.assertEquals(12345, ((CAApplicationData) response).getFundingAmount());
    }

    @Test
    public void populateApplicationDataTestUdyamDetails() throws InvalidRequestException {
        GetApplicationDataRequest request = getSampleAppDataRequest();
        UserStatus userStatus = getSampleUserStatus();
        CurrentAccountJourneyMetadata journeyMetadata = (CurrentAccountJourneyMetadata) userStatus.getJourneyMetadata();
        journeyMetadata.setOperatingAddressType("UDYAM");
        ApplicationData response =  caApplicationDataTransformer.populateApplicationData(request, userStatus);

        Assertions.assertNotNull(response);
        Assertions.assertInstanceOf(CAApplicationData.class, response);
        Assertions.assertEquals("*********", ((CAApplicationData) response).getEntityCrn());
        Assertions.assertEquals("*********", ((CAApplicationData) response).getCrn());
        Assertions.assertNotNull(((CAApplicationData) response).getUdyamDetails());
        Assertions.assertNotNull(((CAApplicationData) response).getUdyamDetails().getUdyamAddress());
        Assertions.assertEquals(12345, ((CAApplicationData) response).getFundingAmount());
        Assertions.assertEquals(TEST_FLAT_NO, ((CAApplicationData) response).getUdyamDetails().getUdyamAddress().getFlat());
        Assertions.assertEquals(TEST_ENTITY_NAME, ((CAApplicationData) response).getUdyamDetails().getEntityName());
        Assertions.assertEquals(TEST_UDYAM_REGISTRATION_NUMBER, ((CAApplicationData) response).getUdyamDetails().getUdyamRegistrationNo());
        Assertions.assertEquals(TEST_INCORPORATION_DATE, ((CAApplicationData) response).getUdyamDetails().getDateOfIncorporation());
    }

    @Test
    public void invalidRequestThrowsExceptionTest() {
        UserStatus userStatus = getSampleUserStatus();
        userStatus.setJourneyMetadata(null);
        Assertions.assertThrows(InvalidRequestException.class,
                () -> caApplicationDataTransformer.populateApplicationData(new GetApplicationDataRequest(), userStatus));
    }

    @Test
    public void invalidRequestThrowsExceptionOnExtraFilterTest() {
        UserStatus userStatus = getSampleUserStatus();
        GetApplicationDataRequest request = GetApplicationDataRequest.builder()
                .dataFilters(List.of(ApplicationDataFilter.INCOME)).build();
        Assertions.assertThrows(InvalidRequestException.class,
                () -> caApplicationDataTransformer.populateApplicationData(request, userStatus));
    }

    @Test
    public void getProductTest() {
        Assertions.assertEquals(Constants.CURRENT_ACCOUNT, caApplicationDataTransformer.getProduct());
    }

    @Test
    public void populateApplicationDataTestNullDeclarations() throws InvalidRequestException {
        GetApplicationDataRequest request = getSampleAppDataRequest();
        UserStatus userStatus = getSampleUserStatus();
        CurrentAccountJourneyMetadata journeyMetadata = (CurrentAccountJourneyMetadata) userStatus.getJourneyMetadata();
        journeyMetadata.setDeclarations(null);
        ApplicationData response =  caApplicationDataTransformer.populateApplicationData(request, userStatus);
        Assertions.assertNotNull(response);
        Assertions.assertInstanceOf(CAApplicationData.class, response);
        Assertions.assertEquals("*********", ((CAApplicationData) response).getEntityCrn());
        Assertions.assertEquals("*********", ((CAApplicationData) response).getCrn());
        Assertions.assertNull(((CAApplicationData) response).getDeclarationsResponse());
        Assertions.assertEquals(12345, ((CAApplicationData) response).getFundingAmount());
    }

    @Test
    public void populateApplicationDataTestDeclarations() throws InvalidRequestException {
        GetApplicationDataRequest request = getSampleAppDataRequest();
        UserStatus userStatus = getSampleUserStatus();
        ApplicationData response =  caApplicationDataTransformer.populateApplicationData(request, userStatus);
        Assertions.assertNotNull(response);
        Assertions.assertInstanceOf(CAApplicationData.class, response);
        Assertions.assertEquals("*********", ((CAApplicationData) response).getEntityCrn());
        Assertions.assertEquals("*********", ((CAApplicationData) response).getCrn());
        Assertions.assertEquals(12345, ((CAApplicationData) response).getFundingAmount());
        Assertions.assertEquals("i'm not robot", ((CAApplicationData) response).getDeclarationsResponse().get("declaration2").getText());
        Assertions.assertTrue(((CAApplicationData) response).getDeclarationsResponse().get("declaration2").getDeclarationAccepted());
    }

    @Test
    public void populateApplicationDataTestAddOns() throws InvalidRequestException {
        GetApplicationDataRequest request = getSampleAppDataRequest();
        UserStatus userStatus = getSampleUserStatus();
        ApplicationData response =  caApplicationDataTransformer.populateApplicationData(request, userStatus);
        Assertions.assertNotNull(response);
        Assertions.assertInstanceOf(CAApplicationData.class, response);
        Assertions.assertEquals("*********", ((CAApplicationData) response).getEntityCrn());
        Assertions.assertEquals("*********", ((CAApplicationData) response).getCrn());
        Assertions.assertEquals(12345, ((CAApplicationData) response).getFundingAmount());
        Assertions.assertNotNull(((CAApplicationData) response).getAddOnResponses());
        Assertions.assertNotNull(((CAApplicationData) response).getAddOnResponses().get(0));
        Assertions.assertEquals(AddOnType.EMAIL_ALERT.toString(), ((CAApplicationData) response).getAddOnResponses().get(0).getType().toString());
        Assertions.assertEquals("weekly", ((CAApplicationData) response).getAddOnResponses().get(0).getProperties().get("frequency"));

    }

    @Test
    public void populateApplicationDataTestNullAddOns() throws InvalidRequestException {
        GetApplicationDataRequest request = getSampleAppDataRequest();
        UserStatus userStatus = getSampleUserStatus();
        CurrentAccountJourneyMetadata journeyMetadata = (CurrentAccountJourneyMetadata) userStatus.getJourneyMetadata();
        journeyMetadata.setAddOns(null);
        ApplicationData response =  caApplicationDataTransformer.populateApplicationData(request, userStatus);
        Assertions.assertNotNull(response);
        Assertions.assertInstanceOf(CAApplicationData.class, response);
        Assertions.assertEquals("*********", ((CAApplicationData) response).getEntityCrn());
        Assertions.assertEquals("*********", ((CAApplicationData) response).getCrn());
        Assertions.assertNull(((CAApplicationData) response).getAddOnResponses());
    }

    @Test
    public void populateApplicationDataTestEntityProofDetails() throws InvalidRequestException {
        GetApplicationDataRequest request = getSampleAppDataRequest();
        UserStatus userStatus = getSampleUserStatus();
        ApplicationData response =  caApplicationDataTransformer.populateApplicationData(request, userStatus);
        Assertions.assertNotNull(response);
        Assertions.assertInstanceOf(CAApplicationData.class, response);
        Assertions.assertNotNull(((CAApplicationData) response).getPrimaryEntityProofDetails());
        Assertions.assertEquals(TEST_ENTITY_NAME, ((CAApplicationData) response).getEntityName());
        Assertions.assertEquals(TEST_INCORPORATION_DATE, ((CAApplicationData) response).getDateOfIncorporation());
        Assertions.assertEquals(TEST_ADDRESS_LINE1, ((CAApplicationData) response).getPrimaryEntityProofDetails().getAddress().getLine1());
    }
    @Test
    public void populateApplicationDataTestEtbDetails() throws InvalidRequestException {
        GetApplicationDataRequest request = getSampleAppDataRequest();
        UserStatus userStatus = getSampleUserStatus();
        ApplicationData response =  caApplicationDataTransformer.populateApplicationData(request, userStatus);
        Assertions.assertNotNull(response);
        Assertions.assertInstanceOf(CAApplicationData.class, response);
        Assertions.assertEquals(TEST_AADHAAR_REF_KEY, ((CAApplicationData) response).getEtbDetailsResponse().getAadhaarRefKey());
        Assertions.assertEquals(TEST_ADDRESS_CITY, ((CAApplicationData) response).getEtbDetailsResponse().getCommunicationAddress().getCity());
        Assertions.assertEquals(TEST_ADDRESS_LINE1, ((CAApplicationData) response).getEtbDetailsResponse().getCommunicationAddress().getLine1());
        Assertions.assertEquals(TEST_ADDRESS_LINE2, ((CAApplicationData) response).getEtbDetailsResponse().getCommunicationAddress().getLine2());
        Assertions.assertEquals(TEST_ADDRESS_LINE3, ((CAApplicationData) response).getEtbDetailsResponse().getCommunicationAddress().getLine3());
        Assertions.assertEquals(TEST_ADDRESS_STATE, ((CAApplicationData) response).getEtbDetailsResponse().getCommunicationAddress().getState());
        Assertions.assertEquals(TEST_ADDRESS_PINCODE, ((CAApplicationData) response).getEtbDetailsResponse().getCommunicationAddress().getPincode());
//         Assertions.assertEquals(TEST, ((CAApplicationData) response).getEtbDetailsResponse().getCommunicationAddress().getPincode());
    }

    @Test
    public void populateApplicationDataTestEntityProofDetailsWithUdyam() throws InvalidRequestException {
        GetApplicationDataRequest request = getSampleAppDataRequest();
        UserStatus userStatus = getSampleUserStatus();
        CurrentAccountJourneyMetadata journeyMetadata = (CurrentAccountJourneyMetadata) userStatus.getJourneyMetadata();
        journeyMetadata.getSecondaryEntityProofDetails().setEntityProofCode("MSME_Udyam");
        journeyMetadata.getSecondaryEntityProofDetails().setDocumentNumber(TEST_UDYAM_REGISTRATION_NUMBER);
        ApplicationData response =  caApplicationDataTransformer.populateApplicationData(request, userStatus);
        Assertions.assertNotNull(response);
        Assertions.assertInstanceOf(CAApplicationData.class, response);
        Assertions.assertNotNull(((CAApplicationData) response).getPrimaryEntityProofDetails());
        Assertions.assertNotNull(((CAApplicationData) response).getSecondaryEntityProofDetails());
        Assertions.assertEquals(TEST_UDYAM_REGISTRATION_NUMBER, ((CAApplicationData) response).getSecondaryEntityProofDetails().getDocumentNumber());
    }
    @Test
    public void populateApplicationDataTestNullEntityProofDetails() throws InvalidRequestException {
        GetApplicationDataRequest request = getSampleAppDataRequest();
        UserStatus userStatus = getSampleUserStatus();
        CurrentAccountJourneyMetadata journeyMetadata = (CurrentAccountJourneyMetadata) userStatus.getJourneyMetadata();
        journeyMetadata.setPrimaryEntityProofDetails(null);
        journeyMetadata.setSecondaryEntityProofDetails(null);
        ApplicationData response =  caApplicationDataTransformer.populateApplicationData(request, userStatus);
        Assertions.assertNotNull(response);
        Assertions.assertInstanceOf(CAApplicationData.class, response);
        Assertions.assertNull(((CAApplicationData) response).getPrimaryEntityProofDetails());
        Assertions.assertNull(((CAApplicationData) response).getSecondaryEntityProofDetails());
        Assertions.assertEquals("*********", ((CAApplicationData) response).getEntityCrn());
        Assertions.assertEquals("*********", ((CAApplicationData) response).getCrn());
    }

    @Test
    public void populateApplicationDataForPrivyTest() throws InvalidRequestException {
        GetApplicationDataRequest request = getSampleAppDataRequest();
        UserStatus userStatus = getSampleUserStatus();
        ApplicationData response =  caApplicationDataTransformer.populateApplicationData(request, userStatus);
        Assertions.assertNotNull(response);
        Assertions.assertInstanceOf(CAApplicationData.class, response);
        Assertions.assertEquals("*********", ((CAApplicationData) response).getEntityCrn());
        Assertions.assertEquals("*********", ((CAApplicationData) response).getCrn());
        Assertions.assertEquals(true, ((CAApplicationData) response).getPrivyDetails().getIsPrivyOpted());
        Assertions.assertEquals("associatedPersonClassificationCode", ((CAApplicationData) response).getPrivyDetails().getAssociatedPersonClassificationCode());
        Assertions.assertEquals("privyLeagueProgramName", ((CAApplicationData) response).getPrivyDetails().getPrivyLeagueProgramName());
        Assertions.assertEquals("branchCategoryType", ((CAApplicationData) response).getPrivyDetails().getBranchCategoryType());
        Assertions.assertEquals("privyLeagueProgramVariant", ((CAApplicationData) response).getPrivyDetails().getPrivyLeagueProgramVariant());
        Assertions.assertEquals("privyProgramClassificationType", ((CAApplicationData) response).getPrivyDetails().getPrivyProgramClassificationType());
        Assertions.assertEquals("associatedPersonCRNNumber", ((CAApplicationData) response).getPrivyDetails().getAssociatedPersonCRNNumber());
        Assertions.assertEquals("associatedPersonName", ((CAApplicationData) response).getPrivyDetails().getAssociatedPersonName());
        Assertions.assertEquals("associatedPersonLeagueProgramName", ((CAApplicationData) response).getPrivyDetails().getAssociatedPersonLeagueProgramName());
        Assertions.assertEquals("associatedPersonProgramVariant", ((CAApplicationData) response).getPrivyDetails().getAssociatedPersonProgramVariant());
    }

    private UserStatus getSampleUserStatus() {
        return UserStatus.builder()
                .phoneNumber("*********")
                .userIp("***************")
                .journeyType(Constants.BUSINESS_CURRENT_ACCOUNT_GRAPH_NAME)
                .journeyMetadata(getCAJourneyMetadata())
                .createdTime(Instant.EPOCH)
                .build();
    }

    private JourneyMetadata getCAJourneyMetadata() {

        Map<String, Declaration> declarationMap = new HashMap<>();
        declarationMap.put("declaration1",Declaration.builder()
                .name("human declaration")
                .text("i'm a human")
                .declarationAccepted(true)
                .dateTime(Instant.now())
                .build());
        declarationMap.put("declaration2",Declaration.builder()
                .name("robot declaration")
                .text("i'm not robot")
                .declarationAccepted(true)
                .dateTime(Instant.now())
                .build());

        return CurrentAccountJourneyMetadata.builder()
                .entityCrn("*********")
                .crn("*********")
                .emailAddress("<EMAIL>")
                .accountNumber("**********")
                .accountOpeningDate(Instant.now())
                .bankDetails(BankDetails.builder().build())
                .gstDetails(GstDetails.builder().build())
                .panDetails(PanDetails.builder().build())
                .aadhaarDetails(AadhaarDetails.builder().build())
                .isCommunicationAddressSameAsAadhaarAddress(true)
                .operatingAddress(OperatingAddress.builder().build())
                .communicationAddress(Address.builder().build())
                .basicDetails(BasicDetails.builder().build())
                .nomineeDetails(NomineeDetails.builder().build())
                .productSpecifications(new ProductSpecifications())
                .fundingAmount(12345)
                .isActivMoneyOpted(true)
                .occupationDetails(OccupationDetails.builder()
                        .incomeSource("Business Income")
                        .incomeSourceCode("3")
                        .occupationCode("SE")
                        .occupationType("self employed")
                        .build())
                .privyDetails(PrivyDetails.builder()
                        .branchCategoryType("branchCategoryType")
                        .isPrivyOpted(true)
                        .privyLeagueProgramName("privyLeagueProgramName")
                        .associatedPersonClassificationCode("associatedPersonClassificationCode")
                        .associatedPersonClassificationType("associatedPersonClassificationType")
                        .associatedPersonCRNNumber("associatedPersonCRNNumber")
                        .associatedPersonLeagueProgramName("associatedPersonLeagueProgramName")
                        .associatedPersonProgramVariant("associatedPersonProgramVariant")
                        .associatedPersonName("associatedPersonName")
                        .privyProgramClassificationType("privyProgramClassificationType")
                        .privyClassificationCode("privyClassificationCode")
                        .privyLeagueProgramVariant("privyLeagueProgramVariant")
                        .build())
                .operatingAddressType("IEC")
                .iecDetails(IECDetails.builder()
                        .address("iec address")
                        .build())
                .fssaiDetails(FSSAIDetails.builder()
                        .address("fssai address")
                        .build())
                .udyamDetails(UdyamDetails.builder()
                        .udyamRegistrationNo(TEST_UDYAM_REGISTRATION_NUMBER)
                        .entityName(TEST_ENTITY_NAME)
                        .dateOfIncorporation(TEST_INCORPORATION_DATE)
                        .udyamAddress(UdyamAddress.builder()
                                .flat(TEST_FLAT_NO)
                                .build())
                        .build())
                .derivedEntityProof2Details(DerivedEntityProof2Details.builder()
                        .address(Address.builder()
                                .line1(TEST_ADDRESS_LINE1)
                                .line2(TEST_ADDRESS_LINE2)
                                .line3(TEST_ADDRESS_LINE3)
                                .build())
                        .entityName(TEST_ENTITY_NAME)
                        .build())
                .declarations(declarationMap)
                .etbDetails(EtbDetails.builder()
                        .aadhaarRefKey(TEST_AADHAAR_REF_KEY)
                        .communicationAddress(Address.builder()
                                .line1(TEST_ADDRESS_LINE1)
                                .line2(TEST_ADDRESS_LINE2)
                                .line3(TEST_ADDRESS_LINE3)
                                .state(TEST_ADDRESS_STATE)
                                .landmark(TEST_AADHAAR_LANDMARK)
                                .pincode(TEST_ADDRESS_PINCODE)
                                .city(TEST_ADDRESS_CITY)
                                .build())
//                        TODO: add details for aus CRN on basis RE-KYC
                        .build())

                .addOns(Collections.singletonList(AddOns.builder().type(AddOnType.EMAIL_ALERT).properties(Map.of("frequency", "weekly")).build()))
                .primaryEntityProofDetails(EntityProofDetails.builder()
                        .entityProofCode(FSSAI_LFDCA_CODE)
                        .documentNumber(TEST_DOCUMENT_NUMBER)
                        .isDigital(true)
                        .address(Address.builder()
                                .line1(TEST_ADDRESS_LINE1)
                                .line2(TEST_ADDRESS_LINE2)
                                .line3(TEST_ADDRESS_LINE3)
                                .build())
                        .validationStatus("success")
                        .build())
                .isSecondaryEntityProofOpted(true)
                .secondaryEntityProofDetails(EntityProofDetails.builder()
                        .entityProofCode("IEC")
                        .documentNumber(TEST_DOCUMENT_NUMBER)
                        .isDigital(true)
                        .address(Address.builder()
                                .line1(TEST_ADDRESS_LINE1)
                                .line2(TEST_ADDRESS_LINE2)
                                .line3(TEST_ADDRESS_LINE3)
                                .build())
                        .validationStatus("success")
                        .build())
                .entityName(TEST_ENTITY_NAME)
                .dateOfIncorporation(TEST_INCORPORATION_DATE)
                .isPrimaryEntityAddressOptedAsCommunicationAddress(true)
                .build();
    }

    private GetApplicationDataRequest getSampleAppDataRequest() {
        return GetApplicationDataRequest.builder()
                .dataFilters(List.of(
                        ApplicationDataFilter.CRN,
                        ApplicationDataFilter.GST_DETAILS,
                        ApplicationDataFilter.AADHAAR,
                        ApplicationDataFilter.ACCOUNT_DETAILS,
                        ApplicationDataFilter.PAN,
                        ApplicationDataFilter.EMAIL_ID,
                        ApplicationDataFilter.PHONE_NUMBER,
                        ApplicationDataFilter.PRODUCT_SPECIFICATIONS,
                        ApplicationDataFilter.BANK_DETAILS,
                        ApplicationDataFilter.BASIC_DETAIL,
                        ApplicationDataFilter.NOMINEE_DETAILS,
                        ApplicationDataFilter.ADDRESS,
                        ApplicationDataFilter.OCCUPATION_DETAILS,
                        ApplicationDataFilter.FUNDING_DETAILS,
                        ApplicationDataFilter.SECONDARY_ENTITY_PROOF_DETAILS,
                        ApplicationDataFilter.DECLARATIONS,
                        ApplicationDataFilter.USER_IP,
                        ApplicationDataFilter.ENTITY_PROOF_DETAILS,
                        ApplicationDataFilter.ETB_DETAILS,
                        ApplicationDataFilter.PRIVY_DETAILS,
                        ApplicationDataFilter.ADD_ONS,
                        ApplicationDataFilter.APPLICATION_CREATED_TIME))
                .build();
    }

}
