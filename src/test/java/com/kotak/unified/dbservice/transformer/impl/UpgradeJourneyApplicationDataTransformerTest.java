package com.kotak.unified.dbservice.transformer.impl;

import com.kotak.unified.common.exception.InvalidRequestException;
import com.kotak.unified.db.ApplicationDataFilter;
import com.kotak.unified.db.CrnUpgradeDetailsResponse;
import com.kotak.unified.db.DeclarationResponse;
import com.kotak.unified.db.GetApplicationDataRequest;
import com.kotak.unified.db.UpgradeJourneyApplicationData;
import com.kotak.unified.db.response.EtbDetailsDto;
import com.kotak.unified.dbservice.exceptions.EntityNotFoundException;
import com.kotak.unified.orchestrator.common.dbmodels.Declaration;
import com.kotak.unified.orchestrator.common.dbmodels.EtbDetails;
import com.kotak.unified.orchestrator.common.dbmodels.SavingsAccountJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.Status;
import com.kotak.unified.orchestrator.common.dbmodels.UpgradeCustomerJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.UserStatus;
import com.kotak.unified.orchestrator.common.dbmodels.upgrade.CrnUpgradeDetails;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.modelmapper.ModelMapper;
import org.modelmapper.convention.MatchingStrategies;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.kotak.unified.dbservice.utils.Constants.UPGRADE_CUSTOMER_PRODUCT;
import static org.junit.jupiter.api.Assertions.*;

class UpgradeJourneyApplicationDataTransformerTest {
    public static final String TEST_LEAD_ID = "1234";
    public static final String TEST_CRN = "********";

    private final UpgradeJourneyApplicationDataTransformer upgradeJourneyApplicationDataTransformer;
    UpgradeJourneyApplicationDataTransformerTest() {
        ModelMapper modelMapper = new ModelMapper();
        modelMapper.getConfiguration().setMatchingStrategy(MatchingStrategies.STRICT);
        upgradeJourneyApplicationDataTransformer = new UpgradeJourneyApplicationDataTransformer(modelMapper);
    }

    @SneakyThrows
    @Test
    void populateApplicationData_success() {
        UserStatus mockUserStatus = getUserStatus();
        GetApplicationDataRequest request = GetApplicationDataRequest.builder()
                .dataFilters(List.of(ApplicationDataFilter.ALL))
                .build();
        Map<String, DeclarationResponse> declarationResponseMap = new HashMap<>();
        declarationResponseMap.put("dc1",DeclarationResponse.builder().declarationAccepted(true).build());
        UpgradeJourneyApplicationData applicationData = (UpgradeJourneyApplicationData) upgradeJourneyApplicationDataTransformer.populateApplicationData(request, mockUserStatus);
        UpgradeJourneyApplicationData expectedApplicationData = UpgradeJourneyApplicationData.builder()
                .primaryCrnDetails(EtbDetailsDto.builder()
                        .partyType("I")
                        .build())
                .primaryCrnUpgradeDetails(CrnUpgradeDetailsResponse.builder()
                        .targetClassification("tschemeCode")
                        .currentSubClassification("Key")
                        .fullName("John Doe")
                        .build())
                .linkedCrnDetails(List.of(EtbDetailsDto.builder()
                                .partyType("I")
                                .subClassification("Associate")
                                .build(),
                        EtbDetailsDto.builder()
                                .partyType("I")
                                .subClassification("Associate")
                                .build()))
                .linkedCrnUpgradeDetails(List.of(CrnUpgradeDetailsResponse.builder()
                                .fullName("name1")
                                .build(),
                        CrnUpgradeDetailsResponse.builder()
                                .fullName("name2")
                                .build()))
                .primaryCrnBcifUpdateStatus("COMPLETED")
                .declarations(declarationResponseMap)
                .build();
        assertEquals(expectedApplicationData, applicationData);
    }

    @SneakyThrows
    @Test
    void populateApplicationData_success_dropped_customer() {
        UserStatus mockUserStatus = getUserStatus();
        UpgradeCustomerJourneyMetadata droppedMetadata = UpgradeCustomerJourneyMetadata
                .builder()
                .build();
        mockUserStatus.setJourneyMetadata(droppedMetadata);
        GetApplicationDataRequest request = GetApplicationDataRequest.builder()
                .dataFilters(List.of(ApplicationDataFilter.ALL))
                .build();
        UpgradeJourneyApplicationData applicationData = (UpgradeJourneyApplicationData) upgradeJourneyApplicationDataTransformer.populateApplicationData(request, mockUserStatus);
        UpgradeJourneyApplicationData expectedApplicationData = UpgradeJourneyApplicationData.builder()
                .build();
        assertEquals(expectedApplicationData, applicationData);
    }

    @SneakyThrows
    @Test
    void populateApplicationData_EntityNotFoundException() {
        UserStatus mockUserStatus = UserStatus.builder().build();
        GetApplicationDataRequest request = GetApplicationDataRequest.builder()
                .dataFilters(List.of(ApplicationDataFilter.ALL))
                .build();
        assertThrows(EntityNotFoundException.class, () -> upgradeJourneyApplicationDataTransformer.populateApplicationData(request, mockUserStatus));
    }

    @SneakyThrows
    @Test
    void populateApplicationData_InvalidRequestException() {
        UserStatus mockUserStatus = UserStatus.builder()
                .journeyMetadata(SavingsAccountJourneyMetadata.builder().build())
                .build();
        GetApplicationDataRequest request = GetApplicationDataRequest.builder()
                .dataFilters(List.of(ApplicationDataFilter.ALL))
                .build();
        assertThrows(InvalidRequestException.class, () -> upgradeJourneyApplicationDataTransformer.populateApplicationData(request, mockUserStatus));
    }

    @SneakyThrows
    @Test
    void populateApplicationData_InvalidRequestFilter() {
        UserStatus mockUserStatus = UserStatus.builder()
                .journeyMetadata(UpgradeCustomerJourneyMetadata.builder().build())
                .build();
        GetApplicationDataRequest request = GetApplicationDataRequest.builder()
                .dataFilters(List.of(ApplicationDataFilter.LOAN_TYPE))
                .build();
        InvalidRequestException invalidRequestException = assertThrows(InvalidRequestException.class, () -> upgradeJourneyApplicationDataTransformer.populateApplicationData(request, mockUserStatus));
        Assertions.assertEquals("Data filter passed is not applicable : LOAN_TYPE",invalidRequestException.getMessage());
    }

    @Test
    void getProduct() {
        assertEquals(UPGRADE_CUSTOMER_PRODUCT, upgradeJourneyApplicationDataTransformer.getProduct());
    }

    private UserStatus getUserStatus() {
        Map<String, Declaration> declarationMap = new HashMap<>();
        declarationMap.put("dc1",Declaration.builder().declarationAccepted(true).build());
        return UserStatus.builder()
                .leadTrackingNumber(TEST_LEAD_ID)
                .crn(TEST_CRN)
                .journeyMetadata(UpgradeCustomerJourneyMetadata.builder()
                        .primaryCrnUpgradeDetails(CrnUpgradeDetails.builder()
                                .targetClassification("tschemeCode")
                                .currentSubClassification("Key")
                                .fullName("John Doe")
                                .build())
                        .linkedCrnUpgradeDetails(List.of(CrnUpgradeDetails.builder()
                                        .fullName("name1")
                                        .build(),
                                CrnUpgradeDetails.builder()
                                        .fullName("name2")
                                        .build()))
                        .linkedCrnDetails(List.of(EtbDetails.builder()
                                        .partyType("I")
                                        .subClassification("Associate")
                                .build(),
                                EtbDetails.builder()
                                        .partyType("I")
                                        .subClassification("Associate")
                                        .build()))
                        .primaryCrnDetails(EtbDetails.builder()
                                .partyType("I")
                                .build())
                        .primaryCrnBcifUpdateStatus(Status.COMPLETED)
                        .declarations(declarationMap)
                        .build())
                .build();
    }
}