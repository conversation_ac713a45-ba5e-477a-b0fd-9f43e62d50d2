package com.kotak.unified.dbservice.transformer;

import builder.TransactionTestDataBuilder;
import com.kotak.unified.db.response.TransactionResponse;
import com.kotak.unified.orchestrator.common.dbmodels.Transaction;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class TransactionTransformerTest {
    private final TransactionTransformer transactionTransformer;

    TransactionTransformerTest() {
        this.transactionTransformer = new TransactionTransformer();
    }

    @Test
    void convertTransactionToResponse() {
        Transaction dummyTransaction = TransactionTestDataBuilder.createDummyTransaction();

        TransactionResponse transactionResponse = transactionTransformer.convertTransactionToResponse(dummyTransaction);

        Assertions.assertEquals(dummyTransaction.getTxnId(), transactionResponse.getTxnId());
        Assertions.assertEquals(dummyTransaction.getLeadTrackingNumber(), transactionResponse.getLeadTrackingNumber());
        Assertions.assertEquals(dummyTransaction.getStatus(), transactionResponse.getStatus());
        Assertions.assertEquals(dummyTransaction.getFundsTransferStatus().name(), transactionResponse.getFundsTransferStatus().name());
        Assertions.assertEquals(dummyTransaction.getCreatedAt(), transactionResponse.getCreatedAt());
        Assertions.assertEquals(dummyTransaction.getMihPayId(), transactionResponse.getMihPayId());
        Assertions.assertEquals(dummyTransaction.getMode(), transactionResponse.getMode());
        Assertions.assertEquals(dummyTransaction.getUnmappedStatus(), transactionResponse.getUnmappedStatus());
        Assertions.assertEquals(dummyTransaction.getKey(), transactionResponse.getKey());
        Assertions.assertEquals(dummyTransaction.getAmount(), transactionResponse.getAmount());
        Assertions.assertEquals(dummyTransaction.getDiscount(), transactionResponse.getDiscount());
        Assertions.assertEquals(dummyTransaction.getNetAmountDebit(), transactionResponse.getNetAmountDebit());
        Assertions.assertEquals(dummyTransaction.getAddedOn(), transactionResponse.getAddedOn());
        Assertions.assertEquals(dummyTransaction.getProductInfo(), transactionResponse.getProductInfo());
        Assertions.assertEquals(dummyTransaction.getFirstName(), transactionResponse.getFirstName());
        Assertions.assertEquals(dummyTransaction.getLastName(), transactionResponse.getLastName());
        Assertions.assertEquals(dummyTransaction.getAddress1(), transactionResponse.getAddress1());
        Assertions.assertEquals(dummyTransaction.getAddress2(), transactionResponse.getAddress2());
        Assertions.assertEquals(dummyTransaction.getCity(), transactionResponse.getCity());
        Assertions.assertEquals(dummyTransaction.getState(), transactionResponse.getState());
        Assertions.assertEquals(dummyTransaction.getCountry(), transactionResponse.getCountry());
        Assertions.assertEquals(dummyTransaction.getZipcode(), transactionResponse.getZipcode());
        Assertions.assertEquals(dummyTransaction.getEmail(), transactionResponse.getEmail());
        Assertions.assertEquals(dummyTransaction.getPhone(), transactionResponse.getPhone());
        Assertions.assertEquals(dummyTransaction.getRequestHash(), transactionResponse.getRequestHash());
        Assertions.assertEquals(dummyTransaction.getResponseHash(), transactionResponse.getResponseHash());
        Assertions.assertEquals(dummyTransaction.getHashMatching(), transactionResponse.getHashMatching());
        Assertions.assertEquals(dummyTransaction.getField1(), transactionResponse.getField1());
        Assertions.assertEquals(dummyTransaction.getField2(), transactionResponse.getField2());
        Assertions.assertEquals(dummyTransaction.getField3(), transactionResponse.getField3());
        Assertions.assertEquals(dummyTransaction.getField4(), transactionResponse.getField4());
        Assertions.assertEquals(dummyTransaction.getField5(), transactionResponse.getField5());
        Assertions.assertEquals(dummyTransaction.getField6(), transactionResponse.getField6());
        Assertions.assertEquals(dummyTransaction.getField7(), transactionResponse.getField7());
        Assertions.assertEquals(dummyTransaction.getField8(), transactionResponse.getField8());
        Assertions.assertEquals(dummyTransaction.getField9(), transactionResponse.getField9());
        Assertions.assertEquals(dummyTransaction.getPaymentSource(), transactionResponse.getPaymentSource());
        Assertions.assertEquals(dummyTransaction.getMeCode(), transactionResponse.getMeCode());
        Assertions.assertEquals(dummyTransaction.getPgType(), transactionResponse.getPgType());
        Assertions.assertEquals(dummyTransaction.getBankRefNum(), transactionResponse.getBankRefNum());
        Assertions.assertEquals(dummyTransaction.getBankCode(), transactionResponse.getBankCode());
        Assertions.assertEquals(dummyTransaction.getError(), transactionResponse.getError());
        Assertions.assertEquals(dummyTransaction.getErrorMessage(), transactionResponse.getErrorMessage());
        Assertions.assertEquals(dummyTransaction.getFinacleProcessStatus(), transactionResponse.getFinacleProcessStatus());
        Assertions.assertEquals(dummyTransaction.getFinacleTransactionDate(), transactionResponse.getFinacleTransactionDate());
        Assertions.assertEquals(dummyTransaction.getFinacleProcessRemarks(), transactionResponse.getFinacleProcessRemarks());
        Assertions.assertEquals(dummyTransaction.getFinacleTransactionId(), transactionResponse.getFinacleTransactionId());
    }
}