package com.kotak.unified.dbservice.transformer.impl;

import com.kotak.unified.common.exception.InvalidRequestException;
import com.kotak.unified.db.ApplicationData;
import com.kotak.unified.db.ApplicationDataFilter;
import com.kotak.unified.db.GetApplicationDataRequest;
import com.kotak.unified.db.PqHlDiyJourneyApplicationData;
import com.kotak.unified.dbservice.exceptions.EntityNotFoundException;
import com.kotak.unified.dbservice.utils.Constants;
import com.kotak.unified.orchestrator.common.dbmodels.CreditCardJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.Declaration;
import com.kotak.unified.orchestrator.common.dbmodels.JourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.PqHlDiyJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.hl.PqHlDiyJourneyMilestone;
import com.kotak.unified.orchestrator.common.dbmodels.hl.PqHlDiyJourneyType;
import com.kotak.unified.orchestrator.common.dbmodels.hl.SanctionLetterDetails;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.modelmapper.ModelMapper;

import java.time.Instant;
import java.util.List;

@ExtendWith(MockitoExtension.class)
class PqHlDiyJourneyApplicationDataTransformerV2Test {

    private final ModelMapper modelMapper = new ModelMapper();
    private final PqHlDiyJourneyApplicationDataTransformerV2 transformer =
            new PqHlDiyJourneyApplicationDataTransformerV2(modelMapper);

    @Test
    void populateApplicationData_ValidRequest_PqHlHlDiy_ReturnsPqHlDiyJourneyApplicationData()
            throws EntityNotFoundException, InvalidRequestException {
        // Arrange
        GetApplicationDataRequest getApplicationDataRequest = GetApplicationDataRequest.builder()
                .dataFilters(List.of(ApplicationDataFilter.ALL))
                .build();
        com.kotak.unified.orchestrator.common.dbmodels.ApplicationData applicationDataDDBModel =
                getApplicationDataDDBModel(
                        Constants.HomeloanConstants.PQ_HL_DIY_GRAPH_NAME, getPqHlDiyJourneyMetadata(PqHlDiyJourneyType.PQ_HL_DIY));

        // Act
        ApplicationData applicationData = transformer.populateApplicationData(getApplicationDataRequest,
                applicationDataDDBModel);

        // Assert
        Assertions.assertNotNull(applicationData);
        Assertions.assertInstanceOf(PqHlDiyJourneyApplicationData.class, applicationData);
        PqHlDiyJourneyApplicationData data = (PqHlDiyJourneyApplicationData) applicationData;
        Assertions.assertEquals(((PqHlDiyJourneyApplicationData) applicationData).getPqHlDiyJourneyMilestone(),
                data.getPqHlDiyJourneyMilestone());
        Assertions.assertEquals(((PqHlDiyJourneyApplicationData) applicationData).getPqHlDiyMilestoneHistory(),
                data.getPqHlDiyMilestoneHistory());
        Assertions.assertEquals(((PqHlDiyJourneyApplicationData) applicationData).getUserDetails(),
                data.getUserDetails());
        Assertions.assertEquals(((PqHlDiyJourneyApplicationData) applicationData).getHomeLoanOfferDetails(),
                data.getHomeLoanOfferDetails());
        Assertions.assertEquals(((PqHlDiyJourneyApplicationData) applicationData).getSanctionLetterDetails(),
                data.getSanctionLetterDetails());
        Assertions.assertEquals(((PqHlDiyJourneyApplicationData) applicationData).getTermsAndConditionsDeclaration(),
                data.getTermsAndConditionsDeclaration());
    }

    @Test
    void populateApplicationData_NullJourneyMetadata_ThrowsEntityNotFoundException() {
        // Arrange
        GetApplicationDataRequest getApplicationDataRequest = new GetApplicationDataRequest();
        com.kotak.unified.orchestrator.common.dbmodels.ApplicationData applicationDataDDBModel =
                getApplicationDataDDBModel(Constants.HomeloanConstants.PQ_HL_DIY_GRAPH_NAME, null);

        // Act & Assert
        Assertions.assertThrows(InvalidRequestException.class,
                () -> transformer.populateApplicationData(getApplicationDataRequest, applicationDataDDBModel));
    }

    @Test
    void populateApplicationData_InvalidJourneyMetadata_ThrowsInvalidRequestException() {
        // Arrange
        GetApplicationDataRequest getApplicationDataRequest = GetApplicationDataRequest.builder()
                .dataFilters(List.of())
                .build();
        com.kotak.unified.orchestrator.common.dbmodels.ApplicationData applicationDataDDBModel =
                getApplicationDataDDBModel(Constants.CC, new CreditCardJourneyMetadata());

        // Act & Assert
        Assertions.assertThrows(InvalidRequestException.class,
                () -> transformer.populateApplicationData(getApplicationDataRequest, applicationDataDDBModel));
    }

    @Test
    void populateApplicationData_InvalidDataFilter_ThrowsInvalidRequestException() {
        // Arrange
        GetApplicationDataRequest request = GetApplicationDataRequest.builder()
                .dataFilters(List.of(ApplicationDataFilter.ADDRESS))
                .build();
        com.kotak.unified.orchestrator.common.dbmodels.ApplicationData applicationDataDDBModel =
                getApplicationDataDDBModel(Constants.HomeloanConstants.PQ_HL_DIY_GRAPH_NAME,
                        getPqHlDiyJourneyMetadata(PqHlDiyJourneyType.PQ_HL_DIY));

        // Act & Assert
        Assertions.assertThrows(InvalidRequestException.class,
                () -> transformer.populateApplicationData(request, applicationDataDDBModel));
    }

    @Test
    void getProduct_ReturnsCorrectProduct() {
        // Act
        String product = transformer.getProduct();

        // Assert
        Assertions.assertEquals("PQ_DIY", product);
    }

    private com.kotak.unified.orchestrator.common.dbmodels.ApplicationData getApplicationDataDDBModel(
            String applicationType, JourneyMetadata journeyMetadata) {
        com.kotak.unified.orchestrator.common.dbmodels.ApplicationData applicationDataDDBModel =
                new com.kotak.unified.orchestrator.common.dbmodels.ApplicationData();
        applicationDataDDBModel.setCrn("CRN123456");
        applicationDataDDBModel.setApplicationType(applicationType);
        applicationDataDDBModel.setJourneyMetadata(journeyMetadata);
        return applicationDataDDBModel;
    }

    private PqHlDiyJourneyMetadata getPqHlDiyJourneyMetadata(PqHlDiyJourneyType pqHlDiyJourneyType) {
        PqHlDiyJourneyMetadata pqHlDiyJourneyMetadata = new PqHlDiyJourneyMetadata();
        pqHlDiyJourneyMetadata.setPqHlDiyJourneyMilestone(
                com.kotak.unified.orchestrator.common.dbmodels.hl.PqHlDiyJourneyMilestone.SANCTION_LETTER_GENERATED);
        pqHlDiyJourneyMetadata.setPqHlDiyMilestoneHistory(
                List.of(
                        com.kotak.unified.orchestrator.common.dbmodels.MilestoneHistory.<PqHlDiyJourneyMilestone>builder()
                                .milestone(com.kotak.unified.orchestrator.common.dbmodels.hl.PqHlDiyJourneyMilestone.SANCTION_LETTER_GENERATED.name())
                                .timestamp(Instant.now())
                                .build()
                )
        );
        pqHlDiyJourneyMetadata.setPqHlDiyJourneyType(pqHlDiyJourneyType);
        pqHlDiyJourneyMetadata.setUserDetails(com.kotak.unified.orchestrator.common.dbmodels.hl.UserDetails.builder().build());
        pqHlDiyJourneyMetadata.setHomeLoanOfferDetails(
                com.kotak.unified.orchestrator.common.dbmodels.hl.HomeLoanOfferDetails.builder().build());
        pqHlDiyJourneyMetadata.setEmploymentCheckDetails(
                com.kotak.unified.orchestrator.common.dbmodels.hl.EmploymentCheckDetails.builder().build());
        pqHlDiyJourneyMetadata.setSanctionLetterDetails(SanctionLetterDetails.builder().build());
        pqHlDiyJourneyMetadata.setBankingDataDeclaration(Declaration.builder().build());
        pqHlDiyJourneyMetadata.setNdncDeclaration(Declaration.builder().build());
        pqHlDiyJourneyMetadata.setTermsAndConditionsDeclaration(Declaration.builder().build());

        return pqHlDiyJourneyMetadata;
    }
}