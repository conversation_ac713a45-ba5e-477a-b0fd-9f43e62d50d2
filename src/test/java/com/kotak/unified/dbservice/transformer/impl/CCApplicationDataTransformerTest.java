package com.kotak.unified.dbservice.transformer.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kotak.unified.db.ApplicationDataFilter;
import com.kotak.unified.db.CCApplicationData;
import com.kotak.unified.db.CCProductSpecificationsResponse;
import com.kotak.unified.db.GetApplicationDataRequest;
import com.kotak.unified.orchestrator.common.dbmodels.AadhaarDetails;
import com.kotak.unified.orchestrator.common.dbmodels.Address;
import com.kotak.unified.orchestrator.common.dbmodels.CCApplicationMileStone;
import com.kotak.unified.orchestrator.common.dbmodels.CCProductSpecifications;
import com.kotak.unified.orchestrator.common.dbmodels.CreditCardJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.DerivedAadhaarDetails;
import com.kotak.unified.orchestrator.common.dbmodels.JourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.PanDetails;
import com.kotak.unified.orchestrator.common.dbmodels.PersonalLoanJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.UserStatus;
import com.kotak.unified.orchestrator.common.dbmodels.assets.ApplicantDetails;
import com.kotak.unified.orchestrator.common.dbmodels.assets.CCClipOfferDetails;
import com.kotak.unified.orchestrator.common.dbmodels.assets.CCUpgradeOfferDetails;
import com.kotak.unified.orchestrator.common.dbmodels.assets.CrnDetails;
import com.kotak.unified.orchestrator.common.dbmodels.assets.CrnNameMap;
import com.kotak.unified.orchestrator.common.dbmodels.assets.IncomeDetails;
import com.kotak.unified.orchestrator.common.dbmodels.cc.Card;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.modelmapper.ModelMapper;
import org.springframework.data.util.Pair;

import java.util.Date;
import java.util.List;

@ExtendWith(MockitoExtension.class)
public class CCApplicationDataTransformerTest {

    @Spy
    private ModelMapper modelMapper = new ModelMapper();

    @InjectMocks
    private CCApplicationDataTransformer transformer;

    private static final String CC = "CC";

    @Test
    void testGetProduct() {
        Assertions.assertEquals(CC,
                transformer.getProduct());
    }

    @Test
    void testCCApplicationDataTransformerConstructorWithNullModelMapper() {
        Assertions.assertThrows(NullPointerException.class, () -> new CCApplicationDataTransformer(null));
    }

    @Test
    void testPopulateApplicationDataWithNullAppData() {
        UserStatus userStatus = UserStatus.builder().build();
        Assertions.assertThrows(NullPointerException.class, () -> transformer.populateApplicationData(null, userStatus));
    }

    @Test
    void testPopulateApplicationDataWithNullUserStatus() {
        GetApplicationDataRequest request = GetApplicationDataRequest.builder()
                .dataFilters(List.of(ApplicationDataFilter.ALL))
                .build();
        Assertions.assertThrows(NullPointerException.class, () -> transformer.populateApplicationData(request, null));
    }

    @Test
    void testPopulateApplicationDataWhenJourneyMetadataIsNotOfCc() {
        JourneyMetadata ccJourneyMetadata = PersonalLoanJourneyMetadata.builder().build();
        UserStatus userStatus = UserStatus.builder()
                .journeyMetadata(ccJourneyMetadata)
                .build();

        Assertions.assertThrows(RuntimeException.class, () -> transformer.populateApplicationData(GetApplicationDataRequest.builder()
                .dataFilters(List.of(ApplicationDataFilter.ALL)).build(), userStatus));
    }

    @Test
    void testPopulateApplicationDataWhenJourneyMetadataIsNull() {
        UserStatus userStatus = UserStatus.builder().build();
        Assertions.assertThrows(RuntimeException.class, () -> transformer.populateApplicationData(GetApplicationDataRequest.builder()
                .dataFilters(List.of(ApplicationDataFilter.ALL)).build(), userStatus));
    }

    @Test
    void testPopulateApplicationDataWithALLFilter() throws JsonProcessingException {
        ObjectMapper objectMapper = new ObjectMapper();
        CreditCardJourneyMetadata ccJourneyMetadata = CreditCardJourneyMetadata.builder()
            .productSpecifications(CCProductSpecifications.builder().build())
                .applicantDetails(ApplicantDetails.builder()
                        .panDetails(PanDetails.builder().aadhaarSeed("Y").number("ABCDEF").build())
                        .incomeDetails(IncomeDetails.builder().companyName("Company Name Inc.").modeOfSalary("Salaried").build())
                        .address(Address.builder().city("Resi city").build())
                        .aadhaarDetails(AadhaarDetails.builder().derivedAadhaarDetails(DerivedAadhaarDetails.builder().address(Address.builder().city("Perma city").build()).build()).build())
                        .build())
                .build();
        UserStatus userStatus = UserStatus.builder()
                .journeyMetadata(ccJourneyMetadata)
                .build();

        CCApplicationData applicationData = (CCApplicationData)transformer.populateApplicationData(GetApplicationDataRequest.builder()
                .dataFilters(List.of(ApplicationDataFilter.ALL)).build(), userStatus);

        CCApplicationData mapped = modelMapper.map(ccJourneyMetadata, CCApplicationData.class);
        mapped.setProductSpecifications(CCProductSpecificationsResponse.builder().build());
        Assertions.assertEquals(objectMapper.writeValueAsString(mapped), objectMapper.writeValueAsString(applicationData));
    }

    @Test
    void testForMileStoneHistory() throws JsonProcessingException {
        ObjectMapper objectMapper = new ObjectMapper();
        CreditCardJourneyMetadata ccJourneyMetadata = CreditCardJourneyMetadata.builder()
                .productSpecifications(CCProductSpecifications.builder().build())
                .applicantDetails(ApplicantDetails.builder()
                        .panDetails(PanDetails.builder().aadhaarSeed("Y").number("ABCDEF").build())
                        .incomeDetails(IncomeDetails.builder().companyName("Company Name Inc.").modeOfSalary("Salaried").build())
                        .address(Address.builder().city("Resi city").build())
                        .aadhaarDetails(AadhaarDetails.builder().derivedAadhaarDetails(DerivedAadhaarDetails.builder().address(Address.builder().city("Perma city").build()).build()).build())
                        .build())
                .ccApplicationMileStoneHistory(List.of(Pair.of(CCApplicationMileStone.APPLICATION_CREATED, new Date().toInstant())))
                .build();
        UserStatus userStatus = UserStatus.builder()
                .journeyMetadata(ccJourneyMetadata)
                .build();

        CCApplicationData applicationData = (CCApplicationData)transformer.populateApplicationData(GetApplicationDataRequest.builder()
                .dataFilters(List.of(ApplicationDataFilter.ALL)).build(), userStatus);
        Assertions.assertEquals(1, applicationData.getCcApplicationMileStoneHistoryList().size());
        Assertions.assertEquals(CCApplicationMileStone.APPLICATION_CREATED.toString(), applicationData.getCcApplicationMileStoneHistoryList().get(0).getMilestone());
    }

    @Test
    void testPopulateApplicationDataWithUnknownFilterProvided() throws JsonProcessingException {
        ObjectMapper objectMapper = new ObjectMapper();
        CreditCardJourneyMetadata ccJourneyMetadata = CreditCardJourneyMetadata.builder().build();

        UserStatus userStatus = UserStatus.builder()
                .journeyMetadata(ccJourneyMetadata)
                .build();

        CCApplicationData applicationData = (CCApplicationData)transformer.populateApplicationData(GetApplicationDataRequest.builder()
                .dataFilters(List.of(ApplicationDataFilter.GST_DETAILS)).build(), userStatus);

        CCApplicationData mapped = modelMapper.map(ccJourneyMetadata, CCApplicationData.class);
        Assertions.assertEquals(objectMapper.writeValueAsString(mapped), objectMapper.writeValueAsString(applicationData));
    }

    @Test
    void testForPopulateCrnDetails() throws JsonProcessingException {
        ObjectMapper objectMapper = new ObjectMapper();

        CreditCardJourneyMetadata ccJourneyMetadata = CreditCardJourneyMetadata.builder()
                .productSpecifications(CCProductSpecifications.builder().build())
                .applicantDetails(ApplicantDetails.builder()
                        .crnDetails(CrnDetails.builder().selectedCrn(CrnNameMap.builder()
                                .crn("12345")
                                .title("Mr.")
                                .mothersMaidenName("TEST1")
                                .sourceOfFund("test")
                                .incomeSlab("test")
                                .occupation("Salaried")
                                .maritalStatus("married")
                                .build()).build())
                        .panDetails(PanDetails.builder().aadhaarSeed("Y").number("ABCDEF").build())
                        .incomeDetails(IncomeDetails.builder().companyName("Company Name Inc.").modeOfSalary("Salaried").build())
                        .address(Address.builder().city("Resi city").build())
                        .aadhaarDetails(AadhaarDetails.builder().derivedAadhaarDetails(DerivedAadhaarDetails.builder().address(Address.builder().city("Perma city").build()).build()).build())
                        .build())
                .build();
        UserStatus userStatus = UserStatus.builder()
                .journeyMetadata(ccJourneyMetadata)
                .build();

        CCApplicationData applicationData = (CCApplicationData)transformer.populateApplicationData(GetApplicationDataRequest.builder()
                .dataFilters(List.of(ApplicationDataFilter.ALL)).build(), userStatus);

        CCApplicationData mapped = modelMapper.map(ccJourneyMetadata, CCApplicationData.class);
        mapped.setProductSpecifications(CCProductSpecificationsResponse.builder().build());
        Assertions.assertEquals(objectMapper.writeValueAsString(mapped), objectMapper.writeValueAsString(applicationData));
    }

    @Test
    public void testForSettingCardDetails() throws JsonProcessingException {
        ObjectMapper objectMapper = new ObjectMapper();
        Card card = Card.builder().cardNumber("12345678").creditLimit(123456).build();
        CreditCardJourneyMetadata ccJourneyMetadata = CreditCardJourneyMetadata.builder()
                .selectedCard(card)
                .activeCards(List.of(card))
                .terminalNodesReasonCode("reason")
                .build();

        UserStatus userStatus = UserStatus.builder()
                .journeyMetadata(ccJourneyMetadata)
                .build();

        CCApplicationData applicationData = (CCApplicationData)transformer.populateApplicationData(GetApplicationDataRequest.builder()
                .dataFilters(List.of(ApplicationDataFilter.ALL)).build(), userStatus);

        CCApplicationData mapped = modelMapper.map(ccJourneyMetadata, CCApplicationData.class);
        Assertions.assertEquals(objectMapper.writeValueAsString(mapped), objectMapper.writeValueAsString(applicationData));
        Assertions.assertEquals(123456, applicationData.getSelectedCard().getCreditLimit());
        Assertions.assertEquals("12345678", applicationData.getSelectedCard().getCardNumber());
        Assertions.assertEquals("reason", applicationData.getTerminalNodesReasonCode());

    }

    @Test
    public void testForSettingClipAndUpgradeDetails() throws JsonProcessingException {
        ObjectMapper objectMapper = new ObjectMapper();
        CCUpgradeOfferDetails ccUpgradeOfferDetails = CCUpgradeOfferDetails.builder()
                .isOfferUsed(true)
                .build();
        CCClipOfferDetails ccClipOfferDetails = CCClipOfferDetails.builder()
                .isOfferUsed(true)
                .offeredAmount(1200000)
                .build();
        CreditCardJourneyMetadata ccJourneyMetadata = CreditCardJourneyMetadata.builder()
                .terminalNodesReasonCode("reason")
                .clipOfferDetails(ccClipOfferDetails)
                .upgradeOfferDetails(ccUpgradeOfferDetails)
                .build();

        UserStatus userStatus = UserStatus.builder()
                .journeyMetadata(ccJourneyMetadata)
                .build();

        CCApplicationData applicationData = (CCApplicationData)transformer.populateApplicationData(GetApplicationDataRequest.builder()
                .dataFilters(List.of(ApplicationDataFilter.ALL)).build(), userStatus);

        CCApplicationData mapped = modelMapper.map(ccJourneyMetadata, CCApplicationData.class);
        Assertions.assertEquals(objectMapper.writeValueAsString(mapped), objectMapper.writeValueAsString(applicationData));
        Assertions.assertEquals(true, mapped.getUpgradeOfferDetails().getIsOfferUsed());
        Assertions.assertEquals(true, mapped.getClipOfferDetails().getIsOfferUsed());
        Assertions.assertEquals(1200000, mapped.getClipOfferDetails().getOfferedAmount());
    }

}
