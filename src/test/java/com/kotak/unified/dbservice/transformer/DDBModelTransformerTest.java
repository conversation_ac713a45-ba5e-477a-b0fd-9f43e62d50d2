package com.kotak.unified.dbservice.transformer;

import com.kotak.unified.dbservice.TestUtils;
import com.kotak.unified.orchestrator.common.dbmodels.Address;
import com.kotak.unified.orchestrator.common.dbmodels.ApplicationDataDDBModel;
import com.kotak.unified.orchestrator.common.dbmodels.ExecutionData;
import com.kotak.unified.orchestrator.common.dbmodels.JourneyStatus;
import com.kotak.unified.orchestrator.common.dbmodels.SavingsAccountJourneyMetadata;
import com.kotak.unified.orchestrator.common.dbmodels.Status;
import com.kotak.unified.orchestrator.common.dbmodels.UserJourneyStatusDDBModel;
import com.kotak.unified.orchestrator.common.dbmodels.UserStatus;
import com.kotak.unified.orchestrator.common.dbmodels.UserStepStatus;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.kotak.unified.dbservice.TestUtils.SAVINGS_ACCOUNT_GRAPH_NAME;
import static com.kotak.unified.dbservice.TestUtils.TEST_LAST_UPDATED_TIME;

@ExtendWith(MockitoExtension.class)
class DDBModelTransformerTest {

    private DDBModelTransformer tranformer;
    @BeforeEach
    void setUp() {
        tranformer = new DDBModelTransformer();
    }

    @Test
    void getUserStatusFromUserJourneyStatusAndApplicationData_happyCase() {
        UserJourneyStatusDDBModel mockUserJourneyStatus = getMockUserJourneyStatusDDBModel();
        ApplicationDataDDBModel mockApplicationData = getMockApplicationDataDDBModel();
        UserStatus expectedUserStatus = getMockUserStatus();
        Assertions.assertEquals(expectedUserStatus, tranformer.getUserStatusFromUserJourneyStatusAndApplicationData(mockUserJourneyStatus, mockApplicationData));
    }

    @Test
    void getUserStatusFromUserJourneyStatusAndApplicationData_null_userJourneyStatus() {
        Assertions.assertThrows(NullPointerException.class,
                () -> tranformer.getUserStatusFromUserJourneyStatusAndApplicationData(null,
                        ApplicationDataDDBModel.builder().build()));
    }

    @Test
    void getUserJourneyStatusDDBModelFromUserStatus_happyCase() {
        UserJourneyStatusDDBModel expectedUserJourneyStatus = getMockUserJourneyStatusDDBModel();
        UserStatus mockUserStatus = getMockUserStatus();
        Assertions.assertEquals(expectedUserJourneyStatus.toString(), tranformer.getUserJourneyStatusDDBModelFromUserStatus(mockUserStatus).toString());
    }

    @Test
    void getUserJourneyStatusDDBModelFromUserStatus_null_userStatus() {
        Assertions.assertThrows(NullPointerException.class, () -> tranformer.getUserJourneyStatusDDBModelFromUserStatus(null));
    }

    @Test
    void getApplicationDataDDBModelFromUserStatus_happyCase() {
        ApplicationDataDDBModel expectedApplicationData = getMockApplicationDataDDBModel();
        UserStatus mockUserStatus = getMockUserStatus();
        Assertions.assertEquals(expectedApplicationData.toString(), tranformer.getApplicationDataDDBModelFromUserStatus(mockUserStatus).toString());
    }

    @Test
    void getApplicationDataDDBModelFromUserStatus_null_userStatus() {
        Assertions.assertThrows(NullPointerException.class, () -> tranformer.getApplicationDataDDBModelFromUserStatus(null));
    }

    @Test
    void updateUserStatus_happyCase() {
        UserJourneyStatusDDBModel mockUserJourneyStatus = getMockUserJourneyStatusDDBModel();
        mockUserJourneyStatus.setVersion(3);
        ApplicationDataDDBModel mockApplicationData = getMockApplicationDataDDBModel();
        mockApplicationData.setVersion(2);
        UserStatus mockUserStatus = getMockUserStatus();
        tranformer.updateUserStatus(mockUserStatus, mockUserJourneyStatus, mockApplicationData);
        Assertions.assertEquals(3, mockUserStatus.getUserJourneyStatusVersion());
        Assertions.assertEquals(2, mockUserStatus.getApplicationDataVersion());
    }

    @Test
    void updateUserStatus_null_userStatus() {
        Assertions.assertThrows(NullPointerException.class,
                () -> tranformer.updateUserStatus(null, UserJourneyStatusDDBModel.builder().leadTrackingNumber(TestUtils.LEAD_ID).build(), ApplicationDataDDBModel.builder().build()));
    }

    @Test
    void updateUserStatus_null_userJourneyStatus() {
        Assertions.assertThrows(NullPointerException.class,
                () -> tranformer.updateUserStatus(UserStatus.builder().build(), null, ApplicationDataDDBModel.builder().build()));
    }

    @Test
    void updateUserStatus_null_applicationData() {
        Assertions.assertThrows(NullPointerException.class,
                () -> tranformer.updateUserStatus(UserStatus.builder().build(), UserJourneyStatusDDBModel.builder().leadTrackingNumber(TestUtils.LEAD_ID).build(), null));
    }

    private UserJourneyStatusDDBModel getMockUserJourneyStatusDDBModel() {

        UserStepStatus userStepStatus = UserStepStatus.builder()
                .version("1.0")
                .status(Status.INITIATED)
                .isAsyncUIStep(false)
                .lastUpdatedTime(TEST_LAST_UPDATED_TIME)
                .stepsStatus(List.of())
                .build();
        Map<String, UserStepStatus> graphStatus = new HashMap<>();
        graphStatus.put(SAVINGS_ACCOUNT_GRAPH_NAME, userStepStatus);

        return UserJourneyStatusDDBModel.builder()
                .leadTrackingNumber(TestUtils.LEAD_ID)
                .bankLeadTrackingNumber("test")
                .phoneNumber(TestUtils.PHONE_NUMBER)
                .crn(TestUtils.TEST_CRN)
                .journeyType(SAVINGS_ACCOUNT_GRAPH_NAME)
                .productType(SAVINGS_ACCOUNT_GRAPH_NAME)
                .customerType("ETB")
                .createdAt(TEST_LAST_UPDATED_TIME)
                .status(graphStatus)
                .journeyStatus(JourneyStatus.ACCOUNT_OPENED)
                .lastModifiedAt(TEST_LAST_UPDATED_TIME)
                .userIp(TestUtils.USER_IP)
                .productId(TestUtils.PRODUCT_ID)
                .journeyStatusReasonCode("NewLeadCreated")
                .version(1)
                .build();
    }

    private ApplicationDataDDBModel getMockApplicationDataDDBModel() {
        return ApplicationDataDDBModel.builder()
                .applicationTrackingId(TestUtils.LEAD_ID)
                .journeyMetadata(SavingsAccountJourneyMetadata.builder()
                        .communicationAddress(Address.builder()
                                .state("Delhi")
                                .city("New Delhi")
                                .pincode("112233")
                                .build())
                        .build())
                .executionData(ExecutionData.builder()
                        .ncifLastVerifiedTime(TestUtils.TEST_LAST_UPDATED_TIME)
                        .isMobileOtpAlreadySent(true)
                        .otpResendAttemptsLeft(2)
                        .build())
                .crn(TestUtils.TEST_CRN)
                .userIp(TestUtils.USER_IP)
                .applicationType(SAVINGS_ACCOUNT_GRAPH_NAME)
                .bankLeadTrackingNumber("test")
                .productId(TestUtils.PRODUCT_ID)
                .version(1)
                .createdAt(TEST_LAST_UPDATED_TIME)
                .partnerId("PartId")
                .build();
    }

    private UserStatus getMockUserStatus() {
        UserStepStatus userStepStatus = UserStepStatus.builder()
                .version("1.0")
                .status(Status.INITIATED)
                .isAsyncUIStep(false)
                .lastUpdatedTime(TEST_LAST_UPDATED_TIME)
                .stepsStatus(List.of())
                .build();
        Map<String, UserStepStatus> graphStatus = new HashMap<>();
        graphStatus.put(SAVINGS_ACCOUNT_GRAPH_NAME, userStepStatus);

        return UserStatus.builder()
                .leadTrackingNumber(TestUtils.LEAD_ID)
                .bankLeadTrackingNumber("test")
                .phoneNumber(TestUtils.PHONE_NUMBER)
                .crn(TestUtils.TEST_CRN)
                .journeyType(SAVINGS_ACCOUNT_GRAPH_NAME)
                .productType(SAVINGS_ACCOUNT_GRAPH_NAME)
                .customerType("ETB")
                .createdTime(TEST_LAST_UPDATED_TIME)
                .status(graphStatus)
                .journeyStatus(JourneyStatus.ACCOUNT_OPENED)
                .lastModifiedTime(TEST_LAST_UPDATED_TIME)
                .userIp(TestUtils.USER_IP)
                .productId(TestUtils.PRODUCT_ID)
                .journeyStatusReasonCode("NewLeadCreated")
                .userJourneyStatusVersion(1)
                .journeyMetadata(SavingsAccountJourneyMetadata.builder()
                        .communicationAddress(Address.builder()
                                .state("Delhi")
                                .city("New Delhi")
                                .pincode("112233")
                                .build())
                        .build())
                .executionData(ExecutionData.builder()
                        .ncifLastVerifiedTime(TestUtils.TEST_LAST_UPDATED_TIME)
                        .isMobileOtpAlreadySent(true)
                        .otpResendAttemptsLeft(2)
                        .build())
                .applicationDataVersion(1)
                .partnerId("PartId")
                .build();
    }
}