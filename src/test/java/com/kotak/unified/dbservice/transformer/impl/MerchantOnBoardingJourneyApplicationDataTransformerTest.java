package com.kotak.unified.dbservice.transformer.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kotak.unified.db.ApplicationDataFilter;
import com.kotak.unified.db.GetApplicationDataRequest;
import com.kotak.unified.db.MerchantOnBoardingJourneyApplicationData;
import com.kotak.unified.dbservice.exceptions.EntityNotFoundException;
import com.kotak.unified.orchestrator.common.dbmodels.merchantonboarding.MerchantOnboardingJourneyMetadata;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import org.modelmapper.ModelMapper;
import org.modelmapper.convention.MatchingStrategies;

import java.util.List;

import static com.kotak.unified.dbservice.utils.Constants.MERCHANT_ONBOARDING;
import static org.junit.jupiter.api.Assertions.assertEquals;

class MerchantOnBoardingJourneyApplicationDataTransformerTest {

    public static final String TEST_LEAD_ID = "1234";
    public static final String TEST_CRN = "12345678";

    private final MerchantOnBoardingJourneyApplicationDataTransformer merchantOnBoardingJourneyApplicationDataTransformer;

    MerchantOnBoardingJourneyApplicationDataTransformerTest() {
        ModelMapper modelMapper = new ModelMapper();
        modelMapper.getConfiguration().setMatchingStrategy(MatchingStrategies.STRICT);
        merchantOnBoardingJourneyApplicationDataTransformer = new MerchantOnBoardingJourneyApplicationDataTransformer(modelMapper);
    }

    @SneakyThrows
    @Test
    void populateApplicationData_success() {
        com.kotak.unified.orchestrator.common.dbmodels.ApplicationData applicationDataDDBModel = getApplicationDataDDBModel();
        GetApplicationDataRequest request = GetApplicationDataRequest.builder()
                .dataFilters(List.of(ApplicationDataFilter.ALL))
                .build();
        MerchantOnBoardingJourneyApplicationData applicationData = (MerchantOnBoardingJourneyApplicationData)
                merchantOnBoardingJourneyApplicationDataTransformer.populateApplicationData(request, applicationDataDDBModel);
        MerchantOnboardingJourneyMetadata journeyMetadata = (MerchantOnboardingJourneyMetadata) applicationDataDDBModel.getJourneyMetadata();

        assertEquals(journeyMetadata.getSelectedCurrentAccountNumber(), applicationData.getSelectedCurrentAccountNumber());
        assertEquals(journeyMetadata.getQrDisplayName(), applicationData.getQrDisplayName());
        assertEquals(journeyMetadata.isUpiOnRupayCreditCardEnabled(), applicationData.isUpiOnRupayCreditCardEnabled());
        assertEquals(journeyMetadata.getCrnDetails().getFullName(), applicationData.getCrnDetails().getFullName());
        assertEquals(journeyMetadata.getCorpCrnDetails().getCommunicationAddress().getLine1(), applicationData.getCorpCrnDetails().getCommunicationAddress().getLine1());
        assertEquals(journeyMetadata.getCorpCrnDetails().getCommunicationAddress().getLine2(), applicationData.getCorpCrnDetails().getCommunicationAddress().getLine2());
        assertEquals(journeyMetadata.getCorpCrnDetails().getCommunicationAddress().getCity(), applicationData.getCorpCrnDetails().getCommunicationAddress().getCity());
        assertEquals(journeyMetadata.getCorpCrnDetails().getCommunicationAddress().getPincode(), applicationData.getCorpCrnDetails().getCommunicationAddress().getPincode());
        assertEquals(journeyMetadata.getCrnDetails().getPreferredMobile(), applicationData.getCrnDetails().getPreferredMobile());
        assertEquals(journeyMetadata.getCorpCrnDetails().getEtbAccountDetailList().get(0).getBranchCode(), applicationData.getCorpCrnDetails().getEtbAccountDetailList().get(0).getBranchCode());
    }

    @SneakyThrows
    @Test
    void failed_whenjourneymetadataisnotpresn() {
        com.kotak.unified.orchestrator.common.dbmodels.ApplicationData applicationDataDDBModel = getApplicationDataDDBModel();
        applicationDataDDBModel.setJourneyMetadata(null);
        GetApplicationDataRequest request = GetApplicationDataRequest.builder()
                .dataFilters(List.of(ApplicationDataFilter.ALL))
                .build();
        try {
            merchantOnBoardingJourneyApplicationDataTransformer.populateApplicationData(request, applicationDataDDBModel);
        } catch (EntityNotFoundException e) {
            assertEquals("No application data found for lead id : 1234", e.getMessage());
        }
    }

    @Test
    void getProduct() {
        assertEquals(MERCHANT_ONBOARDING, merchantOnBoardingJourneyApplicationDataTransformer.getProduct());
    }

    private com.kotak.unified.orchestrator.common.dbmodels.ApplicationData getApplicationDataDDBModel() {
        com.kotak.unified.orchestrator.common.dbmodels.ApplicationData applicationDataDDBModel =
                new com.kotak.unified.orchestrator.common.dbmodels.ApplicationData();
        applicationDataDDBModel.setCrn("CRN123456");
        applicationDataDDBModel.setApplicationType(MERCHANT_ONBOARDING);
        applicationDataDDBModel.setJourneyMetadata(fetchJourneyMetaData());
        applicationDataDDBModel.setApplicationTrackingId(TEST_LEAD_ID);
        return applicationDataDDBModel;
    }

    private MerchantOnboardingJourneyMetadata fetchJourneyMetaData() {
        String data = "{\"type\":\"merchantOnboardingJourneyMetadata\",\"crnDetails\":{\"nationality\":{\"code\":\"IN\",\"value\":\"India\"},\"primarySegment\":\"RL\",\"fcPkgPresent\":true,\"crnStatusCode\":\"\",\"crnStatusReason\":\"\",\"customerCategory\":\"D\",\"apacDetails\":[{\"pkg\":\"FC\",\"apac\":\"**********\",\"relationType\":\"Trustee\",\"productType\":\"CAA-CINST\",\"effectiveDate\":\"02-MAY-2025\",\"apacStatus\":\"Total Freeze - Active\",\"apacOpenDate\":\"02-MAY-2025\"},{\"pkg\":\"FC\",\"apac\":\"**********\",\"relationType\":\"Trustee\",\"productType\":\"CAA-CAELT\",\"effectiveDate\":\"02-MAY-2025\",\"apacStatus\":\"Active\",\"apacOpenDate\":\"05-MAY-2025\"}],\"partyDocuments\":{\"EKYCAUTHIM\":{\"Document_Code\":\"EKYCAUTHIM\",\"Document_Name\":\"EKYC data received from UIDAI AND Video KYC\",\"Document_Receipt_Date\":\"05-MAY-25\",\"Document_Valid_From_Date\":\"\",\"Document_Valid_To_Date\":\"\",\"Document_Number\":\"\",\"Document_Type\":\"B\"}},\"passportNumber\":\"\",\"partyReportClass\":[{\"classificationCode\":\"Override Type\",\"Value\":\"Related to Corp ID\",\"value\":\"Related to Corp ID\"}],\"classification\":\"K_PLC\",\"fatcaDetails\":{\"FATCA_received_flag\":\"\",\"FATCA_reportable_flag\":\"\",\"Date_of_FATCA_flag_updation\":\"\"},\"voterId\":\"\",\"relCorpPartyId\":\"**********\",\"corporateInformation\":{\"corpName\":\"\",\"registrationNumber\":\"\",\"registrationDate\":\"\",\"expiryDate\":\"\",\"place\":\"\",\"ownerName\":\"\",\"industry\":\"\",\"parentCompany\":\"\",\"establishedYear\":\"\",\"businessNature\":\"\",\"apexHolder\":\"\"},\"financialDetails\":[{\"incomeSlab\":\"02\",\"financialFromDate\":\"05-MAY-25\",\"financialToDate\":\"\",\"turnOver\":\"\",\"sourceOfFund\":\"3\",\"incomeSlabDesc\":\"2,00,001 To 5,00,000\"}],\"crnList\":[\"1000450294\"],\"communicationAddress\":{\"line1\":\"A2 311, EMPIRE INDUSTRIAL\",\"line2\":\"ESTATE, CHIKLOLI, CHIKLOLI,\",\"line3\":\"AMBERNATH INDUSTRIAL ESTATE\",\"landmark\":null,\"pincode\":\"421505\",\"state\":\"Maharashtra\",\"stateCode\":\"MH\",\"finacleStateCode\":null,\"city\":\"Thane\",\"cityCode\":\"THANE\",\"finacleCityCode\":null,\"country\":\"INDIA\",\"countryCode\":null,\"bcifSourceOfAddress\":null},\"overseaAddress\":{\"line1\":\"nageshwar colo, naviganj, ps\",\"line2\":\"-bhagwan bazar, Saran, Chapra\",\"line3\":\"\",\"landmark\":null,\"pincode\":\"841301\",\"state\":\"Bihar\",\"stateCode\":\"BI\",\"finacleStateCode\":null,\"city\":\"Saran_\",\"cityCode\":\"SARAN_\",\"finacleCityCode\":null,\"country\":\"INDIA\",\"countryCode\":null,\"bcifSourceOfAddress\":null},\"email\":\"<EMAIL>\",\"emailValidated\":true,\"mothersMaidenName\":\"SUMII\",\"fatherName\":\"RAMCHANDRA \",\"maritalStatus\":{\"code\":\"S\",\"value\":null},\"occupation\":{\"code\":\"B\",\"value\":\"Business\"},\"sourceOfIncome\":{\"code\":\"3\",\"value\":null},\"annualIncome\":{\"code\":\"02\",\"value\":\"2,00,001 To 5,00,000\"},\"kycStatus\":\"Pending\",\"preferredMobile\":\"\",\"flexiInfo\":[{\"infoCode\":\"KEYCUSTCAT\",\"value1\":\"D\",\"value2\":\"\",\"remarks\":\"\",\"Auth_date\":\"05-MAY-25\"}],\"pan\":\"**********\",\"panAadhaarLink\":\"Y\",\"aadhaarRefKey\":\"R23411703080\",\"title\":\"MISS\",\"firstName\":\"ANUSHKA\",\"middleName\":\"\",\"lastName\":\".\",\"fullName\":\"Anushka .\",\"shortName\":\"ANUSHKA .\",\"partyType\":\"I\",\"partyItType\":\"R\",\"dob\":\"21-DEC-1993\",\"gender\":\"F\",\"nonPreferredMobiles\":[]},\"corpCrnDetails\":{\"nationality\":{\"code\":\"\",\"value\":\"\"},\"primarySegment\":\"RL\",\"fcPkgPresent\":true,\"crnStatusCode\":\"\",\"crnStatusReason\":\"\",\"customerCategory\":\"D\",\"apacDetails\":[{\"pkg\":\"FC\",\"apac\":\"**********\",\"relationType\":\"Sole Account Holder\",\"productType\":\"CAA-CAELT\",\"effectiveDate\":\"02-MAY-2025\",\"apacStatus\":\"Active\",\"apacOpenDate\":\"05-MAY-2025\"},{\"pkg\":\"FC\",\"apac\":\"**********\",\"relationType\":\"Sole Account Holder\",\"productType\":\"CAA-CINST\",\"effectiveDate\":\"02-MAY-2025\",\"apacStatus\":\"Total Freeze - Active\",\"apacOpenDate\":\"02-MAY-2025\"}],\"partyDocuments\":{\"MSME_Udyam\":{\"Document_Code\":\"MSME_Udyam\",\"Document_Name\":\"MSME and Udyam Registration Certificate\",\"Document_Receipt_Date\":\"05-MAY-25\",\"Document_Valid_From_Date\":\"\",\"Document_Valid_To_Date\":\"\",\"Document_Number\":\"\",\"Document_Type\":\"A\"},\"FCTRYREG\":{\"Document_Code\":\"FCTRYREG\",\"Document_Name\":\"Factory Registration certificate issued by any state OR central govt authority\",\"Document_Receipt_Date\":\"05-MAY-25\",\"Document_Valid_From_Date\":\"\",\"Document_Valid_To_Date\":\"\",\"Document_Number\":\"\",\"Document_Type\":\"I\"}},\"passportNumber\":\"\",\"partyReportClass\":[{\"classificationCode\":\"Override Type\",\"Value\":\"Distinct\",\"value\":\"Distinct\"}],\"classification\":\"K_PLC\",\"fatcaDetails\":{\"FATCA_received_flag\":\"Y\",\"FATCA_reportable_flag\":\"N\",\"Date_of_FATCA_flag_updation\":\"05-MAY-25\"},\"voterId\":\"\",\"relCorpPartyId\":\"\",\"corporateInformation\":{\"corpName\":\"\",\"registrationNumber\":\"1235677\",\"registrationDate\":\"05-MAY-2008\",\"expiryDate\":\"\",\"place\":\"\",\"ownerName\":\"\",\"industry\":\"PMP\",\"parentCompany\":\"\",\"establishedYear\":\"2008\",\"businessNature\":\"SVC\",\"apexHolder\":\"\"},\"financialDetails\":[{\"incomeSlab\":\"\",\"financialFromDate\":\"05-MAY-25\",\"financialToDate\":\"\",\"turnOver\":\"400000\",\"sourceOfFund\":\"3\",\"incomeSlabDesc\":\"\"}],\"crnList\":[\"**********\"],\"etbAccountDetailList\":[{\"accountClosed\":\"N\",\"activeStatus\":null,\"ifsc\":\"KKBK0001383\",\"schemeType\":\"CAA\",\"accountHolderRelationCode\":\"SOW\",\"accountBalance\":null,\"isLienVerificationPending\":null,\"accountNumber\":\"**********\",\"schemeCode\":\"CAELT\",\"status\":\"A\",\"accountInquiryUsingCrnStatus\":null,\"closureDate\":null,\"freezeDetailList\":null,\"lien\":null,\"nomineeDetailsList\":null,\"accountFrozen\":false,\"modeOfOperation\":\"009\",\"branchCode\":\"1383\",\"branchName\":\"MUMBAI - SANTACRUZ - S.V.ROAD\",\"accountType\":null},{\"accountClosed\":\"N\",\"activeStatus\":null,\"ifsc\":\"KKBK0000958\",\"schemeType\":\"CAA\",\"accountHolderRelationCode\":\"SOW\",\"accountBalance\":null,\"isLienVerificationPending\":null,\"accountNumber\":\"**********\",\"schemeCode\":\"CINST\",\"status\":\"A\",\"accountInquiryUsingCrnStatus\":null,\"closureDate\":null,\"freezeDetailList\":null,\"lien\":null,\"nomineeDetailsList\":null,\"accountFrozen\":true,\"modeOfOperation\":null,\"branchCode\":\"0958\",\"branchName\":\"MUMBAI-NARIMAN POINT\",\"accountType\":null}],\"communicationAddress\":{\"line1\":\"A2 311, EMPIRE INDUSTRIAL\",\"line2\":\"ESTATE, CHIKLOLI, CHIKLOLI,\",\"line3\":\"AMBERNATH INDUSTRIAL ESTATE\",\"landmark\":null,\"pincode\":\"421505\",\"state\":\"Maharashtra\",\"stateCode\":\"MH\",\"finacleStateCode\":null,\"city\":\"Thane\",\"cityCode\":\"THANE\",\"finacleCityCode\":null,\"country\":\"INDIA\",\"countryCode\":null,\"bcifSourceOfAddress\":null},\"overseaAddress\":{\"line1\":\"Flat560\",\"line2\":\"Sankar suvan nagar\",\"line3\":\"SBI life\",\"landmark\":null,\"pincode\":\"400065\",\"state\":\"Maharashtra\",\"stateCode\":\"MH\",\"finacleStateCode\":null,\"city\":\"Mumbai\",\"cityCode\":\"MUMBAI\",\"finacleCityCode\":null,\"country\":\"INDIA\",\"countryCode\":null,\"bcifSourceOfAddress\":null},\"email\":\"<EMAIL>\",\"emailValidated\":false,\"mothersMaidenName\":\"\",\"fatherName\":\"\",\"maritalStatus\":{\"code\":\"\",\"value\":null},\"occupation\":{\"code\":\"\",\"value\":\"\"},\"sourceOfIncome\":{\"code\":\"3\",\"value\":null},\"annualIncome\":{\"code\":\"\",\"value\":\"\"},\"kycStatus\":\"Pending\",\"preferredMobile\":\"************\",\"flexiInfo\":[{\"infoCode\":\"KEYCUSTCAT\",\"value1\":\"D\",\"value2\":\"\",\"remarks\":\"\",\"Auth_date\":\"05-MAY-25\"}],\"pan\":\"\",\"panAadhaarLink\":\"NA\",\"aadhaarRefKey\":\"\",\"title\":\"\",\"firstName\":\"\",\"middleName\":\"\",\"lastName\":\"M R TECHNOLOGY\",\"fullName\":\" M R Technology\",\"shortName\":\"M R TECHNOLOGY\",\"partyType\":\"C\",\"partyItType\":\"SP\",\"dob\":\"\",\"gender\":\"\",\"nonPreferredMobiles\":[]},\"upiOnRupayCreditCardEnabled\":true,\"selectedCurrentAccountNumber\":\"**********\",\"qrDisplayName\":\"Mrf tyres company\"}";
        MerchantOnboardingJourneyMetadata journeyMetadata = null;
        try {
            journeyMetadata = new ObjectMapper().readValue(data, MerchantOnboardingJourneyMetadata.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        return journeyMetadata;
    }
}