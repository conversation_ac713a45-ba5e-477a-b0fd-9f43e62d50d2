package com.kotak.unified.dbservice.transformer;

import com.kotak.unified.db.model.CreateResumeJourneyNotificationTrackNextCheckRecordRequest;
import com.kotak.unified.db.model.CreateResumeJourneyNotificationTrackNextCheckRecordResponse;
import com.kotak.unified.db.model.GetResumeJourneyNotificationTrackNextCheckRecordResponse;
import com.kotak.unified.db.model.UpdateResumeJourneyNotificationTrackNextCheckRecordRequest;
import com.kotak.unified.orchestrator.common.dbmodels.resumenotification.ResumeJourneyNotificationTrackNextCheck;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.time.Instant;

import static com.kotak.unified.dbservice.TestUtils.LEAD_ID;
import static org.junit.jupiter.api.Assertions.*;

class ResumeJourneyNotificationTrackNextCheckTransformerTest {
    private final ResumeJourneyNotificationTrackNextCheckTransformer resumeJourneyNotificationTrackNextCheckTransformer = new ResumeJourneyNotificationTrackNextCheckTransformer();

    private static Instant instant1 = Instant.ofEpochSecond(1720017971);
    private static Instant instant2 = Instant.ofEpochSecond(1720017882);
    @Test
    void test_convertRequestToResumeJourneyNotificationTrackNextCheckRecord() {
        CreateResumeJourneyNotificationTrackNextCheckRecordRequest createResumeJourneyNotificationTrackNextCheckRecordRequest =
                CreateResumeJourneyNotificationTrackNextCheckRecordRequest.builder()
                        .leadTrackingNumber(LEAD_ID)
                        .nextCheckTime(instant1)
                        .build();
        ResumeJourneyNotificationTrackNextCheck resumeJourneyNotificationTrackNextCheck =
                resumeJourneyNotificationTrackNextCheckTransformer.convertRequestToResumeJourneyNotificationTrackNextCheckRecord(createResumeJourneyNotificationTrackNextCheckRecordRequest);
        Assertions.assertEquals(createResumeJourneyNotificationTrackNextCheckRecordRequest.getLeadTrackingNumber(),
                resumeJourneyNotificationTrackNextCheck.getLeadTrackingNumber());
        Assertions.assertEquals(createResumeJourneyNotificationTrackNextCheckRecordRequest.getNextCheckTime(),
                resumeJourneyNotificationTrackNextCheck.getNextCheckTime());
    }

    @Test
    void test_getCreateResumeJourneyNotificationTrackNextCheckRecordResponse() {
        boolean isSuccess = true;
        CreateResumeJourneyNotificationTrackNextCheckRecordResponse createResumeJourneyNotificationTrackNextCheckRecordResponse =
                resumeJourneyNotificationTrackNextCheckTransformer.getCreateResumeJourneyNotificationTrackNextCheckRecordResponse(isSuccess);
        Assertions.assertEquals(createResumeJourneyNotificationTrackNextCheckRecordResponse.getIsCreateRecordSuccessful(),
                isSuccess);
    }

    @Test
    void test_convertModelToGetResumeJourneyNotificationTrackNextCheckRecordResponse() {
        ResumeJourneyNotificationTrackNextCheck resumeJourneyNotificationTrackNextCheck = ResumeJourneyNotificationTrackNextCheck
                .builder()
                .leadTrackingNumber(LEAD_ID)
                .nextCheckTime(instant1)
                .build();
        GetResumeJourneyNotificationTrackNextCheckRecordResponse getResumeJourneyNotificationTrackNextCheckRecordResponse =
                resumeJourneyNotificationTrackNextCheckTransformer.convertModelToGetResumeJourneyNotificationTrackNextCheckRecordResponse(resumeJourneyNotificationTrackNextCheck);

        Assertions.assertEquals(resumeJourneyNotificationTrackNextCheck.getLeadTrackingNumber(),
                getResumeJourneyNotificationTrackNextCheckRecordResponse.getLeadTrackingNumber());
        Assertions.assertEquals(resumeJourneyNotificationTrackNextCheck.getNextCheckTime(),
                getResumeJourneyNotificationTrackNextCheckRecordResponse.getNextCheckTime());
    }

    @Test
    void updateResumeJourneyNotificationTrackNextCheckDetails() {
        ResumeJourneyNotificationTrackNextCheck resumeJourneyNotificationTrackNextCheck = ResumeJourneyNotificationTrackNextCheck
                .builder()
                .leadTrackingNumber(LEAD_ID)
                .nextCheckTime(instant1)
                .build();

        UpdateResumeJourneyNotificationTrackNextCheckRecordRequest updateResumeJourneyNotificationTrackNextCheckRecordRequest =
                UpdateResumeJourneyNotificationTrackNextCheckRecordRequest.builder()
                        .nextCheckTime(instant2)
                        .build();

        resumeJourneyNotificationTrackNextCheckTransformer.updateResumeJourneyNotificationTrackNextCheckDetails(resumeJourneyNotificationTrackNextCheck, updateResumeJourneyNotificationTrackNextCheckRecordRequest);
        Assertions.assertEquals(resumeJourneyNotificationTrackNextCheck.getNextCheckTime(), instant2);
    }
}