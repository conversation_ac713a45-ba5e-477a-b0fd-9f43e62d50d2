FROM 147642600964.dkr.ecr.ap-south-1.amazonaws.com/kmbl/corretto18withcert:v1-1202430 as builder
USER appuser
WORKDIR "/home/<USER>"
COPY target/*.jar application.jar
RUN java -Djarmode=layertools -jar application.jar extract
FROM 147642600964.dkr.ecr.ap-south-1.amazonaws.com/kmbl/corretto18withcert:v1-1202430
USER appuser
WORKDIR "/"
COPY --from=builder /home/<USER>/dependencies/ ./
RUN true
COPY --from=builder /home/<USER>/spring-boot-loader/ ./
RUN true
COPY --from=builder /home/<USER>/snapshot-dependencies/ ./
RUN true
COPY --from=builder /home/<USER>/application/ ./
ENTRYPOINT ["java", "org.springframework.boot.loader.launch.JarLauncher"]
#