trigger: none

resources:
  repositories:
    - repository: templates
      type: git
      url: https://dev.azure.com/kmbl-devops//Builder%20Tools/_git/cicd-templates
      name: Builder Tools/cicd-templates
      ref: refs/tags/v1.3.12

variables:
  - group: uao_aws_service_Dev
  - name: AWS-ACCESS-KEY-ID
    value: $[variables.aws_access_key_id]
  - name: AWS-SECRET-ACCESS-KEY
    value: $[variables.aws_secret_key_id]
  - name: AWS-DEFAULT-REGION
    value: $[variables.aws_region]
  - name: AWS-ACCOUNT-ID
    value: $[variables.aws_account_id]
  - name: DOCKER_REPO_NAME
    value: kotak-uao-db-service
  - name:  DOCKER_REPOSITORY
    value: '$(AWS-ACCOUNT-ID).dkr.ecr.ap-south-1.amazonaws.com/$(DOCKER_REPO_NAME)'
  - name: ARTIFACT_NAME
    value: 'Kotak-Uao'
  - name: applicationPort
    value: 8080
  - name: AccountID
    value: ************.dkr.ecr.ap-south-1.amazonaws.com
  - name: appName
    value: 'kotak-uao-db-service'


stages:
  ## Build stage
  - template: templates/build-stage/build-java-maven.yaml@templates
    parameters:
      settingsFile: 'uao_common_settings.xml'
      mavenGoals: '-U clean package'
      poolName: 'MB2_DOCKER_POOL'
      dockerImageDetails:
        - dockerFilePath: '$(system.defaultworkingdirectory)/JAVA'
          dockerRepoName: 'kotak-uao-db-service'
      jdkVersionOption: 'default'
      filePathOfCodeCoverageHtml: 'target/site/jacoco/index.html'
      dockerNetwork: host

  ## Code Quality stage
  - template: templates/code-quality-stage/scan-java-code.yaml@templates
    parameters:
      sonarsources: .
      #poolName: 'MB2_DOCKER_POOL'
      projectNameForCheckMarx: 'Kotak Mobile Banking 2.0'
      projectIDForCheckMarx: 'APP-01756'
      CheckmarxServiceConnection: 'uao-Checkmarx-Connection'
      #syncMode: 'true'
      infraRepositoryName: 'Kotak-Uao-Platform'
      infraRepositoryBranch: 'main'
      prismaServiceConnection: 'uao-prisma-access'
      dependencyCheckSuppressionFile: '$(system.defaultworkingdirectory)/dependency-check-suppression.xml'
      disableOpenSSFScorecard: 'true' ## only for maven builds
      dockerImageDetails:
        - dockerFilePath: '$(system.defaultworkingdirectory)/JAVA'
          dockerRepoName: kotak-uao-db-service
      manifestsFolder: |
        $(System.DefaultWorkingDirectory)/Kotak-Uao-Platform/_helm_charts/kmbl/helm-kmbl-service-chart-0.0.1
        $(System.DefaultWorkingDirectory)/Kotak-Uao-Platform/envs/_base/applications
        $(System.DefaultWorkingDirectory)/Kotak-Uao-Platform/envs/dev/03-applications/_base 
        $(System.DefaultWorkingDirectory)/Kotak-Uao-Platform/envs/dev/03-applications/kotak-uao-backend-services/

    ## Test stage
  - template: templates/test-stage/test-java.yaml@templates
    parameters:
      imageName: '************.dkr.ecr.ap-south-1.amazonaws.com/kotak-uao-db-service'
      imageVersion: '$(Build.BuildId)-dev'
      infraRepositoryName: 'Kotak-Uao-Platform'
      infraRepositoryBranch: 'main'
      poolName: 'Azure Pipelines'
      applicationPort: 8080
      appName: 'kotak-uao-db-service'
      pathsToHelmCharts: '_helm_charts/kmbl/helm-kmbl-service-chart-0.0.1'
      valuesFiles: 'envs/_base/applications/service/values.yaml,envs/dev/03-applications/_base/service/values.yaml,envs/dev/03-applications/kotak-uao-backend-services/kotak-uao-db-service/values.yaml,envs/dev/03-applications/kotak-uao-backend-services/kotak-uao-db-service/dev-cm.yaml'
      dockerImageDetails:
        - dockerFilePath: '$(system.defaultworkingdirectory)/JAVA'
          dockerRepoName: kotak-uao-db-service

  ## Package stage
  - template: templates/package-stage/package-java.yaml@templates
    parameters:
      dockerImageDetails:
        - dockerFilePath: '$(system.defaultworkingdirectory)/JAVA'
          dockerRepoName: kotak-uao-db-service
      poolName: 'MB2_DOCKER_POOL'

